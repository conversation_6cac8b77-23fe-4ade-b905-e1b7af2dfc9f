using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.Entities
{
    public enum AppealBody
    {
        IRD,
        RevenueTribunal,
        SupremeCourt
    }
    public class Negotiation
    {
        public Guid NegotiationId { get; set; }
        public Guid AppealId { get; set; }
        public string OfficerId { get; set; } = null!;
        public decimal NegotiatedAmount { get; set; }
        public DateTime NegotiationDate { get; set; } = DateTime.UtcNow;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Appeal Completion Notice Fields
        public string? TaxpayerName { get; set; }
        public string? PANNumber { get; set; }
        public string? TaxOfficeAddress { get; set; }
        public AppealBody AppealBody { get; set; }
        public string? DecisionNumber { get; set; }
        public DateTime DecisionDate { get; set; }
        public virtual ICollection<TaxPeriod> TaxPeriods { get; set; } = new List<TaxPeriod>();
        public decimal OriginalTax { get; set; }
        public decimal OriginalPenalty { get; set; }
        public decimal OriginalFee { get; set; }
        public decimal OriginalInterest { get; set; }
        public decimal DecidedTax { get; set; }
        public decimal DecidedPenalty { get; set; }
        public decimal DecidedFee { get; set; }
        public decimal DecidedInterest { get; set; }
        public bool IsWithdrawn { get; set;}
        public string? Status { get; set; }
        public string? Remarks { get; set; }

        // Navigation properties
        public virtual Appeal Appeal { get; set; } = null!;
        public virtual ApplicationUser Officer { get; set; } = null!;
    }
}
