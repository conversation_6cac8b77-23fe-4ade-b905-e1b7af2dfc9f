using System;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.Entities
{
    public class ReturnFiling
    {
        [Key]
        public Guid ReturnFilingId { get; set; }

        [Required]
        public Guid PropertyId { get; set; }
        public Property Property { get; set; } = null!;

        [Required]
        public Guid FiscalYearId { get; set; }
        public FiscalYear FiscalYear { get; set; } = null!;

        [Required]
        [MaxLength(450)]
        public string SubmittedByUserId { get; set; } = string.Empty;
        public ApplicationUser SubmittedByUser { get; set; } = null!;

        [Required]
        public DateTime SubmissionDate { get; set; }

        public string? AdditionalNotes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}
