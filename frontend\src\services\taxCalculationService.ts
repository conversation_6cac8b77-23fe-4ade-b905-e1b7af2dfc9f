import api from './api';
import type { TaxCalculationResult } from '../types';

/**
 * API service for tax calculation functionality
 */
export const taxCalculationService = {
  /**
   * Get tax calculation for a specific property and fiscal year
   * @param propertyId - The ID of the property
   * @param fiscalYearId - The ID of the fiscal year
   * @returns Promise containing the tax calculation result
   */
  getTaxCalculation: async (propertyId: string, fiscalYearId: string): Promise<TaxCalculationResult> => {
    const response = await api.get(`/properties/${propertyId}/tax-calculation`, {
      params: {
        fiscalYearId
      }
    });
    return response.data;
  },

  /**
   * Get tax calculation for a property using the current active fiscal year
   * @param propertyId - The ID of the property
   * @returns Promise containing the tax calculation result
   */
  getCurrentTaxCalculation: async (propertyId: string): Promise<TaxCalculationResult> => {
    // First get the active fiscal year
    const fiscalYearResponse = await api.get('/FiscalYears/active');
    const activeFiscalYear = fiscalYearResponse.data;
    
    // Then calculate tax for that fiscal year
    return await taxCalculationService.getTaxCalculation(propertyId, activeFiscalYear.fiscalYearId);
  }
};

export default taxCalculationService;
