using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProvincesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public ProvincesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetProvinces()
        {
            try
            {
                var provinces = await _context.Provinces
                    .OrderBy(p => p.Name)
                    .Select(p => new
                    {
                        p.ProvinceId,
                        p.Name,
                        p.Code
                    })
                    .ToListAsync();

                return Ok(provinces);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving provinces", error = ex.Message });
            }
        }

        [HttpGet("{id}/districts")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetDistrictsByProvince(Guid id)
        {
            try
            {
                var districts = await _context.Districts
                    .Where(d => d.ProvinceId == id)
                    .OrderBy(d => d.Name)
                    .Select(d => new
                    {
                        d.DistrictId,
                        d.Name,
                        d.Code,
                        d.ProvinceId
                    })
                    .ToListAsync();

                return Ok(districts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving districts", error = ex.Message });
            }
        }
    }
}
