import api from './api';
import type { FiscalYear } from './taxConfigService';

// API functions for fiscal year management
export const fiscalYearService = {
  // Get all fiscal years
  getAll: async (): Promise<FiscalYear[]> => {
    const response = await api.get('/FiscalYears');
    return response.data;
  },

  // Get active fiscal year
  getActive: async (): Promise<FiscalYear> => {
    const response = await api.get('/FiscalYears/active');
    return response.data;
  },

  // Get fiscal year by ID
  getById: async (id: string): Promise<FiscalYear> => {
    const response = await api.get(`/FiscalYears/${id}`);
    return response.data;
  },

  // Create a new fiscal year
  create: async (data: {
    name: string;
    startDate: string;
    endDate: string;
    isActive: boolean;
  }): Promise<FiscalYear> => {
    // Convert string dates to ISO format if they're not already
    const formattedData = {
      ...data,
      startDate: new Date(data.startDate).toISOString(),
      endDate: new Date(data.endDate).toISOString()
    };
    const response = await api.post('/FiscalYears', formattedData);
    return response.data;
  },

  // Update a fiscal year
  update: async (
    id: string,
    data: {
      name?: string;
      startDate?: string;
      endDate?: string;
      isActive?: boolean;
    }
  ): Promise<FiscalYear> => {
    // Format dates if they exist
    const formattedData = { ...data };
    if (formattedData.startDate) {
      formattedData.startDate = new Date(formattedData.startDate).toISOString();
    }
    if (formattedData.endDate) {
      formattedData.endDate = new Date(formattedData.endDate).toISOString();
    }
    const response = await api.put(`/FiscalYears/${id}`, formattedData);
    return response.data;
  },

  // Set a fiscal year as active
  setActive: async (id: string): Promise<FiscalYear> => {
    // Since there's no explicit activate endpoint, we'll update with isActive=true
    const response = await api.put(`/FiscalYears/${id}`, { isActive: true });
    return response.data;
  }
};

export default fiscalYearService;
