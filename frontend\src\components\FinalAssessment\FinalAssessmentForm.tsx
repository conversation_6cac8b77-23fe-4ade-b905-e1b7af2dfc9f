import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import type {
    FinalAssessmentCreateDto,
    FinalAssessmentDetailDto,
} from "../../types/finalAssessment";
import type { Province, District, User } from "../../types";
import type { MunicipalityLocation } from "../../services/api";
import type { ReturnFiling } from "../../services/api";
import type { PreliminaryAssessmentDto } from "../../types/preliminaryAssessment";

// Interface for transformed return filing data
interface TransformedReturnFiling {
    returnFilingId: string;
    propertyId: string;
    propertyAddress: string;
    fiscalYearId: string;
    fiscalYearName: string;
    submissionDate: string;
    createdAt: string;
}

interface ReturnFilingWithRelations extends ReturnFiling {
    property?: {
        address?: string;
    };
    fiscalYear?: {
        name?: string;
    };
}
import type { FiscalYear } from "../../services/taxConfigService";
import finalAssessmentService from "../../services/finalAssessmentService";
import preliminaryAssessmentService from "../../services/preliminaryAssessmentService";
import fiscalYearService from "../../services/fiscalYearService";
import userService from "../../services/userService";
import propertyService from "../../services/propertyService";
import AdminLayout from "../admin/AdminLayout";
import AdminForm from "../admin/AdminForm";
import AdminTable from "../admin/AdminTable";

interface FinalAssessmentFormProps {
    mode: "create" | "edit";
}

export const FinalAssessmentForm: React.FC<FinalAssessmentFormProps> = ({
    mode,
}) => {
    const { id: assessmentId } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [taxpayerLoading, setTaxpayerLoading] = useState(false);
    const [taxpayersLoading, setTaxpayersLoading] = useState(false);
    const [taxpayerFound, setTaxpayerFound] = useState(false);
    const [editingDetailIndex, setEditingDetailIndex] = useState<number | null>(
        null
    );

    // Form data
    const [formData, setFormData] = useState<FinalAssessmentCreateDto>({
        taxpayerRegistration: "",
        fiscalYearId: "",
        municipalityId: "",
        returnFilingId: "",
        preliminaryAssessmentId: "",
        taxpayerName: "",
        address: "",
        phone: "",
        assessmentPeriodFrom: new Date(),
        assessmentPeriodTo: new Date(),
        sectionsRules: "",
        bankName: "",
        branchName: "",
        reasonCode: "",
        appealNumber: "",
        reasonDescription: "",
        interestPenaltyCalculationDate: new Date(),
        finalAssessmentDate: new Date(),
        taxDetails: [],
    });

    // Dropdown data
    const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
    const [provinces, setProvinces] = useState<Province[]>([]);
    const [districts, setDistricts] = useState<District[]>([]);
    const [municipalities, setMunicipalities] = useState<MunicipalityLocation[]>([]);
    const [availableTaxpayers, setAvailableTaxpayers] = useState<User[]>([]);
    const [preliminaryAssessments, setPreliminaryAssessments] = useState<PreliminaryAssessmentDto[]>(
        []
    );
    const [filedTaxes, setFiledTaxes] = useState<TransformedReturnFiling[]>([]);

    // Location hierarchy state
    const [selectedProvince, setSelectedProvince] = useState("");
    const [selectedDistrict, setSelectedDistrict] = useState("");
    const [selectedMunicipality, setSelectedMunicipality] = useState("");
    const [wardNumber, setWardNumber] = useState("");
    const [selectedTaxpayer, setSelectedTaxpayer] = useState("");

    // Current tax detail being added/edited
    const [currentDetail, setCurrentDetail] =
        useState<FinalAssessmentDetailDto>({
            serialNumber: 1,
            filingPeriod: "",
            taxPeriod: "",
            taxYear: "",
            assessedAmount: 0,
            penalty: 0,
            additionalAmount: 0,
            interest: 0,
            total: 0,
        });

    const loadFiledTaxes = useCallback(async (taxpayerId: string) => {
        try {
            const filedReturns = await propertyService.getTaxpayerReturnFilings(
                taxpayerId
            );
            const transformedData = filedReturns.map((filing: ReturnFilingWithRelations) => ({
                returnFilingId: filing.id,
                propertyId: filing.propertyId,
                propertyAddress: filing.property?.address || 'N/A',
                fiscalYearId: filing.fiscalYearId,
                fiscalYearName: filing.fiscalYear?.name || 'N/A',
                submissionDate: filing.submissionDate,
                createdAt: filing.createdAt,
            }));
            setFiledTaxes(transformedData);
        } catch (error) {
            console.error("Error loading filed taxes:", error);
            if (error instanceof Error) {
                toast.error("Failed to load filed tax returns");
            } else {
                toast.error("Failed to load filed tax returns");
            }
        }
    }, []);

    const loadAssessment = useCallback(async (id: string) => {
        try {
            setLoading(true);
            const assessment = await finalAssessmentService.getById(id);
            if (assessment) {
                setFormData({
                    taxpayerRegistration: assessment.taxpayerRegistration,
                    taxpayerName: assessment.taxpayerName,
                    address: assessment.address || "",
                    phone: assessment.phone || "",
                    assessmentPeriodFrom: new Date(
                        assessment.assessmentPeriodFrom
                    ),
                    assessmentPeriodTo: new Date(assessment.assessmentPeriodTo),
                    sectionsRules:
                        (assessment.actSection || "") +
                        (assessment.rule ? " - " + assessment.rule : ""),
                    appealNumber: assessment.appealNumber || "",
                    finalAssessmentDate: assessment.finalAssessmentDate
                        ? new Date(assessment.finalAssessmentDate)
                        : new Date(),
                    interestPenaltyCalculationDate:
                        assessment.interestCalculationDate
                            ? new Date(assessment.interestCalculationDate)
                            : new Date(),
                    bankName: assessment.bank || "",
                    branchName: assessment.branch || "",
                    reasonCode: assessment.reasonForAssessment || "",
                    reasonDescription: assessment.otherReasonDescription || "",
                    fiscalYearId: assessment.fiscalYearId || "",
                    municipalityId: assessment.municipalityId || "",
                    returnFilingId: "",
                    preliminaryAssessmentId:
                        assessment.preliminaryAssessmentId || "",
                    taxDetails: assessment.taxDetails || [],
                });
                setTaxpayerFound(true);
                if (assessment.taxpayerRegistration) {
                    try {
                        const user = await userService.getUserByRegistration(
                            assessment.taxpayerRegistration
                        );
                        if (user && user.id) {
                            await loadFiledTaxes(user.id);
                        }
                    } catch (error) {
                        console.error(
                            "Error loading taxpayer for filed taxes:",
                            error
                        );
                    }
                }
            }
        } catch (error) {
            console.error("Error loading assessment:", error);
            toast.error("Failed to load assessment");
        } finally {
            setLoading(false);
        }
    }, [loadFiledTaxes]);

    useEffect(() => {
        loadInitialData();
        if (mode === "edit" && assessmentId) {
            loadAssessment(assessmentId);
        }
    }, [mode, assessmentId, loadAssessment]);

    const loadInitialData = async () => {
        try {
            const [fiscalYearsData, provincesData] = await Promise.all([
                fiscalYearService.getAll(),
                userService.getProvinces(),
            ]);
            setFiscalYears(fiscalYearsData);
            setProvinces(provincesData);
        } catch (error) {
            console.error("Error loading initial data:", error);
            toast.error("Failed to load form data");
        }
    };

    // Load districts when province is selected
    useEffect(() => {
        const loadDistricts = async () => {
            if (selectedProvince) {
                try {
                    const districtsData =
                        await userService.getDistrictsByProvince(
                            selectedProvince
                        );
                    setDistricts(districtsData);
                    setSelectedDistrict("");
                    setMunicipalities([]);
                    setSelectedMunicipality("");
                    setWardNumber("");
                    setAvailableTaxpayers([]);
                    setSelectedTaxpayer("");
                } catch (error) {
                    console.error("Error loading districts:", error);
                }
            } else {
                setDistricts([]);
                setSelectedDistrict("");
                setMunicipalities([]);
                setSelectedMunicipality("");
            }
        };

        loadDistricts();
    }, [selectedProvince]);

    // Load municipalities when district is selected
    useEffect(() => {
        const loadMunicipalities = async () => {
            if (selectedDistrict) {
                try {
                    const municipalitiesData =
                        await userService.getMunicipalitiesByDistrict(
                            selectedDistrict
                        );
                    setMunicipalities(municipalitiesData);
                    setSelectedMunicipality("");
                    setWardNumber("");
                    setAvailableTaxpayers([]);
                    setSelectedTaxpayer("");
                } catch (error) {
                    console.error("Error loading municipalities:", error);
                }
            } else {
                setMunicipalities([]);
                setSelectedMunicipality("");
            }
        };

        loadMunicipalities();
    }, [selectedDistrict]);

    // Load taxpayers when municipality and ward are selected
    useEffect(() => {
        const loadTaxpayers = async () => {
            if (selectedMunicipality && wardNumber) {
                setTaxpayersLoading(true);
                try {
                    const taxpayersData = await userService.getTaxpayers(
                        selectedMunicipality,
                        wardNumber
                    );
                    setAvailableTaxpayers(taxpayersData);
                    setSelectedTaxpayer("");
                } catch (error) {
                    console.error("Error loading taxpayers:", error);
                    setAvailableTaxpayers([]);
                } finally {
                    setTaxpayersLoading(false);
                }
            } else {
                setAvailableTaxpayers([]);
                setSelectedTaxpayer("");
            }
        };

        loadTaxpayers();
    }, [selectedMunicipality, wardNumber]);

    // Load preliminary assessments when taxpayer is selected
    useEffect(() => {
        const loadPreliminaryAssessments = async () => {
            if (formData.taxpayerRegistration) {
                try {
                    const assessments =
                        await preliminaryAssessmentService.getByTaxpayerRegistration(
                            formData.taxpayerRegistration
                        );
                    setPreliminaryAssessments(assessments.data);
                } catch (error) {
                    console.error(
                        "Error loading preliminary assessments:",
                        error
                    );
                    setPreliminaryAssessments([]);
                }
            } else {
                setPreliminaryAssessments([]);
            }
        };

        loadPreliminaryAssessments();
    }, [formData.taxpayerRegistration]);

    const handleTaxpayerSelection = async (taxpayerId: string) => {
        if (!taxpayerId) {
            setSelectedTaxpayer("");
            setTaxpayerFound(false);
            setFiledTaxes([]);
            // Reset fiscal years to all available when no taxpayer is selected
            try {
                const allFiscalYears = await fiscalYearService.getAll();
                setFiscalYears(allFiscalYears);
            } catch (error) {
                console.error("Error loading fiscal years:", error);
            }
            return;
        }

        const selectedUser = availableTaxpayers.find(
            (user) => user.id === taxpayerId
        );
        if (selectedUser) {
            setSelectedTaxpayer(taxpayerId);
            setFormData((prev) => ({
                ...prev,
                taxpayerRegistration:
                    selectedUser.citizenshipNumber || selectedUser.id || "",
                taxpayerName: selectedUser.fullName || "",
                address: "",
                phone: selectedUser.phoneNumber || "",
                municipalityId: selectedUser.municipalityId || "",
            }));
            setTaxpayerFound(true);
            await loadFiledTaxes(selectedUser.id);
        }
    };

    const lookupTaxpayer = async (registration: string) => {
        if (!registration.trim()) {
            setTaxpayerFound(false);
            setFiledTaxes([]);
            // Reset fiscal years to all available when no registration
            try {
                const allFiscalYears = await fiscalYearService.getAll();
                setFiscalYears(allFiscalYears);
            } catch (error) {
                console.error("Error loading fiscal years:", error);
            }
            return;
        }

        try {
            setTaxpayerLoading(true);
            const user = await userService.getUserByRegistration(registration);
            if (user) {
                setFormData((prev) => ({
                    ...prev,
                    taxpayerName: user.fullName || "",
                    address: "",
                    phone: user.phoneNumber || "",
                    accountNumber: user.pan || "",
                }));
                setTaxpayerFound(true);
                await loadFiledTaxes(user.id);
            } else {
                setTaxpayerFound(false);
                setFiledTaxes([]);
                // Reset fiscal years when taxpayer not found
                try {
                    const allFiscalYears = await fiscalYearService.getAll();
                    setFiscalYears(allFiscalYears);
                } catch (fiscalYearError) {
                    console.error(
                        "Error loading fiscal years:",
                        fiscalYearError
                    );
                }
                toast.error("Taxpayer not found");
            }
        } catch (error) {
            console.error("Error looking up taxpayer:", error);
            setTaxpayerFound(false);
            setFiledTaxes([]);
            // Reset fiscal years on lookup error
            try {
                const allFiscalYears = await fiscalYearService.getAll();
                setFiscalYears(allFiscalYears);
            } catch (fiscalYearError) {
                console.error("Error loading fiscal years:", fiscalYearError);
            }
            toast.error("Failed to lookup taxpayer");
        } finally {
            setTaxpayerLoading(false);
        }
    };



    const handleInputChange = (
        field: keyof FinalAssessmentCreateDto,
        value: string | Date | FinalAssessmentDetailDto[]
    ) => {
        setFormData((prev) => ({ ...prev, [field]: value }));

        if (field === "taxpayerRegistration" && typeof value === "string") {
            if (value !== selectedTaxpayer) {
                setSelectedTaxpayer("");
            }
            setTimeout(() => {
                if (value === formData.taxpayerRegistration) {
                    lookupTaxpayer(value);
                }
            }, 500);
        }

        // Auto-set fiscalYearId when a filed tax is selected
        if (field === "returnFilingId" && typeof value === "string" && value) {
            const selectedFiling = filedTaxes.find(
                (filing) => filing.returnFilingId === value
            );
            if (selectedFiling && selectedFiling.fiscalYearId) {
                setFormData((prev) => ({
                    ...prev,
                    fiscalYearId: selectedFiling.fiscalYearId,
                }));
            }
        }
    };

    const handleDetailChange = (
        field: keyof FinalAssessmentDetailDto,
        value: string | number
    ) => {
        setCurrentDetail((prev) => {
            const updated = { ...prev, [field]: value };
            if (
                [
                    "assessedAmount",
                    "penalty",
                    "additionalAmount",
                    "interest",
                ].includes(field)
            ) {
                updated.total =
                    updated.assessedAmount +
                    updated.penalty +
                    updated.additionalAmount +
                    updated.interest;
            }
            return updated;
        });
    };

    const addOrUpdateDetail = () => {
        if (!currentDetail.filingPeriod) {
            toast.error("Please select a fiscal year for the tax detail.");
            return;
        }
        if (!currentDetail.taxYear) {
            toast.error("Please enter a tax year for the tax detail.");
            return;
        }

        if (editingDetailIndex !== null) {
            const updatedDetails = [...formData.taxDetails];
            updatedDetails[editingDetailIndex] = currentDetail;
            setFormData((prev) => ({ ...prev, taxDetails: updatedDetails }));
            setEditingDetailIndex(null);
        } else {
            setFormData((prev) => ({
                ...prev,
                taxDetails: [...prev.taxDetails, currentDetail],
            }));
        }

        setCurrentDetail({
            serialNumber: formData.taxDetails.length + 1,
            filingPeriod: "",
            taxPeriod: "",
            taxYear: "",
            assessedAmount: 0,
            penalty: 0,
            additionalAmount: 0,
            interest: 0,
            total: 0,
        });
    };

    const editDetail = (index: number) => {
        setCurrentDetail(formData.taxDetails[index]);
        setEditingDetailIndex(index);
    };

    const removeDetail = (index: number) => {
        const updatedDetails = formData.taxDetails.filter(
            (_, i) => i !== index
        );
        const reorderedDetails = updatedDetails.map((detail, i) => ({
            ...detail,
            serialNumber: i + 1,
        }));
        setFormData((prev) => ({ ...prev, taxDetails: reorderedDetails }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (
            !formData.taxpayerRegistration ||
            !formData.taxpayerName ||
            !formData.fiscalYearId ||
            !formData.municipalityId
        ) {
            toast.error("Please fill in all required fields");
            return;
        }

        if (!taxpayerFound) {
            toast.error("Please select a valid taxpayer");
            return;
        }

        // Check if there are unsaved tax details in the current form
        const hasPartialTaxDetail =
            currentDetail.filingPeriod?.trim() ||
            currentDetail.taxYear?.trim() ||
            currentDetail.assessedAmount > 0 ||
            currentDetail.penalty > 0 ||
            currentDetail.additionalAmount > 0 ||
            currentDetail.interest > 0;

        if (hasPartialTaxDetail) {
            toast.error(
                "Please add the current tax detail before submitting or clear the form"
            );
            return;
        }

        if (formData.taxDetails.length === 0) {
            toast.error("Please add at least one tax detail");
            return;
        }

        try {
            setLoading(true);

            // Debug: Log the fiscalYearId being sent
            console.log("Form submission data:", {
                fiscalYearId: formData.fiscalYearId,
                fiscalYearIdType: typeof formData.fiscalYearId,
                formData: formData,
            });

            if (mode === "create") {
                await finalAssessmentService.create(formData);
                toast.success("Final assessment created successfully");
            } else if (mode === "edit" && assessmentId) {
                await finalAssessmentService.update(assessmentId, formData);
                toast.success("Final assessment updated successfully");
            }

            // Delay navigation to allow toast to be visible
            setTimeout(() => {
                navigate("/final-assessments");
            }, 2000);
        } catch (error: unknown) {
            const errorMessage =
                error instanceof Error &&
                "response" in error &&
                typeof error.response === "object" &&
                error.response !== null &&
                "data" in error.response &&
                typeof error.response.data === "object" &&
                error.response.data !== null &&
                "message" in error.response.data &&
                typeof error.response.data.message === "string"
                    ? error.response.data.message
                    : "Failed to save assessment";
            toast.error(errorMessage);
            console.error("Error saving assessment:", error);
        } finally {
            setLoading(false);
        }
    };

    const grandTotal = formData.taxDetails.reduce(
        (sum, detail) => sum + detail.total,
        0
    );

    const taxDetailColumns = [
        {
            accessor: "serialNumber" as keyof FinalAssessmentDetailDto,
            header: "S.N.",
        },
        {
            accessor: "filingPeriod" as keyof FinalAssessmentDetailDto,
            header: "Fiscal Year",
        },
        {
            accessor: "taxPeriod" as keyof FinalAssessmentDetailDto,
            header: "Tax Period",
        },
        {
            accessor: "taxYear" as keyof FinalAssessmentDetailDto,
            header: "Tax Year",
        },
        {
            accessor: (item: FinalAssessmentDetailDto) =>
                `Rs. ${item.assessedAmount.toLocaleString()}`,
            header: "Assessed Amount",
        },
        {
            accessor: (item: FinalAssessmentDetailDto) =>
                `Rs. ${item.penalty.toLocaleString()}`,
            header: "Penalty Amount",
        },
        {
            accessor: (item: FinalAssessmentDetailDto) =>
                `Rs. ${item.additionalAmount.toLocaleString()}`,
            header: "Additional Amount",
        },
        {
            accessor: (item: FinalAssessmentDetailDto) =>
                `Rs. ${item.interest.toLocaleString()}`,
            header: "Interest Amount",
        },
        {
            accessor: (item: FinalAssessmentDetailDto) =>
                `Rs. ${item.total.toLocaleString()}`,
            header: "Total Amount",
        },
    ];

    return (
        <AdminLayout
            title={
                mode === "create"
                    ? "Create Final Tax Assessment"
                    : "Edit Final Tax Assessment"
            }
        >
            <div className="space-y-6">
                <AdminForm onSubmit={handleSubmit}>
                    {/* Basic Information */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Basic Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Province *
                                </label>
                                <select
                                    value={selectedProvince}
                                    onChange={(e) =>
                                        setSelectedProvince(e.target.value)
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Province</option>
                                    {provinces.map((province) => (
                                        <option
                                            key={province.provinceId}
                                            value={province.provinceId}
                                        >
                                            {province.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    District *
                                </label>
                                <select
                                    value={selectedDistrict}
                                    onChange={(e) =>
                                        setSelectedDistrict(e.target.value)
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    disabled={!selectedProvince}
                                >
                                    <option value="">Select District</option>
                                    {districts.map((district) => (
                                        <option
                                            key={district.districtId}
                                            value={district.districtId}
                                        >
                                            {district.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Municipality *
                                </label>
                                <select
                                    value={selectedMunicipality}
                                    onChange={(e) => {
                                        setSelectedMunicipality(e.target.value);
                                        handleInputChange(
                                            "municipalityId",
                                            e.target.value
                                        );
                                    }}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    disabled={!selectedDistrict}
                                >
                                    <option value="">
                                        Select Municipality
                                    </option>
                                    {municipalities.map((municipality) => (
                                        <option
                                            key={municipality.id}
                                            value={municipality.id}
                                        >
                                            {municipality.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Ward Number *
                                </label>
                                <input
                                    type="number"
                                    value={wardNumber}
                                    onChange={(e) =>
                                        setWardNumber(e.target.value)
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    min="1"
                                    max="35"
                                    disabled={!selectedMunicipality}
                                    placeholder="Enter ward number"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Select Taxpayer *
                                </label>
                                <div className="relative">
                                    <select
                                        value={selectedTaxpayer}
                                        onChange={(e) =>
                                            handleTaxpayerSelection(
                                                e.target.value
                                            )
                                        }
                                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                            taxpayersLoading
                                                ? "border-yellow-300"
                                                : taxpayerFound
                                                ? "border-green-300"
                                                : "border-gray-300"
                                        }`}
                                        disabled={
                                            !selectedMunicipality ||
                                            !wardNumber ||
                                            taxpayersLoading
                                        }
                                    >
                                        <option value="">
                                            Select Taxpayer
                                        </option>
                                        {availableTaxpayers.map((taxpayer) => (
                                            <option
                                                key={taxpayer.id}
                                                value={taxpayer.id}
                                            >
                                                {taxpayer.fullName}
                                            </option>
                                        ))}
                                    </select>
                                    {taxpayersLoading && (
                                        <div className="absolute right-3 top-2.5">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                                        </div>
                                    )}
                                </div>
                                {availableTaxpayers.length === 0 &&
                                    selectedMunicipality &&
                                    wardNumber &&
                                    !taxpayersLoading && (
                                        <p className="text-sm text-yellow-600 mt-1">
                                            No taxpayers found in this ward
                                        </p>
                                    )}
                                {taxpayerFound && (
                                    <p className="text-sm text-green-600 mt-1">
                                        Taxpayer selected
                                    </p>
                                )}
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Fiscal Year *
                                </label>
                                <select
                                    value={formData.fiscalYearId}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "fiscalYearId",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Fiscal Year</option>
                                    {fiscalYears.map((year) => (
                                        <option
                                            key={year.fiscalYearId}
                                            value={year.fiscalYearId}
                                        >
                                            {year.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Manual Taxpayer Registration (Optional)
                                </label>
                                <div className="relative">
                                    <input
                                        type="text"
                                        value={formData.taxpayerRegistration}
                                        onChange={(e) =>
                                            handleInputChange(
                                                "taxpayerRegistration",
                                                e.target.value
                                            )
                                        }
                                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                            taxpayerLoading
                                                ? "border-yellow-300"
                                                : taxpayerFound
                                                ? "border-green-300"
                                                : formData.taxpayerRegistration &&
                                                  !taxpayerFound
                                                ? "border-red-300"
                                                : "border-gray-300"
                                        }`}
                                        maxLength={50}
                                        placeholder="Enter taxpayer registration ID manually"
                                    />
                                    {taxpayerLoading && (
                                        <div className="absolute right-3 top-2.5">
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                                        </div>
                                    )}
                                </div>
                                {formData.taxpayerRegistration &&
                                    !taxpayerFound &&
                                    !taxpayerLoading && (
                                        <p className="text-sm text-red-600 mt-1">
                                            Taxpayer not found
                                        </p>
                                    )}
                                <p className="text-sm text-gray-500 mt-1">
                                    Use this field if taxpayer is not found in
                                    the dropdown above
                                </p>
                            </div>

                            {preliminaryAssessments.length > 0 && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Preliminary Assessment
                                    </label>
                                    <select
                                        value={
                                            formData.preliminaryAssessmentId ||
                                            ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                "preliminaryAssessmentId",
                                                e.target.value
                                            )
                                        }
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">
                                            Select Preliminary Assessment
                                        </option>
                                        {preliminaryAssessments.map(
                                            (assessment) => (
                                                <option
                                                    key={assessment.id}
                                                    value={assessment.id}
                                                >
                                                    {assessment.taxpayerName} -{" "}
                                                    {new Date(
                                                        assessment.assessmentPeriodFrom
                                                    ).toLocaleDateString()}
                                                </option>
                                            )
                                        )}
                                    </select>
                                </div>
                            )}

                            {taxpayerFound && filedTaxes.length > 0 && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Filed Taxes
                                    </label>
                                    <select
                                        value={formData.returnFilingId || ""}
                                        onChange={(e) =>
                                            handleInputChange(
                                                "returnFilingId",
                                                e.target.value
                                            )
                                        }
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">
                                            Select Filed Tax
                                        </option>
                                        {filedTaxes.map((filing) => (
                                            <option
                                                key={filing.returnFilingId}
                                                value={filing.returnFilingId}
                                            >
                                                {filing.fiscalYearName} -{" "}
                                                {filing.propertyAddress}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            )}

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Taxpayer Name *
                                </label>
                                <input
                                    type="text"
                                    value={formData.taxpayerName}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "taxpayerName",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={200}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Phone
                                </label>
                                <input
                                    type="text"
                                    value={formData.phone}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "phone",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={20}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Sections Rules
                                </label>
                                <input
                                    type="text"
                                    value={formData.sectionsRules || ""}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "sectionsRules",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={255}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Bank Name
                                </label>
                                <input
                                    type="text"
                                    value={formData.bankName || ""}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "bankName",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={100}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Branch Name
                                </label>
                                <input
                                    type="text"
                                    value={formData.branchName || ""}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "branchName",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={100}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Reason Code
                                </label>
                                <input
                                    type="text"
                                    value={formData.reasonCode || ""}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "reasonCode",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={50}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Reason Description
                                </label>
                                <textarea
                                    value={formData.reasonDescription || ""}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "reasonDescription",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows={3}
                                    maxLength={500}
                                    placeholder="Provide reason description"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Appeal Number
                                </label>
                                <input
                                    type="text"
                                    value={formData.appealNumber || ""}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "appealNumber",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    maxLength={50}
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Final Assessment Date
                                </label>
                                <input
                                    type="date"
                                    value={
                                        formData.finalAssessmentDate
                                            ? formData.finalAssessmentDate
                                                  .toISOString()
                                                  .split("T")[0]
                                            : ""
                                    }
                                    onChange={(e) =>
                                        handleInputChange(
                                            "finalAssessmentDate",
                                            new Date(e.target.value)
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Interest Penalty Calculation Date
                                </label>
                                <input
                                    type="date"
                                    value={
                                        formData.interestPenaltyCalculationDate
                                            ? formData.interestPenaltyCalculationDate
                                                  .toISOString()
                                                  .split("T")[0]
                                            : ""
                                    }
                                    onChange={(e) =>
                                        handleInputChange(
                                            "interestPenaltyCalculationDate",
                                            new Date(e.target.value)
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Assessment Period From
                                </label>
                                <input
                                    type="date"
                                    value={
                                        formData.assessmentPeriodFrom
                                            ? formData.assessmentPeriodFrom
                                                  .toISOString()
                                                  .split("T")[0]
                                            : ""
                                    }
                                    onChange={(e) =>
                                        handleInputChange(
                                            "assessmentPeriodFrom",
                                            new Date(e.target.value)
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Assessment Period To
                                </label>
                                <input
                                    type="date"
                                    value={
                                        formData.assessmentPeriodTo
                                            ? formData.assessmentPeriodTo
                                                  .toISOString()
                                                  .split("T")[0]
                                            : ""
                                    }
                                    onChange={(e) =>
                                        handleInputChange(
                                            "assessmentPeriodTo",
                                            new Date(e.target.value)
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        <div className="mt-4 space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Address
                                </label>
                                <textarea
                                    value={formData.address}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "address",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows={2}
                                    maxLength={500}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Tax Details */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Tax Details
                        </h3>

                        {/* Add/Edit Tax Detail Form */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Fiscal Year *
                                </label>
                                <select
                                    value={currentDetail.filingPeriod}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "filingPeriod",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Fiscal Year</option>
                                    {fiscalYears.map((year) => (
                                        <option
                                            key={year.fiscalYearId}
                                            value={year.name}
                                        >
                                            {year.name}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Tax Period
                                </label>
                                <input
                                    type="text"
                                    value={currentDetail.taxPeriod}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "taxPeriod",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="e.g., Q1, Q2, Annual"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Tax Year *
                                </label>
                                <input
                                    type="text"
                                    value={currentDetail.taxYear}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "taxYear",
                                            e.target.value
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="e.g., 2080/81"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Assessed Amount
                                </label>
                                <input
                                    type="number"
                                    value={currentDetail.assessedAmount}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "assessedAmount",
                                            parseFloat(e.target.value) || 0
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    min="0"
                                    step="0.01"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Penalty Amount
                                </label>
                                <input
                                    type="number"
                                    value={currentDetail.penalty}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "penalty",
                                            parseFloat(e.target.value) || 0
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    min="0"
                                    step="0.01"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Additional Amount
                                </label>
                                <input
                                    type="number"
                                    value={currentDetail.additionalAmount}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "additionalAmount",
                                            parseFloat(e.target.value) || 0
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    min="0"
                                    step="0.01"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Interest Amount
                                </label>
                                <input
                                    type="number"
                                    value={currentDetail.interest}
                                    onChange={(e) =>
                                        handleDetailChange(
                                            "interest",
                                            parseFloat(e.target.value) || 0
                                        )
                                    }
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    min="0"
                                    step="0.01"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Total Amount
                                </label>
                                <input
                                    type="number"
                                    value={currentDetail.total}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                                    readOnly
                                />
                            </div>

                            <div className="flex items-end space-x-2">
                                <button
                                    type="button"
                                    onClick={addOrUpdateDetail}
                                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    {editingDetailIndex !== null
                                        ? "Update Detail"
                                        : "Add Detail"}
                                </button>
                                {(currentDetail.filingPeriod?.trim() ||
                                    currentDetail.taxPeriod?.trim() ||
                                    currentDetail.taxYear?.trim() ||
                                    currentDetail.assessedAmount > 0 ||
                                    currentDetail.penalty > 0 ||
                                    currentDetail.additionalAmount > 0 ||
                                    currentDetail.interest > 0) && (
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setCurrentDetail({
                                                serialNumber:
                                                    formData.taxDetails.length +
                                                    1,
                                                filingPeriod: "",
                                                taxPeriod: "",
                                                taxYear: "",
                                                assessedAmount: 0,
                                                penalty: 0,
                                                additionalAmount: 0,
                                                interest: 0,
                                                total: 0,
                                            });
                                            setEditingDetailIndex(null);
                                        }}
                                        className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                                    >
                                        Clear
                                    </button>
                                )}
                            </div>
                        </div>

                        {/* Tax Details Table */}
                        {formData.taxDetails.length > 0 && (
                            <div>
                                <AdminTable
                                    columns={taxDetailColumns}
                                    data={formData.taxDetails}
                                    keyField="serialNumber"
                                    isLoading={false}
                                    emptyMessage="No tax details added"
                                    actions={(item) => {
                                        const index =
                                            formData.taxDetails.findIndex(
                                                (detail) =>
                                                    detail.serialNumber ===
                                                    item.serialNumber
                                            );
                                        return (
                                            <div className="flex space-x-2">
                                                <button
                                                    onClick={() =>
                                                        editDetail(index)
                                                    }
                                                    className="px-3 py-1.5 bg-blue-600 text-white hover:bg-blue-700 text-xs font-medium rounded-md border border-blue-600 hover:border-blue-700 transition-colors"
                                                >
                                                    Edit
                                                </button>
                                                <button
                                                    onClick={() =>
                                                        removeDetail(index)
                                                    }
                                                    className="px-3 py-1.5 bg-red-600 text-white hover:bg-red-700 text-xs font-medium rounded-md border border-red-600 hover:border-red-700 transition-colors"
                                                >
                                                    Remove
                                                </button>
                                            </div>
                                        );
                                    }}
                                />

                                {/* Grand Total */}
                                <div className="mt-4 border-t border-gray-200 pt-4">
                                    <div className="flex justify-end">
                                        <div className="text-right">
                                            <p className="text-lg font-semibold text-gray-900">
                                                Grand Total: Rs.{" "}
                                                {grandTotal.toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Form Actions */}
                    <div className="bg-white rounded-lg shadow p-6">
                        <div className="flex justify-end space-x-4">
                            <button
                                type="button"
                                onClick={() =>
                                    navigate("/admin/final-assessments")
                                }
                                className="px-6 py-2 border border-gray-300 text-white rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={loading}
                                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                            >
                                {loading
                                    ? "Saving..."
                                    : mode === "create"
                                    ? "Create Assessment"
                                    : "Update Assessment"}
                            </button>
                        </div>
                    </div>
                </AdminForm>
            </div>
        </AdminLayout>
    );
};
