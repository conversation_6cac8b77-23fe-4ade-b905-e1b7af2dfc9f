import React, { useState, useEffect, useCallback } from "react";
import { useParams, useSearchParams, Link } from "react-router-dom";
import api from "../services/api";
import type { Property, Assessment } from "../types";

const TaxPayment: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const assessmentId = searchParams.get("assessment");

  const [property, setProperty] = useState<Property | null>(null);
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [error, setError] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("card");

  const loadPaymentData = useCallback(async (propertyId: string) => {
    try {
      setLoading(true);
      // Load property details
      const propertyResponse = await api.get(`/properties/${propertyId}`);
      setProperty(propertyResponse.data);

      // Load specific assessment or latest pending assessment
      let assessmentResponse;
      if (assessmentId) {
        assessmentResponse = await api.get(`/assessments/${assessmentId}`);
      } else {
        assessmentResponse = await api.get(
          `/properties/${propertyId}/assessments/pending`
        );
      }
      setAssessment(assessmentResponse.data);
    } catch (error) {
      console.error("Failed to load payment data:", error);
      // Mock data for development
      const mockProperty: Property = {
        id: propertyId,
        ownerUserId: "1",
        municipalityId: "1",
        municipalityName: "Kathmandu Metropolitan City",
        province: "Bagmati",
        district: "Kathmandu",
        wardNumber: 5,
        street: "Main Street",
        parcelNumber: "KTM-123",
        parcelGeoJson: {
          type: "Polygon",
          coordinates: [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]
        },
        address: "123 Main Street, Ward 5, Kathmandu",
        landAreaSqM: 500.5,
        landArea: 500.5,
        usageType: "Residential",
        buildingBuiltUpAreaSqM: 300.25,
        builtUpArea: 300.25,
        buildingConstructionType: "RCC",
        constructionType: "RCC",
        buildingConstructionYear: 2020,
        constructionYear: 2020,
        registrationDate: "2024-01-15T10:30:00Z",
        status: "Approved",
        isDefaulter: false,
        assessedValue: 15000000,
        taxDue: 75000,
      };
      setProperty(mockProperty);

      const mockAssessment: Assessment = {
        id: assessmentId || "1",
        propertyId: propertyId,
        assessmentYear: 2024,
        calculatedValue: 15000000,
        finalAssessedValue: 15000000,
        taxAmount: 75000,
        assessmentDate: "2024-01-20T14:00:00Z",
        paymentStatus: "Pending",
        province: "Bagmati",
        district: "Kathmandu",
        municipality: "Kathmandu Metropolitan City",
        wardNumber: 5,
        street: "Main Street",
        parcelNumber: "KTM-123",
        propertyAddress: "123 Main Street, Ward 5, Kathmandu",
        createdAt: "2024-01-15T10:30:00Z",
        updatedAt: "2024-01-20T14:00:00Z",
      };
      setAssessment(mockAssessment);
    } finally {
      setLoading(false);
    }
  }, [assessmentId]);

  useEffect(() => {
    if (id) {
      loadPaymentData(id);
    }
  }, [id, loadPaymentData]);

  const handlePayment = async () => {
    if (!assessment) return;

    setProcessing(true);
    setError("");

    try {
      // Mock payment processing
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // In a real application, this would integrate with a payment gateway
      const paymentData = {
        assessmentId: assessment.id,
        amount: assessment.taxAmount,
        paymentMethod,
        // Mock transaction details
        transactionId: `TXN_${Date.now()}`,
        gateway:
          paymentMethod === "card" ? "Mock Card Gateway" : "Mock Bank Transfer",
      };

      // Submit payment to backend
      await api.post("/payments", paymentData);

      setPaymentSuccess(true);
    } catch (error: unknown) {
      setError(
        error instanceof Error
          ? error.message
          : "Payment processing failed. Please try again."
      );
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `NPR ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
          <div className="text-green-600 text-6xl mb-4">✓</div>
          <h2 className="text-2xl font-bold text-green-800 mb-2">
            Payment Successful!
          </h2>
          <p className="text-green-700 mb-6">
            Your tax payment has been processed successfully. You should receive
            a confirmation receipt shortly.
          </p>

          {assessment && (
            <div className="bg-white border border-green-200 rounded-lg p-4 mb-6 text-left">
              <h3 className="font-semibold text-gray-900 mb-3">
                Payment Details
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Property:</span>
                  <span className="text-gray-900">{property?.address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Assessment Year:</span>
                  <span className="text-gray-900">
                    {assessment.assessmentYear}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount Paid:</span>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(assessment.taxAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="text-gray-900">TXN_{Date.now()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Date:</span>
                  <span className="text-gray-900">
                    {formatDate(new Date().toISOString())}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-wrap justify-center gap-3">
            <Link to="/properties" className="btn-primary">
              Back to Properties
            </Link>
            <button onClick={() => window.print()} className="btn-secondary">
              Print Receipt
            </button>
            <button 
              onClick={() => alert('Receipt downloaded successfully!')}
              className="btn-secondary flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
              Download Receipt
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (error && !assessment) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-red-800 mb-2">
            Unable to Load Payment Information
          </h2>
          <p className="text-red-700">{error}</p>
          <Link to="/properties" className="btn-primary mt-4 inline-block">
            Back to Properties
          </Link>
        </div>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-yellow-800 mb-2">
            No Pending Tax Payment
          </h2>
          <p className="text-yellow-700">
            There are no pending tax payments for this property.
          </p>
          <Link to="/properties" className="btn-primary mt-4 inline-block">
            Back to Properties
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        Property Tax Payment
      </h1>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {property && assessment && (
        <>
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Property Information
            </h2>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Address:</span>
                <span className="text-gray-900">{property.address}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Usage Type:</span>
                <span className="text-gray-900">{property.usageType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Land Area:</span>
                <span className="text-gray-900">
                  {property.landAreaSqM.toLocaleString()} sq.m
                </span>
              </div>
              {property.builtUpArea && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Built-up Area:</span>
                  <span className="text-gray-900">
                    {property.builtUpArea.toLocaleString()} sq.m
                  </span>
                </div>
              )}
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Assessment Details
            </h2>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Assessment Year:</span>
                <span className="text-gray-900">
                  {assessment.assessmentYear}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Assessed Value:</span>
                <span className="text-gray-900">
                  {formatCurrency(assessment.finalAssessedValue)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 font-medium">Tax Amount:</span>
                <span className="text-gray-900 font-bold">
                  {formatCurrency(assessment.taxAmount)}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Payment Method
            </h2>
            <div className="space-y-4">
              <div>
                <label className="flex items-center mb-2">
                  <input
                    type="radio"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500"
                    name="paymentMethod"
                    value="card"
                    checked={paymentMethod === "card"}
                    onChange={() => setPaymentMethod("card")}
                  />
                  <span className="ml-2 text-gray-700">Credit/Debit Card</span>
                </label>
                {paymentMethod === "card" && (
                  <div className="ml-6 space-y-3">
                    <div>
                      <label
                        className="block text-sm font-medium text-gray-700 mb-1"
                        htmlFor="cardNumber"
                      >
                        Card Number
                      </label>
                      <input
                        type="text"
                        id="cardNumber"
                        className="input-field"
                        placeholder="1234 5678 9012 3456"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label
                          className="block text-sm font-medium text-gray-700 mb-1"
                          htmlFor="expiry"
                        >
                          Expiry Date
                        </label>
                        <input
                          type="text"
                          id="expiry"
                          className="input-field"
                          placeholder="MM/YY"
                        />
                      </div>
                      <div>
                        <label
                          className="block text-sm font-medium text-gray-700 mb-1"
                          htmlFor="cvc"
                        >
                          CVC
                        </label>
                        <input
                          type="text"
                          id="cvc"
                          className="input-field"
                          placeholder="123"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="flex items-center mb-2">
                  <input
                    type="radio"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500"
                    name="paymentMethod"
                    value="bank"
                    checked={paymentMethod === "bank"}
                    onChange={() => setPaymentMethod("bank")}
                  />
                  <span className="ml-2 text-gray-700">Bank Transfer</span>
                </label>
                {paymentMethod === "bank" && (
                  <div className="ml-6 space-y-3">
                    <div>
                      <label
                        className="block text-sm font-medium text-gray-700 mb-1"
                        htmlFor="accountNumber"
                      >
                        Account Number
                      </label>
                      <input
                        type="text"
                        id="accountNumber"
                        className="input-field"
                        placeholder="Enter your account number"
                      />
                    </div>
                    <div>
                      <label
                        className="block text-sm font-medium text-gray-700 mb-1"
                        htmlFor="bankName"
                      >
                        Bank Name
                      </label>
                      <select
                        id="bankName"
                        className="input-field"
                        defaultValue=""
                      >
                        <option value="" disabled>
                          Select your bank
                        </option>
                        <option value="nepal-bank">Nepal Bank Limited</option>
                        <option value="nabil">Nabil Bank</option>
                        <option value="nic-asia">NIC Asia Bank</option>
                        <option value="global-ime">Global IME Bank</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="flex items-center">
                  <input
                    type="radio"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500"
                    name="paymentMethod"
                    value="digital"
                    checked={paymentMethod === "digital"}
                    onChange={() => setPaymentMethod("digital")}
                  />
                  <span className="ml-2 text-gray-700">Digital Wallet</span>
                </label>
                {paymentMethod === "digital" && (
                  <div className="ml-6 space-y-3 mt-2">
                    <div className="flex space-x-4">
                      <button className="border border-gray-300 rounded-lg p-3 flex items-center justify-center hover:bg-gray-50">
                        <span>eSewa</span>
                      </button>
                      <button className="border border-gray-300 rounded-lg p-3 flex items-center justify-center hover:bg-gray-50">
                        <span>Khalti</span>
                      </button>
                      <button className="border border-gray-300 rounded-lg p-3 flex items-center justify-center hover:bg-gray-50">
                        <span>Connect IPS</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link to="/properties" className="btn-secondary">
              Cancel
            </Link>
            <button
              className={`btn-primary ${processing ? "opacity-75" : ""}`}
              onClick={handlePayment}
              disabled={processing}
            >
              {processing ? (
                <span className="flex items-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Processing...
                </span>
              ) : (
                `Pay ${assessment ? formatCurrency(assessment.taxAmount) : ""}`
              )}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default TaxPayment;
