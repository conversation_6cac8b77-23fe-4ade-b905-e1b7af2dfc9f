using System;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.Infrastructure.Services
{
    /// <summary>
    /// Helper service to fetch the active (finalised) MunicipalityTaxConfig for a given municipality.
    /// </summary>
    public class TaxConfigResolver
    {
        private readonly ApplicationDbContext _context;

        public TaxConfigResolver(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Returns the most-recent finalised tax configuration for the municipality.
        /// Throws <see cref="InvalidOperationException"/> if no configuration is found.
        /// </summary>
        public MunicipalityTaxConfig GetActiveConfig(Guid municipalityId)
        {
            var config = _context.MunicipalityTaxConfigs
                .Include(c => c.FiscalYear)
                .Where(c => c.MunicipalityId == municipalityId && c.IsFinalized)
                .OrderByDescending(c => c.FiscalYear.StartDate)
                .FirstOrDefault();

            if (config == null)
            {
                throw new InvalidOperationException("No finalised tax configuration found for the municipality.");
            }
            
            // Ensure JSON fields are initialized with default values if null or empty
            EnsureConfigHasValidJsonFields(config);

            return config;
        }
        
        /// <summary>
        /// Asynchronously returns the most-recent finalised tax configuration for the municipality.
        /// Returns null if no configuration is found.
        /// </summary>
        public async Task<MunicipalityTaxConfig> GetActiveTaxConfigAsync(Guid municipalityId)
        {
            var config = await _context.MunicipalityTaxConfigs
                .Include(c => c.FiscalYear)
                .Where(c => c.MunicipalityId == municipalityId && c.IsFinalized)
                .OrderByDescending(c => c.FiscalYear.StartDate)
                .FirstOrDefaultAsync();
                
            if (config == null)
            {
                throw new InvalidOperationException("No finalised tax configuration found for the municipality.");
            }
            
            // Ensure JSON fields are initialized with default values if null or empty
            EnsureConfigHasValidJsonFields(config);
            
            return config;
        }
        
        /// <summary>
        /// Ensures that the tax configuration has valid JSON fields by initializing any null or empty fields with default values.
        /// </summary>
        /// <param name="config">The tax configuration to validate</param>
        private void EnsureConfigHasValidJsonFields(MunicipalityTaxConfig config)
        {
            // Log warning if tax slabs JSON is null or empty
            if (string.IsNullOrWhiteSpace(config.TaxSlabsConfigJson))
            {
                // We don't set default values anymore as per requirements
                // Log this issue for administrators to fix in the database
                Console.WriteLine($"WARNING: Municipality {config.MunicipalityId} has no tax slabs configuration");
            }
            
            // Log warning if valuation rules JSON is null or empty
            if (string.IsNullOrWhiteSpace(config.ValuationRulesConfigJson))
            {
                // We don't set default values anymore as per requirements
                // Log this issue for administrators to fix in the database
                Console.WriteLine($"WARNING: Municipality {config.MunicipalityId} has no valuation rules configuration");
            }
            
            // Log warning if exemption rules JSON is null or empty
            if (string.IsNullOrWhiteSpace(config.ExemptionRulesConfigJson))
            {
                // We don't set default values anymore as per requirements
                // Log this issue for administrators to fix in the database
                Console.WriteLine($"WARNING: Municipality {config.MunicipalityId} has no exemption rules configuration");
            }
        }
    }
}
