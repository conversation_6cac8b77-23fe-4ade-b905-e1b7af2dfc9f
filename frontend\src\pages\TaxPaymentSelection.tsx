import React from "react";
import { Link, useParams } from "react-router-dom";

const TaxPaymentSelection: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="card bg-base-100 shadow-xl">
        {/* Header */}
        <div className="card-body">
          <div className="card-title text-2xl">Property Tax Payment</div>
          <p className="text-base-content/70">
            Choose how you want to pay your property tax
          </p>
          
          <div className="divider"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Individual Payment Option */}
            <div className="card bg-base-200 shadow-md hover:shadow-lg transition-all">
              <div className="card-body text-center">
                <div className="text-primary text-4xl mb-4">🏠</div>
                <h2 className="card-title justify-center text-xl mb-2">
                  Individual Property Payment
                </h2>
                <p className="text-base-content/70 mb-6">
                  Pay tax for a single property at a time. Best for when you need 
                  to review specific assessment details.
                </p>
                
                <div className="bg-base-300 rounded-lg p-4 mb-6">
                  <h3 className="font-medium mb-2">Features:</h3>
                  <ul className="text-sm space-y-2">
                    <li className="flex items-start">
                      <span className="text-success mr-2">✓</span>
                      Detailed property assessment view
                    </li>
                    <li className="flex items-start">
                      <span className="text-success mr-2">✓</span>
                      Review tax calculation breakdown
                    </li>
                    <li className="flex items-start">
                      <span className="text-success mr-2">✓</span>
                      Print individual receipts
                    </li>
                  </ul>
                </div>
                
                <div className="card-actions justify-center">
                  <Link
                    to={id ? `/tax-payment/${id}` : "/properties"}
                    className="btn btn-primary w-full"
                  >
                    {id ? "Proceed to Payment" : "View My Properties"}
                  </Link>
                </div>
              </div>
            </div>

            {/* Bulk Payment Option */}
            <div className="card bg-base-200 shadow-md hover:shadow-lg transition-all">
              <div className="card-body text-center">
                <div className="text-primary text-4xl mb-4">🏘️</div>
                <h2 className="card-title justify-center text-xl mb-2">
                  Bulk Payment
                </h2>
                <p className="text-base-content/70 mb-6">
                  Pay tax for multiple properties at once. Save time and 
                  potentially reduce transaction fees.
                </p>
                
                <div className="bg-base-300 rounded-lg p-4 mb-6">
                  <h3 className="font-medium mb-2">Features:</h3>
                  <ul className="text-sm space-y-2">
                    <li className="flex items-start">
                      <span className="text-success mr-2">✓</span>
                      Select multiple properties
                    </li>
                    <li className="flex items-start">
                      <span className="text-success mr-2">✓</span>
                      Single transaction for all selected properties
                    </li>
                    <li className="flex items-start">
                      <span className="text-success mr-2">✓</span>
                      Consolidated receipt
                    </li>
                  </ul>
                </div>
                
                <div className="card-actions justify-center">
                  <Link
                    to="/bulk-tax-payment"
                    className="btn btn-primary w-full"
                  >
                    Proceed to Bulk Payment
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div className="divider"></div>
          
          <div className="text-center">
            <Link to="/properties" className="link link-primary">
              ← Back to My Properties
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaxPaymentSelection;
