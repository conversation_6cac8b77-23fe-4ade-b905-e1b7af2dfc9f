using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using LandTaxSystem.Core.Entities;

namespace LandTaxSystem.Core.DTOs.Negotiation
{
    public class TaxPeriodDto
    {
        public Guid TaxPeriodId { get; set; } = Guid.NewGuid();
        
        [Required]
        public string FiscalYear { get; set; } = null!;
        
        [Required]
        public string StartDate { get; set; } = null!;
        
        [Required]
        public string EndDate { get; set; } = null!;
        
        // Legacy fields kept for backward compatibility
        public string? Year { get; set; }
        
        public string? Period { get; set; }
        
        public string? TaxPeriodValue { get; set; }
        
        public string? AppealSubject { get; set; }
    }

    public class CreateNegotiationDto
    {
        [Required]
        public Guid AppealId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Negotiated amount must be greater than zero")]
        public decimal NegotiatedAmount { get; set; }
        
        // Appeal Completion Notice Fields
        [Required] public string TaxpayerName { get; set; } = null!;
        [Required] public string PANNumber { get; set; } = null!;
        [Required] public string TaxOfficeAddress { get; set; } = null!;
        [Required] public AppealBody AppealBody { get; set; }
        [Required] public string DecisionNumber { get; set; } = null!;
        [Required] public DateTime DecisionDate { get; set; }
        public List<TaxPeriodDto> TaxPeriods { get; set; } = new();
        [Range(0, double.MaxValue)] public decimal OriginalTax { get; set; }
        [Range(0, double.MaxValue)] public decimal OriginalPenalty { get; set; }
        [Range(0, double.MaxValue)] public decimal OriginalFee { get; set; }
        [Range(0, double.MaxValue)] public decimal OriginalInterest { get; set; }
        [Range(0, double.MaxValue)] public decimal DecidedTax { get; set; }
        [Range(0, double.MaxValue)] public decimal DecidedPenalty { get; set; }
        [Range(0, double.MaxValue)] public decimal DecidedFee { get; set; }
        [Range(0, double.MaxValue)] public decimal DecidedInterest { get; set; }
        public bool IsWithdrawn { get; set; }
    }

    public class NegotiationDto
    {
        public Guid NegotiationId { get; set; }
        public Guid AppealId { get; set; }
        public string OfficerId { get; set; } = null!;
        public string OfficerName { get; set; } = null!;
        public decimal NegotiatedAmount { get; set; }
        public DateTime NegotiationDate { get; set; }
        
        // Appeal related information
        public string Status { get; set; } = "Pending";
        public string AppealReason { get; set; } = null!;
        public DateTime AppealSubmittedAt { get; set; }
        
        // Assessment related information
        public Guid AssessmentId { get; set; }
        public decimal OriginalAssessmentAmount { get; set; }
        
        // Property related information
        public Guid PropertyId { get; set; }
        public string PropertyAddress { get; set; } = null!;
        
        // Taxpayer related information
        public string TaxPayerId { get; set; } = null!;
        public string TaxPayerName { get; set; } = null!;
        
        // Appeal Completion Notice Fields
        public string PANNumber { get; set; } = null!;
        public string TaxOfficeAddress { get; set; } = null!;
        public AppealBody AppealBody { get; set; }
        public string DecisionNumber { get; set; } = null!;
        public DateTime DecisionDate { get; set; }
        public List<TaxPeriodDto> TaxPeriods { get; set; } = new();
        public decimal OriginalTax { get; set; }
        public decimal OriginalPenalty { get; set; }
        public decimal OriginalFee { get; set; }
        public decimal OriginalInterest { get; set; }
        public decimal DecidedTax { get; set; }
        public decimal DecidedPenalty { get; set; }
        public decimal DecidedFee { get; set; }
        public decimal DecidedInterest { get; set; }
        public bool IsWithdrawn { get; set; }
    }
}
