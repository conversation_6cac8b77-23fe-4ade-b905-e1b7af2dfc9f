import api from './api';
import type {
    FinalAssessmentDto,
    FinalAssessmentCreateDto,
    FinalAssessmentUpdateDto,
    FinalAssessmentListResponse,
    FinalAssessmentSearchParams,
} from "../types/finalAssessment";

export const finalAssessmentService = {
    // Get all final assessments with pagination and filtering
    getAll: async (
        params?: FinalAssessmentSearchParams
    ): Promise<FinalAssessmentListResponse> => {
        const response = await api.get('/finalassessments', { params });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const pageNumber = parseInt(response.headers['x-page'] || '1', 10);
        const pageSize = parseInt(response.headers['x-page-size'] || '10', 10);
        const totalPages = Math.ceil(totalCount / pageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber,
            pageSize,
            totalPages
        };
    },

    // Get final assessment by ID
    getById: async (id: string): Promise<FinalAssessmentDto> => {
        const response = await api.get(`/finalassessments/${id}`);
        return response.data;
    },

    // Get final assessment by order number
    getByOrderNumber: async (
        orderNumber: string
    ): Promise<FinalAssessmentDto> => {
        const response = await api.get(`/finalassessments/order/${orderNumber}`);
        return response.data;
    },

    // Get final assessments by taxpayer registration
    getByTaxpayerRegistration: async (
        registration: string,
        pageNumber = 1,
        pageSize = 10
    ): Promise<FinalAssessmentListResponse> => {
        const response = await api.get(`/finalassessments/by-taxpayer/${registration}`, {
            params: { pageNumber, pageSize }
        });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const actualPageNumber = parseInt(response.headers['x-page'] || pageNumber.toString(), 10);
        const actualPageSize = parseInt(response.headers['x-page-size'] || pageSize.toString(), 10);
        const totalPages = Math.ceil(totalCount / actualPageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber: actualPageNumber,
            pageSize: actualPageSize,
            totalPages
        };
    },

    // Get final assessments by municipality
    getByMunicipality: async (
        municipalityId: string,
        pageNumber = 1,
        pageSize = 10
    ): Promise<FinalAssessmentListResponse> => {
        const response = await api.get(`/finalassessments/municipality/${municipalityId}`, {
            params: { pageNumber, pageSize }
        });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const actualPageNumber = parseInt(response.headers['x-page'] || pageNumber.toString(), 10);
        const actualPageSize = parseInt(response.headers['x-page-size'] || pageSize.toString(), 10);
        const totalPages = Math.ceil(totalCount / actualPageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber: actualPageNumber,
            pageSize: actualPageSize,
            totalPages
        };
    },

    // Get final assessments by fiscal year
    getByFiscalYear: async (
        fiscalYearId: string,
        pageNumber = 1,
        pageSize = 10
    ): Promise<FinalAssessmentListResponse> => {
        const response = await api.get(`/finalassessments/fiscalyear/${fiscalYearId}`, {
            params: { pageNumber, pageSize }
        });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const actualPageNumber = parseInt(response.headers['x-page'] || pageNumber.toString(), 10);
        const actualPageSize = parseInt(response.headers['x-page-size'] || pageSize.toString(), 10);
        const totalPages = Math.ceil(totalCount / actualPageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber: actualPageNumber,
            pageSize: actualPageSize,
            totalPages
        };
    },

    // Get final assessments by preliminary assessment
    getByPreliminaryAssessment: async (
        preliminaryAssessmentId: string,
        pageNumber = 1,
        pageSize = 10
    ): Promise<FinalAssessmentListResponse> => {
        const response = await api.get(`/finalassessments/preliminary/${preliminaryAssessmentId}`, {
            params: { pageNumber, pageSize }
        });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const actualPageNumber = parseInt(response.headers['x-page'] || pageNumber.toString(), 10);
        const actualPageSize = parseInt(response.headers['x-page-size'] || pageSize.toString(), 10);
        const totalPages = Math.ceil(totalCount / actualPageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber: actualPageNumber,
            pageSize: actualPageSize,
            totalPages
        };
    },

    // Search final assessments (alias for getAll for consistency)
    search: async (
        params: FinalAssessmentSearchParams
    ): Promise<FinalAssessmentListResponse> => {
        return finalAssessmentService.getAll(params);
    },

    // Create new final assessment
    create: async (
        data: FinalAssessmentCreateDto
    ): Promise<FinalAssessmentDto> => {
        const response = await api.post('/finalassessments', data);
        return response.data;
    },

    // Update final assessment
    update: async (
        id: string,
        data: FinalAssessmentUpdateDto
    ): Promise<FinalAssessmentDto> => {
        const response = await api.put(`/finalassessments/${id}`, data);
        return response.data;
    },

    // Delete final assessment
    delete: async (id: string): Promise<void> => {
        await api.delete(`/finalassessments/${id}`);
    },

    // Check if final assessment exists
    exists: async (id: string): Promise<boolean> => {
        try {
            const response = await api.get(`/finalassessments/${id}/exists`);
            return response.data.exists;
        } catch {
            return false;
        }
    },

    // Get total count of final assessments
    getTotalCount: async (): Promise<number> => {
        const response = await api.get('/finalassessments/count');
        return response.data.count;
    },

    // Export final assessments to Excel
    exportToExcel: async (
        params?: FinalAssessmentSearchParams
    ): Promise<Blob> => {
        const response = await api.get('/finalassessments/export', {
            params,
            responseType: 'blob',
            headers: {
                Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            },
        });
        return response.data;
    },

    // Generate PDF report
    generatePdfReport: async (id: string): Promise<Blob> => {
        const response = await api.get(`/finalassessments/${id}/pdf`, {
            responseType: 'blob',
            headers: {
                Accept: 'application/pdf',
            },
        });
        return response.data;
    },
};

export default finalAssessmentService;