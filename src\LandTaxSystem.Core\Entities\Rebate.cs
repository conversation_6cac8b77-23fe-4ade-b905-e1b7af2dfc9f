using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.Entities
{
    public class Rebate
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string? OfficeCode { get; set; }

        [Required]
        public string? Pan { get; set; }

        [Required]
        public string? AccountType { get; set; }

        [Required]
        public string? Name { get; set; }

        [Required]
        public DateTime? ExemptionDate { get; set; }

        [Required]
        public string? SerialNo { get; set; }

        [Required]
        public string? Scheme { get; set; }

        public bool IsReversal { get; set; }
        public string? MaNo { get; set; }

        [Required]
        public string? Reason { get; set; }

        public ICollection<RebateItem> RebateItems { get; set; } = new List<RebateItem>();
    }
}