import React, { useState, useEffect, useCallback } from "react";
import * as api from "../services/api";
import { fiscalYearService } from "../services/fiscalYearService";
import type { FiscalYear } from "../services/taxConfigService";
import { AdminLayout, AdminTabs } from "../components/admin";

// Interface for properties with assessment data
interface PropertyWithAssessmentData {
  propertyId: string;
  propertyAddress: string;
  amountPaid: number;
  calculatedTaxAmount: number;
}

// Define API response types to avoid 'any'
interface AssessmentResponse {
  assessmentId: string;
  propertyId: string;
  propertyAddress: string;
  assessmentYear: number;
  calculatedValue: number;
  overriddenValue?: number;
  finalAssessedValue: number;
  taxAmount: number;
  assessmentDate: string;
  paymentStatus: string;
  overrideReason?: string;
  assessedByOfficerId?: string;
  assessedByOfficerName?: string;
  province: string;
  district: string;
  municipality: string;
  wardNumber: number;
  street: string;
  parcelNumber: string;
  createdAt: string;
  updatedAt: string;
  exemptionAppliedDetails?: Record<string, unknown>;
  lineItems: {
    id: string;
    serialNumber: number;
    taxDescription: string;
    fiscalYear: number;
    assessedAmount: number;
    penaltyAmount: number;
    interestAmount: number;
    totalAmount: number;
    createdAt: string;
    updatedAt?: string;
  }[];
}

// Frontend assessment type with all the fields we need
interface AssessmentWithLocation {
  id: string;
  propertyId: string;
  propertyAddress: string;
  assessmentYear: number;
  calculatedValue: number;
  overriddenValue?: number;
  finalAssessedValue: number;
  taxAmount: number;
  assessmentDate: string;
  paymentStatus: "Pending" | "Paid" | "Overdue" | "Underpaid";
  overrideReason?: string;
  assessedByOfficerId?: string;
  assessedByOfficerName?: string;

  // Location hierarchy fields
  province: string;
  district: string;
  municipality: string;
  wardNumber: number;
  street: string;
  parcelNumber: string;

  createdAt: string;
  updatedAt: string;
  exemptionAppliedDetails?: Record<string, unknown>;
  lineItems: {
    id: string;
    serialNumber: number;
    taxDescription: string;
    fiscalYear: number;
    assessedAmount: number;
    penaltyAmount: number;
    interestAmount: number;
    totalAmount: number;
    createdAt: string;
    updatedAt?: string;
  }[];
}

interface AssessmentFilter {
  year?: number;
  paymentStatus?: string;
  minTaxAmount?: number;
  maxTaxAmount?: number;
  createdAfter?: string;
  createdBefore?: string;
}

interface NewAssessmentForm {
  propertyId: string;
  fiscalYearId: string;
  originalAmount?: number;
  overriddenValue?: number;
  overrideReason?: string;
  lineItems: {
    serialNumber: number;
    taxDescription: string;
    fiscalYear: number;
    assessedAmount: number;
    penaltyAmount: number;
    interestAmount: number;
  }[];
}

const AssessmentManagement: React.FC = () => {
  const [assessments, setAssessments] = useState<AssessmentWithLocation[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false); // Used during form submission
  const [error, setError] = useState(""); // Used to display error messages
  const [success, setSuccess] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showOverrideForm, setShowOverrideForm] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [selectedTab, setSelectedTab] = useState<
    "all" | "pending" | "paid" | "overdue"
  >("all");

  // Filter state
  const [filters, setFilters] = useState<AssessmentFilter>({});
  const [showFilters, setShowFilters] = useState(false);

  // New assessment form state
  const [newAssessment, setNewAssessment] = useState<NewAssessmentForm>({
    propertyId: "",
    fiscalYearId: "",
    originalAmount: undefined,
    overriddenValue: undefined,
    overrideReason: "",
    lineItems: [],
  });

  // Fiscal years state
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [activeFiscalYear, setActiveFiscalYear] = useState<FiscalYear | null>(
    null
  );

  // Properties state
  const [properties, setProperties] = useState<PropertyWithAssessmentData[]>([]);

  // Override form state
  const [overrideForm, setOverrideForm] = useState({
    overriddenValue: "",
    overrideReason: "",
  });

  const loadProperties = useCallback(async () => {
    try {
      setError("");
      const token = localStorage.getItem("token");
      if (!token) {
        setError("You must be logged in to view properties.");
        setProperties([]);
        return;
      }

      const data = await api.get("/assessments/eligible");
      setProperties(data as PropertyWithAssessmentData[]);
    } catch (error) {
      console.error("Failed to load eligible properties:", error);
      setError("Failed to load eligible properties. Please try again.");
      setProperties([]);
    }
  }, []);

  const loadAssessments = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      const token = localStorage.getItem("token");
      if (!token) {
        setError("You must be logged in to view assessments.");
        setAssessments([]);
        setLoading(false);
        return;
      }

      const filterParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== "") {
          filterParams.append(key, value.toString());
        }
      });

      let response;
      try {
        if (selectedTab === "overdue") {
          response = await api.get(
            `/assessments/overdue?${filterParams.toString()}`
          ) as unknown;
        } else {
          response = await api.get(
            `/assessments/filter?${filterParams.toString()}`
          ) as unknown;
        }
      } catch (apiError) {
        console.error("API request failed:", apiError);
        setError(
          "Failed to connect to the server. Please check your connection and try again."
        );
        setLoading(false);
        return;
      }

      const assessmentsData = (response as { data: AssessmentResponse[] })?.data?.length > 0
        ? (response as { data: AssessmentResponse[] }).data.map((assessment: AssessmentResponse) => ({
            id: assessment.assessmentId,
            propertyId: assessment.propertyId,
            propertyAddress: assessment.propertyAddress,
            assessmentYear: assessment.assessmentYear,
            calculatedValue: assessment.calculatedValue,
            overriddenValue: assessment.overriddenValue,
            finalAssessedValue: assessment.finalAssessedValue,
            taxAmount: assessment.taxAmount,
            assessmentDate: assessment.assessmentDate,
            paymentStatus: assessment.paymentStatus as
              | "Pending"
              | "Paid"
              | "Overdue"
              | "Underpaid",
            overrideReason: assessment.overrideReason,
            assessedByOfficerId: assessment.assessedByOfficerId,
            assessedByOfficerName: assessment.assessedByOfficerName,

            // Location hierarchy fields
            province: assessment.province,
            district: assessment.district,
            municipality: assessment.municipality,
            wardNumber: assessment.wardNumber,
            street: assessment.street,
            parcelNumber: assessment.parcelNumber,

            createdAt: assessment.createdAt,
            updatedAt: assessment.updatedAt,
            exemptionAppliedDetails: assessment.exemptionAppliedDetails,
            lineItems: assessment.lineItems,
          }))
        : [];

      setAssessments(assessmentsData);
    } catch (error) {
      console.error("Failed to load assessments:", error);
      setError("Failed to load assessments. Please try again.");
      setAssessments([]);
    } finally {
      setLoading(false);
    }
  }, [
    filters,
    selectedTab,
  ]);

  const loadFiscalYears = useCallback(async () => {
    try {
      const allFiscalYears = await fiscalYearService.getAll();
      setFiscalYears(allFiscalYears);

      const activeFY = allFiscalYears.find((fy) => fy.isActive);
      if (activeFY) {
        setActiveFiscalYear(activeFY);
        setNewAssessment((prev) => ({
          ...prev,
          fiscalYearId: activeFY.fiscalYearId,
        }));
      } else if (allFiscalYears.length > 0) {
        setNewAssessment((prev) => ({
          ...prev,
          fiscalYearId: allFiscalYears[0].fiscalYearId,
        }));
      }
    } catch (error) {
      console.error("Failed to load fiscal years:", error);
    }
  }, []);

  useEffect(() => {
    loadAssessments();
    loadFiscalYears();
  }, [
    filters,
    selectedTab,
    loadAssessments,
    loadFiscalYears,
  ]);

  useEffect(() => {
    if (showCreateForm) {
      loadProperties();
    }
  }, [showCreateForm, loadProperties]);

  const createAssessment = async (formData: {
    propertyId: string;
    fiscalYearId: string;
    originalAmount?: number;
    overriddenValue?: number;
    overrideReason?: string;
    lineItems: {
      serialNumber: number;
      taxDescription: string;
      fiscalYear: number;
      assessedAmount: number;
      penaltyAmount: number;
      interestAmount: number;
    }[];
  }): Promise<void> => {
    try {
      setSubmitting(true);
      setSubmitError("");

      const response = await api.post("/assessments", {
        propertyId: formData.propertyId,
        fiscalYearId: formData.fiscalYearId,
        originalAmount: formData.originalAmount,
        overriddenValue: formData.overriddenValue,
        overrideReason: formData.overrideReason,
        lineItems: formData.lineItems,
      }) as unknown;

      const newAssessment: AssessmentWithLocation = {
        id: (response as { data: AssessmentResponse }).data.assessmentId,
        propertyId: (response as { data: AssessmentResponse }).data.propertyId,
        propertyAddress: (response as { data: AssessmentResponse }).data.propertyAddress,
        assessmentYear: (response as { data: AssessmentResponse }).data.assessmentYear,
        calculatedValue: (response as { data: AssessmentResponse }).data.calculatedValue,
        overriddenValue: (response as { data: AssessmentResponse }).data.overriddenValue,
        finalAssessedValue: (response as { data: AssessmentResponse }).data.finalAssessedValue,
        taxAmount: (response as { data: AssessmentResponse }).data.taxAmount,
        assessmentDate: (response as { data: AssessmentResponse }).data.assessmentDate,
        paymentStatus: (response as { data: AssessmentResponse }).data.paymentStatus as
          | "Pending"
          | "Paid"
          | "Overdue"
          | "Underpaid",
        overrideReason: (response as { data: AssessmentResponse }).data.overrideReason,
        assessedByOfficerId: (response as { data: AssessmentResponse }).data.assessedByOfficerId,
        assessedByOfficerName: (response as { data: AssessmentResponse }).data.assessedByOfficerName,

        province: (response as { data: AssessmentResponse }).data.province,
        district: (response as { data: AssessmentResponse }).data.district,
        municipality: (response as { data: AssessmentResponse }).data.municipality,
        wardNumber: (response as { data: AssessmentResponse }).data.wardNumber,
        street: (response as { data: AssessmentResponse }).data.street,
        parcelNumber: (response as { data: AssessmentResponse }).data.parcelNumber,

        createdAt: (response as { data: AssessmentResponse }).data.createdAt,
        updatedAt: (response as { data: AssessmentResponse }).data.updatedAt,
        exemptionAppliedDetails: (response as { data: AssessmentResponse }).data.exemptionAppliedDetails,
        lineItems: (response as { data: AssessmentResponse }).data.lineItems,
      };

      setAssessments([newAssessment, ...assessments]);
      setShowCreateForm(false);
      setNewAssessment({
        propertyId: "",
        fiscalYearId: activeFiscalYear?.fiscalYearId || "",
        originalAmount: undefined,
        overriddenValue: undefined,
        overrideReason: "",
        lineItems: [],
      });

      setSuccessMessage("Assessment created successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);

      loadAssessments();
    } catch (error) {
      console.error("Failed to create assessment:", error);
      setSubmitError("Failed to create assessment. Please try again.");
      throw error;
    } finally {
      setSubmitting(false);
    }
  };

  const handleCreateAssessment = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      setSubmitError("");

      if (newAssessment.propertyId) {
        const selectedProperty = properties.find(
          (p) => p.propertyId === newAssessment.propertyId
        );

        if (selectedProperty) {
          newAssessment.originalAmount = selectedProperty.amountPaid;
        } else {
          setSubmitError(
            "Could not find the selected property data. Please try again."
          );
          setSubmitting(false);
          return;
        }
      }

      await createAssessment(newAssessment);

      setNewAssessment({
        propertyId: "",
        fiscalYearId: "",
        originalAmount: 0,
        overriddenValue: 0,
        overrideReason: "",
        lineItems: [],
      });

      setSuccessMessage("Assessment created successfully!");
      setTimeout(() => setSuccessMessage(""), 5000);

      setShowCreateForm(false);
    } catch (error) {
      console.error("Error in form submission:", error);
      setSubmitError("Failed to create assessment. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleOverrideAssessment = async (assessmentId: string) => {
    try {
      setError("");
      setSuccess("");

      const overrideData = {
        propertyId: assessments.find((a) => a.id === assessmentId)?.propertyId,
        assessmentYear: assessments.find((a) => a.id === assessmentId)
          ?.assessmentYear,
        overriddenValue: parseFloat(overrideForm.overriddenValue),
        overrideReason: overrideForm.overrideReason,
      };

      await api.post("/assessments", overrideData);
      setSuccess("Assessment overridden successfully!");
      setShowOverrideForm(null);
      setOverrideForm({ overriddenValue: "", overrideReason: "" });
      loadAssessments();
    } catch (error) {
      console.error("Failed to override assessment:", error);
      setError("Failed to override assessment. Please try again.");
    }
  };

  const handleUpdatePaymentStatus = async (
    assessmentId: string,
    status: string
  ) => {
    try {
      setError("");
      await api.put(`/assessments/${assessmentId}/payment-status`, {
        paymentStatus: status,
        paymentId: null,
      });
      setSuccess(`Payment status updated to ${status}`);
      loadAssessments();
    } catch (error) {
      console.error("Failed to update payment status:", error);
      setError("Failed to update payment status. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center">
        <div className="text-center">
          <span className="loading loading-spinner loading-lg"></span>
          <p className="mt-4 text-base-content/70">Loading assessments...</p>
        </div>
      </div>
    );
  }

  if (submitting) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center">
        <div className="text-center">
          <span className="loading loading-spinner loading-lg text-warning"></span>
          <p className="mt-4 text-base-content/70">Submitting assessment data...</p>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout
      title="Assessment Management"
      subtitle="Manage property tax assessments for your municipality"
    >
      {/* Success/Error Messages */}
      {(success || successMessage) && (
        <div className="alert alert-success mb-6">
          <span>{success || successMessage}</span>
        </div>
      )}
      {(error || submitError) && (
        <div className="alert alert-error mb-6">
          <span>{error || submitError}</span>
        </div>
      )}

      {/* Header Actions */}
      <div className="flex justify-end mb-6">
        <button
          onClick={() => setShowCreateForm(true)}
          className="btn btn-primary"
        >
          + Create Assessment
        </button>
      </div>

        {/* Tabs */}
        <AdminTabs
          tabs={[
            {
              key: "all",
              label: "All Assessments",
              count: assessments.length,
            },
            {
              key: "pending",
              label: "Pending Payment",
              count: assessments.filter((a) => a.paymentStatus === "Pending")
                .length,
            },
            {
              key: "paid",
              label: "Paid",
              count: assessments.filter((a) => a.paymentStatus === "Paid")
                .length,
            },
            {
              key: "overdue",
              label: "Overdue",
              count: assessments.filter((a) => a.paymentStatus === "Overdue")
                .length,
            },
          ]}
          activeTab={selectedTab}
          onTabChange={(tabKey) => setSelectedTab(tabKey as "all" | "pending" | "paid" | "overdue")}
          className="mb-6"
        />

        {/* Search and Filters */}
        <div className="card bg-base-100 shadow-xl mb-6">
          <div className="card-body">
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="w-full sm:w-auto">
                <input
                  type="text"
                  placeholder="Search by property address or assessment ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input input-bordered w-full sm:w-80"
                />
              </div>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="btn btn-secondary"
              >
                {showFilters ? "Hide Filters" : "Show Filters"}
              </button>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="mt-6 pt-6 border-t border-base-300">
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Assessment Year</span>
                    </label>
                    <select
                      value={filters.year || ""}
                      onChange={(e) =>
                        setFilters({
                          ...filters,
                          year: e.target.value
                            ? parseInt(e.target.value)
                            : undefined,
                        })
                      }
                      className="select select-bordered"
                    >
                      <option value="">All Years</option>
                      <option value="2024">2024</option>
                      <option value="2023">2023</option>
                      <option value="2022">2022</option>
                    </select>
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Min Tax Amount</span>
                    </label>
                    <input
                      type="number"
                      placeholder="Min amount"
                      value={filters.minTaxAmount || ""}
                      onChange={(e) =>
                        setFilters({
                          ...filters,
                          minTaxAmount: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                      className="input input-bordered"
                    />
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Max Tax Amount</span>
                    </label>
                    <input
                      type="number"
                      placeholder="Max amount"
                      value={filters.maxTaxAmount || ""}
                      onChange={(e) =>
                        setFilters({
                          ...filters,
                          maxTaxAmount: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                      className="input input-bordered"
                    />
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Created After</span>
                    </label>
                    <input
                      type="date"
                      value={filters.createdAfter || ""}
                      onChange={(e) =>
                        setFilters({ ...filters, createdAfter: e.target.value })
                      }
                      className="input input-bordered"
                    />
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Created Before</span>
                    </label>
                    <input
                      type="date"
                      value={filters.createdBefore || ""}
                      onChange={(e) =>
                        setFilters({
                          ...filters,
                          createdBefore: e.target.value,
                        })
                      }
                      className="input input-bordered"
                    />
                  </div>
                </div>
                <div className="mt-4 flex gap-2">
                  <button
                    onClick={() => loadAssessments()}
                    className="btn btn-primary"
                  >
                    Apply Filters
                  </button>
                  <button
                    onClick={() => {
                      setFilters({});
                      loadAssessments();
                    }}
                    className="btn btn-secondary"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Assessments Table */}
        <div className="card bg-base-100 shadow-xl mb-6">
          <div className="card-body">
            {assessments.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-base-content/70">No assessments found.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table table-zebra">
                  <thead>
                    <tr>
                      <th>Property Address</th>
                      <th>Assessment Year</th>
                      <th>Tax Amount</th>
                      <th>Payment Status</th>
                      <th>Assessment Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {assessments
                      .filter((assessment) => {
                        if (selectedTab === "all") return true;
                        return assessment.paymentStatus.toLowerCase() === selectedTab;
                      })
                      .filter((assessment) => {
                        if (!searchTerm) return true;
                        return (
                          assessment.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          assessment.id.toLowerCase().includes(searchTerm.toLowerCase())
                        );
                      })
                      .map((assessment) => (
                        <tr key={assessment.id}>
                          <td>{assessment.propertyAddress}</td>
                          <td>{assessment.assessmentYear}</td>
                          <td>${assessment.taxAmount.toFixed(2)}</td>
                          <td>
                            <div className={`badge ${
                              assessment.paymentStatus === "Paid" ? "badge-success" :
                              assessment.paymentStatus === "Pending" ? "badge-warning" :
                              assessment.paymentStatus === "Overdue" ? "badge-error" :
                              "badge-info"
                            }`}>
                              {assessment.paymentStatus}
                            </div>
                          </td>
                          <td>{new Date(assessment.assessmentDate).toLocaleDateString()}</td>
                          <td>
                            <div className="flex gap-2">
                              <button
                                onClick={() => setShowOverrideForm(assessment.id)}
                                className="btn btn-ghost btn-xs"
                              >
                                Override
                              </button>
                              {assessment.paymentStatus === "Pending" && (
                                <button
                                  onClick={() => handleUpdatePaymentStatus(assessment.id, "Paid")}
                                  className="btn btn-success btn-xs"
                                >
                                  Mark Paid
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Create Assessment Full Page Form */}
        {showCreateForm && (
          <div className="modal modal-open">
            <div className="modal-box max-w-4xl">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-base-content">
                  Create New Assessment
                </h2>
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="btn btn-ghost btn-sm btn-circle"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleCreateAssessment} className="space-y-6">
                {submitError && (
                  <div className="alert alert-error mb-4">
                    <span>{submitError}</span>
                  </div>
                )}
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-base-content">
                    Create New Assessment
                  </h3>
                  <p className="text-sm text-base-content/70">
                    Create an assessment for eligible properties
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Property</span>
                    </label>
                    <select
                      value={newAssessment.propertyId}
                      onChange={(e) =>
                        setNewAssessment({
                          ...newAssessment,
                          propertyId: e.target.value,
                        })
                      }
                      className="select select-bordered"
                      required
                    >
                      <option value="">Select a property</option>
                      {properties.map((property) => (
                        <option
                          key={property.propertyId}
                          value={property.propertyId}
                        >
                          {property.propertyAddress} - Paid: $
                          {property.amountPaid.toFixed(2)}, Expected: $
                          {property.calculatedTaxAmount.toFixed(2)}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Fiscal Year</span>
                    </label>
                    <select
                      value={newAssessment.fiscalYearId}
                      onChange={(e) =>
                        setNewAssessment({
                          ...newAssessment,
                          fiscalYearId: e.target.value,
                        })
                      }
                      className="select select-bordered"
                      required
                    >
                      <option value="">Select a fiscal year</option>
                      {fiscalYears.map((year) => (
                        <option
                          key={year.fiscalYearId}
                          value={year.fiscalYearId}
                        >
                          {year.name} {year.isActive ? "(Active)" : ""}
                        </option>
                      ))}
                    </select>
                    {activeFiscalYear && (
                      <p className="mt-1 text-sm text-base-content/70">
                        Active fiscal year: {activeFiscalYear.name}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Original Amount Paid</span>
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newAssessment.originalAmount || ""}
                      onChange={(e) =>
                        setNewAssessment({
                          ...newAssessment,
                          originalAmount: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                      className="input input-bordered"
                      placeholder="Amount automatically filled from property data"
                      disabled={true}
                    />
                    <label className="label">
                      <span className="label-text-alt">
                        Amount paid will be automatically filled from the selected property
                      </span>
                    </label>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">
                        Override Value <span className="text-error">*</span>
                      </span>
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={newAssessment.overriddenValue || ""}
                      onChange={(e) =>
                        setNewAssessment({
                          ...newAssessment,
                          overriddenValue: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                        })
                      }
                      className="input input-bordered"
                      placeholder="Enter the override value"
                      required
                    />
                    <label className="label">
                      <span className="label-text-alt">
                        The assessment value override is required
                      </span>
                    </label>
                  </div>
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">
                      Override Reason <span className="text-error">*</span>
                    </span>
                  </label>
                  <textarea
                    value={newAssessment.overrideReason || ""}
                    onChange={(e) =>
                      setNewAssessment({
                        ...newAssessment,
                        overrideReason: e.target.value,
                      })
                    }
                    className="textarea textarea-bordered"
                    rows={4}
                    placeholder="Explain why the value is being overridden..."
                    required
                  />
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Line Items</span>
                  </label>
                  {newAssessment.lineItems.map((lineItem, index) => (
                    <div key={index} className="card bg-base-100 border border-base-300 mb-6">
                      <div className="card-body">
                        <div className="flex justify-between items-center mb-4">
                          <h4 className="text-md font-medium">Line Item #{index + 1}</h4>
                          {index === newAssessment.lineItems.length - 1 && (
                            <button
                              type="button"
                              onClick={() =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.slice(0, -1),
                                })
                              }
                              className="btn btn-ghost btn-sm text-error"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </button>
                          )}
                        </div>
                      
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="form-control">
                            <label className="label">
                              <span className="label-text">Serial Number</span>
                            </label>
                            <input
                              type="number"
                              value={lineItem.serialNumber}
                              onChange={(e) =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.map(
                                    (item, i) =>
                                      i === index
                                        ? { ...item, serialNumber: Number(e.target.value) }
                                        : item
                                  ),
                                })
                              }
                              className="input input-bordered"
                            />
                          </div>
                          
                          <div className="form-control">
                            <label className="label">
                              <span className="label-text">Tax Description</span>
                            </label>
                            <input
                              type="text"
                              value={lineItem.taxDescription}
                              onChange={(e) =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.map(
                                    (item, i) =>
                                      i === index
                                        ? { ...item, taxDescription: e.target.value }
                                        : item
                                  ),
                                })
                              }
                              className="input input-bordered"
                            />
                          </div>
                          
                          <div className="form-control">
                            <label className="label">
                              <span className="label-text">Fiscal Year</span>
                            </label>
                            <input
                              type="number"
                              value={lineItem.fiscalYear}
                              onChange={(e) =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.map(
                                    (item, i) =>
                                      i === index
                                        ? { ...item, fiscalYear: Number(e.target.value) }
                                        : item
                                  ),
                                })
                              }
                              className="input input-bordered"
                            />
                          </div>
                          
                          <div className="form-control">
                            <label className="label">
                              <span className="label-text">Assessed Amount</span>
                            </label>
                            <input
                              type="number"
                              value={lineItem.assessedAmount}
                              onChange={(e) =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.map(
                                    (item, i) =>
                                      i === index
                                        ? { ...item, assessedAmount: Number(e.target.value) }
                                        : item
                                  ),
                                })
                              }
                              className="input input-bordered"
                            />
                          </div>
                          
                          <div className="form-control">
                            <label className="label">
                              <span className="label-text">Penalty Amount</span>
                            </label>
                            <input
                              type="number"
                              value={lineItem.penaltyAmount}
                              onChange={(e) =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.map(
                                    (item, i) =>
                                      i === index
                                        ? { ...item, penaltyAmount: Number(e.target.value) }
                                        : item
                                  ),
                                })
                              }
                              className="input input-bordered"
                            />
                          </div>
                          
                          <div className="form-control">
                            <label className="label">
                              <span className="label-text">Interest Amount</span>
                            </label>
                            <input
                              type="number"
                              value={lineItem.interestAmount}
                              onChange={(e) =>
                                setNewAssessment({
                                  ...newAssessment,
                                  lineItems: newAssessment.lineItems.map(
                                    (item, i) =>
                                      i === index
                                        ? { ...item, interestAmount: Number(e.target.value) }
                                        : item
                                  ),
                                })
                              }
                              className="input input-bordered"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex gap-4 mb-6">
                  <button
                    type="button"
                    onClick={() =>
                      setNewAssessment({
                        ...newAssessment,
                        lineItems: [
                          ...newAssessment.lineItems,
                          {
                            serialNumber: newAssessment.lineItems.length + 1,
                            taxDescription: "",
                            fiscalYear: new Date().getFullYear(),
                            assessedAmount: 0,
                            penaltyAmount: 0,
                            interestAmount: 0,
                          },
                        ],
                      })
                    }
                    className="btn btn-primary"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Add Line Item
                  </button>
                  {newAssessment.lineItems.length > 0 && (
                    <button
                      type="button"
                      onClick={() =>
                        setNewAssessment({
                          ...newAssessment,
                          lineItems: newAssessment.lineItems.slice(0, -1),
                        })
                      }
                      className="btn btn-error"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      Remove Last Item
                    </button>
                  )}
                </div>

                {/* Property details preview if a property is selected */}
                {newAssessment.propertyId && (
                  <div className="card bg-base-200">
                    <div className="card-body">
                      <h3 className="card-title text-base-content mb-2">
                        Selected Property Details
                      </h3>
                      {properties
                        .filter((p) => p.propertyId === newAssessment.propertyId)
                        .map((property) => (
                          <div
                            key={property.propertyId}
                            className="grid grid-cols-2 gap-2 text-sm"
                          >
                            <div className="font-medium">Address:</div>
                            <div>{property.propertyAddress}</div>
                            <div className="font-medium">Amount Paid:</div>
                            <div>${property.amountPaid.toFixed(2)}</div>
                            <div className="font-medium">Expected Amount:</div>
                            <div>${property.calculatedTaxAmount.toFixed(2)}</div>
                            <div className="font-medium">Difference:</div>
                            <div className="text-error font-bold">
                              $
                              {(
                                property.calculatedTaxAmount - property.amountPaid
                              ).toFixed(2)}
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                <div className="modal-action">
                  <button type="submit" className="btn btn-primary">
                    Create Assessment
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowCreateForm(false)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Override Assessment Modal */}
        {showOverrideForm && (
          <div className="modal modal-open">
            <div className="modal-box">
              <h3 className="font-bold text-lg mb-4">
                Override Assessment
              </h3>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleOverrideAssessment(showOverrideForm);
                }}
                className="space-y-4"
              >
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">New Assessed Value</span>
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={overrideForm.overriddenValue}
                    onChange={(e) =>
                      setOverrideForm({
                        ...overrideForm,
                        overriddenValue: e.target.value,
                      })
                    }
                    className="input input-bordered"
                    required
                  />
                </div>

                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Override Reason</span>
                  </label>
                  <textarea
                    value={overrideForm.overrideReason}
                    onChange={(e) =>
                      setOverrideForm({
                        ...overrideForm,
                        overrideReason: e.target.value,
                      })
                    }
                    className="textarea textarea-bordered"
                    rows={3}
                    placeholder="Explain why the value is being overridden..."
                    required
                  />
                </div>

                <div className="modal-action">
                  <button type="submit" className="btn btn-primary">
                    Override Assessment
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowOverrideForm(null)}
                    className="btn btn-secondary"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
    </AdminLayout>
  );
};

export default AssessmentManagement;
