﻿// <auto-generated />
using System;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250715023418_UpdateSpecialPenaltyTaxpayerIdToString")]
    partial class UpdateSpecialPenaltyTaxpayerIdToString
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "postgis");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Appeal", b =>
                {
                    b.Property<Guid>("AppealId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AppealAuthority")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("AppealDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AppealDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("AppealSubject")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LocationCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OfficeCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("OrderDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("RegistrationNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TaxDeterminationOrderNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TaxPayerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TaxpayerAddress")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TaxpayerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("AppealId");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("TaxPayerId");

                    b.ToTable("Appeals", t =>
                        {
                            t.HasCheckConstraint("CK_Appeal_Status", "\"Status\" IN ('Pending', 'Resolved')");
                        });
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CitizenshipIssueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CitizenshipIssueDistrict")
                        .HasColumnType("text");

                    b.Property<string>("CitizenshipIssueOffice")
                        .HasColumnType("text");

                    b.Property<string>("CitizenshipNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("ContactPreferences")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocumentPath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FatherName")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("GrandfatherName")
                        .HasColumnType("text");

                    b.Property<string>("GrandmotherName")
                        .HasColumnType("text");

                    b.Property<string>("GuardianName")
                        .HasColumnType("text");

                    b.Property<string>("GuardianRelation")
                        .HasColumnType("text");

                    b.Property<bool>("IsMinor")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MaritalStatus")
                        .HasColumnType("text");

                    b.Property<string>("MotherName")
                        .HasColumnType("text");

                    b.Property<Guid?>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("NationalIdDocumentPath")
                        .HasColumnType("text");

                    b.Property<DateTime?>("NationalIdIssueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NationalIdIssueDistrict")
                        .HasColumnType("text");

                    b.Property<string>("NationalIdIssueOffice")
                        .HasColumnType("text");

                    b.Property<string>("NationalIdNumber")
                        .HasColumnType("text");

                    b.Property<string>("Nationality")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PAN")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("PanDocumentPath")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PanIssueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PanIssueDistrict")
                        .HasColumnType("text");

                    b.Property<string>("PanIssueOffice")
                        .HasColumnType("text");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PermanentAddress")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("Profession")
                        .HasColumnType("text");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<string>("SpouseName")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SubmissionNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Telephone")
                        .HasColumnType("text");

                    b.Property<string>("TemporaryAddress")
                        .HasColumnType("text");

                    b.Property<string>("ToleStreet")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("TwoGenerations")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("WardNumber")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.HasKey("Id");

                    b.HasIndex("MunicipalityId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("PAN")
                        .IsUnique();

                    b.HasIndex("SubmissionNumber")
                        .IsUnique();

                    b.ToTable("AspNetUsers", null, t =>
                        {
                            t.HasCheckConstraint("CK_User_Status", "\"Status\" IN ('PendingApproval', 'Active', 'Rejected')");
                        });
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Assessment", b =>
                {
                    b.Property<Guid>("AssessmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("ActualAssessment")
                        .HasColumnType("numeric");

                    b.Property<string>("AssessedByOfficerId")
                        .HasColumnType("text");

                    b.Property<DateTime>("AssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AssessmentYear")
                        .HasColumnType("integer");

                    b.Property<decimal>("CalculatedValue")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("DiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<string>("ExemptionAppliedDetailsJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<decimal>("FinalAssessedValue")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("LowerAssessment")
                        .HasColumnType("numeric");

                    b.Property<int>("Origin")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OriginalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("OverriddenValue")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<string>("OverrideReason")
                        .HasColumnType("text");

                    b.Property<string>("PaymentStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("PenaltyPercent")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("PreviousAssessmentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PropertyId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Superseded")
                        .HasColumnType("boolean");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("UpperAssessment")
                        .HasColumnType("numeric");

                    b.HasKey("AssessmentId");

                    b.HasIndex("AssessedByOfficerId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("PropertyId", "AssessmentYear")
                        .IsUnique();

                    b.ToTable("Assessments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AssessmentLineItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AssessedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FiscalYear")
                        .HasColumnType("integer");

                    b.Property<decimal>("InterestAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PenaltyAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SerialNumber")
                        .HasColumnType("integer");

                    b.Property<string>("TaxDescription")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("AssessmentLineItem");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AuditLog", b =>
                {
                    b.Property<long>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("LogId"));

                    b.Property<string>("ActionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("NewValueJson")
                        .HasColumnType("jsonb");

                    b.Property<string>("OldValueJson")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("LogId");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.District", b =>
                {
                    b.Property<Guid>("DistrictId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("ProvinceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("DistrictId");

                    b.HasIndex("ProvinceId");

                    b.ToTable("Districts");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FinalAssessment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("AppealNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("AssessmentPeriodFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("AssessmentPeriodTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BankName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("BranchName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("FinalAssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("InterestPenaltyCalculationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("PreliminaryAssessmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("ReasonCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ReasonDescription")
                        .HasColumnType("text");

                    b.Property<Guid?>("ReturnFilingId")
                        .HasColumnType("uuid");

                    b.Property<string>("SectionsRules")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Draft");

                    b.Property<string>("TaxpayerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TaxpayerRegistration")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("MunicipalityId");

                    b.HasIndex("PreliminaryAssessmentId");

                    b.HasIndex("ReturnFilingId");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("FinalAssessments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FinalAssessmentDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AdditionalAmount")
                        .HasColumnType("decimal(15,2)");

                    b.Property<decimal>("AssessedAmount")
                        .HasColumnType("decimal(15,2)");

                    b.Property<string>("FilingPeriod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("FinalAssessmentId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Interest")
                        .HasColumnType("decimal(15,2)");

                    b.Property<decimal>("Penalty")
                        .HasColumnType("decimal(15,2)");

                    b.Property<int>("SerialNumber")
                        .HasColumnType("integer");

                    b.Property<string>("TaxPeriod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TaxYear")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(15,2)");

                    b.HasKey("Id");

                    b.HasIndex("FinalAssessmentId");

                    b.ToTable("FinalAssessmentDetails");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FiscalYear", b =>
                {
                    b.Property<Guid>("FiscalYearId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("FiscalYearId");

                    b.ToTable("FiscalYears");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.LandDetail", b =>
                {
                    b.Property<Guid>("LandDetailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AreaAana")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("AreaBigha")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("AreaDaam")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("AreaDhur")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("AreaKattha")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("AreaPaisa")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("AreaRopani")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrentWardNo")
                        .HasColumnType("text");

                    b.Property<bool>("DeactivatePlot")
                        .HasColumnType("boolean");

                    b.Property<string>("FiscalYear")
                        .HasColumnType("text");

                    b.Property<bool>("IsCultivable")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMultiRateValuation")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTemporaryHouse")
                        .HasColumnType("boolean");

                    b.Property<string>("KittaNo")
                        .HasColumnType("text");

                    b.Property<string>("LandRevenueApplicable")
                        .HasColumnType("text");

                    b.Property<string>("LastFyForInclusion")
                        .HasColumnType("text");

                    b.Property<string>("MapNo")
                        .HasColumnType("text");

                    b.Property<string>("MeasurementUnit")
                        .HasColumnType("text");

                    b.Property<string>("OldVdc")
                        .HasColumnType("text");

                    b.Property<string>("OldWardNo")
                        .HasColumnType("text");

                    b.Property<string>("OtherDetails")
                        .HasColumnType("text");

                    b.Property<Guid>("PropertyId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("StructureAreaBreadth")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("StructureAreaLength")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TaxpayerLandRevenuePrice")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("TaxpayerPrice")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("LandDetailId");

                    b.HasIndex("FiscalYear");

                    b.HasIndex("KittaNo");

                    b.HasIndex("LastFyForInclusion");

                    b.HasIndex("PropertyId");

                    b.ToTable("LandDetails");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Municipality", b =>
                {
                    b.Property<Guid>("MunicipalityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("DefaultDiscountPercent")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DefaultPenaltyPercent")
                        .HasColumnType("numeric");

                    b.Property<Guid>("DistrictId")
                        .HasColumnType("uuid");

                    b.Property<string>("ExemptionRulesConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TaxSlabsConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("[]");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ValuationRulesConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<int>("WardCount")
                        .HasColumnType("integer");

                    b.HasKey("MunicipalityId");

                    b.HasIndex("DistrictId");

                    b.ToTable("Municipalities");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.MunicipalityTaxConfig", b =>
                {
                    b.Property<Guid>("MunicipalityTaxConfigId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExemptionRulesConfigJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("FinalizedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FinalizedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("PenaltyRulesJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<string>("TaxSlabsConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("[]");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ValuationRulesConfigJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("MunicipalityTaxConfigId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("MunicipalityId");

                    b.ToTable("MunicipalityTaxConfigs");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Negotiation", b =>
                {
                    b.Property<Guid>("NegotiationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AppealBody")
                        .HasColumnType("integer");

                    b.Property<Guid>("AppealId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("DecidedFee")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DecidedInterest")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DecidedPenalty")
                        .HasColumnType("numeric");

                    b.Property<decimal>("DecidedTax")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("DecisionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DecisionNumber")
                        .HasColumnType("text");

                    b.Property<bool>("IsWithdrawn")
                        .HasColumnType("boolean");

                    b.Property<decimal>("NegotiatedAmount")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<DateTime>("NegotiationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OfficerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("OriginalFee")
                        .HasColumnType("numeric");

                    b.Property<decimal>("OriginalInterest")
                        .HasColumnType("numeric");

                    b.Property<decimal>("OriginalPenalty")
                        .HasColumnType("numeric");

                    b.Property<decimal>("OriginalTax")
                        .HasColumnType("numeric");

                    b.Property<string>("PANNumber")
                        .HasColumnType("text");

                    b.Property<string>("Remarks")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("TaxOfficeAddress")
                        .HasColumnType("text");

                    b.Property<string>("TaxpayerName")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("NegotiationId");

                    b.HasIndex("AppealId")
                        .IsUnique();

                    b.HasIndex("OfficerId");

                    b.ToTable("Negotiations");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Notification", b =>
                {
                    b.Property<Guid>("NotificationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("RelatedEntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("RelatedEntityType")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("NotificationId");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Payment", b =>
                {
                    b.Property<Guid>("PaymentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AmountPaid")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<Guid?>("AssessmentId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("BatchPaymentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsReconciled")
                        .HasColumnType("boolean");

                    b.Property<bool>("Partial")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentGateway")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("PropertyId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Provisional")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("PaymentId");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("Provisional")
                        .HasDatabaseName("IX_Payments_Provisional");

                    b.HasIndex("PropertyId", "FiscalYearId")
                        .HasDatabaseName("IX_Payments_PropertyId_FiscalYearId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.PreliminaryAssessment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ActSection")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("AppealNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("AssessmentPeriod")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("AssessmentPeriodFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("AssessmentPeriodTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Bank")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Branch")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("InterestCalculationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MobileNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("OtherReasonDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Phone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("PreliminaryAssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<string>("ReasonForAssessment")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Regulations")
                        .HasColumnType("text");

                    b.Property<Guid?>("ReturnFilingId")
                        .HasColumnType("uuid");

                    b.Property<string>("Rule")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Draft");

                    b.Property<string>("TaxpayerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TaxpayerRegistration")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("MunicipalityId");

                    b.HasIndex("ReturnFilingId");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("PreliminaryAssessments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.PreliminaryAssessmentDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AdditionalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("AssessedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FilingPeriod")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("Interest")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Penalty")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Period")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("PreliminaryAssessmentId")
                        .HasColumnType("uuid");

                    b.Property<int>("SerialNumber")
                        .HasColumnType("integer");

                    b.Property<string>("TaxYear")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("PreliminaryAssessmentId");

                    b.ToTable("PreliminaryAssessmentDetails");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Property", b =>
                {
                    b.Property<Guid>("PropertyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Aana")
                        .HasColumnType("numeric");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("ApplicableFiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Bigha")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("BuildingBuiltUpAreaSqM")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)");

                    b.Property<string>("BuildingConstructionType")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("BuildingConstructionYear")
                        .HasColumnType("integer");

                    b.Property<int?>("BuildingConstructionYearAlt")
                        .HasColumnType("integer");

                    b.Property<string>("BuildingNumber")
                        .HasColumnType("text");

                    b.Property<string>("BuildingPermitPath")
                        .HasColumnType("text");

                    b.Property<string>("BuildingType")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("Daam")
                        .HasColumnType("numeric");

                    b.Property<string>("DeregistrationReason")
                        .HasColumnType("text");

                    b.Property<decimal?>("Dhur")
                        .HasColumnType("numeric");

                    b.Property<string>("EastBoundary")
                        .HasColumnType("text");

                    b.Property<string>("ExemptionReason")
                        .HasColumnType("text");

                    b.Property<string>("Facilities")
                        .HasColumnType("text");

                    b.Property<string>("FloorNumber")
                        .HasColumnType("text");

                    b.Property<bool>("IsDefaulter")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeregistered")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExempted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTaxApplicable")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("Kattha")
                        .HasColumnType("numeric");

                    b.Property<string>("LalpurjaNo")
                        .HasColumnType("text");

                    b.Property<decimal>("LandAreaSqM")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)");

                    b.Property<string>("LandOwnership")
                        .HasColumnType("text");

                    b.Property<string>("LandType")
                        .HasColumnType("text");

                    b.Property<string>("LandUse")
                        .HasColumnType("text");

                    b.Property<string>("MolNo")
                        .HasColumnType("text");

                    b.Property<Guid>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("NatureOfLand")
                        .HasColumnType("text");

                    b.Property<string>("NorthBoundary")
                        .HasColumnType("text");

                    b.Property<string>("OtherDetails")
                        .HasColumnType("text");

                    b.Property<string>("OwnerUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OwnershipCertificatePath")
                        .HasColumnType("text");

                    b.Property<string>("OwnershipDetails")
                        .HasColumnType("text");

                    b.Property<string>("OwnershipType")
                        .HasColumnType("text");

                    b.Property<string>("OwnershipValue")
                        .HasColumnType("text");

                    b.Property<decimal?>("Paisa")
                        .HasColumnType("numeric");

                    b.Property<Polygon>("ParcelGeometry")
                        .IsRequired()
                        .HasColumnType("geometry(Polygon, 4326)");

                    b.Property<string>("ParcelNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhysicalArea")
                        .HasColumnType("text");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RelationWithStreet")
                        .HasColumnType("text");

                    b.Property<decimal?>("Ropani")
                        .HasColumnType("numeric");

                    b.Property<string>("SheetNo")
                        .HasColumnType("text");

                    b.Property<string>("SouthBoundary")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Street")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StreetType")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UsageType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("WardNumber")
                        .HasColumnType("integer");

                    b.Property<string>("WestBoundary")
                        .HasColumnType("text");

                    b.HasKey("PropertyId");

                    b.HasIndex("ApplicableFiscalYearId");

                    b.HasIndex("MunicipalityId");

                    b.HasIndex("OwnerUserId");

                    b.ToTable("Properties");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Province", b =>
                {
                    b.Property<Guid>("ProvinceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("ProvinceId");

                    b.ToTable("Provinces");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Rebate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ExemptionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsReversal")
                        .HasColumnType("boolean");

                    b.Property<string>("MaNo")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OfficeCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Pan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Scheme")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SerialNo")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Rebates");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.RebateItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FilingPeriod")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FiscalYear")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RebateId")
                        .HasColumnType("integer");

                    b.Property<string>("TaxPeriod")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("TotalExemptedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("RebateId");

                    b.ToTable("RebateItems");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ReturnFiling", b =>
                {
                    b.Property<Guid>("ReturnFilingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdditionalNotes")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PropertyId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("SubmissionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubmittedByUserId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("ReturnFilingId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("SubmissionDate");

                    b.HasIndex("SubmittedByUserId");

                    b.HasIndex("PropertyId", "FiscalYearId")
                        .IsUnique();

                    b.ToTable("ReturnFilings");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.SpecialPenalty", b =>
                {
                    b.Property<Guid>("SpecialPenaltyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("CreatedById")
                        .HasColumnType("text");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OtherReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("ReasonDetails")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("SpecialPenaltyNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Active");

                    b.Property<int>("TaxYear")
                        .HasColumnType("integer");

                    b.Property<string>("TaxpayerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("UpdatedById")
                        .HasColumnType("text");

                    b.HasKey("SpecialPenaltyId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SpecialPenaltyNo")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("TaxYear");

                    b.HasIndex("TaxpayerId");

                    b.HasIndex("UpdatedById");

                    b.ToTable("SpecialPenalties");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.TaxPeriod", b =>
                {
                    b.Property<Guid>("TaxPeriodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AppealAmount")
                        .HasMaxLength(50)
                        .HasColumnType("numeric");

                    b.Property<Guid>("AppealId")
                        .HasColumnType("uuid");

                    b.Property<string>("AppealSubject")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EndDate")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("FiscalYear")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("NegotiationId")
                        .HasColumnType("uuid");

                    b.Property<string>("Period")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("StartDate")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TaxPeriodValue")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Year")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("TaxPeriodId");

                    b.HasIndex("AppealId");

                    b.HasIndex("NegotiationId");

                    b.ToTable("TaxPeriods");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Appeal", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Assessment", "Assessment")
                        .WithMany()
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "TaxPayer")
                        .WithMany()
                        .HasForeignKey("TaxPayerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("TaxPayer");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ApplicationUser", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany("Officers")
                        .HasForeignKey("MunicipalityId");

                    b.Navigation("Municipality");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Assessment", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "AssessedByOfficer")
                        .WithMany()
                        .HasForeignKey("AssessedByOfficerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Property", "Property")
                        .WithMany("Assessments")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AssessedByOfficer");

                    b.Navigation("FiscalYear");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AssessmentLineItem", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Assessment", "Assessment")
                        .WithMany("LineItems")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AuditLog", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.District", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Province", "Province")
                        .WithMany("Districts")
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Province");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FinalAssessment", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany()
                        .HasForeignKey("MunicipalityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.PreliminaryAssessment", "PreliminaryAssessment")
                        .WithMany()
                        .HasForeignKey("PreliminaryAssessmentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("LandTaxSystem.Core.Entities.ReturnFiling", "ReturnFiling")
                        .WithMany()
                        .HasForeignKey("ReturnFilingId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("FiscalYear");

                    b.Navigation("Municipality");

                    b.Navigation("PreliminaryAssessment");

                    b.Navigation("ReturnFiling");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FinalAssessmentDetail", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.FinalAssessment", "FinalAssessment")
                        .WithMany("TaxDetails")
                        .HasForeignKey("FinalAssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FinalAssessment");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.LandDetail", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Property", "Property")
                        .WithMany("LandDetails")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Property");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Municipality", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.District", "District")
                        .WithMany("Municipalities")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.MunicipalityTaxConfig", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany("MunicipalityTaxConfigs")
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany("MunicipalityTaxConfigs")
                        .HasForeignKey("MunicipalityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FiscalYear");

                    b.Navigation("Municipality");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Negotiation", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Appeal", "Appeal")
                        .WithOne("Negotiation")
                        .HasForeignKey("LandTaxSystem.Core.Entities.Negotiation", "AppealId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "Officer")
                        .WithMany()
                        .HasForeignKey("OfficerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Appeal");

                    b.Navigation("Officer");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Payment", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Assessment", "Assessment")
                        .WithMany("Payments")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("FiscalYear");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.PreliminaryAssessment", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany()
                        .HasForeignKey("MunicipalityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ReturnFiling", "ReturnFiling")
                        .WithMany()
                        .HasForeignKey("ReturnFilingId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedByUser");

                    b.Navigation("Municipality");

                    b.Navigation("ReturnFiling");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.PreliminaryAssessmentDetail", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.PreliminaryAssessment", "PreliminaryAssessment")
                        .WithMany("TaxDetails")
                        .HasForeignKey("PreliminaryAssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PreliminaryAssessment");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Property", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "ApplicableFiscalYear")
                        .WithMany()
                        .HasForeignKey("ApplicableFiscalYearId");

                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany("Properties")
                        .HasForeignKey("MunicipalityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ApplicableFiscalYear");

                    b.Navigation("Municipality");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.RebateItem", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Rebate", "Rebate")
                        .WithMany("RebateItems")
                        .HasForeignKey("RebateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rebate");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ReturnFiling", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "SubmittedByUser")
                        .WithMany()
                        .HasForeignKey("SubmittedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("FiscalYear");

                    b.Navigation("Property");

                    b.Navigation("SubmittedByUser");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.SpecialPenalty", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "Taxpayer")
                        .WithMany()
                        .HasForeignKey("TaxpayerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "UpdatedBy")
                        .WithMany()
                        .HasForeignKey("UpdatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("Taxpayer");

                    b.Navigation("UpdatedBy");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.TaxPeriod", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Appeal", "Appeal")
                        .WithMany("TaxPeriods")
                        .HasForeignKey("AppealId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Negotiation", null)
                        .WithMany("TaxPeriods")
                        .HasForeignKey("NegotiationId");

                    b.Navigation("Appeal");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Appeal", b =>
                {
                    b.Navigation("Negotiation");

                    b.Navigation("TaxPeriods");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Assessment", b =>
                {
                    b.Navigation("LineItems");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.District", b =>
                {
                    b.Navigation("Municipalities");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FinalAssessment", b =>
                {
                    b.Navigation("TaxDetails");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FiscalYear", b =>
                {
                    b.Navigation("MunicipalityTaxConfigs");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Municipality", b =>
                {
                    b.Navigation("MunicipalityTaxConfigs");

                    b.Navigation("Officers");

                    b.Navigation("Properties");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Negotiation", b =>
                {
                    b.Navigation("TaxPeriods");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.PreliminaryAssessment", b =>
                {
                    b.Navigation("TaxDetails");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Property", b =>
                {
                    b.Navigation("Assessments");

                    b.Navigation("LandDetails");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Province", b =>
                {
                    b.Navigation("Districts");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Rebate", b =>
                {
                    b.Navigation("RebateItems");
                });
#pragma warning restore 612, 618
        }
    }
}
