using System.Collections.Generic;

namespace LandTaxSystem.Core.Models
{
    public class ValuationRulesConfig
    {
        public Dictionary<string, decimal> LandMVR { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, decimal> BuildingBaseRatePerSqm { get; set; } = new Dictionary<string, decimal>();
        public decimal AnnualDepreciationRate { get; set; } = 0.01m;
    }

    public class LegacyTaxSlab
    {
        public decimal MinAssessedValue { get; set; }
        public decimal MaxAssessedValue { get; set; }
        public decimal RatePercent { get; set; }
        public decimal FixedAmount { get; set; }
    }

    public class ExemptionRule
    {
        public string Condition { get; set; } = string.Empty;
        public decimal DiscountPercent { get; set; }
    }

    public class ExemptionRulesConfig
    {
        public List<ExemptionRule> Rules { get; set; } = new List<ExemptionRule>();
    }
}
