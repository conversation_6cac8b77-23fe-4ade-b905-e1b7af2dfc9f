import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { appealService, type AppealDto } from '../services/appealService';
import { format } from 'date-fns';
import { AdminLayout } from '../components/admin';

const AppealsList: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [appeals, setAppeals] = useState<AppealDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isOfficer = user?.role === 'Officer';
  const isCitizen = user?.role === 'Citizen';

  const fetchAppeals = useCallback(async () => {
    try {
      setLoading(true);
      const data = isOfficer 
        ? await appealService.getAllAppeals()
        : await appealService.getUserAppeals();
      setAppeals(data);
    } catch (err) {
      setError('Failed to fetch appeals');
      console.error('Error fetching appeals:', err);
    } finally {
      setLoading(false);
    }
  }, [isOfficer]);

  useEffect(() => {
    fetchAppeals();
  }, [fetchAppeals]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'badge-warning';
      case 'approved':
        return 'badge-success';
      case 'rejected':
        return 'badge-error';
      case 'resolved':
        return 'badge-info';
      default:
        return 'badge-ghost';
    }
  };

  const handleCreateNegotiation = (appealId: string) => {
    navigate(`/negotiations/create/${appealId}`);
  };

  const handleNewAppeal = () => {
    navigate('/appeals/new');
  };

  const handleViewAppeal = (appealId: string) => {
    navigate(`/appeals/${appealId}`);
  };

  if (loading) {
    return (
      <AdminLayout title={isOfficer ? 'Manage Appeals' : 'My Appeals'}>
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout title={isOfficer ? 'Manage Appeals' : 'My Appeals'}>
        <div className="alert alert-error">
          {error}
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={isOfficer ? 'Manage Appeals' : 'My Appeals'}>
      <div className="flex justify-end mb-6">
        {isCitizen && (
          <button 
            className="btn btn-primary"
            onClick={handleNewAppeal}
          >
            New Appeal
          </button>
        )}
      </div>

      {appeals.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            {isOfficer 
              ? 'No appeals found in your municipality.' 
              : 'You have no appeals yet.'
            }
          </div>
          {isCitizen && (
            <button 
              className="btn btn-primary"
              onClick={handleNewAppeal}
            >
              Submit Your First Appeal
            </button>
          )}
        </div>
      ) : (
        <div className="grid gap-4">
          {appeals.map((appeal) => (
            <div key={appeal.appealId} className="card bg-base-100 shadow hover:shadow-md transition-shadow">
              <div className="card-body">
                <div className="flex justify-between items-start">
                  <h2 className="card-title">
                    Appeal #{appeal.appealId.slice(0, 8)}
                  </h2>
                  <span className={`badge ${getStatusBadgeVariant(appeal.status)}`}>
                    {appeal.status}
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <div className="label-text text-sm">Property Address</div>
                    <div className="font-medium">{appeal.propertyAddress}</div>
                  </div>
                  
                  {isOfficer && (
                    <div>
                      <div className="label-text text-sm">Taxpayer</div>
                      <div className="font-medium">{appeal.taxPayerName}</div>
                    </div>
                  )}
                  
                  <div>
                    <div className="label-text text-sm">Assessment Amount</div>
                    <div className="font-medium">NPR {appeal.assessmentAmount ? appeal.assessmentAmount.toLocaleString() : '0'}</div>
                  </div>
                  
                  <div>
                    <div className="label-text text-sm">Submitted Date</div>
                    <div className="font-medium">
                      {appeal.submittedAt ? format(new Date(appeal.submittedAt), 'MMM dd, yyyy') : 'N/A'}
                    </div>
                  </div>
                  
                  <div>
                    <div className="label-text text-sm">Reason</div>
                    <div className="font-medium truncate" title={appeal.reason}>
                      {appeal.reason}
                    </div>
                  </div>
                  
                  {appeal.resolvedAt && (
                    <div>
                      <div className="label-text text-sm">Resolved Date</div>
                      <div className="font-medium">
                        {appeal.resolvedAt ? format(new Date(appeal.resolvedAt), 'MMM dd, yyyy') : 'N/A'}
                      </div>
                    </div>
                  )}
                </div>
                
                {appeal.responseMessage && (
                  <div className="mt-4 p-3 bg-base-200 rounded">
                    <div className="label-text text-sm">Response</div>
                    <div className="text-sm">{appeal.responseMessage}</div>
                  </div>
                )}
                
                <div className="card-actions justify-end mt-4">
                  <button 
                    className="btn btn-ghost"
                    onClick={() => handleViewAppeal(appeal.appealId)}
                  >
                    View Details
                  </button>
                  
                  {isOfficer && appeal.status.toLowerCase() === 'pending' && (
                    <button 
                      className="btn btn-primary"
                      onClick={() => handleCreateNegotiation(appeal.appealId)}
                    >
                      Create Negotiation
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </AdminLayout>
  );
};

export default AppealsList;
