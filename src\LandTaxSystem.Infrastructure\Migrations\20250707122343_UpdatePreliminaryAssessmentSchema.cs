﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePreliminaryAssessmentSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AssessmentOrderNumber",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "OfficeCode",
                table: "PreliminaryAssessments");

            migrationBuilder.AddColumn<Guid>(
                name: "FiscalYearId",
                table: "PreliminaryAssessments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "MunicipalityId",
                table: "PreliminaryAssessments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "ReturnFilingId",
                table: "PreliminaryAssessments",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessments_FiscalYearId",
                table: "PreliminaryAssessments",
                column: "FiscalYearId");

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessments_MunicipalityId",
                table: "PreliminaryAssessments",
                column: "MunicipalityId");

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessments_ReturnFilingId",
                table: "PreliminaryAssessments",
                column: "ReturnFilingId");

            migrationBuilder.AddForeignKey(
                name: "FK_PreliminaryAssessments_FiscalYears_FiscalYearId",
                table: "PreliminaryAssessments",
                column: "FiscalYearId",
                principalTable: "FiscalYears",
                principalColumn: "FiscalYearId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PreliminaryAssessments_Municipalities_MunicipalityId",
                table: "PreliminaryAssessments",
                column: "MunicipalityId",
                principalTable: "Municipalities",
                principalColumn: "MunicipalityId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PreliminaryAssessments_ReturnFilings_ReturnFilingId",
                table: "PreliminaryAssessments",
                column: "ReturnFilingId",
                principalTable: "ReturnFilings",
                principalColumn: "ReturnFilingId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PreliminaryAssessments_FiscalYears_FiscalYearId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropForeignKey(
                name: "FK_PreliminaryAssessments_Municipalities_MunicipalityId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropForeignKey(
                name: "FK_PreliminaryAssessments_ReturnFilings_ReturnFilingId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropIndex(
                name: "IX_PreliminaryAssessments_FiscalYearId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropIndex(
                name: "IX_PreliminaryAssessments_MunicipalityId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropIndex(
                name: "IX_PreliminaryAssessments_ReturnFilingId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "FiscalYearId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "MunicipalityId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "ReturnFilingId",
                table: "PreliminaryAssessments");

            migrationBuilder.AddColumn<string>(
                name: "AssessmentOrderNumber",
                table: "PreliminaryAssessments",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OfficeCode",
                table: "PreliminaryAssessments",
                type: "character varying(10)",
                maxLength: 10,
                nullable: false,
                defaultValue: "");
        }
    }
}
