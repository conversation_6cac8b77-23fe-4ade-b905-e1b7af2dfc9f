import api from './api';

export interface TaxPeriodDto {
  fiscalYear: string;
  startDate: string;
  endDate: string;
  
  // Legacy fields - keeping these for type compatibility with backend
  year?: number;
  period?: string;
  taxPeriodValue?: string;
  appealSubject?: string;
  appealAmount?: number;
}

export interface NegotiationDto {
  negotiationId: string;
  appealId: string;
  officerId: string;
  officerName: string;
  negotiatedAmount: number;
  negotiationDate: string;
  
  // Appeal Completion Notice Fields
  taxpayerName: string;
  panNumber: string;
  taxOfficeAddress: string;
  appealBody: 'IRD' | 'RevenueTribunal' | 'SupremeCourt';
  decisionNumber: string;
  decisionDate: string;
  taxPeriods: TaxPeriodDto[];
  originalTax: number;
  originalPenalty: number;
  originalFee: number;
  originalInterest: number;
  decidedTax: number;
  decidedPenalty: number;
  decidedFee: number;
  decidedInterest: number;
  isWithdrawn: boolean;
  
  // Legacy fields (kept for backward compatibility)
  appealReason?: string;
  appealSubmittedAt?: string;
  assessmentId?: string;
  originalAssessmentAmount?: number;
  propertyId?: string;
  propertyAddress?: string;
  taxPayerId?: string;
  taxPayerName?: string;
  status?: string;
}

export interface CreateNegotiationDto {
  appealId: string;
  negotiatedAmount: number;
  
  // New fields for appeal completion notice
  taxpayerName: string;
  panNumber: string;
  taxOfficeAddress: string;
  appealBody: 'IRD' | 'RevenueTribunal' | 'SupremeCourt';
  decisionNumber: string;
  decisionDate: string;
  taxPeriods: Omit<TaxPeriodDto, 'id'>[];
  originalTax: number;
  originalPenalty: number;
  originalFee: number;
  originalInterest: number;
  decidedTax: number;
  decidedPenalty: number;
  decidedFee: number;
  decidedInterest: number;
  isWithdrawn: boolean;
}

export const negotiationService = {
  // Get negotiations for the current user
  getUserNegotiations: async (): Promise<NegotiationDto[]> => {
    // The backend controller automatically filters by current user
    const response = await api.get('/Negotiations');
    return response.data;
  },

  // Get all negotiations (for officers)
  getAllNegotiations: async (): Promise<NegotiationDto[]> => {
    const response = await api.get('/Negotiations');
    return response.data;
  },

  // Get a specific negotiation by ID
  getNegotiation: async (negotiationId: string): Promise<NegotiationDto> => {
    const response = await api.get(`/Negotiations/${negotiationId}`);
    return response.data;
  },

  // Create a new negotiation (officer only)
  createNegotiation: async (createNegotiationDto: CreateNegotiationDto): Promise<NegotiationDto> => {
    // Convert string AppealBody to numeric enum value
    const mappedAppealBody = {
      'Tax Office': 0,
      'Revenue Tribunal': 1,
      'Supreme Court': 2
    };
    
    // Ensure appealBody is properly formatted as a number
    const formattedDto = {
      ...createNegotiationDto,
      appealBody: typeof createNegotiationDto.appealBody === 'string' 
        ? mappedAppealBody[createNegotiationDto.appealBody as keyof typeof mappedAppealBody] 
        : createNegotiationDto.appealBody
    };
    
    const response = await api.post('/Negotiations', formattedDto);
    return response.data;
  },

  // Accept a negotiation (taxpayer only)
  acceptNegotiation: async (negotiationId: string): Promise<NegotiationDto> => {
    const response = await api.put(`/Negotiations/${negotiationId}/accept`, {});
    return response.data;
  },

  // Reject a negotiation (taxpayer only)
  rejectNegotiation: async (negotiationId: string, reason: string): Promise<NegotiationDto> => {
    const response = await api.put(`/Negotiations/${negotiationId}/reject`, { reason });
    return response.data;
  },

  // Cancel a negotiation (officer only)
  cancelNegotiation: async (negotiationId: string): Promise<void> => {
    await api.delete(`/Negotiations/${negotiationId}`);
  }
};
