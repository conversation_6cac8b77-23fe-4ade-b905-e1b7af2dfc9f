<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mock Payment Gateway</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f7f7f7;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
      }
      .payment-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 30px;
        max-width: 500px;
        width: 100%;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #333;
        margin: 0;
        font-size: 24px;
      }
      .header p {
        color: #666;
        margin-top: 8px;
      }
      .payment-details {
        margin-bottom: 30px;
      }
      .payment-details .row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #eee;
      }
      .payment-details .row .label {
        font-weight: bold;
        color: #333;
      }
      .payment-details .row .value {
        color: #555;
      }
      .total {
        font-size: 18px;
        font-weight: bold;
      }
      .form-group {
        margin-bottom: 20px;
      }
      .form-group label {
        display: block;
        margin-bottom: 6px;
        font-weight: bold;
        color: #333;
      }
      .form-group input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
      }
      .btn {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        width: 100%;
        margin-bottom: 10px;
      }
      .btn.cancel {
        background-color: #dc3545;
      }
      .btn:hover {
        opacity: 0.9;
      }
      .receipt {
        text-align: center;
        display: none;
      }
      .success-icon {
        font-size: 50px;
        color: #28a745;
        margin-bottom: 20px;
      }
      .error-icon {
        font-size: 50px;
        color: #dc3545;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <div class="payment-container">
      <div id="payment-form">
        <div class="header">
          <h1>Mock Payment Gateway</h1>
          <p>This is a simulated payment experience for the Land Tax System.</p>
        </div>

        <div class="payment-details">
          <div class="row">
            <div class="label">Assessment ID:</div>
            <div class="value" id="assessmentId"></div>
          </div>
          <div class="row">
            <div class="label">Payment ID:</div>
            <div class="value" id="paymentId"></div>
          </div>
          <div class="row total">
            <div class="label">Amount:</div>
            <div class="value" id="amount"></div>
          </div>
        </div>

        <div class="form-group">
          <label for="cardNumber">Card Number</label>
          <input
            type="text"
            id="cardNumber"
            placeholder="1234 5678 9012 3456"
            value="4111 1111 1111 1111"
            readonly
          />
        </div>

        <div class="form-group">
          <label for="cardName">Cardholder Name</label>
          <input
            type="text"
            id="cardName"
            placeholder="John Doe"
            value="Test User"
            readonly
          />
        </div>

        <div style="display: flex; gap: 10px">
          <div class="form-group" style="flex: 1">
            <label for="expiry">Expiry Date</label>
            <input
              type="text"
              id="expiry"
              placeholder="MM/YY"
              value="12/30"
              readonly
            />
          </div>
          <div class="form-group" style="flex: 1">
            <label for="cvv">CVV</label>
            <input
              type="text"
              id="cvv"
              placeholder="123"
              value="123"
              readonly
            />
          </div>
        </div>

        <button id="pay-button" class="btn">Pay Now</button>
        <button id="cancel-button" class="btn cancel">Cancel Payment</button>
      </div>

      <div id="success-receipt" class="receipt">
        <div class="success-icon">✓</div>
        <h2>Payment Successful!</h2>
        <p>Your transaction has been completed successfully.</p>
        <p>Transaction ID: <span id="transaction-id"></span></p>
        <button id="close-button" class="btn">Close</button>
      </div>

      <div id="error-receipt" class="receipt">
        <div class="error-icon">✗</div>
        <h2>Payment Failed</h2>
        <p>Your transaction could not be processed.</p>
        <button id="retry-button" class="btn">Try Again</button>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Parse query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const assessmentId = urlParams.get("assessmentId");
        const amount = urlParams.get("amount");
        const paymentId = urlParams.get("paymentId");

        // Display payment details
        document.getElementById("assessmentId").textContent = assessmentId;
        document.getElementById("paymentId").textContent = paymentId;
        document.getElementById("amount").textContent =
          "NPR " + parseFloat(amount).toFixed(2);

        // Generate a random transaction ID
        const transactionId = "TX" + Date.now().toString();
        document.getElementById("transaction-id").textContent = transactionId;

        // Handle pay button click
        document
          .getElementById("pay-button")
          .addEventListener("click", function () {
            // Simulate payment processing
            document.getElementById("pay-button").disabled = true;
            document.getElementById("pay-button").textContent = "Processing...";

            setTimeout(function () {
              // Call the callback API
              fetch("/api/payments/callback/mock", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  internalPaymentId: paymentId,
                  assessmentId: assessmentId,
                  status: "Success",
                  transactionId: transactionId,
                }),
              })
                .then((response) => {
                  if (response.ok) {
                    // Show success receipt
                    document.getElementById("payment-form").style.display =
                      "none";
                    document.getElementById("success-receipt").style.display =
                      "block";
                  } else {
                    // Show error receipt
                    document.getElementById("payment-form").style.display =
                      "none";
                    document.getElementById("error-receipt").style.display =
                      "block";
                  }
                })
                .catch((error) => {
                  console.error("Error:", error);
                  // Show error receipt
                  document.getElementById("payment-form").style.display =
                    "none";
                  document.getElementById("error-receipt").style.display =
                    "block";
                });
            }, 2000); // Simulate 2-second processing time
          });

        // Handle cancel button click
        document
          .getElementById("cancel-button")
          .addEventListener("click", function () {
            // Call the callback API with failure status
            fetch("/api/payments/callback/mock", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                internalPaymentId: paymentId,
                assessmentId: assessmentId,
                status: "Failed",
                transactionId: transactionId,
              }),
            })
              .then(() => {
                // Redirect back to the main application
                window.location.href = "/";
              })
              .catch((error) => {
                console.error("Error:", error);
                window.location.href = "/";
              });
          });

        // Handle close button click
        document
          .getElementById("close-button")
          .addEventListener("click", function () {
            // Redirect back to the main application
            window.location.href = "/";
          });

        // Handle retry button click
        document
          .getElementById("retry-button")
          .addEventListener("click", function () {
            // Show payment form again
            document.getElementById("payment-form").style.display = "block";
            document.getElementById("error-receipt").style.display = "none";
            document.getElementById("pay-button").disabled = false;
            document.getElementById("pay-button").textContent = "Pay Now";
          });
      });
    </script>
  </body>
</html>
