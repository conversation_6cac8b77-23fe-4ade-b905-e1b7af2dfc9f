using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Core.Models;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LandTaxSystem.Infrastructure.Services
{
    /// <summary>
    /// Service for calculating tax on properties based on fiscal year rules
    /// </summary>
    public class TaxCalculationService : ITaxCalculationService
    {
        private readonly ApplicationDbContext _context;
        private readonly GisService _gisService;
        private readonly TaxConfigResolver _taxConfigResolver;
        private readonly ILogger<TaxCalculationService> _logger;

        public TaxCalculationService(
            ApplicationDbContext context,
            GisService gisService,
            TaxConfigResolver taxConfigResolver,
            ILogger<TaxCalculationService> logger)
        {
            _context = context;
            _gisService = gisService;
            _taxConfigResolver = taxConfigResolver;
            _logger = logger;
        }

        public async Task<TaxCalculationResult> CalculateTaxAsync(Property property, Guid fiscalYearId)
        {
            try
            {
                _logger.LogInformation("Starting tax calculation for Property {PropertyId} and FiscalYear {FiscalYearId}", 
                    property.PropertyId, fiscalYearId);

                var result = new TaxCalculationResult
                {
                    PropertyId = property.PropertyId,
                    FiscalYearId = fiscalYearId,
                    IsSuccessful = false
                };

                // Validate property status
                if (property.Status != "Approved")
                {
                    result.ErrorMessage = "Tax can only be calculated for approved properties";
                    _logger.LogWarning("Tax calculation failed: Property {PropertyId} status is {Status}, not Approved", 
                        property.PropertyId, property.Status);
                    return result;
                }

                // Get fiscal year
                var fiscalYear = await _context.FiscalYears
                    .FirstOrDefaultAsync(fy => fy.FiscalYearId == fiscalYearId);

                if (fiscalYear == null)
                {
                    result.ErrorMessage = $"Fiscal year {fiscalYearId} not found";
                    _logger.LogWarning("Tax calculation failed: Fiscal year {FiscalYearId} not found", fiscalYearId);
                    return result;
                }

                // Get tax configuration (officer-configured or municipality defaults)
                var taxConfig = await _taxConfigResolver.GetActiveTaxConfigAsync(property.MunicipalityId);

                ValuationRulesConfig valuationRules;
                List<LegacyTaxSlab> taxSlabs;
                ExemptionRulesConfig exemptionRules;

                if (taxConfig == null)
                {
                    // Fallback to municipality defaults
                    _logger.LogInformation("Using municipality default tax configuration for Property {PropertyId}", property.PropertyId);
                    
                    valuationRules = ParseValuationRules(property.Municipality?.ValuationRulesConfigJson);
                    taxSlabs = ParseTaxSlabs(property.Municipality?.TaxSlabsConfigJson);
                    exemptionRules = ParseExemptionRules(property.Municipality?.ExemptionRulesConfigJson);
                }
                else
                {
                    // Use officer-configured tax settings with municipality fallbacks
                    _logger.LogInformation("Using officer-configured tax settings for Property {PropertyId}", property.PropertyId);
                    
                    valuationRules = ParseValuationRules(
                        taxConfig.ValuationRulesConfigJson ?? property.Municipality?.ValuationRulesConfigJson);
                    taxSlabs = ParseTaxSlabs(
                        taxConfig.TaxSlabsConfigJson ?? property.Municipality?.TaxSlabsConfigJson);
                    exemptionRules = ParseExemptionRules(
                        taxConfig.ExemptionRulesConfigJson ?? property.Municipality?.ExemptionRulesConfigJson);
                }

                // Store the rules used in calculation
                result.ValuationRulesUsed = valuationRules;
                result.TaxSlabsUsed = taxSlabs;
                result.ExemptionRulesUsed = exemptionRules;

                // Calculate property value
                var propertyValue = _gisService.CalculatePropertyValue(property, valuationRules);
                result.PropertyValue = propertyValue;

                // Build property value breakdown
                result.ValueBreakdown = BuildPropertyValueBreakdown(property, valuationRules, propertyValue);

                // Calculate tax amount
                var taxAmount = _gisService.CalculateTaxAmount(propertyValue, taxSlabs, exemptionRules);
                result.TaxAmount = taxAmount;

                // Build tax calculation breakdown
                result.TaxBreakdown = BuildTaxCalculationBreakdown(propertyValue, taxSlabs, exemptionRules, taxAmount);

                result.IsSuccessful = true;
                _logger.LogInformation("Tax calculation completed successfully for Property {PropertyId}: Value=NPR {PropertyValue:N2}, Tax=NPR {TaxAmount:N2}", 
                    property.PropertyId, propertyValue, taxAmount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating tax for Property {PropertyId} and FiscalYear {FiscalYearId}", 
                    property.PropertyId, fiscalYearId);

                return new TaxCalculationResult
                {
                    PropertyId = property.PropertyId,
                    FiscalYearId = fiscalYearId,
                    IsSuccessful = false,
                    ErrorMessage = $"Tax calculation failed: {ex.Message}"
                };
            }
        }

        public async Task<TaxCalculationResult> CalculateTaxAsync(Property property)
        {
            // Get the current active fiscal year
            var activeFiscalYear = await _context.FiscalYears
                .Where(fy => fy.IsActive)
                .OrderByDescending(fy => fy.StartDate)
                .FirstOrDefaultAsync();

            if (activeFiscalYear == null)
            {
                _logger.LogWarning("No active fiscal year found for tax calculation");
                return new TaxCalculationResult
                {
                    PropertyId = property.PropertyId,
                    IsSuccessful = false,
                    ErrorMessage = "No active fiscal year found"
                };
            }

            return await CalculateTaxAsync(property, activeFiscalYear.FiscalYearId);
        }

        public async Task<TaxCalculationResult> CalculateTaxAsync(Guid propertyId, Guid fiscalYearId)
        {
            var property = await _context.Properties
                .Include(p => p.Municipality)
                .FirstOrDefaultAsync(p => p.PropertyId == propertyId);

            if (property == null)
            {
                _logger.LogWarning("Property {PropertyId} not found for tax calculation", propertyId);
                return new TaxCalculationResult
                {
                    PropertyId = propertyId,
                    FiscalYearId = fiscalYearId,
                    IsSuccessful = false,
                    ErrorMessage = "Property not found"
                };
            }

            return await CalculateTaxAsync(property, fiscalYearId);
        }

        private ValuationRulesConfig ParseValuationRules(string? json)
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return new ValuationRulesConfig();
            }

            try
            {
                return JsonSerializer.Deserialize<ValuationRulesConfig>(json) ?? new ValuationRulesConfig();
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Failed to parse valuation rules JSON: {Json}", json);
                return new ValuationRulesConfig();
            }
        }

        private List<LegacyTaxSlab> ParseTaxSlabs(string? json)
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return new List<LegacyTaxSlab>();
            }

            try
            {
                return JsonSerializer.Deserialize<List<LegacyTaxSlab>>(json) ?? new List<LegacyTaxSlab>();
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Failed to parse tax slabs JSON: {Json}", json);
                return new List<LegacyTaxSlab>();
            }
        }

        private ExemptionRulesConfig ParseExemptionRules(string? json)
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return new ExemptionRulesConfig { Rules = new List<ExemptionRule>() };
            }

            try
            {
                return JsonSerializer.Deserialize<ExemptionRulesConfig>(json) ?? 
                       new ExemptionRulesConfig { Rules = new List<ExemptionRule>() };
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Failed to parse exemption rules JSON: {Json}", json);
                return new ExemptionRulesConfig { Rules = new List<ExemptionRule>() };
            }
        }

        private PropertyValueBreakdown BuildPropertyValueBreakdown(Property property, ValuationRulesConfig valuationRules, decimal totalValue)
        {
            var breakdown = new PropertyValueBreakdown
            {
                LandAreaSqM = property.LandAreaSqM,
                TotalValue = totalValue
            };

            // Calculate land value details
            if (valuationRules.LandMVR.TryGetValue(property.UsageType, out var mvrRate))
            {
                breakdown.LandMVRPerSqM = mvrRate;
                breakdown.LandValue = property.LandAreaSqM * mvrRate;
            }
            else
            {
                breakdown.LandMVRPerSqM = 1000m; // Default rate
                breakdown.LandValue = property.LandAreaSqM * 1000m;
            }

            // Calculate building value details if applicable
            if (property.BuildingBuiltUpAreaSqM.HasValue && 
                property.BuildingConstructionType != null && 
                property.BuildingConstructionYear.HasValue)
            {
                breakdown.BuildingAreaSqM = property.BuildingBuiltUpAreaSqM.Value;
                breakdown.BuildingAgeYears = DateTime.Now.Year - property.BuildingConstructionYear.Value;

                if (valuationRules.BuildingBaseRatePerSqm.TryGetValue(property.BuildingConstructionType, out var baseRate))
                {
                    breakdown.BuildingBaseRatePerSqM = baseRate;
                }
                else
                {
                    breakdown.BuildingBaseRatePerSqM = 5000m; // Default rate
                }

                breakdown.DepreciationFactor = Math.Min(breakdown.BuildingAgeYears.Value * valuationRules.AnnualDepreciationRate, 0.6m);
                breakdown.BuildingValue = totalValue - breakdown.LandValue;
            }

            return breakdown;
        }

        private TaxCalculationBreakdown BuildTaxCalculationBreakdown(decimal assessedValue, List<LegacyTaxSlab> taxSlabs, ExemptionRulesConfig exemptionRules, decimal finalTaxAmount)
        {
            var breakdown = new TaxCalculationBreakdown
            {
                AssessedValue = assessedValue,
                NetTaxAmount = finalTaxAmount
            };

            // Find applicable tax slab
            var applicableSlab = taxSlabs
                .Where(slab => assessedValue >= slab.MinAssessedValue &&
                              (slab.MaxAssessedValue == 0 || assessedValue <= slab.MaxAssessedValue))
                .OrderBy(slab => slab.MinAssessedValue)
                .FirstOrDefault();

            if (applicableSlab != null)
            {
                breakdown.ApplicableTaxSlab = applicableSlab;
                breakdown.FixedAmount = applicableSlab.FixedAmount;
                breakdown.VariableAmount = (assessedValue - applicableSlab.MinAssessedValue) * (applicableSlab.RatePercent / 100);
                breakdown.GrossTaxAmount = breakdown.FixedAmount + breakdown.VariableAmount;
            }
            else
            {
                // Default 1% tax if no slab found
                breakdown.GrossTaxAmount = assessedValue * 0.01m;
                breakdown.VariableAmount = breakdown.GrossTaxAmount;
            }

            // Calculate exemption amount (for now, exemptions are not fully implemented in GisService)
            breakdown.ExemptionAmount = breakdown.GrossTaxAmount - finalTaxAmount;

            return breakdown;
        }
    }
}
