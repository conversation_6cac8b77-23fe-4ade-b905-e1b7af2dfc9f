using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Rebate
{
    public class RebateItemDto
    {
        public int Id { get; set; }

        [Required]
        public string? FiscalYear { get; set; }

        [Required]
        public string? FilingPeriod { get; set; }

        [Required]
        public string? TaxPeriod { get; set; }

        [Required]
        public decimal TotalExemptedAmount { get; set; }

        [Required]
        public decimal DiscountAmount { get; set; }
    }
}