using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Mail;
using System.Net;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Core.Models;
using Microsoft.Extensions.Options;

namespace LandTaxSystem.Infrastructure.Services
{
    public interface ILegacyEmailService
    {
        Task SendEmailAsync(string to, string subject, string htmlContent);
        Task SendPaymentNotificationToOfficer(Payment payment, string officerEmail, string municipalityName);
        Task SendAppealNotificationToOfficer(Appeal appeal, string officerEmail, string municipalityName);
        Task SendNegotiationNotificationToTaxPayer(Negotiation negotiation, string taxpayerEmail);
    }

    public class EmailService : IEmailService, ILegacyEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly EmailSettings _emailSettings;
        private readonly bool _enableActualEmailSending;

        public EmailService(ILogger<EmailService> logger, IOptions<EmailSettings> emailSettings, IConfiguration configuration)
        {
            _logger = logger;
            _emailSettings = emailSettings.Value;
            _enableActualEmailSending = configuration.GetValue<bool>("EmailSettings:EnableActualSending", false);
        }

        public async Task SendEmailAsync(string to, string subject, string htmlContent)
        {
            try
            {
                if (_enableActualEmailSending)
                {
                    using var client = new SmtpClient(_emailSettings.SmtpHost, _emailSettings.SmtpPort)
                    {
                        Credentials = new NetworkCredential(_emailSettings.Username, _emailSettings.Password),
                        EnableSsl = _emailSettings.EnableSsl
                    };

                    var mailMessage = new MailMessage
                    {
                        From = new MailAddress(_emailSettings.From, "Land Tax System"),
                        Subject = subject,
                        Body = htmlContent,
                        IsBodyHtml = true
                    };

                    mailMessage.To.Add(to);
                    await client.SendMailAsync(mailMessage);
                }
                else
                {
                    // For development/testing, just log the email
                    _logger.LogInformation($"Email would be sent to: {to}");
                    _logger.LogInformation($"Subject: {subject}");
                    _logger.LogInformation($"Content: {htmlContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending email to {to}: {ex.Message}");
                // Don't throw - we don't want email failures to break the application flow
            }
        }

        // Implementation of IEmailService interface methods
        public async Task SendRegistrationConfirmationAsync(string email, string fullName, Guid userId, string panNumber = null)
        {
            var subject = "Welcome to Land Tax System - Registration Confirmed";
            var htmlContent = "<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">" +
                "<div style=\"color: #1a365d; border-bottom: 2px solid #e2e8f0; padding-bottom: 10px; margin-bottom: 20px;\">" +
                "<h2>Welcome to Land Tax System!</h2></div>" +
                "<div style=\"margin-bottom: 20px;\">" +
                "<p>Dear " + fullName + ",</p>" +
                "<p>Thank you for registering with the Land Tax System. Your account has been successfully created and is pending approval.</p>" +
                "<h3>Account Details:</h3>" +
                "<ul style=\"padding-left: 20px;\">" +
                "<li style=\"margin-bottom: 8px;\"><strong>User ID:</strong> " + userId + "</li>" +
                "<li style=\"margin-bottom: 8px;\"><strong>Email:</strong> " + email + "</li>" +
                (string.IsNullOrEmpty(panNumber) ? "" : "<li style=\"margin-bottom: 8px;\"><strong>PAN Number:</strong> " + panNumber + "</li>") +
                "<li style=\"margin-bottom: 8px;\"><strong>Registration Date:</strong> " + DateTime.Now.ToString("yyyy-MM-dd HH:mm") + "</li>" +
                "</ul>" +
                "<p>Once your account is approved by an administrator, you will receive a confirmation email with further instructions on how to access the system.</p>" +
                "<p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>" +
                "</div>" +
                "<div style=\"margin-top: 30px; font-size: 0.9em; color: #718096; border-top: 1px solid #e2e8f0; padding-top: 10px;\">" +
                "<p>Best regards,<br>Land Tax System Team</p>" +
                "</div></div>";

            await SendEmailAsync(email, subject, htmlContent);
        }

        public async Task SendPropertySubmissionAsync(string email, string fullName, Guid propertyId)
        {
            var subject = "Property Submission Confirmation - Land Tax System";
            var htmlContent = $@"
                <h2>Property Submission Confirmed</h2>
                <p>Dear {fullName},</p>
                <p>Your property has been successfully submitted to the Land Tax System.</p>
                <h3>Submission Details:</h3>
                <ul>
                    <li><strong>Property ID:</strong> {propertyId}</li>
                    <li><strong>Submission Date:</strong> {DateTime.Now:yyyy-MM-dd HH:mm}</li>
                    <li><strong>Status:</strong> Pending Review</li>
                </ul>
                <p>Your property submission is now under review by the municipal officers. You will receive another notification once the assessment is completed.</p>
                <p>You can track the status of your property in your dashboard.</p>
                <p>Thank you for using our service.</p>
                <p>Best regards,<br/>Land Tax System Team</p>
            ";

            await SendEmailAsync(email, subject, htmlContent);
        }

        public async Task SendAssessmentNotificationAsync(string email, string fullName, Guid assessmentId)
        {
            var subject = "Assessment Notification - Land Tax System";
            var htmlContent = $@"
                <h2>Property Assessment Completed</h2>
                <p>Dear {fullName},</p>
                <p>Your property assessment has been completed by our municipal officers.</p>
                <h3>Assessment Details:</h3>
                <ul>
                    <li><strong>Assessment ID:</strong> {assessmentId}</li>
                    <li><strong>Assessment Date:</strong> {DateTime.Now:yyyy-MM-dd HH:mm}</li>
                    <li><strong>Status:</strong> Completed</li>
                </ul>
                <p>Please log in to your account to view the detailed assessment results and any applicable tax calculations.</p>
                <p>If you have any questions about your assessment, you can contact the municipal office or submit an appeal through the system.</p>
                <p>Thank you for your cooperation.</p>
                <p>Best regards,<br/>Land Tax System Team</p>
            ";

            await SendEmailAsync(email, subject, htmlContent);
        }

        public async Task SendPaymentNotificationToOfficer(Payment payment, string officerEmail, string municipalityName)
        {
            var subject = $"Provisional Payment Received - {municipalityName}";
            var htmlContent = $@"
                <h2>Provisional Payment Notification</h2>
                <p>A provisional payment has been received for a property in {municipalityName}.</p>
                <h3>Payment Details:</h3>
                <ul>
                    <li><strong>Property ID:</strong> {payment.PropertyId}</li>
                    <li><strong>Amount Paid:</strong> NPR {payment.AmountPaid:N2}</li>
                    <li><strong>Payment Date:</strong> {payment.PaymentDate}</li>
                    <li><strong>Transaction ID:</strong> {payment.TransactionId}</li>
                </ul>
                <p>Please create an assessment for this property at your earliest convenience.</p>
                <p>This is an automated message from the Land Tax System.</p>
            ";

            await SendEmailAsync(officerEmail, subject, htmlContent);
        }

        public async Task SendAppealNotificationToOfficer(Appeal appeal, string officerEmail, string municipalityName)
        {
            var subject = $"New Tax Appeal - {municipalityName}";
            var htmlContent = $@"
                <h2>New Tax Appeal Notification</h2>
                <p>A new tax appeal has been submitted for a property in {municipalityName}.</p>
                <h3>Appeal Details:</h3>
                <ul>
                    <li><strong>Appeal ID:</strong> {appeal.AppealId}</li>
                    <li><strong>Assessment ID:</strong> {appeal.AssessmentId}</li>
                    <li><strong>Reason:</strong> {appeal.Reason}</li>
                    <li><strong>Submitted At:</strong> {appeal.SubmittedAt}</li>
                </ul>
                <p>Please review this appeal at your earliest convenience.</p>
                <p>This is an automated message from the Land Tax System.</p>
            ";

            await SendEmailAsync(officerEmail, subject, htmlContent);
        }

        public async Task SendNegotiationNotificationToTaxPayer(Negotiation negotiation, string taxpayerEmail)
        {
            var subject = "Your Tax Appeal Has Been Resolved";
            
            // Format the tax periods if they exist
            string taxPeriodsHtml = "";
            if (negotiation.TaxPeriods != null && negotiation.TaxPeriods.Any())
            {
                taxPeriodsHtml = "<h4>Tax Periods:</h4><ul>";
                foreach (var period in negotiation.TaxPeriods)
                {
                    // Use new fields if available, fall back to legacy fields if not
                    string fiscalYear = !string.IsNullOrEmpty(period.FiscalYear) ? period.FiscalYear : period.Year ?? "N/A";
                    string dateRange = !string.IsNullOrEmpty(period.StartDate) && !string.IsNullOrEmpty(period.EndDate) 
                        ? $"{period.StartDate} to {period.EndDate}" 
                        : period.TaxPeriodValue ?? "";
                    
                    taxPeriodsHtml += $"<li>Fiscal Year: {fiscalYear} - {dateRange}</li>";
                }
                taxPeriodsHtml += "</ul>";
            }

            var htmlContent = $@"
                <h2>Tax Appeal Resolution Notification</h2>
                <p>Your tax appeal has been reviewed and a decision has been made.</p>
                
                <h3>Taxpayer Information:</h3>
                <ul>
                    <li><strong>Name:</strong> {negotiation.TaxpayerName}</li>
                    <li><strong>PAN Number:</strong> {negotiation.PANNumber}</li>
                    <li><strong>Tax Office:</strong> {negotiation.TaxOfficeAddress}</li>
                </ul>

                <h3>Appeal Decision Details:</h3>
                <ul>
                    <li><strong>Appeal Body:</strong> {negotiation.AppealBody}</li>
                    <li><strong>Decision Number:</strong> {negotiation.DecisionNumber}</li>
                    <li><strong>Decision Date:</strong> {negotiation.DecisionDate:yyyy-MM-dd}</li>
                </ul>

                {taxPeriodsHtml}

                <h3>Original Amounts:</h3>
                <ul>
                    <li><strong>Tax:</strong> NPR {negotiation.OriginalTax:N2}</li>
                    <li><strong>Penalty:</strong> NPR {negotiation.OriginalPenalty:N2}</li>
                    <li><strong>Fee:</strong> NPR {negotiation.OriginalFee:N2}</li>
                    <li><strong>Interest:</strong> NPR {negotiation.OriginalInterest:N2}</li>
                </ul>

                <h3>Decided Amounts:</h3>
                <ul>
                    <li><strong>Tax:</strong> NPR {negotiation.DecidedTax:N2}</li>
                    <li><strong>Penalty:</strong> NPR {negotiation.DecidedPenalty:N2}</li>
                    <li><strong>Fee:</strong> NPR {negotiation.DecidedFee:N2}</li>
                    <li><strong>Interest:</strong> NPR {negotiation.DecidedInterest:N2}</li>
                </ul>

                <p>Your appeal status is: <strong>{negotiation.Status}</strong></p>
                <p>Remarks: {negotiation.Remarks}</p>
                <p>If you have any questions, please contact the tax office.</p>
                <p>Best regards,<br/>Land Tax System Team</p>
            ";

            await SendEmailAsync(taxpayerEmail, subject, htmlContent);
        }

        public async Task SendPropertyVerificationAsync(string email, string fullName, Guid propertyId)
        {
            var subject = "Property Verification Complete - Land Tax System";
            var htmlContent = $@"
                <h2>Property Verification Complete!</h2>
                <p>Dear {fullName},</p>
                <p>Your property with ID <strong>{propertyId}</strong> has been successfully verified and approved by the municipal officers.</p>
                <p>You can now view the updated status of your property in your dashboard.</p>
                <p>If you have any questions, please contact the municipal office.</p>
                <p>Best regards,<br/>Land Tax System Team</p>
            ";

            await SendEmailAsync(email, subject, htmlContent);
        }

        public async Task SendPaymentConfirmationToTaxPayer(Payment payment, string taxpayerEmail)
        {
            var subject = "Tax Payment Confirmation - Land Tax System";
            var htmlContent = $@"
                <h2>Tax Payment Confirmation</h2>
                <p>Dear Taxpayer,</p>
                <p>This email confirms that your tax payment has been successfully processed.</p>
                <h3>Payment Details:</h3>
                <ul>
                    <li><strong>Property ID:</strong> {payment.PropertyId}</li>
                    <li><strong>Amount Paid:</strong> NPR {payment.AmountPaid:N2}</li>
                    <li><strong>Payment Date:</strong> {payment.PaymentDate:yyyy-MM-dd HH:mm}</li>
                    <li><strong>Transaction ID:</strong> {payment.TransactionId}</li>
                    <li><strong>Status:</strong> {payment.Status}</li>
                </ul>
                <p>Thank you for your payment. If you have any questions, please contact the tax office.</p>
                <p>Best regards,<br/>Land Tax System Team</p>
            ";

            await SendEmailAsync(taxpayerEmail, subject, htmlContent);
        }

        public async Task SendUserApprovalNotificationAsync(string email, string fullName, string pan, string username)
        {
            var subject = "Your Land Tax System Account Has Been Approved";
            var htmlContent = $@"
                <h2>Account Approval Notification</h2>
                <p>Dear {fullName},</p>
                <p>Your account with the Land Tax System has been approved by the administrator.</p>
                <p>Below are your account details:</p>
                <ul>
                    <li><strong>Username:</strong> {username}</li>
                    <li><strong>Taxpayer Number (PAN):</strong> {pan}</li>
                </ul>
                <p>You can now log in to the system using the password you set during registration.</p>
                <p>If you have forgotten your password, please use the &quot;Forgot Password&quot; feature on the login page.</p>
                <p>If you have any questions, please contact our support team.</p>
                <p>Best regards,<br/>Land Tax System Team</p>
            ";

            await SendEmailAsync(email, subject, htmlContent);
        }
    }
}
