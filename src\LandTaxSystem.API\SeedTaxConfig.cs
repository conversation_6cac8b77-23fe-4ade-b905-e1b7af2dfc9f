using System;
using System.Linq;
using System.Text.Json;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Models;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace LandTaxSystem.API
{
    public class SeedTaxConfig
    {
        public static void SeedMunicipalityTaxConfig(IServiceProvider serviceProvider, Guid municipalityId)
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Check if the municipality exists
            var municipality = dbContext.Municipalities
                .FirstOrDefault(m => m.MunicipalityId == municipalityId);

            if (municipality == null)
            {
                Console.WriteLine($"Municipality with ID {municipalityId} not found.");
                return;
            }

            // Get the current fiscal year
            var currentFiscalYear = dbContext.FiscalYears
                .OrderByDescending(f => f.StartDate)
                .FirstOrDefault();

            if (currentFiscalYear == null)
            {
                // Create a new fiscal year if none exists
                currentFiscalYear = new FiscalYear
                {
                    FiscalYearId = Guid.NewGuid(),
                    Name = "FY 2025/2026",
                    StartDate = new DateTime(2025, 4, 1),
                    EndDate = new DateTime(2026, 3, 31),
                    IsActive = true
                };
                dbContext.FiscalYears.Add(currentFiscalYear);
                dbContext.SaveChanges();
                Console.WriteLine("Created new fiscal year: " + currentFiscalYear.Name);
            }

            // Check if tax config exists for this municipality and fiscal year
            var existingConfig = dbContext.MunicipalityTaxConfigs
                .FirstOrDefault(c => c.MunicipalityId == municipalityId && c.FiscalYearId == currentFiscalYear.FiscalYearId);

            if (existingConfig != null)
            {
                // Create proper tax slabs JSON
                var taxSlabs = new List<LegacyTaxSlab>
                {
                    new LegacyTaxSlab { MinAssessedValue = 0, MaxAssessedValue = 100000, RatePercent = 1.0m, FixedAmount = 0 },
                    new LegacyTaxSlab { MinAssessedValue = 100000, MaxAssessedValue = 500000, RatePercent = 1.5m, FixedAmount = 1000 },
                    new LegacyTaxSlab { MinAssessedValue = 500000, MaxAssessedValue = 0, RatePercent = 2.0m, FixedAmount = 7000 }
                };
                existingConfig.TaxSlabsConfigJson = JsonSerializer.Serialize(taxSlabs);

                // Create proper valuation rules JSON
                var valuationRules = new ValuationRulesConfig
                {
                    LandMVR = new Dictionary<string, decimal>
                    {
                        { "Residential", 1000 },
                        { "Commercial", 2000 },
                        { "Industrial", 1500 }
                    },
                    BuildingBaseRatePerSqm = new Dictionary<string, decimal>
                    {
                        { "Residential", 2000 },
                        { "RCC", 2500 },
                        { "Commercial", 3000 },
                        { "Industrial", 2500 }
                    },
                    AnnualDepreciationRate = 0.01m
                };
                existingConfig.ValuationRulesConfigJson = JsonSerializer.Serialize(valuationRules);

                // Create proper exemption rules JSON
                var exemptionRules = new ExemptionRulesConfig
                {
                    Rules = new List<ExemptionRule>()
                };
                existingConfig.ExemptionRulesConfigJson = JsonSerializer.Serialize(exemptionRules);

                existingConfig.IsFinalized = true;
            }
            else
            {
                // Create new config
                Console.WriteLine($"Creating new tax config for {municipality.Name}");

                // Create proper tax slabs JSON
                var taxSlabs = new List<LegacyTaxSlab>
                {
                    new LegacyTaxSlab { MinAssessedValue = 0, MaxAssessedValue = 100000, RatePercent = 1.0m, FixedAmount = 0 },
                    new LegacyTaxSlab { MinAssessedValue = 100000, MaxAssessedValue = 500000, RatePercent = 1.5m, FixedAmount = 1000 },
                    new LegacyTaxSlab { MinAssessedValue = 500000, MaxAssessedValue = 0, RatePercent = 2.0m, FixedAmount = 7000 }
                };

                // Create proper valuation rules JSON
                var valuationRules = new ValuationRulesConfig
                {
                    LandMVR = new Dictionary<string, decimal>
                    {
                        { "Residential", 1000 },
                        { "Commercial", 2000 },
                        { "Industrial", 1500 }
                    },
                    BuildingBaseRatePerSqm = new Dictionary<string, decimal>
                    {
                        { "Residential", 2000 },
                        { "RCC", 2500 },
                        { "Commercial", 3000 },
                        { "Industrial", 2500 }
                    },
                    AnnualDepreciationRate = 0.01m
                };

                // Create proper exemption rules JSON
                var exemptionRules = new ExemptionRulesConfig
                {
                    Rules = new List<ExemptionRule>()
                };

                var newConfig = new MunicipalityTaxConfig
                {
                    MunicipalityTaxConfigId = Guid.NewGuid(),
                    MunicipalityId = municipalityId,
                    FiscalYearId = currentFiscalYear.FiscalYearId,
                    TaxSlabsConfigJson = JsonSerializer.Serialize(taxSlabs),
                    ValuationRulesConfigJson = JsonSerializer.Serialize(valuationRules),
                    ExemptionRulesConfigJson = JsonSerializer.Serialize(exemptionRules),
                    IsFinalized = true,
                    CreatedAt = DateTime.UtcNow
                };

                dbContext.MunicipalityTaxConfigs.Add(newConfig);
            }

            dbContext.SaveChanges();
        }
    }
}
