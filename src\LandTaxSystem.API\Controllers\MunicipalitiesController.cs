using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Municipality;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Models;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "RequireCentralAdminRole")]
    public class MunicipalitiesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public MunicipalitiesController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<MunicipalityResponseDto>>> GetMunicipalities()
        {
            var municipalities = await _context.Municipalities
                .Include(m => m.District)
                .ThenInclude(d => d.Province)
                .ToListAsync();

            return Ok(municipalities.Select(m => MapToResponseDto(m)).ToList());
        }

        [HttpGet("{id}")]
        [AllowAnonymous]
        [Authorize(Roles = "CentralAdmin,Officer")]
        public async Task<ActionResult<MunicipalityResponseDto>> GetMunicipality(Guid id)
        {
            var municipality = await _context.Municipalities
                .Include(m => m.District)
                .ThenInclude(d => d.Province)
                .FirstOrDefaultAsync(m => m.MunicipalityId == id);

            if (municipality == null)
            {
                return NotFound();
            }

            return MapToResponseDto(municipality);
        }

        [HttpPost]
        public async Task<ActionResult<MunicipalityResponseDto>> CreateMunicipality(MunicipalityCreateDto createDto)
        {
            // Check if municipality with the same name already exists
            if (await _context.Municipalities.AnyAsync(m => m.Name == createDto.Name))
            {
                return BadRequest("A municipality with this name already exists");
            }
            
            // Verify the district exists
            var district = await _context.Districts
                .Include(d => d.Province)
                .FirstOrDefaultAsync(d => d.DistrictId == createDto.DistrictId);
                
            if (district == null)
            {
                return BadRequest("The specified district does not exist");
            }

            var municipality = new Municipality
            {
                MunicipalityId = Guid.NewGuid(),
                Name = createDto.Name,
                DistrictId = createDto.DistrictId,
                WardCount = createDto.WardCount,
                ValuationRulesConfigJson = JsonSerializer.Serialize(createDto.ValuationRulesConfig ?? new ValuationRulesConfig()),
                TaxSlabsConfigJson = JsonSerializer.Serialize(createDto.TaxSlabsConfig ?? new List<TaxSlab>()),
                ExemptionRulesConfigJson = JsonSerializer.Serialize(createDto.ExemptionRulesConfig ?? new ExemptionRulesConfig()),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Municipalities.Add(municipality);
            await _context.SaveChangesAsync();
            
            // Reload the municipality with district and province data for the response
            await _context.Entry(municipality).Reference(m => m.District).LoadAsync();
            await _context.Entry(municipality.District).Reference(d => d.Province).LoadAsync();

            return CreatedAtAction(
                nameof(GetMunicipality),
                new { id = municipality.MunicipalityId },
                MapToResponseDto(municipality));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateMunicipality(Guid id, MunicipalityUpdateDto updateDto)
        {
            var municipality = await _context.Municipalities.FindAsync(id);

            if (municipality == null)
            {
                return NotFound();
            }

            // Update properties if provided
            if (!string.IsNullOrEmpty(updateDto.Name))
            {
                // Check for name collision, but only if the name is actually changing
                if (municipality.Name != updateDto.Name &&
                    await _context.Municipalities.AnyAsync(m => m.Name == updateDto.Name))
                {
                    return BadRequest("A municipality with this name already exists");
                }

                municipality.Name = updateDto.Name;
            }

            if (updateDto.ValuationRulesConfig != null)
            {
                municipality.ValuationRulesConfigJson = JsonSerializer.Serialize(updateDto.ValuationRulesConfig);
            }

            if (updateDto.TaxSlabsConfig != null)
            {
                municipality.TaxSlabsConfigJson = JsonSerializer.Serialize(updateDto.TaxSlabsConfig);
            }

            if (updateDto.ExemptionRulesConfig != null)
            {
                municipality.ExemptionRulesConfigJson = JsonSerializer.Serialize(updateDto.ExemptionRulesConfig);
            }

            municipality.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await MunicipalityExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return Ok(MapToResponseDto(municipality));
        }

        [HttpGet("public")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetPublicMunicipalities()
        {
            try
            {
                // Join with District and Province tables to get the full location hierarchy
                var municipalities = await _context.Municipalities
                    .Include(m => m.District)
                    .ThenInclude(d => d.Province)
                    .ToListAsync();

                // Return location hierarchy information for public access with null checks
                var publicMunicipalities = municipalities.Select(m => new
                {
                    MunicipalityId = m.MunicipalityId,
                    Name = m.Name,
                    Province = m.District?.Province?.Name ?? "Unknown Province",
                    District = m.District?.Name ?? "Unknown District",
                    DistrictId = m.DistrictId,
                    ProvinceId = m.District?.ProvinceId ?? Guid.Empty
                }).ToList();

                return Ok(publicMunicipalities);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving municipalities", error = ex.Message });
            }
        }

        [HttpGet("list")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetMunicipalitiesList()
        {
            try
            {
                var municipalities = await _context.Municipalities
                    .Select(m => new
                    {
                        Id = m.MunicipalityId,
                        Name = m.Name
                    })
                    .OrderBy(m => m.Name)
                    .ToListAsync();

                return Ok(municipalities);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving municipalities list", error = ex.Message });
            }
        }

        private async Task<bool> MunicipalityExists(Guid id)
        {
            return await _context.Municipalities.AnyAsync(e => e.MunicipalityId == id);
        }

        [HttpPut("{id}/tax-defaults")]
        [Authorize(Roles = "Admin")] // Ensure only Admins can set these defaults
        public async Task<IActionResult> UpdateMunicipalityTaxDefaults(Guid id, MunicipalityTaxDefaultsUpdateDto defaultsDto)
        {
            var municipality = await _context.Municipalities.FindAsync(id);

            if (municipality == null)
            {
                return NotFound();
            }

            municipality.DefaultPenaltyPercent = defaultsDto.DefaultPenaltyPercent;
            municipality.DefaultDiscountPercent = defaultsDto.DefaultDiscountPercent;
            municipality.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await MunicipalityExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return Ok(MapToResponseDto(municipality)); // Return the updated municipality details
        }

        private MunicipalityResponseDto MapToResponseDto(Municipality municipality)
        {
            return new MunicipalityResponseDto
            {
                MunicipalityId = municipality.MunicipalityId,
                Name = municipality.Name,
                DistrictId = municipality.DistrictId,
                DistrictName = municipality.District?.Name ?? string.Empty,
                ProvinceId = municipality.District?.ProvinceId ?? Guid.Empty,
                ProvinceName = municipality.District?.Province?.Name ?? string.Empty,
                WardCount = municipality.WardCount,
                ValuationRulesConfig = JsonSerializer.Deserialize<ValuationRulesConfig>(municipality.ValuationRulesConfigJson) ?? new ValuationRulesConfig(),
                TaxSlabsConfig = JsonSerializer.Deserialize<List<TaxSlab>>(municipality.TaxSlabsConfigJson) ?? new List<TaxSlab>(),
                ExemptionRulesConfig = JsonSerializer.Deserialize<ExemptionRulesConfig>(municipality.ExemptionRulesConfigJson) ?? new ExemptionRulesConfig(),
                CreatedAt = municipality.CreatedAt,
                UpdatedAt = municipality.UpdatedAt,
                DefaultPenaltyPercent = municipality.DefaultPenaltyPercent,
                DefaultDiscountPercent = municipality.DefaultDiscountPercent
            };
        }
    }
}
