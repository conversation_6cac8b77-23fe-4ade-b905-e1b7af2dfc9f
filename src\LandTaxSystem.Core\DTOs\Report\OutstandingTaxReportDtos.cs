using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Report
{
    public class OutstandingTaxReportRequestDto
    {
        public Guid? MunicipalityId { get; set; }
        public string? Ward { get; set; }
        public string? FiscalYear { get; set; }
        public string? TaxpayerNumber { get; set; }
    }

    public class OutstandingTaxRecordDto
    {
        public string TaxpayerNumber { get; set; } = string.Empty;
        public string TaxpayerName { get; set; } = string.Empty;
        public string TaxpayerAddress { get; set; } = string.Empty;
        public string TaxpayerContacts { get; set; } = string.Empty;
        public string FiscalYear { get; set; } = string.Empty;
        public string Ward { get; set; } = string.Empty;
        public string ParcelNumber { get; set; } = string.Empty;
        public decimal OutstandingDue { get; set; }
    }

    public class OutstandingTaxReportResponseDto
    {
        public List<OutstandingTaxRecordDto> Records { get; set; } = new List<OutstandingTaxRecordDto>();
        public int TotalCount { get; set; }
    }

    public class MunicipalityListItemDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }
}