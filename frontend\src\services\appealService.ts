import api from './api';

export interface AppealDto {
  appealId: string;
  assessmentId: string;
  taxPayerId: string;
  taxPayerName: string;
  reason: string;
  submittedAt: string;
  status: string;
  assessmentAmount: number;
  propertyAddress: string;
  responseMessage?: string;
  resolvedAt?: string;
}

export interface CreateAppealDto {
  assessmentId: string;
  reason: string;
}

export const appealService = {
  // Get appeals for the current user
  getUserAppeals: async (): Promise<AppealDto[]> => {
    // The backend controller automatically filters by current user for citizens
    const response = await api.get('/Appeals');
    return response.data;
  },

  // Get all appeals (for officers)
  getAllAppeals: async (): Promise<AppealDto[]> => {
    const response = await api.get('/Appeals');
    return response.data;
  },

  // Get a specific appeal by ID
  getAppeal: async (appealId: string): Promise<AppealDto> => {
    const response = await api.get(`/Appeals/${appealId}`);
    return response.data;
  },

  // Create a new appeal
  createAppeal: async (createAppealDto: CreateAppealDto): Promise<AppealDto> => {
    const response = await api.post('/Appeals', createAppealDto);
    return response.data;
  },

  // Respond to an appeal (for officers)
  respondToAppeal: async (
    appealId: string,
    responseMessage: string,
    status: string
  ): Promise<AppealDto> => {
    const response = await api.put(`/Appeals/${appealId}/respond`, { responseMessage, status });
    return response.data;
  }
};
