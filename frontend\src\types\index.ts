export interface User {
  // Basic Information
  id: string;
  email: string;
  firstName: string;
  middleName: string;
  lastName: string;
  fullName: string;
  phoneNumber: string;
  telephone: string;
  role: "Citizen" | "Officer" | "CentralAdmin";
  municipalityId: string;
  municipalityName: string;
  wardNumber: string;
  isActive: boolean;
  status: "Pending" | "Active" | "Rejected" | "Inactive" | "PendingApproval";
  submissionNumber: string;

  // Personal Information
  dateOfBirth: string;
  gender: string;
  nationality: string;
  citizenshipNumber: string;
  twoGenerations: string;
  profession: string;

  // Family Information
  fatherName: string;
  motherName: string;
  grandfatherName: string;
  grandmotherName: string;
  maritalStatus: string;
  spouseName: string;
  isMinor: boolean;
  guardianName: string;
  guardianRelation: string;

  // Address Information
  permanentAddress: string;
  temporaryAddress: string;
  toleStreet: string;
  district: string;
  province: string;
  houseNumber: string;

  // Temporary Address
  tempWardNumber: string;
  tempToleStreet: string;
  tempDistrict: string;
  tempMunicipality: string;
  tempHouseNumber: string;
  tempProvince: string;

  // Document Information
  documentPath: string;
  pan: string;
  panIssueDistrict: string;
  panIssueDate: string;
  nationalIdNumber?: string;
  nationalIdIssueOffice: string;
  nationalIdIssueDistrict?: string;
  nationalIdIssueDate?: string;
  nationalIdDocumentPath?: string;
  panDocumentPath?: string;
  citizenshipIssueDate: string;
  citizenshipIssueDistrict: string;
  citizenshipIssueOffice?: string;
  otherDocuments?: Array<{
    id: string;
    documentType: string;
    path: string;
    uploadedAt: string;
  }>;

  // System Information
  temporaryPassword: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: string;

  // Contact Preferences
  contactPreferences?: string;
  alternatePhoneNumber?: string;
  preferEmail?: boolean;
}

export interface AuthResponse {
  token: string;
  userId: string;
  role: string;
}

export interface Province {
  provinceId: string;
  name: string;
  code: string;
}

export interface District {
  districtId: string;
  name: string;
  code: string;
  provinceId: string;
}

export interface Municipality {
  municipalityId: string;
  name: string;
  province: string;
  district: string;
  wardCount: number;
  valuationRulesConfig: Record<string, unknown>;
  taxSlabsConfig: Record<string, unknown>[];
  exemptionRulesConfig: Record<string, unknown>;
  defaultPenaltyPercent?: number;
  defaultDiscountPercent?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Property {
  id: string;
  ownerUserId: string;
  municipalityId: string;
  municipalityName?: string; // For display purposes

  // Location hierarchy fields
  province?: string;
  district?: string;
  wardNumber: number;
  street: string; // Street/Tole
  parcelNumber: string; // Kitta No

  parcelGeoJson: string | {
    type: string;
    coordinates: number[][][];
  };
  address: string;
  landAreaSqM: number;
  landArea?: number; // Alias for display
  usageType: "Residential" | "Commercial" | "Industrial" | "Agricultural";
  buildingBuiltUpAreaSqM?: number;
  builtUpArea?: number; // Alias for display
  buildingConstructionType?: "RCC" | "BrickMud" | "Other";
  constructionType?: string; // Alias for display
  buildingConstructionYear?: number;
  constructionYear?: number; // Alias for display
  ownershipCertificatePath?: string;
  buildingPermitPath?: string;
  ownershipType?: string; // Land ownership type
  registrationDate: string;
  status: "PendingReview" | "Approved" | "Rejected" | "Assessed";
  rejectionReason?: string;
  isDefaulter: boolean;
  assessedValue?: number; // Current assessed value
  taxDue?: number; // Outstanding tax amount
  taxPaid?: number; // Total tax paid to date
  estimatedTax?: number; // Estimated tax amount for provisional payments
  landDetails?: LandDetails; // Enhanced property details
  applicableFiscalYearId?: string; // ID of the applicable fiscal year
  applicableFiscalYearName?: string; // Name of the applicable fiscal year for display
}

export interface LandDetails {
  // General Information
  oldVdc?: string;
  oldWardNo?: string;
  currentWardNo?: string;
  kittaNo?: string;
  mapNo?: string;
  fiscalYear?: string;
  otherDetails?: string;

  // Status Flags
  isMultiRateValuation: boolean;
  isTemporaryHouse: boolean;
  isCultivable: boolean;

  // Measurement Units
  measurementUnit?: string;

  // Area Measurements
  areaBigha?: number;
  areaKattha?: number;
  areaDhur?: number;
  areaRopani?: number;
  areaAana?: number;
  areaPaisa?: number;
  areaDaam?: number;

  // Tax and Revenue
  taxpayerPrice?: number;
  taxpayerLandRevenuePrice?: number;
  deactivatePlot: boolean;
  lastFyForInclusion?: string;
  landRevenueApplicable?: string;

  // Structure Details
  structureAreaLength?: number;
  structureAreaBreadth?: number;
}

export interface Assessment {
  id: string;
  propertyId: string;
  assessmentYear: number;
  calculatedValue: number;
  overriddenValue?: number;
  finalAssessedValue: number;
  taxAmount: number;
  assessedByOfficerId?: string;
  assessmentDate: string;
  overrideReason?: string;
  exemptionAppliedDetails?: Record<string, unknown>;
  paymentStatus: "Pending" | "Paid" | "Overdue";

  // Location hierarchy fields for property identification
  province: string;
  district: string;
  municipality: string;
  wardNumber: number;
  street: string;
  parcelNumber: string;

  // Additional metadata
  propertyAddress: string;
  assessedByOfficerName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Payment {
  id: string;
  assessmentId: string;
  amountPaid: number;
  paymentDate: string;
  paymentGateway: string;
  transactionId?: string;
  status: "Success" | "Failed" | "PendingNegotiated";
  isReconciled?: boolean;
}

// Payment Response Types
export interface PaymentResponseDto {
  paymentId: string;
  assessmentId?: string;
  propertyId: string;
  fiscalYearId: string;
  amountPaid: number;
  paymentDate: string;
  paymentGateway: string;
  transactionId?: string;
  status: string;
  provisional: boolean;
  partial: boolean;
  createdAt: string;
  // Additional fields for underpayment scenarios
  message?: string;
  expectedTaxAmount?: number;
  deficitAmount?: number;
  assessmentCreated?: boolean;
}

export interface PropertyTaxSummary {
  propertyId: string;
  fiscalYearId: string;
  propertyAddress: string;
  fiscalYearName: string;
  calculatedTaxAmount: number;
  amountPaid: number;
  unpaidBalance: number;
  paymentIds: string[];
  displayText: string;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role: "Citizen" | "Officer" | "CentralAdmin";
  municipalityId?: number;
}

export interface UpdateUserRequest {
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  role: "Citizen" | "Officer" | "CentralAdmin";
  municipalityId?: number;
  isActive: boolean;
}

export interface UserListQuery {
  role?: string;
  municipalityId?: number;
  isActive?: boolean;
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface UserListResponse {
  users: User[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface ResetPasswordRequest {
  newPassword: string;
}

// Tax Calculation Types
export interface TaxCalculationResult {
  propertyId: string;
  fiscalYearId: string;
  propertyValue: number;
  taxAmount: number;
  valueBreakdown: PropertyValueBreakdown;
  taxBreakdown: TaxCalculationBreakdown;
  valuationRulesUsed: ValuationRulesConfig;
  taxSlabsUsed: LegacyTaxSlab[];
  exemptionRulesUsed: ExemptionRulesConfig;
  isSuccessful: boolean;
  errorMessage?: string;
  calculationTimestamp: string;
}

export interface PropertyValueBreakdown {
  landAreaSqM: number;
  landMVRPerSqM: number;
  landValue: number;
  buildingAreaSqM?: number;
  buildingBaseRatePerSqM?: number;
  buildingAgeYears?: number;
  depreciationFactor?: number;
  buildingValue: number;
  totalValue: number;
}

export interface TaxCalculationBreakdown {
  assessedValue: number;
  applicableTaxSlab?: LegacyTaxSlab;
  fixedAmount: number;
  variableAmount: number;
  grossTaxAmount: number;
  exemptionAmount: number;
  netTaxAmount: number;
}

export interface LegacyTaxSlab {
  minAssessedValue: number;
  maxAssessedValue: number;
  ratePercent: number;
  fixedAmount: number;
}

export interface ValuationRulesConfig {
  landBaseRatePerSqM: number;
  buildingBaseRatePerSqM: number;
  depreciationRatePerYear: number;
  maxDepreciation: number;
  minPropertyValue: number;
}

export interface ExemptionRulesConfig {
  seniorCitizenDiscount: number;
  disabilityDiscount: number;
  economicStatusDiscount: number;
  firstTimeOwnerDiscount: number;
  environmentalRatingBonus: number;
}
