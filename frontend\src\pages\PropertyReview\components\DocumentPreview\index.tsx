import React from "react";
import type { Property } from "../../../../types";
import { SectionHeader } from "../common/SectionHeader";

interface DocumentPreviewProps {
  property: Property;
}

export const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  property,
}) => {
  const documents = [
    {
      label: "Ownership Certificate",
      path: property.ownershipCertificatePath,
      required: true,
    },
    {
      label: "Building Permit",
      path: property.buildingPermitPath,
      required: false,
    },
  ].filter((doc) => doc.path); // Only show documents that exist

  if (documents.length === 0) {
    return (
      <div className="card bg-base-200 shadow-sm">
        <div className="card-body p-4">
          <SectionHeader
            title="Documents"
            icon="📄"
            subtitle="Uploaded property documents"
          />
          <div className="text-center py-4 text-base-content/70">
            <div className="text-4xl mb-2">📄</div>
            <p>No documents uploaded</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-200 shadow-sm">
      <div className="card-body p-4">
        <SectionHeader
          title="Documents"
          icon="📄"
          subtitle="Uploaded property documents"
        />

        <div className="space-y-3">
          {documents.map((document, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 bg-base-100 rounded-lg border"
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">
                  {document.label.includes("Certificate") ? "🏆" : "🏗️"}
                </div>
                <div>
                  <h4 className="font-medium text-base-content">
                    {document.label}
                  </h4>
                  <p className="text-sm text-base-content/70">
                    {document.required
                      ? "Required document"
                      : "Optional document"}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <a
                  href={document.path}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-sm btn-primary"
                >
                  <span className="text-xs">👁️</span>
                  View
                </a>
                <a
                  href={document.path}
                  download
                  className="btn btn-sm btn-secondary"
                >
                  <span className="text-xs">⬇️</span>
                  Download
                </a>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 p-3 bg-info/10 rounded-lg border border-info/20">
          <div className="flex items-start space-x-2">
            <span className="text-info text-lg">ℹ️</span>
            <div>
              <h5 className="font-medium text-info">Document Verification</h5>
              <p className="text-sm text-info/80 mt-1">
                Please review all uploaded documents for authenticity and
                completeness before approving the property registration.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
