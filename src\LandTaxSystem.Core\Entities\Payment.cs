using System;

namespace LandTaxSystem.Core.Entities
{
    public class Payment
    {
        public Guid PaymentId { get; set; }
        public Guid? AssessmentId { get; set; } // Now nullable since payment can exist before assessment
        public Guid PropertyId { get; set; } // Added to allow recording payments before assessment exists
        public Guid FiscalYearId { get; set; } // Added to allow recording payments before assessment exists
        public decimal AmountPaid { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.UtcNow;
        public string PaymentGateway { get; set; } = "SandboxMock";
        public string? TransactionId { get; set; }
        public string Status { get; set; } = "Success"; // Success, Failed
        public Guid? BatchPaymentId { get; set; } // For bulk payments
        public bool Partial { get; set; } = false; // True if this payment is part of a partial payment for an assessment
        public bool Provisional { get; set; } = false; // True if this payment is recorded before an assessment exists
        public bool IsReconciled { get; set; } = false; // True if this payment has been reconciled with an assessment

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Assessment? Assessment { get; set; } // Now nullable since payment can exist before assessment
        public virtual Property Property { get; set; } = null!;
        public virtual FiscalYear FiscalYear { get; set; } = null!;
    }
}
