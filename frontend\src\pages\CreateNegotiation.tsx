import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useNavigate, useParams } from 'react-router-dom';
import { appealService } from '../services/appealService';
import type { AppealDto } from '../services/appealService';
import { negotiationService, type CreateNegotiationDto, type TaxPeriodDto } from '../services/negotiationService';
import { format } from 'date-fns';

// Use ES2015 module syntax instead of namespaces
declare global {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test';
  }
}

// Use the TaxPeriodDto interface from negotiationService
type TaxPeriod = TaxPeriodDto;

interface FormErrors {
  taxpayerName?: string;
  panNumber?: string;
  taxOfficeAddress?: string;
  appealBody?: string;
  decisionNumber?: string;
  decisionDate?: string;
  taxPeriods?: string;
  originalTax?: string;
  originalPenalty?: string;
  originalFee?: string;
  originalInterest?: string;
  decidedTax?: string;
  decidedPenalty?: string;
  decidedFee?: string;
  decidedInterest?: string;
  remarks?: string;
  negotiatedAmount?: string;
  [key: string]: string | undefined; // Index signature for dynamic access
}

interface FormData {
  taxpayerName: string;
  panNumber: string;
  taxOfficeAddress: string;
  appealBody: "IRD" | "RevenueTribunal" | "SupremeCourt";
  decisionNumber: string;
  decisionDate: string;
  isWithdrawn: boolean;
  taxPeriods: TaxPeriod[];
  originalTax: number;
  originalPenalty: number;
  originalFee: number;
  originalInterest: number;
  decidedTax: number;
  decidedPenalty: number;
  decidedFee: number;
  decidedInterest: number;
  remarks: string;
  negotiatedAmount?: number;
}



const CreateNegotiation: React.FC = () => {
  // State for form data and UI
  const [activeTab, setActiveTab] = useState<'taxpayer' | 'appeal' | 'amounts'>('taxpayer');
  const { user } = useAuth();
  const navigate = useNavigate();
  const { appealId } = useParams<{ appealId: string }>();
  const [appeal, setAppeal] = useState<AppealDto | null>(null);
  // Loading and error states are used for UI feedback
  const [loading, setLoading] = useState(true); // Used in fetchAppeal and conditional rendering
  const [error, setError] = useState<string | null>(null); // Used for displaying errors to user
  const [errors, setErrors] = useState<FormErrors>({});
  // Form state
  const [formData, setFormData] = useState<FormData>({
    taxpayerName: '',
    panNumber: '',
    taxOfficeAddress: '',
    appealBody: 'IRD',
    decisionNumber: '',
    decisionDate: format(new Date(), 'yyyy-MM-dd'),
    isWithdrawn: false,
    taxPeriods: [{
      fiscalYear: new Date().getFullYear().toString(),
      startDate: format(new Date(), 'yyyy-MM-dd'),
      endDate: format(new Date(), 'yyyy-MM-dd'),
      // Initialize optional legacy fields with undefined
      year: undefined,
      period: undefined,
      taxPeriodValue: undefined,
      appealSubject: undefined,
      appealAmount: undefined
    }],
    originalTax: 0,
    originalPenalty: 0,
    originalFee: 0,
    originalInterest: 0,
    decidedTax: 0,
    decidedPenalty: 0,
    decidedFee: 0,
    decidedInterest: 0,
    remarks: ''
  });

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    if (user.role !== 'Officer') {
      navigate('/dashboard');
      return;
    }

    if (appealId) {
      fetchAppeal(appealId);
    } else {
      setError('No appeal ID provided');
      setLoading(false);
    }
  }, [user, navigate, appealId]);

  const fetchAppeal = async (id: string) => {
    try {
      setLoading(true);
      const data = await appealService.getAppeal(id);
      setAppeal(data);
      
      // Pre-fill form with appeal data where possible
      setFormData(prev => ({
        ...prev,
        taxpayerName: data.taxPayerName || '',
        taxOfficeAddress: data.propertyAddress || '',
        // Initialize other fields with default values if needed
        originalTax: data.assessmentAmount || 0,
        originalPenalty: 0,
        originalFee: 0,
        originalInterest: 0,
        decidedTax: data.assessmentAmount * 0.9 || 0,
        decidedPenalty: 0,
        decidedFee: 0,
        decidedInterest: 0,
        negotiatedAmount: data.assessmentAmount * 0.9 // Default to 10% reduction
      }));
      
      setError(null);
    } catch (err) {
      setError('Failed to load appeal details. Please try again later.');
      console.error('Error fetching appeal:', err);
    } finally {
      setLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Required fields validation
    if (!formData.taxpayerName.trim()) {
      newErrors.taxpayerName = 'Taxpayer name is required';
      isValid = false;
    }
    if (!formData.panNumber.trim()) {
      newErrors.panNumber = 'PAN number is required';
      isValid = false;
    }
    if (!formData.decisionNumber.trim()) {
      newErrors.decisionNumber = 'Decision number is required';
      isValid = false;
    }
    if (!formData.decisionDate) {
      newErrors.decisionDate = 'Decision date is required';
      isValid = false;
    }
    if (formData.taxPeriods.length === 0) {
      newErrors.taxPeriods = 'At least one tax period is required';
      isValid = false;
    }

    // Tax periods validation
    formData.taxPeriods.forEach((period, index) => {
      if (!period.fiscalYear) newErrors[`taxPeriods.${index}.fiscalYear`] = 'Fiscal Year is required';
      if (!period.startDate) newErrors[`taxPeriods.${index}.startDate`] = 'Start Date is required';
      if (!period.endDate) newErrors[`taxPeriods.${index}.endDate`] = 'End Date is required';
    });

    // Numeric validation
    const numericFields = [
      'originalTax', 'originalPenalty', 'originalFee', 'originalInterest',
      'decidedTax', 'decidedPenalty', 'decidedFee', 'decidedInterest'
    ] as const;
    
    numericFields.forEach(field => {
      const value = formData[field];
      if (value === undefined || value === null || isNaN(Number(value)) || Number(value) < 0) {
        newErrors[field] = 'Please enter a valid number';
        isValid = false;
      }
    });
    
    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!appealId) {
      setError('No appeal ID provided');
      return;
    }

    if (!validateForm()) {
      setError('Please fix the form errors before submitting');
      return;
    }

    try {
      setError(null);
      
      // Calculate total decided amount
      const totalDecidedAmount = 
        formData.decidedTax + 
        formData.decidedPenalty + 
        formData.decidedFee + 
        formData.decidedInterest;
      
      // Prepare the data for submission
      const submissionData: CreateNegotiationDto = {
        ...formData,
        appealId,
        taxPeriods: formData.taxPeriods.map(period => ({
          fiscalYear: period.fiscalYear,
          startDate: period.startDate,
          endDate: period.endDate
        })),
        originalTax: Number(formData.originalTax) || 0,
        originalPenalty: Number(formData.originalPenalty) || 0,
        originalFee: Number(formData.originalFee) || 0,
        originalInterest: Number(formData.originalInterest) || 0,
        decidedTax: Number(formData.decidedTax) || 0,
        decidedPenalty: Number(formData.decidedPenalty) || 0,
        decidedFee: Number(formData.decidedFee) || 0,
        decidedInterest: Number(formData.decidedInterest) || 0,
        negotiatedAmount: totalDecidedAmount
        // Remove remarks if not in the DTO type
      };

      // Submit the form data
      await negotiationService.createNegotiation(submissionData);
      
      // Redirect to negotiations list
      navigate('/negotiations');
      
    } catch (err) {
      console.error('Error creating negotiation:', err);
      setError(err instanceof Error ? err.message : 'Failed to create negotiation');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // This function is used in the JSX for tab navigation buttons
  const handleTabChange = (tab: 'taxpayer' | 'appeal' | 'amounts') => {
    setActiveTab(tab);
  };
  
  // Tax period handling functions - will be used when form is fully restored
  const addTaxPeriod = () => {
    setFormData(prev => ({
      ...prev,
      taxPeriods: [
        ...prev.taxPeriods,
        {
          fiscalYear: new Date().getFullYear().toString(),
          startDate: format(new Date(), 'yyyy-MM-dd'),
          endDate: format(new Date(), 'yyyy-MM-dd'),
          // Initialize optional legacy fields with undefined
          year: undefined,
          period: undefined,
          taxPeriodValue: undefined,
          appealSubject: undefined,
          appealAmount: undefined
        }
      ]
    }));
  };

  const removeTaxPeriod = (index: number) => {
    setFormData(prev => ({
      ...prev,
      taxPeriods: prev.taxPeriods.filter((_, i) => i !== index)
    }));
  };

  const handleTaxPeriodChange = (index: number, field: keyof TaxPeriod, value: string | number | undefined) => {
    setFormData(prev => {
      const updatedPeriods = [...prev.taxPeriods];
      updatedPeriods[index] = {
        ...updatedPeriods[index],
        [field]: value
      };
      return {
        ...prev,
        taxPeriods: updatedPeriods
      };
    });
  };

  const renderFormField = (
    label: string,
    id: keyof FormData | string,
    type = 'text',
    required = false,
    options?: string[]
  ) => {
    // This error variable is used in the className and aria attributes below
    const error = errors[id as keyof FormErrors];
    
    return (
      <div className="form-control mb-4">
        <label htmlFor={id as string} className="label">
          <span className="label-text">{label} {required && <span className="text-error">*</span>}</span>
        </label>
        {type === 'select' ? (
          <select
            id={id}
            name={id}
            value={formData[id as keyof typeof formData] as string}
            onChange={handleInputChange}
            className="select select-bordered"
            required={required}
          >
            {options?.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        ) : type === 'textarea' ? (
          <textarea
            id={id}
            name={id}
            rows={3}
            value={formData[id as keyof typeof formData] as string}
            onChange={handleInputChange}
            className="textarea textarea-bordered"
            required={required}
          />
        ) : type === 'checkbox' ? (
          <div className="mt-2">
            <input
              type="checkbox"
              id={id}
              name={id}
              checked={formData[id as keyof FormData] as boolean | undefined}
              onChange={(e) => {
                setFormData(prev => ({
                  ...prev,
                  [id]: e.target.checked
                }));
              }}
              className="checkbox checkbox-primary"
            />
          </div>
        ) : (
          <input
            type={type}
            id={id}
            name={id}
            value={typeof formData[id as keyof FormData] === 'boolean' ? 
              (formData[id as keyof FormData] ? 'true' : '') : 
              (formData[id as keyof FormData] as string | number | undefined)}
            onChange={handleInputChange}
            required={required}
            {...(type === 'number' ? { step: '0.01' } : {})}
            className={`input input-bordered ${error ? 'input-error' : ''}`}
            aria-invalid={!!error}
            aria-describedby={error ? `${id}-error` : undefined}
          />
        )}
        {error && (
          <label className="label">
            <span className="label-text-alt text-error">{error}</span>
          </label>
        )}
      </div>
    );
  };
  
  // Show loading or error state if appeal is not available
  if (!appeal) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="alert alert-warning mb-4">
          {loading ? (
            <p>Loading appeal details...</p>
          ) : (
            <p>{error || 'Appeal not found. Please check the URL and try again.'}</p>
          )}
        </div>
        <button
          onClick={() => navigate('/appeals')}
          className="btn btn-primary"
        >
          Back to Appeals
        </button>
      </div>
    );
  }
  
  // Calculate totals for summary
  const totalOriginal = formData.originalTax + formData.originalPenalty + formData.originalFee + formData.originalInterest;
  const totalDecided = formData.decidedTax + formData.decidedPenalty + formData.decidedFee + formData.decidedInterest;

  // Calculate reduction percentage for display
  const reductionPercentage = totalOriginal > 0 ? ((totalOriginal - totalDecided) / totalOriginal) * 100 : 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Create Negotiation</h1>
      
      {error && (
        <div className="alert alert-error mb-4">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6" noValidate>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <button
              onClick={() => navigate(-1)}
              type="button"
              className="mr-4 btn btn-ghost btn-sm"
            >
              &larr; Back
            </button>
            <h2 className="text-xl font-semibold">Appeal Resolution Form</h2>
          </div>
          <div>
            <span className="text-sm text-base-content/70">Appeal ID: {appealId}</span>
          </div>
        </div>
        
        {/* Navigation Tabs */}
        <div className="tabs tabs-boxed mb-6">
          <button
            type="button"
            className={`tab ${activeTab === 'taxpayer' ? 'tab-active' : ''}`}
            onClick={() => handleTabChange('taxpayer')}
          >
            Taxpayer Information
          </button>
          <button
            type="button"
            className={`tab ${activeTab === 'appeal' ? 'tab-active' : ''}`}
            onClick={() => handleTabChange('appeal')}
          >
            Appeal Details
          </button>
          <button
            type="button"
            className={`tab ${activeTab === 'amounts' ? 'tab-active' : ''}`}
            onClick={() => handleTabChange('amounts')}
          >
            Amount Details
          </button>
        </div>

        {/* Taxpayer Information Tab */}
        {activeTab === 'taxpayer' && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h3 className="card-title">Taxpayer Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderFormField('Taxpayer Name', 'taxpayerName', 'text', true)}
              {renderFormField('PAN Number', 'panNumber', 'text', true)}
              {renderFormField('Address', 'taxOfficeAddress', 'textarea')}
              {renderFormField('Contact Number', 'contactNumber', 'text')}
              {renderFormField('Email', 'email', 'email')}
            </div>
            </div>
          </div>
        )}

        {/* Appeal Details Tab */}
        {activeTab === 'appeal' && (
          <div className="bg-white p-6 rounded-md shadow-sm border border-gray-200">
            <h3 className="text-lg font-medium mb-4">Appeal Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {renderFormField('Decision Number', 'decisionNumber', 'text', true)}
              {renderFormField('Decision Date', 'decisionDate', 'date', true)}
              {renderFormField('Appeal Body', 'appealBody', 'select', true, ['Tax Office', 'Revenue Tribunal', 'Supreme Court'])}
            </div>

            <div className="mt-6">
              <h4 className="text-md font-medium mb-2">Tax Periods</h4>
              <div className="bg-gray-50 p-4 rounded-md">
                {formData.taxPeriods.map((period, index) => (
                  <div key={index} className="flex items-center mb-2 p-2 border border-gray-200 rounded-md bg-white">
                    <div className="flex-grow grid grid-cols-1 md:grid-cols-3 gap-2">
                      <div>
                        <label className="block text-xs text-gray-500">Fiscal Year</label>
                        <input
                          type="text"
                          value={period.fiscalYear}
                          onChange={(e) => handleTaxPeriodChange(index, 'fiscalYear', e.target.value)}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500">Start Date</label>
                        <input
                          type="date"
                          value={period.startDate}
                          onChange={(e) => handleTaxPeriodChange(index, 'startDate', e.target.value)}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500">End Date</label>
                        <input
                          type="date"
                          value={period.endDate}
                          onChange={(e) => handleTaxPeriodChange(index, 'endDate', e.target.value)}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeTaxPeriod(index)}
                      className="ml-2 text-red-500 hover:text-red-700"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addTaxPeriod}
                  className="mt-2 text-blue-500 hover:text-blue-700 text-sm flex items-center"
                >
                  <span className="mr-1">+</span> Add Tax Period
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Amount Details Tab */}
        {activeTab === 'amounts' && (
          <div className="bg-white p-6 rounded-md shadow-sm border border-gray-200">
            <h3 className="text-lg font-medium mb-4">Amount Details</h3>
            
            <div className="mb-6">
              <h4 className="text-md font-medium mb-2">Original Assessment</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {renderFormField('Tax', 'originalTax', 'number')}
                {renderFormField('Penalty', 'originalPenalty', 'number')}
                {renderFormField('Fee', 'originalFee', 'number')}
                {renderFormField('Interest', 'originalInterest', 'number')}
              </div>
              <div className="mt-2 text-right">
                <span className="font-medium">Total: NPR {totalOriginal.toLocaleString()}</span>
              </div>
            </div>
            
            <div className="mb-6">
              <h4 className="text-md font-medium mb-2">Decided Amount</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {renderFormField('Tax', 'decidedTax', 'number')}
                {renderFormField('Penalty', 'decidedPenalty', 'number')}
                {renderFormField('Fee', 'decidedFee', 'number')}
                {renderFormField('Interest', 'decidedInterest', 'number')}
              </div>
              <div className="mt-2 text-right">
                <span className="font-medium">Total: NPR {totalDecided.toLocaleString()}</span>
                <span className="ml-2 text-sm text-green-600">
                  ({reductionPercentage.toFixed(2)}% reduction)
                </span>
              </div>
            </div>
            
            <div className="mb-6">
              <h4 className="text-md font-medium mb-2">Remarks</h4>
              {renderFormField('Decision Remarks', 'remarks', 'textarea')}
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/appeals')}
            className="btn btn-ghost"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Submitting...' : 'Submit Negotiation'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateNegotiation;
