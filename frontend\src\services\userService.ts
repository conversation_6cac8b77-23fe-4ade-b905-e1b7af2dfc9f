import api from './api';
import type { User, Province, District } from '../types';
import type { MunicipalityLocation } from './api';

export interface UserResponse {
    data: User[];
    total: number;
    page: number;
    limit: number;
}

export interface UserSearchParams {
    page?: number;
    limit?: number;
    search?: string;
    role?: string;
}

class UserService {
    async getAll(params?: UserSearchParams): Promise<UserResponse> {
        const response = await api.get('/users', { params });
        return response.data;
    }

    async getById(id: string): Promise<User> {
        const response = await api.get(`/users/${id}`);
        return response.data;
    }

    async create(userData: Partial<User>): Promise<User> {
        const response = await api.post('/users', userData);
        return response.data;
    }

    async update(id: string, userData: Partial<User>): Promise<User> {
        const response = await api.put(`/users/${id}`, userData);
        return response.data;
    }

    async delete(id: string): Promise<void> {
        await api.delete(`/users/${id}`);
    }

    async getCurrentUser(): Promise<User> {
        const response = await api.get('/users/me');
        return response.data;
    }

    async getUserByRegistration(registration: string): Promise<User> {
        const response = await api.get(`/users/registration/${registration}`);
        return response.data;
    }

    async getProvinces(): Promise<Province[]> {
        const response = await api.get('/provinces');
        return response.data;
    }

    async getDistrictsByProvince(provinceId: string): Promise<District[]> {
        const response = await api.get(`/provinces/${provinceId}/districts`);
        return response.data;
    }

    async getMunicipalitiesByDistrict(districtId: string): Promise<MunicipalityLocation[]> {
        const response = await api.get(`/districts/${districtId}/municipalities`);
        return response.data;
    }

    async getTaxpayers(municipalityId: string, wardNumber?: string): Promise<User[]> {
        let url = `/users/taxpayers?municipalityId=${municipalityId}`;
        if (wardNumber) url += `&wardNumber=${encodeURIComponent(wardNumber)}`;
        const response = await api.get(url);
        return response.data;
    }
}

const userService = new UserService();
export default userService;