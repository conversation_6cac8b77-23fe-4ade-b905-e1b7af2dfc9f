using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Core.DTOs.FinalAssessment;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;

namespace LandTaxSystem.Infrastructure.Services
{
    public class FinalAssessmentService : IFinalAssessmentService
    {
        private readonly ApplicationDbContext _context;

        public FinalAssessmentService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<FinalAssessmentResponseDto> CreateAsync(FinalAssessmentCreateDto dto, string userId)
        {
            var assessment = new FinalAssessment
            {
                Id = Guid.NewGuid(),
                TaxpayerRegistration = dto.TaxpayerRegistration,
                FiscalYearId = dto.FiscalYearId,
                MunicipalityId = dto.MunicipalityId,
                ReturnFilingId = dto.ReturnFilingId,
                PreliminaryAssessmentId = dto.PreliminaryAssessmentId,
                TaxpayerName = dto.TaxpayerName,
                Address = dto.Address,
                Phone = dto.Phone,
                AssessmentPeriodFrom = dto.AssessmentPeriodFrom,
                AssessmentPeriodTo = dto.AssessmentPeriodTo,
                SectionsRules = dto.SectionsRules,
                BankName = dto.BankName,
                BranchName = dto.BranchName,
                ReasonCode = dto.ReasonCode,
                AppealNumber = dto.AppealNumber,
                ReasonDescription = dto.ReasonDescription,
                InterestPenaltyCalculationDate = dto.InterestPenaltyCalculationDate,
                FinalAssessmentDate = dto.FinalAssessmentDate,
                Status = "Draft",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = userId,
                TaxDetails = dto.TaxDetails.Select(detail => new FinalAssessmentDetail
                {
                    Id = Guid.NewGuid(),
                    SerialNumber = detail.SerialNumber,
                    FilingPeriod = detail.FilingPeriod,
                    TaxPeriod = detail.TaxPeriod,
                    TaxYear = detail.TaxYear,
                    AssessedAmount = detail.AssessedAmount,
                    Penalty = detail.Penalty,
                    AdditionalAmount = detail.AdditionalAmount,
                    Interest = detail.Interest,
                    Total = detail.Total
                }).ToList()
            };

            _context.FinalAssessments.Add(assessment);
            await _context.SaveChangesAsync();

            return await GetByIdAsync(assessment.Id) ?? throw new InvalidOperationException("Failed to retrieve created assessment");
        }

        public async Task<FinalAssessmentResponseDto> UpdateAsync(Guid id, FinalAssessmentUpdateDto dto, string userId)
        {
            var assessment = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (assessment == null)
                throw new ArgumentException("Assessment not found", nameof(id));

            assessment.TaxpayerRegistration = dto.TaxpayerRegistration;
            assessment.FiscalYearId = dto.FiscalYearId;
            assessment.MunicipalityId = dto.MunicipalityId;
            assessment.ReturnFilingId = dto.ReturnFilingId;
            assessment.PreliminaryAssessmentId = dto.PreliminaryAssessmentId;
            assessment.TaxpayerName = dto.TaxpayerName;
            assessment.Address = dto.Address;
            assessment.Phone = dto.Phone;
            assessment.AssessmentPeriodFrom = dto.AssessmentPeriodFrom;
            assessment.AssessmentPeriodTo = dto.AssessmentPeriodTo;
            assessment.SectionsRules = dto.SectionsRules;
            assessment.BankName = dto.BankName;
            assessment.BranchName = dto.BranchName;
            assessment.ReasonCode = dto.ReasonCode;
            assessment.AppealNumber = dto.AppealNumber;
            assessment.ReasonDescription = dto.ReasonDescription;
            assessment.InterestPenaltyCalculationDate = dto.InterestPenaltyCalculationDate;
            assessment.FinalAssessmentDate = dto.FinalAssessmentDate;
            assessment.UpdatedAt = DateTime.UtcNow;
            assessment.UpdatedBy = userId;

            // Remove existing tax details
            _context.FinalAssessmentDetails.RemoveRange(assessment.TaxDetails);

            // Add updated tax details
            assessment.TaxDetails = dto.TaxDetails.Select(detail => new FinalAssessmentDetail
            {
                Id = Guid.NewGuid(),
                FinalAssessmentId = assessment.Id,
                SerialNumber = detail.SerialNumber,
                FilingPeriod = detail.FilingPeriod,
                TaxPeriod = detail.TaxPeriod,
                TaxYear = detail.TaxYear,
                AssessedAmount = detail.AssessedAmount,
                Penalty = detail.Penalty,
                AdditionalAmount = detail.AdditionalAmount,
                Interest = detail.Interest,
                Total = detail.Total
            }).ToList();

            await _context.SaveChangesAsync();

            return await GetByIdAsync(assessment.Id) ?? throw new InvalidOperationException("Failed to retrieve updated assessment");
        }

        public async Task<FinalAssessmentResponseDto?> GetByIdAsync(Guid id)
        {
            var assessment = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .Include(a => a.CreatedByUser)
                .Include(a => a.UpdatedByUser)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (assessment == null)
                return null;

            return new FinalAssessmentResponseDto
            {
                Id = assessment.Id,
                TaxpayerRegistration = assessment.TaxpayerRegistration,
                FiscalYearId = assessment.FiscalYearId,
                MunicipalityId = assessment.MunicipalityId,
                ReturnFilingId = assessment.ReturnFilingId,
                PreliminaryAssessmentId = assessment.PreliminaryAssessmentId,
                TaxpayerName = assessment.TaxpayerName,
                Address = assessment.Address,
                Phone = assessment.Phone,
                AssessmentPeriodFrom = assessment.AssessmentPeriodFrom,
                AssessmentPeriodTo = assessment.AssessmentPeriodTo,
                SectionsRules = assessment.SectionsRules,
                BankName = assessment.BankName,
                BranchName = assessment.BranchName,
                ReasonCode = assessment.ReasonCode,
                AppealNumber = assessment.AppealNumber,
                ReasonDescription = assessment.ReasonDescription,
                InterestPenaltyCalculationDate = assessment.InterestPenaltyCalculationDate,
                FinalAssessmentDate = assessment.FinalAssessmentDate,
                Status = assessment.Status,
                CreatedAt = assessment.CreatedAt,
                CreatedBy = assessment.CreatedByUser?.UserName ?? "Unknown",
                UpdatedAt = assessment.UpdatedAt,
                UpdatedBy = assessment.UpdatedByUser?.UserName,
                TaxDetails = assessment.TaxDetails.Select(detail => new FinalAssessmentDetailDto
                {
                    SerialNumber = detail.SerialNumber,
                    FilingPeriod = detail.FilingPeriod,
                    TaxPeriod = detail.TaxPeriod,
                    TaxYear = detail.TaxYear,
                    AssessedAmount = detail.AssessedAmount,
                    Penalty = detail.Penalty,
                    AdditionalAmount = detail.AdditionalAmount,
                    Interest = detail.Interest,
                    Total = detail.Total
                }).ToList(),
                GrandTotal = assessment.TaxDetails.Sum(d => d.Total)
            };
        }

        public async Task<IEnumerable<FinalAssessmentListDto>> GetAllAsync(int page = 1, int pageSize = 10)
        {
            var assessments = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(a => new FinalAssessmentListDto
                {
                    Id = a.Id,
                    TaxpayerRegistration = a.TaxpayerRegistration,
                    FiscalYearId = a.FiscalYearId,
                    MunicipalityId = a.MunicipalityId,
                    ReturnFilingId = a.ReturnFilingId,
                    PreliminaryAssessmentId = a.PreliminaryAssessmentId,
                    TaxpayerName = a.TaxpayerName,
                    AssessmentPeriodFrom = a.AssessmentPeriodFrom,
                    AssessmentPeriodTo = a.AssessmentPeriodTo,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    GrandTotal = a.TaxDetails.Sum(d => d.Total)
                })
                .ToListAsync();

            return assessments;
        }

        public async Task<IEnumerable<FinalAssessmentListDto>> GetByTaxpayerRegistrationAsync(string taxpayerRegistration)
        {
            var assessments = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .Where(a => a.TaxpayerRegistration == taxpayerRegistration)
                .OrderByDescending(a => a.CreatedAt)
                .Select(a => new FinalAssessmentListDto
                {
                    Id = a.Id,
                    TaxpayerRegistration = a.TaxpayerRegistration,
                    FiscalYearId = a.FiscalYearId,
                    MunicipalityId = a.MunicipalityId,
                    ReturnFilingId = a.ReturnFilingId,
                    PreliminaryAssessmentId = a.PreliminaryAssessmentId,
                    TaxpayerName = a.TaxpayerName,
                    AssessmentPeriodFrom = a.AssessmentPeriodFrom,
                    AssessmentPeriodTo = a.AssessmentPeriodTo,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    GrandTotal = a.TaxDetails.Sum(d => d.Total)
                })
                .ToListAsync();

            return assessments;
        }

        public async Task<IEnumerable<FinalAssessmentListDto>> GetByMunicipalityAsync(Guid municipalityId)
        {
            var assessments = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .Where(a => a.MunicipalityId == municipalityId)
                .OrderByDescending(a => a.CreatedAt)
                .Select(a => new FinalAssessmentListDto
                {
                    Id = a.Id,
                    TaxpayerRegistration = a.TaxpayerRegistration,
                    FiscalYearId = a.FiscalYearId,
                    MunicipalityId = a.MunicipalityId,
                    ReturnFilingId = a.ReturnFilingId,
                    PreliminaryAssessmentId = a.PreliminaryAssessmentId,
                    TaxpayerName = a.TaxpayerName,
                    AssessmentPeriodFrom = a.AssessmentPeriodFrom,
                    AssessmentPeriodTo = a.AssessmentPeriodTo,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    GrandTotal = a.TaxDetails.Sum(d => d.Total)
                })
                .ToListAsync();

            return assessments;
        }

        public async Task<IEnumerable<FinalAssessmentListDto>> GetByFiscalYearAsync(Guid fiscalYearId)
        {
            var assessments = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .Where(a => a.FiscalYearId == fiscalYearId)
                .OrderByDescending(a => a.CreatedAt)
                .Select(a => new FinalAssessmentListDto
                {
                    Id = a.Id,
                    TaxpayerRegistration = a.TaxpayerRegistration,
                    FiscalYearId = a.FiscalYearId,
                    MunicipalityId = a.MunicipalityId,
                    ReturnFilingId = a.ReturnFilingId,
                    PreliminaryAssessmentId = a.PreliminaryAssessmentId,
                    TaxpayerName = a.TaxpayerName,
                    AssessmentPeriodFrom = a.AssessmentPeriodFrom,
                    AssessmentPeriodTo = a.AssessmentPeriodTo,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    GrandTotal = a.TaxDetails.Sum(d => d.Total)
                })
                .ToListAsync();

            return assessments;
        }

        public async Task<IEnumerable<FinalAssessmentListDto>> GetByPreliminaryAssessmentAsync(Guid preliminaryAssessmentId)
        {
            var assessments = await _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .Where(a => a.PreliminaryAssessmentId == preliminaryAssessmentId)
                .OrderByDescending(a => a.CreatedAt)
                .Select(a => new FinalAssessmentListDto
                {
                    Id = a.Id,
                    TaxpayerRegistration = a.TaxpayerRegistration,
                    FiscalYearId = a.FiscalYearId,
                    MunicipalityId = a.MunicipalityId,
                    ReturnFilingId = a.ReturnFilingId,
                    PreliminaryAssessmentId = a.PreliminaryAssessmentId,
                    TaxpayerName = a.TaxpayerName,
                    AssessmentPeriodFrom = a.AssessmentPeriodFrom,
                    AssessmentPeriodTo = a.AssessmentPeriodTo,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    GrandTotal = a.TaxDetails.Sum(d => d.Total)
                })
                .ToListAsync();

            return assessments;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var assessment = await _context.FinalAssessments.FindAsync(id);
            if (assessment == null)
                return false;

            _context.FinalAssessments.Remove(assessment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ExistsAsync(string taxpayerRegistration)
        {
            return await _context.FinalAssessments
                .AnyAsync(a => a.TaxpayerRegistration == taxpayerRegistration);
        }

        public async Task<int> GetTotalCountAsync()
        {
            return await _context.FinalAssessments.CountAsync();
        }

        public async Task<FinalAssessmentListResponse> SearchAsync(FinalAssessmentSearchParams searchParams)
        {
            var query = _context.FinalAssessments
                .Include(a => a.TaxDetails)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchParams.TaxpayerRegistration))
            {
                query = query.Where(a => a.TaxpayerRegistration.Contains(searchParams.TaxpayerRegistration));
            }

            if (!string.IsNullOrEmpty(searchParams.TaxpayerName))
            {
                query = query.Where(a => a.TaxpayerName.Contains(searchParams.TaxpayerName));
            }

            if (searchParams.FiscalYearId.HasValue)
            {
                query = query.Where(a => a.FiscalYearId == searchParams.FiscalYearId.Value);
            }

            if (searchParams.MunicipalityId.HasValue)
            {
                query = query.Where(a => a.MunicipalityId == searchParams.MunicipalityId.Value);
            }

            if (!string.IsNullOrEmpty(searchParams.Status))
            {
                query = query.Where(a => a.Status == searchParams.Status);
            }

            if (searchParams.CreatedFrom.HasValue)
            {
                query = query.Where(a => a.CreatedAt >= searchParams.CreatedFrom.Value);
            }

            if (searchParams.CreatedTo.HasValue)
            {
                query = query.Where(a => a.CreatedAt <= searchParams.CreatedTo.Value);
            }

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalCount / searchParams.PageSize);

            var assessments = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((searchParams.Page - 1) * searchParams.PageSize)
                .Take(searchParams.PageSize)
                .Select(a => new FinalAssessmentListDto
                {
                    Id = a.Id,
                    TaxpayerRegistration = a.TaxpayerRegistration,
                    FiscalYearId = a.FiscalYearId,
                    MunicipalityId = a.MunicipalityId,
                    ReturnFilingId = a.ReturnFilingId,
                    PreliminaryAssessmentId = a.PreliminaryAssessmentId,
                    TaxpayerName = a.TaxpayerName,
                    AssessmentPeriodFrom = a.AssessmentPeriodFrom,
                    AssessmentPeriodTo = a.AssessmentPeriodTo,
                    Status = a.Status,
                    CreatedAt = a.CreatedAt,
                    GrandTotal = a.TaxDetails.Sum(d => d.Total)
                })
                .ToListAsync();

            return new FinalAssessmentListResponse
            {
                Items = assessments,
                TotalCount = totalCount,
                Page = searchParams.Page,
                PageSize = searchParams.PageSize,
                TotalPages = totalPages
            };
        }
    }
}