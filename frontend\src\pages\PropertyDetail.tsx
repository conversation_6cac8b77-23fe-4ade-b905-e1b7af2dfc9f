import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, Polygon } from "react-leaflet";
import api from "../services/api";
import type { Property, Assessment } from "../types";
import "leaflet/dist/leaflet.css";
import TaxPaymentHistory from "../components/TaxPaymentHistory";
import TaxCalculator from "../components/TaxCalculator";
import DirectTaxPayment from "../components/DirectTaxPayment";
import { AdminLayout } from "../components/admin";

const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [property, setProperty] = useState<Property | null>(null);
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showTaxCalculator, setShowTaxCalculator] = useState(false);
  const [showDirectPayment, setShowDirectPayment] = useState(false);
  const [calculatedTaxAmount] = useState<number | null>(null);
  const [hasFiledReturn, setHasFiledReturn] = useState<boolean>(false);

  useEffect(() => {
    if (id) {
      loadPropertyDetails(id);
    } else {
      setError("No property ID provided. Please go back and select a property.");
      setLoading(false);
    }
  }, [id]);

  const loadPropertyDetails = async (propertyId: string) => {
    try {
      setLoading(true);
      setError("");

      // Load property details
      const propertyResponse = await api.get(`/properties/${propertyId}`);
      const propertyData = propertyResponse.data;

      // Handle field name differences between backend and frontend
      const mappedProperty: Property = {
        ...propertyData,
        // Map propertyId to id for frontend compatibility (if needed)
        id: propertyData.propertyId || propertyData.id,
        // Add any missing fields or map differently named fields
        landArea: propertyData.landAreaSqM,
        builtUpArea: propertyData.buildingBuiltUpAreaSqM,
        constructionType: propertyData.buildingConstructionType,
        constructionYear: propertyData.buildingConstructionYear,
        parcelGeoJson: propertyData.parcelGeoJson,
      };

      setProperty(mappedProperty);

      // Load assessments
      try {
        const assessmentsResponse = await api.get(`/assessments/property/${propertyId}`);
        setAssessments((assessmentsResponse as { data: unknown[] }).data as Assessment[]);
      } catch (assessmentError) {
        console.error("Failed to load assessments:", assessmentError);
        // Don't fail the whole page if assessments can't be loaded
        setAssessments([]);
      }

      // Load active fiscal year and check filing status
      try {
        const fiscalYearsResponse = await api.get("/FiscalYears");
        const activeFiscalYear = fiscalYearsResponse.data.find(
          (fy: { isActive: boolean; fiscalYearId: string }) => fy.isActive
        );
        if (activeFiscalYear) {
          // Check if return has been filed for this property
          try {
            const filingStatusResponse = await api.get(
              `/properties/${propertyId}/return-filing-status/${activeFiscalYear.fiscalYearId}`
            );
            setHasFiledReturn(filingStatusResponse.data.hasFiled);
          } catch (filingError) {
            console.error("Failed to check filing status:", filingError);
            setHasFiledReturn(false);
          }
        }
      } catch (fiscalYearError) {
        console.error("Failed to load fiscal years:", fiscalYearError);
      }
    } catch (err: unknown) {
      console.error("Error loading property details:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to load property details";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number | null | undefined): string => {
    if (amount === null || amount === undefined) return "N/A";
    return `NPR ${new Intl.NumberFormat("en-US").format(amount)}`;
  };

  const getStatusColor = (status: string): string => {
    switch (status?.toLowerCase()) {
      case "approved":
        return "badge-success";
      case "pending":
        return "badge-warning";
      case "rejected":
        return "badge-error";
      case "under review":
        return "badge-info";
      default:
        return "badge-neutral";
    }
  };

  const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "Invalid Date";
    }
  };

  const formatArea = (area: number | null | undefined): string => {
    if (area === null || area === undefined) return "N/A";
    return `${area.toLocaleString()} sq m`;
  };

  const getMapCenter = (property: Property): [number, number] => {
    // Default to Kathmandu coordinates if no valid coordinates
    const defaultLat = 27.7172;
    const defaultLng = 85.324;

    // Property interface doesn't have latitude/longitude properties
    // Only use GeoJSON data

    // Try to extract from GeoJSON if available
    if (property.parcelGeoJson) {
      try {
        const geoJson =
          typeof property.parcelGeoJson === "string" ? JSON.parse(property.parcelGeoJson) : property.parcelGeoJson;

        if (geoJson.coordinates && geoJson.coordinates.length > 0) {
          const coords = geoJson.coordinates[0];
          if (coords.length > 0) {
            // GeoJSON uses [lng, lat] format
            return [coords[0][1], coords[0][0]];
          }
        }
      } catch (e) {
        console.error("Error parsing GeoJSON:", e);
      }
    }

    return [defaultLat, defaultLng];
  };

  const getPolygonCoordinates = (property: Property): [number, number][] => {
    if (!property.parcelGeoJson) return [];

    try {
      const geoJson =
        typeof property.parcelGeoJson === "string" ? JSON.parse(property.parcelGeoJson) : property.parcelGeoJson;

      if (geoJson.coordinates && geoJson.coordinates.length > 0) {
        // Convert from [lng, lat] to [lat, lng] for Leaflet
        return geoJson.coordinates[0].map((coord: [number, number]) => [coord[1], coord[0]]);
      }
    } catch (e) {
      console.error("Error parsing GeoJSON for polygon:", e);
    }

    return [];
  };

  const renderMapContent = (property: Property) => {
    const polygonCoords = getPolygonCoordinates(property);

    if (polygonCoords.length === 0) {
      return null;
    }

    return <Polygon positions={polygonCoords} color="blue" fillColor="lightblue" fillOpacity={0.5} />;
  };

  if (loading) {
    return (
      <AdminLayout title="Property Details">
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout title="Property Details">
        <div className="alert alert-error">
          <h2 className="text-xl font-semibold mb-2">Error Loading Property</h2>
          <p>{error}</p>
          <Link to="/properties" className="btn btn-primary mt-4">
            Back to Properties
          </Link>
        </div>
      </AdminLayout>
    );
  }

  if (!property) {
    return (
      <AdminLayout title="Property Details">
        <div className="alert alert-warning">
          <h2 className="text-xl font-semibold mb-2">Property Not Found</h2>
          <p>The requested property could not be found.</p>
          <Link to="/properties" className="btn btn-primary mt-4">
            Back to Properties
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={property.address}>
      <div className="flex justify-end mb-6">
        <div className="flex space-x-2">
          <Link to="/properties" className="btn btn-secondary">
            Back to Properties
          </Link>
          {property.status === "Approved" && (
            <Link to={`/tax-payment/${property.id}`} className="btn btn-primary">
              {assessments && assessments.length > 0 ? "Pay Tax" : "Make Payment"}
            </Link>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {/* Status Badge */}
        <div className="flex items-center space-x-3">
          <span className={`badge ${getStatusColor(property.status)} badge-lg`}>{property.status}</span>
          <span className="text-sm opacity-70">Property ID: {property.id}</span>
        </div>

        {/* Property Details */}
        <div className="card bg-base-100 shadow">
          <div className="card-body">
            <h2 className="card-title">Property Information</h2>

            <div className="mb-4 pb-4 border-b border-base-300">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Property Type</label>
                  <p className="text-base font-medium">{property.usageType || "N/A"}</p>
                </div>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Land Area</label>
                  <p className="text-base font-medium">{formatArea(property.landArea)}</p>
                </div>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Built-up Area</label>
                  <p className="text-base font-medium">{formatArea(property.builtUpArea)}</p>
                </div>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Construction Type</label>
                  <p className="text-base font-medium">{property.constructionType || "N/A"}</p>
                </div>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Construction Year</label>
                  <p className="text-base font-medium">{property.constructionYear || "N/A"}</p>
                </div>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Registration Date</label>
                  <p className="text-base font-medium">{formatDate(property.registrationDate)}</p>
                </div>
              </div>
            </div>

            {/* Owner Information */}
            <div className="mb-4 pb-4 border-b border-base-300">
              <h3 className="text-lg font-semibold mb-3">Owner Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Owner Name</label>
                  <p className="text-base font-medium">{"N/A"}</p>
                </div>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Contact Information</label>
                  <p className="text-base font-medium">{"N/A"}</p>
                </div>
              </div>
            </div>

            {/* Assessment Information */}
            {property.assessedValue && (
              <div className="mb-4 pb-4 border-b border-base-300">
                <h3 className="text-lg font-semibold mb-3">Assessment Information</h3>
                <div>
                  <label className="label-text text-sm font-medium opacity-70">Current Assessed Value</label>
                  <p className="text-lg font-semibold text-blue-500">{formatCurrency(property.assessedValue)}</p>
                </div>
              </div>
            )}

            {property.status === "Approved" && (
              <div className="mt-4 p-4 bg-info/10 rounded-lg border border-info/20">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-md font-semibold">Tax Information</h3>
                  <div className="space-x-2">
                    <Link to="/return-filing" className="btn btn-warning btn-sm">
                      Submit Return Filing
                    </Link>
                    <button onClick={() => setShowTaxCalculator(!showTaxCalculator)} className="btn btn-info btn-sm">
                      {showTaxCalculator ? "Hide" : "Calculate Tax"}
                    </button>
                    {(calculatedTaxAmount || property.estimatedTax) && (
                      <button
                        onClick={() => setShowDirectPayment(!showDirectPayment)}
                        className={`btn btn-sm ${hasFiledReturn ? "btn-success" : "btn-disabled"}`}
                        disabled={!hasFiledReturn}
                        title={hasFiledReturn ? "Click to pay tax" : "Return filing must be submitted before payment"}
                      >
                        {showDirectPayment ? "Hide Payment" : "Pay Tax"}
                      </button>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="label-text text-sm font-medium opacity-70">Estimated Tax Amount</label>
                    <p className="text-lg font-semibold text-blue-500">
                      {property.estimatedTax !== null && property.estimatedTax !== undefined
                        ? formatCurrency(property.estimatedTax)
                        : "Click 'Calculate Tax' to get estimate"}
                    </p>
                  </div>
                  {assessments && assessments.length > 0 ? (
                    <div>
                      <label className="label-text text-sm font-medium opacity-70">Current Tax Due</label>
                      <p className="text-lg font-semibold text-blue-500">
                        {property.taxDue ? formatCurrency(property.taxDue) : "No tax due"}
                      </p>
                    </div>
                  ) : (
                    <div>
                      <label className="label-text text-sm font-medium opacity-70">Assessment Status</label>
                      <p className="text-sm text-warning">No assessment created yet. You can make a direct payment.</p>
                    </div>
                  )}
                </div>

                {/* Return Filing Status Notice */}
                {!hasFiledReturn && (
                  <div className="mt-4 p-3 bg-warning/10 border border-warning/20 rounded-lg">
                    <div className="flex items-center">
                      <div className="text-warning mr-3">⚠️</div>
                      <div>
                        <h4 className="text-sm font-medium text-warning">Return Filing Required</h4>
                        <p className="text-xs mt-1">
                          You must submit your tax return filing before making any payments.
                          <Link to="/return-filing" className="link link-warning ml-1">
                            Submit Return Filing
                          </Link>
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {hasFiledReturn && (
                  <div className="mt-4 p-3 bg-success/10 border border-success/20 rounded-lg">
                    <div className="flex items-center">
                      <div className="text-success mr-3">✅</div>
                      <div>
                        <h4 className="text-sm font-medium text-success">Return Filed</h4>
                        <p className="text-xs mt-1">
                          Your tax return has been filed. You can now proceed with payment.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Tax Calculator Component */}
                {showTaxCalculator && property.id && (
                  <div className="mt-4 border-t border-info/20 pt-4">
                    <TaxCalculator propertyId={property.id} className="bg-base-100 rounded-lg" />
                  </div>
                )}

                {/* Direct Tax Payment Component */}
                {showDirectPayment && property.id && (
                  <div className="mt-4 border-t border-info/20 pt-4">
                    <DirectTaxPayment
                      propertyId={property.id}
                      calculatedTaxAmount={calculatedTaxAmount || property.estimatedTax}
                      onPaymentSuccess={() => {
                        setShowDirectPayment(false);
                        // Refresh property details to get updated payment status
                        if (property.id) {
                          loadPropertyDetails(property.id);
                        }
                      }}
                      onPaymentError={error => {
                        console.error("Payment failed:", error);
                        // Could show a toast notification here
                      }}
                      onCancel={() => setShowDirectPayment(false)}
                      className="bg-base-100 rounded-lg"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Map */}
        <div className="card bg-base-100 shadow">
          <div className="card-body">
            <h2 className="card-title">Property Location</h2>
            <div className="h-64 w-full rounded-lg overflow-hidden">
              <MapContainer center={getMapCenter(property)} zoom={15} style={{ height: "100%", width: "100%" }}>
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                />
                {renderMapContent(property)}
              </MapContainer>
            </div>
          </div>
        </div>

        {/* Documents */}
        <div className="card bg-base-100 shadow">
          <div className="card-body">
            <h2 className="card-title">Documents</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border border-base-300 rounded-lg">
                <div>
                  <p className="font-medium">Property Registration Certificate</p>
                  <p className="text-sm opacity-70">PDF Document</p>
                </div>
                <button className="btn btn-sm btn-outline">Download</button>
              </div>
              <div className="flex items-center justify-between p-3 border border-base-300 rounded-lg">
                <div>
                  <p className="font-medium">Land Ownership Certificate</p>
                  <p className="text-sm opacity-70">PDF Document</p>
                </div>
                <button className="btn btn-sm btn-outline">Download</button>
              </div>
            </div>
          </div>
        </div>

        {/* Payment History */}
        <div className="card bg-base-100 shadow">
          <div className="card-body">
            <h2 className="card-title">Payment History</h2>
            <TaxPaymentHistory propertyId={property.id} />
          </div>
        </div>

        {/* Tax Assessments */}
        <div className="card bg-base-100 shadow">
          <div className="card-body">
            <h2 className="card-title">Tax Assessments</h2>
            {assessments && assessments.length > 0 ? (
              <div className="space-y-4">
                {assessments.map(assessment => (
                  <div key={assessment.id} className="border border-base-300 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold">Assessment #{assessment.id}</h4>
                        <p className="text-sm opacity-70">Created: {formatDate(assessment.createdAt)}</p>
                        <div className="mt-2 grid grid-cols-2 gap-4">
                          <div>
                            <label className="label-text text-sm font-medium opacity-70">Assessment Amount</label>
                            <p className="text-lg font-semibold">{formatCurrency(assessment.finalAssessedValue)}</p>
                          </div>
                          <div>
                            <label className="label-text text-sm font-medium opacity-70">Tax Amount</label>
                            <p className="text-lg font-semibold text-blue-500">
                              {formatCurrency(assessment.taxAmount)}
                            </p>
                          </div>
                        </div>
                      </div>
                      <span className={`badge ${getStatusColor(assessment.paymentStatus)}`}>
                        {assessment.paymentStatus}
                      </span>
                    </div>
                    {assessment.paymentStatus !== "Paid" && (
                      <div className="mt-3 pt-3 border-t border-base-300">
                        <Link
                          to={`/tax-payment/${property.id}?assessment=${assessment.id}`}
                          className="btn btn-primary"
                        >
                          Pay Tax ({formatCurrency(assessment.taxAmount)})
                        </Link>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 opacity-70">
                <div className="text-4xl mb-2">📊</div>
                <p>No tax assessments yet</p>
                {property.status === "Approved" && (
                  <p className="text-sm mt-1">Assessment will be generated once the property is reviewed</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default PropertyDetail;
