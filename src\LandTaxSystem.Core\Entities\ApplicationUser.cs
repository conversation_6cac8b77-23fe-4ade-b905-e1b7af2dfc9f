using System;
using System.Text.Json;
using Microsoft.AspNetCore.Identity;

namespace LandTaxSystem.Core.Entities
{
    public class ApplicationUser : IdentityUser
    {
        // Basic Information
        public string FullName { get; set; } = string.Empty;
        public new string? PhoneNumber { get; set; }
        public string? Telephone { get; set; }
        public string Role { get; set; } = "Citizen"; // Citizen, Officer, CentralAdmin
        
        // Identity and Status
        public string? PAN { get; set; } // Serves as TaxPayerID for login
        public string? SubmissionNumber { get; set; }
        public string Status { get; set; } = "PendingApproval"; // PendingApproval, Active, Rejected
        
        // Personal Information
        public DateTime DateOfBirth { get; set; }
        public string Gender { get; set; } = string.Empty;
        public string? Nationality { get; set; } = "Nepali";
        public string? Profession { get; set; }
        public string CitizenshipNumber { get; set; } = string.Empty;
        public string? TwoGenerations { get; set; }
        
        // Family Information
        public string? FatherName { get; set; }
        public string? MotherName { get; set; }
        public string? GrandfatherName { get; set; }
        public string? GrandmotherName { get; set; }
        public string? MaritalStatus { get; set; }
        public string? SpouseName { get; set; }
        public bool IsMinor { get; set; } = false;
        public string? GuardianName { get; set; }
        public string? GuardianRelation { get; set; }
        
        // Address Information
        public string PermanentAddress { get; set; } = string.Empty;
        public string? TemporaryAddress { get; set; }
        public string WardNumber { get; set; } = string.Empty;
        public string ToleStreet { get; set; } = string.Empty;
        public Guid? MunicipalityId { get; set; }
        public virtual Municipality? Municipality { get; set; }
        
        // Documents and Files
        public string DocumentPath { get; set; } = string.Empty;
        
        // Document Metadata
        // Citizenship Document
        public string? CitizenshipIssueDistrict { get; set; }
        public DateTime? CitizenshipIssueDate { get; set; }
        public string? CitizenshipIssueOffice { get; set; }
        
        // National ID Document
        public string? NationalIdNumber { get; set; }
        public string? NationalIdIssueDistrict { get; set; }
        public DateTime? NationalIdIssueDate { get; set; }
        public string? NationalIdIssueOffice { get; set; }
        public string? NationalIdDocumentPath { get; set; }
        
        // PAN Document
        public DateTime? PanIssueDate { get; set; }
        public string? PanIssueDistrict { get; set; }
        public string? PanIssueOffice { get; set; }
        public string? PanDocumentPath { get; set; }
        
        // Contact Preferences (stored as JSON string)
        private string? _contactPreferences;
        public string? ContactPreferences
        {
            get => _contactPreferences;
            set => _contactPreferences = value != null ? JsonSerializer.Serialize(value) : null;
        }
        
        // Audit Fields
        public DateTime? LastLoginAt { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
