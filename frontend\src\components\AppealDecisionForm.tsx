import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import AdminForm from "./admin/AdminForm";

interface AppealDecisionData {
  taxpayerRegistrationNumber: string;
  taxpayerName: string;
  province: string;
  district: string;
  ward: string;
  streetTole: string;
  houseNumber: string;
  email: string;
  mobileNo: string;
  reregistrationDate: string;
  appealDescription: string;
  appealLines: AppealLine[];
}

interface AppealLine {
  sno: number;
  fiscalYear: string;
  ward: string;
  parcelNo: string;
  appealOn: string;
  appealAmount: number;
  retainedAmount: number;
}

const AppealDecisionForm: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<AppealDecisionData>({
    taxpayerRegistrationNumber: "",
    taxpayerName: "",
    province: "",
    district: "",
    ward: "",
    streetTole: "",
    houseNumber: "",
    email: "",
    mobileNo: "",
    reregistrationDate: "",
    appealDescription: "",
    appealLines: [
      {
        sno: 1,
        fiscalYear: "",
        ward: "",
        parcelNo: "",
        appealOn: "कर",
        appealAmount: 0,
        retainedAmount: 5000,
      },
      {
        sno: 2,
        fiscalYear: "",
        ward: "",
        parcelNo: "",
        appealOn: "जरिवाना",
        appealAmount: 0,
        retainedAmount: 15000,
      },
    ],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange =
    (field: keyof AppealDecisionData) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData(prev => ({
        ...prev,
        [field]: e.target.value,
      }));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: "" }));
      }
    };

  const handleAppealLineChange = (index: number, field: keyof AppealLine, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      appealLines: prev.appealLines.map((line, i) => (i === index ? { ...line, [field]: value } : line)),
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.taxpayerRegistrationNumber.trim()) {
      newErrors.taxpayerRegistrationNumber = "पुनरावेदन दर्ता नम्बर आवश्यक छ";
    }

    if (!formData.taxpayerName.trim()) {
      newErrors.taxpayerName = "करदाताको नाम आवश्यक छ";
    }

    if (!formData.province.trim()) {
      newErrors.province = "प्रदेश आवश्यक छ";
    }

    if (!formData.district.trim()) {
      newErrors.district = "जिल्ला आवश्यक छ";
    }

    if (!formData.ward.trim()) {
      newErrors.ward = "वडा आवश्यक छ";
    }

    if (!formData.mobileNo.trim()) {
      newErrors.mobileNo = "मोबाइल नम्बर आवश्यक छ";
    } else if (!/^\d{10}$/.test(formData.mobileNo)) {
      newErrors.mobileNo = "मोबाइल नम्बर १० अंकको हुनुपर्छ";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "वैध इमेल ठेगाना प्रविष्ट गर्नुहोस्";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("कृपया सबै आवश्यक फिल्डहरू भर्नुहोस्");
      return;
    }

    setIsSubmitting(true);

    try {
      // Calculate total amounts
      const totalAppealAmount = formData.appealLines.reduce((sum, line) => sum + line.appealAmount, 0);
      const totalRetainedAmount = formData.appealLines.reduce((sum, line) => sum + line.retainedAmount, 0);

      const submissionData = {
        ...formData,
        totalAppealAmount,
        totalRetainedAmount,
      };

      // Here you would typically call an API service
      console.log("Submitting appeal decision:", submissionData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success("पुनरावेदन निर्णय सफलतापूर्वक दर्ता भयो");

      // Reset form
      setFormData({
        taxpayerRegistrationNumber: "",
        taxpayerName: "",
        province: "",
        district: "",
        ward: "",
        streetTole: "",
        houseNumber: "",
        email: "",
        mobileNo: "",
        reregistrationDate: "",
        appealDescription: "",
        appealLines: [
          {
            sno: 1,
            fiscalYear: "",
            ward: "",
            parcelNo: "",
            appealOn: "कर",
            appealAmount: 0,
            retainedAmount: 5000,
          },
          {
            sno: 2,
            fiscalYear: "",
            ward: "",
            parcelNo: "",
            appealOn: "जरिवाना",
            appealAmount: 0,
            retainedAmount: 15000,
          },
        ],
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("दर्ता गर्दा त्रुटि भयो। कृपया पुनः प्रयास गर्नुहोस्।");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  const totalAppealAmount = formData.appealLines.reduce((sum, line) => sum + line.appealAmount, 0);
  const totalRetainedAmount = formData.appealLines.reduce((sum, line) => sum + line.retainedAmount, 0);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6 text-center">पुनरावेदन समाधानको सूचना (Appeal Decision Form)</h1>

      <AdminForm onSubmit={handleSubmit}>
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <h2 className="text-xl font-semibold mb-4">मुख्य विवरण (Main Details)</h2>
          
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="form-control">
            <label className="label">
              <span className="label-text">Taxpayer Registration Number: *</span>
            </label>
            <input
              type="text"
              name="taxpayerRegistrationNumber"
              value={formData.taxpayerRegistrationNumber}
              onChange={handleInputChange("taxpayerRegistrationNumber")}
              className="input input-bordered input-sm"
              required
            />
            {errors.taxpayerRegistrationNumber && (
              <span className="text-red-500 text-sm">{errors.taxpayerRegistrationNumber}</span>
            )}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Taxpayer Name: *</span>
            </label>
            <input
              type="text"
              name="taxpayerName"
              value={formData.taxpayerName}
              onChange={handleInputChange("taxpayerName")}
              className="input input-bordered input-sm"
              required
            />
            {errors.taxpayerName && (
              <span className="text-red-500 text-sm">{errors.taxpayerName}</span>
            )}
          </div>
          </div>

          {/* Address Information */}
          <h3 className="text-lg font-medium mb-3">ठेगाना विवरण (Address Details)</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="form-control">
            <label className="label">
              <span className="label-text">Province: *</span>
            </label>
            <input
              type="text"
              name="province"
              value={formData.province}
              onChange={handleInputChange("province")}
              className="input input-bordered input-sm"
              required
            />
            {errors.province && (
              <span className="text-red-500 text-sm">{errors.province}</span>
            )}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">District: *</span>
            </label>
            <input
              type="text"
              name="district"
              value={formData.district}
              onChange={handleInputChange("district")}
              className="input input-bordered input-sm"
              required
            />
            {errors.district && (
              <span className="text-red-500 text-sm">{errors.district}</span>
            )}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Ward: *</span>
            </label>
            <input
              type="text"
              name="ward"
              value={formData.ward}
              onChange={handleInputChange("ward")}
              className="input input-bordered input-sm"
              required
            />
            {errors.ward && (
              <span className="text-red-500 text-sm">{errors.ward}</span>
            )}
          </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="form-control">
            <label className="label">
              <span className="label-text">Street/Tole:</span>
            </label>
            <input
              type="text"
              name="streetTole"
              value={formData.streetTole}
              onChange={handleInputChange("streetTole")}
              className="input input-bordered input-sm"
            />
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">House Number:</span>
            </label>
            <input
              type="text"
              name="houseNumber"
              value={formData.houseNumber}
              onChange={handleInputChange("houseNumber")}
              className="input input-bordered input-sm"
            />
          </div>
          </div>

          {/* Contact Information */}
          <h3 className="text-lg font-medium mb-3">सम्पर्क विवरण (Contact Details)</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="form-control">
            <label className="label">
              <span className="label-text">Email:</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange("email")}
              className="input input-bordered input-sm"
            />
            {errors.email && (
              <span className="text-red-500 text-sm">{errors.email}</span>
            )}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Mobile No.: *</span>
            </label>
            <input
              type="text"
              name="mobileNo"
              value={formData.mobileNo}
              onChange={handleInputChange("mobileNo")}
              className="input input-bordered input-sm"
              required
            />
            {errors.mobileNo && (
              <span className="text-red-500 text-sm">{errors.mobileNo}</span>
            )}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">पुनरावेदन दर्ता मि.:</span>
            </label>
            <input
              type="text"
              name="reregistrationDate"
              value={formData.reregistrationDate}
              onChange={handleInputChange("reregistrationDate")}
              className="input input-bordered input-sm"
            />
          </div>
          </div>

          {/* Appeal Description */}
          <h3 className="text-lg font-medium mb-3">निर्णय विवरण (Decision Details)</h3>
          <div className="form-control mb-6">
          <label className="label">
            <span className="label-text">निर्णय विवरण:</span>
          </label>
          <textarea
            name="appealDescription"
            value={formData.appealDescription}
            onChange={handleInputChange("appealDescription")}
            rows={3}
            className="textarea textarea-bordered textarea-sm"
          />
          </div>
        </div>

        {/* Appeal Lines Table */}
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <h2 className="text-xl font-semibold mb-4">Appeal Lines</h2>
          <div className="overflow-x-auto">
            <table className="table table-bordered w-full">
              <thead>
                <tr className="bg-gray-100">
                  <th className="text-center">S.No.</th>
                  <th className="text-center">Fiscal Year</th>
                  <th className="text-center">Ward</th>
                  <th className="text-center">Parcel No</th>
                  <th className="text-center">Appeal On</th>
                  <th className="text-center">Appeal Amount</th>
                  <th className="text-center">Retained Amount</th>
                </tr>
              </thead>
              <tbody>
                {formData.appealLines.map((line, index) => (
                  <tr key={index}>
                    <td className="text-center">{line.sno}</td>
                    <td>
                      <input
                        type="text"
                        value={line.fiscalYear}
                        onChange={e => handleAppealLineChange(index, "fiscalYear", e.target.value)}
                        className="input input-bordered input-sm w-full"
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={line.ward}
                        onChange={e => handleAppealLineChange(index, "ward", e.target.value)}
                        className="input input-bordered input-sm w-full"
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={line.parcelNo}
                        onChange={e => handleAppealLineChange(index, "parcelNo", e.target.value)}
                        className="input input-bordered input-sm w-full"
                      />
                    </td>
                    <td className="text-center">
                      <span className="font-medium">{line.appealOn}</span>
                    </td>
                    <td>
                      <input
                        type="number"
                        value={line.appealAmount}
                        onChange={e => handleAppealLineChange(index, "appealAmount", Number(e.target.value))}
                        className="input input-bordered input-sm w-full text-right"
                        min="0"
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        value={line.retainedAmount}
                        onChange={e => handleAppealLineChange(index, "retainedAmount", Number(e.target.value))}
                        className="input input-bordered input-sm w-full text-right"
                        min="0"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="bg-gray-50 font-semibold">
                  <td colSpan={5} className="text-right">
                    कुल निर्णय ठहर रकम गर्नुहोस्:
                  </td>
                  <td className="text-right">{totalAppealAmount.toLocaleString()}</td>
                  <td className="text-right">{totalRetainedAmount.toLocaleString()}</td>
                </tr>
                <tr className="bg-gray-100 font-bold">
                  <td colSpan={5} className="text-right">
                    कुल निर्णय ठहर गरिएको रकम रु.: 20000
                  </td>
                  <td colSpan={2}></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        {/* Form Actions */}
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <div className="flex justify-end space-x-4">
            <button type="button" onClick={handleCancel} className="btn btn-outline btn-error" disabled={isSubmitting}>
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  दर्ता गर्दै...
                </>
              ) : (
                "Submit"
              )}
            </button>
          </div>
        </div>
      </AdminForm>
    </div>
  );
};

export default AppealDecisionForm;