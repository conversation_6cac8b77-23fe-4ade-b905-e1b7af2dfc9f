import api from "./api";

// Types for Tax Configuration
export interface TaxSlab {
  minValue: number;
  maxValue: number;
  rate: number;
  description: string;
}

export interface PenaltyRule {
  daysAfterDue: number;
  rate: number;
  maxPenalty: number;
  description: string;
}

export interface FiscalYear {
  fiscalYearId: string;
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ValuationRulesConfig {
  landMVR: Record<string, number>;
  buildingBaseRatePerSqm: Record<string, number>;
  annualDepreciationRate: number;
}

export interface MunicipalityTaxConfig {
  municipalityTaxConfigId: string;
  municipalityId: string;
  municipalityName: string;
  fiscalYearId: string;
  fiscalYearName: string;
  taxSlabsConfig: TaxSlab[];
  penaltyRules: PenaltyRule[];
  valuationRulesConfig?: ValuationRulesConfig;
  defaultPenaltyPercent?: number;
  defaultDiscountPercent?: number;
  isFinalized: boolean;
  finalizedAt?: string;
  finalizedBy?: string;
  createdAt: string;
  updatedAt: string;
}

// API functions
export const getFiscalYears = async () => {
  const response = await api.get("/FiscalYears");
  return response.data;
};

export const getActiveFiscalYear = async () => {
  const response = await api.get("/FiscalYears/active");
  return response.data;
};

export const getMunicipalityTaxConfigs = async (municipalityId: string) => {
  const response = await api.get(
    `/MunicipalityTaxConfigs/municipality/${municipalityId}`
  );
  return response.data;
};

export const getMunicipalityTaxConfig = async (id: string) => {
  const response = await api.get(`/MunicipalityTaxConfigs/${id}`);
  return response.data;
};

export const createMunicipalityTaxConfig = async (data: {
  municipalityId: string;
  fiscalYearId: string;
  taxSlabsConfig?: TaxSlab[];
  penaltyRules?: PenaltyRule[];
  valuationRules?: Record<string, string>;
}) => {
  const response = await api.post("/MunicipalityTaxConfigs", data);
  return response.data;
};

export const updateMunicipalityTaxConfig = async (
  id: string,
  data: {
    taxSlabsConfig?: TaxSlab[];
    penaltyRules?: PenaltyRule[];
    defaultPenaltyPercent?: number;
    defaultDiscountPercent?: number;
    valuationRules?: Record<string, string>;
  }
) => {
  const response = await api.put(`/MunicipalityTaxConfigs/${id}`, data);
  return response.data;
};

export const finalizeMunicipalityTaxConfig = async (id: string) => {
  const response = await api.put(`/MunicipalityTaxConfigs/${id}/finalize`, {
    finalize: true,
  });
  return response.data;
};

export const getTaxConfigByMunicipalityAndFiscalYear = async (
  municipalityId: string,
  fiscalYearId: string
) => {
  const response = await api.get(
    `/MunicipalityTaxConfigs/municipality/${municipalityId}/fiscal-year/${fiscalYearId}`
  );
  return response.data;
};
