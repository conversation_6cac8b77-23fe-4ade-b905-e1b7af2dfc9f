using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using LandTaxSystem.Core.DTOs.Report;
using LandTaxSystem.Core.Interfaces;
using System.Text;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly IReportService _reportService;
        private readonly ILogger<ReportsController> _logger;

        public ReportsController(IReportService reportService, ILogger<ReportsController> logger)
        {
            _reportService = reportService;
            _logger = logger;
        }

        [HttpGet("outstanding-tax")]
        public async Task<ActionResult<OutstandingTaxReportResponseDto>> GetOutstandingTaxReport(
            [FromQuery] OutstandingTaxReportRequestDto request)
        {
            try
            {
                var result = await _reportService.GetOutstandingTaxReportAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating outstanding tax report");
                return StatusCode(500, "An error occurred while generating the report");
            }
        }

        [HttpGet("outstanding-tax/export/csv")]
        public async Task<IActionResult> ExportOutstandingTaxReportToCsv(
            [FromQuery] OutstandingTaxReportRequestDto request)
        {
            try
            {
                var csvData = await _reportService.ExportOutstandingTaxReportToCsvAsync(request);
                var fileName = $"outstanding-tax-report-{DateTime.Now:yyyyMMdd-HHmmss}.csv";
                
                return File(Encoding.UTF8.GetBytes(csvData), "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting outstanding tax report to CSV");
                return StatusCode(500, "An error occurred while exporting the report");
            }
        }

        [HttpGet("outstanding-tax/export/pdf")]
        public async Task<IActionResult> ExportOutstandingTaxReportToPdf(
            [FromQuery] OutstandingTaxReportRequestDto request)
        {
            try
            {
                var pdfData = await _reportService.ExportOutstandingTaxReportToPdfAsync(request);
                var fileName = $"outstanding-tax-report-{DateTime.Now:yyyyMMdd-HHmmss}.pdf";
                
                return File(pdfData, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting outstanding tax report to PDF");
                return StatusCode(500, "An error occurred while exporting the report");
            }
        }
    }
}