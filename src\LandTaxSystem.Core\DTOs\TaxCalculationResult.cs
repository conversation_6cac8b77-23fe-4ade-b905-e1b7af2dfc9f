using System;
using System.Collections.Generic;
using LandTaxSystem.Core.Models;

namespace LandTaxSystem.Core.DTOs
{
    /// <summary>
    /// Represents the result of a tax calculation for a property
    /// </summary>
    public class TaxCalculationResult
    {
        /// <summary>
        /// The property ID for which tax was calculated
        /// </summary>
        public Guid PropertyId { get; set; }

        /// <summary>
        /// The fiscal year ID used for calculation
        /// </summary>
        public Guid FiscalYearId { get; set; }

        /// <summary>
        /// The calculated property value (land + building value)
        /// </summary>
        public decimal PropertyValue { get; set; }

        /// <summary>
        /// The calculated tax amount
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// Breakdown of the property value calculation
        /// </summary>
        public PropertyValueBreakdown ValueBreakdown { get; set; } = new PropertyValueBreakdown();

        /// <summary>
        /// Breakdown of the tax calculation
        /// </summary>
        public TaxCalculationBreakdown TaxBreakdown { get; set; } = new TaxCalculationBreakdown();

        /// <summary>
        /// The valuation rules used in the calculation
        /// </summary>
        public ValuationRulesConfig ValuationRulesUsed { get; set; } = new ValuationRulesConfig();

        /// <summary>
        /// The tax slabs used in the calculation
        /// </summary>
        public List<LegacyTaxSlab> TaxSlabsUsed { get; set; } = new List<LegacyTaxSlab>();

        /// <summary>
        /// The exemption rules used in the calculation
        /// </summary>
        public ExemptionRulesConfig ExemptionRulesUsed { get; set; } = new ExemptionRulesConfig();

        /// <summary>
        /// Whether the calculation was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// Error message if calculation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Timestamp when the calculation was performed
        /// </summary>
        public DateTime CalculationTimestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Breakdown of property value calculation
    /// </summary>
    public class PropertyValueBreakdown
    {
        /// <summary>
        /// Land area in square meters
        /// </summary>
        public decimal LandAreaSqM { get; set; }

        /// <summary>
        /// Market value rate per square meter for land
        /// </summary>
        public decimal LandMVRPerSqM { get; set; }

        /// <summary>
        /// Total land value
        /// </summary>
        public decimal LandValue { get; set; }

        /// <summary>
        /// Building built-up area in square meters (if applicable)
        /// </summary>
        public decimal? BuildingAreaSqM { get; set; }

        /// <summary>
        /// Building base rate per square meter (if applicable)
        /// </summary>
        public decimal? BuildingBaseRatePerSqM { get; set; }

        /// <summary>
        /// Building age in years (if applicable)
        /// </summary>
        public int? BuildingAgeYears { get; set; }

        /// <summary>
        /// Depreciation factor applied to building (if applicable)
        /// </summary>
        public decimal? DepreciationFactor { get; set; }

        /// <summary>
        /// Total building value after depreciation (if applicable)
        /// </summary>
        public decimal BuildingValue { get; set; }

        /// <summary>
        /// Total property value (land + building)
        /// </summary>
        public decimal TotalValue { get; set; }
    }

    /// <summary>
    /// Breakdown of tax calculation
    /// </summary>
    public class TaxCalculationBreakdown
    {
        /// <summary>
        /// The assessed value used for tax calculation
        /// </summary>
        public decimal AssessedValue { get; set; }

        /// <summary>
        /// The tax slab that was applied
        /// </summary>
        public LegacyTaxSlab? ApplicableTaxSlab { get; set; }

        /// <summary>
        /// Fixed amount from the tax slab
        /// </summary>
        public decimal FixedAmount { get; set; }

        /// <summary>
        /// Variable amount calculated from the rate
        /// </summary>
        public decimal VariableAmount { get; set; }

        /// <summary>
        /// Gross tax before exemptions
        /// </summary>
        public decimal GrossTaxAmount { get; set; }

        /// <summary>
        /// Total exemption/discount amount applied
        /// </summary>
        public decimal ExemptionAmount { get; set; }

        /// <summary>
        /// Final tax amount after exemptions
        /// </summary>
        public decimal NetTaxAmount { get; set; }
    }
}
