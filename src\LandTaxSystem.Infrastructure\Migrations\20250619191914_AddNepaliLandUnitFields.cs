﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddNepaliLandUnitFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Aana",
                table: "Properties",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Bigha",
                table: "Properties",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Daam",
                table: "Properties",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "<PERSON>hur",
                table: "Properties",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Kattha",
                table: "Properties",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Pa<PERSON>",
                table: "Properties",
                type: "numeric",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "<PERSON><PERSON><PERSON>",
                table: "Properties",
                type: "numeric",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Aana",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Bigha",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Daam",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Dhur",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Kattha",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Paisa",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Ropani",
                table: "Properties");
        }
    }
}
