using LandTaxSystem.Core.DTOs.Report;

namespace LandTaxSystem.Core.Interfaces
{
    public interface IReportService
    {
        Task<OutstandingTaxReportResponseDto> GetOutstandingTaxReportAsync(OutstandingTaxReportRequestDto request);
        Task<string> ExportOutstandingTaxReportToCsvAsync(OutstandingTaxReportRequestDto request);
        Task<byte[]> ExportOutstandingTaxReportToPdfAsync(OutstandingTaxReportRequestDto request);
    }
}