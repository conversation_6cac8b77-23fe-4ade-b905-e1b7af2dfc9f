using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Rebate
{
    public class CreateRebateDto
    {
        [Required]
        public string? OfficeCode { get; set; }

        [Required]
        public string? Pan { get; set; }

        [Required]
        public string? AccountType { get; set; }

        [Required]
        public string? Name { get; set; }

        [Required]
        public DateTime? ExemptionDate { get; set; }

        [Required]
        public string? SerialNo { get; set; }

        [Required]
        public string? Scheme { get; set; }

        public bool IsReversal { get; set; }
        public string? MaNo { get; set; }

        [Required]
        public string? Reason { get; set; }

        public List<RebateItemDto>? RebateItems { get; set; }
    }
}