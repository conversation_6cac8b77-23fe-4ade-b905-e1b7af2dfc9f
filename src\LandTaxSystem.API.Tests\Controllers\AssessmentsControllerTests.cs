using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LandTaxSystem.API.Controllers;
using LandTaxSystem.Core.DTOs.Payment;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace LandTaxSystem.API.Tests.Controllers
{
    public class AssessmentsControllerTests
    {
        private readonly Mock<IPaymentService> _mockPaymentService;
        private readonly AssessmentsController _controller;

        public AssessmentsControllerTests()
        {
            _mockPaymentService = new Mock<IPaymentService>();
            _controller = new AssessmentsController(
                _mockPaymentService.Object,
                null, // Mock other dependencies as needed
                null
            );
        }

        [Fact]
        public async Task GetEligibleProperties_ReturnsOkResult_WithEligibleProperties()
        {
            // Arrange
            var mockProperties = new List<EligiblePropertyDto>
            {
                new EligiblePropertyDto
                {
                    PropertyId = Guid.NewGuid(),
                    FiscalYearId = Guid.NewGuid(),
                    PropertyAddress = "123 Test Street",
                    FiscalYearName = "2024-2025",
                    CalculatedTaxAmount = 5000,
                    AmountPaid = 3000,
                    UnpaidBalance = 2000,
                    PaymentIds = new List<Guid> { Guid.NewGuid() }
                }
            };

            _mockPaymentService.Setup(x => x.GetEligibleProperties(It.IsAny<Guid>()))
                .ReturnsAsync(mockProperties);

            // Act
            var result = await _controller.GetEligibleProperties();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<EligiblePropertyDto>>(okResult.Value);
            Assert.Single(returnValue);
            Assert.Equal("123 Test Street", returnValue[0].PropertyAddress);
        }

        [Fact]
        public async Task GetEligibleProperties_ReturnsBadRequest_WhenMunicipalityMissing()
        {
            // Arrange - No municipality claim set up
            
            // Act
            var result = await _controller.GetEligibleProperties();

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }
    }
}
