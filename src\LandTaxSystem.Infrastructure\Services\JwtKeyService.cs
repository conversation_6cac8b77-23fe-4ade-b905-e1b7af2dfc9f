using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace LandTaxSystem.Infrastructure.Services
{
    public class JwtKeyService
    {
        private readonly SymmetricSecurityKey _key;
        private readonly string _issuer;
        private readonly string _audience;

        public JwtKeyService(IConfiguration configuration)
        {
            var keyString = configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key is not configured");
            _key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(keyString));
            _issuer = configuration["Jwt:Issuer"] ?? throw new InvalidOperationException("JWT Issuer is not configured");
            _audience = configuration["Jwt:Audience"] ?? throw new InvalidOperationException("JWT Audience is not configured");
        }

        public SymmetricSecurityKey GetKey() => _key;
        public string GetIssuer() => _issuer;
        public string GetAudience() => _audience;
    }
}
