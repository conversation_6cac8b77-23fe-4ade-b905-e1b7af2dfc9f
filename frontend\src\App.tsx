import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "./context/AuthContext";
import { NotificationProvider } from "./context/NotificationContext";
import { ThemeProvider } from "./context/ThemeContext";
import LayoutWrapper from "./components/LayoutWrapper";
import ProtectedRoute from "./components/ProtectedRoute";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import SubmissionStatus from "./pages/SubmissionStatus";
import PropertyRegistration from "./pages/PropertyRegistration";
import MyProperties from "./pages/MyProperties";
import PropertyDetail from "./pages/PropertyDetail";
import TaxPayment from "./pages/TaxPayment";
import TaxPaymentPageNew from "./pages/TaxPaymentPageNew";
import ProvisionalPayment from "./pages/ProvisionalPayment";
import TaxPaymentSelection from "./pages/TaxPaymentSelection";
import BulkTaxPayment from "./pages/BulkTaxPayment";
import PaymentsPage from "./pages/PaymentsPage";
import UserManagement from "./pages/UserManagement";
import MunicipalityManagement from "./pages/MunicipalityManagement";
import FiscalYearManagement from "./pages/FiscalYearManagement";
import PropertyReview from "./pages/PropertyReview";
import AssessmentManagement from "./pages/AssessmentManagement";
import TaxConfigManagement from "./pages/TaxConfigManagement";
import TaxConfigDetail from "./components/TaxConfigDetail";
import AppealsList from "./pages/AppealsList";
import Appeals from "./pages/Appeals";
import AppealDetail from "./pages/AppealDetail";
import Negotiations from "./pages/Negotiations";
import CreateNegotiation from "./pages/CreateNegotiation";
import PendingUsers from "./pages/PendingUsers";
import ReturnFiling from "./pages/ReturnFiling";
import {
  PreliminaryAssessmentList,
  PreliminaryAssessmentDetail,
  PreliminaryAssessmentForm,
} from "./components/PreliminaryAssessment";
import { FinalAssessmentList, FinalAssessmentDetail, FinalAssessmentForm } from "./components/FinalAssessment";
import { SpecialPenaltyForm } from "./components/SpecialPenalty";
import { RebateForm } from "./components/Rebate";
import OutstandingTaxReport from "./pages/reports/OutstandingTaxReport";
import AppealDecision from "./pages/AppealDecision";
import ReceiptPayments from "./pages/ReceiptPayments";
import "./index.css";

function App() {
  return (
    <Router>
      <AuthProvider>
        <ThemeProvider>
          <NotificationProvider>
            <div className="min-h-screen bg-base-200">
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: "#ffffff",
                    color: "#1f2937",
                    border: "1px solid #e5e7eb",
                  },
                  success: {
                    duration: 3000,
                    iconTheme: {
                      primary: "#10b981",
                      secondary: "#ffffff",
                    },
                  },
                  error: {
                    duration: 4000,
                    iconTheme: {
                      primary: "#ef4444",
                      secondary: "#ffffff",
                    },
                  },
                }}
              />
              <LayoutWrapper>
                <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<Home />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/track-submission" element={<SubmissionStatus />} />
                  {/* Protected Routes */}
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/properties/register"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <PropertyRegistration />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/properties"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <MyProperties />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/return-filing"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <ReturnFiling />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/payments"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <PaymentsPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/properties/:id"
                    element={
                      <ProtectedRoute>
                        <PropertyDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/properties/review"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <PropertyReview />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/assessments"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <AssessmentManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/tax-config"
                    element={
                      <ProtectedRoute roles={["Admin", "Officer"]}>
                        <TaxConfigManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/reports/outstanding-tax"
                    element={
                      <ProtectedRoute roles={["Admin", "Officer"]}>
                        <OutstandingTaxReport />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/tax-config/:id"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <TaxConfigDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/municipalities"
                    element={
                      <ProtectedRoute roles={["CentralAdmin"]}>
                        <MunicipalityManagement />
                      </ProtectedRoute>
                    }
                  />{" "}
                  <Route
                    path="/fiscal-years"
                    element={
                      <ProtectedRoute roles={["CentralAdmin", "Admin", "Officer"]}>
                        <FiscalYearManagement />
                      </ProtectedRoute>
                    }
                  />{" "}
                  <Route
                    path="/tax-payment-options/:id"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <TaxPaymentSelection />
                      </ProtectedRoute>
                    }
                  />{" "}
                  <Route
                    path="/tax-payment/:id"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <TaxPaymentPageNew />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/tax-payment-legacy/:id"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <TaxPayment />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/provisional-payment/:id"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <ProvisionalPayment />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/bulk-tax-payment"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <BulkTaxPayment />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/users"
                    element={
                      <ProtectedRoute roles={["CentralAdmin"]}>
                        <UserManagement />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/users/pending"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <PendingUsers />
                      </ProtectedRoute>
                    }
                  />
                  {/* Appeals and Negotiations Routes */}
                  <Route
                    path="/appeals"
                    element={
                      <ProtectedRoute>
                        <AppealsList />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/appeals/:id"
                    element={
                      <ProtectedRoute>
                        <AppealDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/appeals/new"
                    element={
                      <ProtectedRoute>
                        <Appeals />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/negotiations"
                    element={
                      <ProtectedRoute>
                        <Negotiations />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/negotiations/create/:appealId"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <CreateNegotiation />
                      </ProtectedRoute>
                    }
                  />
                  {/* Preliminary Assessment Routes */}
                  <Route
                    path="/preliminary-assessments"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <PreliminaryAssessmentList />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/preliminary-assessments/new"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <PreliminaryAssessmentForm mode="create" />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/preliminary-assessments/:id"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <PreliminaryAssessmentDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/preliminary-assessments/:id/edit"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <PreliminaryAssessmentForm mode="edit" />
                      </ProtectedRoute>
                    }
                  />
                  {/* Final Assessment Routes */}
                  <Route
                    path="/final-assessments"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <FinalAssessmentList />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/final-assessments/new"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <FinalAssessmentForm mode="create" />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/final-assessments/:id"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <FinalAssessmentDetail />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/final-assessments/:id/edit"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <FinalAssessmentForm mode="edit" />
                      </ProtectedRoute>
                    }
                  />
                  {/* Special Penalty Routes */}
                  <Route
                    path="/special-penalties/new"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <SpecialPenaltyForm />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/special-penalties/:id/edit"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <SpecialPenaltyForm />
                      </ProtectedRoute>
                    }
                  />
                  {/* Rebate Routes */}
                  <Route
                    path="/rebates/new"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <RebateForm />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/rebates/:id/edit"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <RebateForm />
                      </ProtectedRoute>
                    }
                  />
                  {/* Appeal Decision Route */}
                  <Route
                    path="/appeal-decision"
                    element={
                      <ProtectedRoute roles={["Officer"]}>
                        <AppealDecision />
                      </ProtectedRoute>
                    }
                  />
                  {/* Receipt Payments Route */}
                  <Route
                    path="/receipt-payments"
                    element={
                      <ProtectedRoute roles={["Citizen"]}>
                        <ReceiptPayments />
                      </ProtectedRoute>
                    }
                  />
                  {/* Catch-all redirect */}
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </LayoutWrapper>
            </div>
          </NotificationProvider>
        </ThemeProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
