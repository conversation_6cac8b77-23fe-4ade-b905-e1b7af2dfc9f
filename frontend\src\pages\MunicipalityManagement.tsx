import React, { useState, useEffect } from "react";
import { municipalityService } from "../services/api";
import type { Municipality } from "../types";
import { AdminLayout, AdminBreadcrumb, AdminTabs } from "../components/admin";

interface MunicipalityForm {
  name: string;
  province: string;
  district: string;
  wardCount: number;
}

const MunicipalityManagement: React.FC = () => {
  const [municipalities, setMunicipalities] = useState<Municipality[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingMunicipality, setEditingMunicipality] = useState<Municipality | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"all" | "settings" | "statistics">("all");

  // Form state
  const [municipalityForm, setMunicipalityForm] = useState<MunicipalityForm>({
    name: "",
    province: "",
    district: "",
    wardCount: 0
  });

  useEffect(() => {
    loadMunicipalities();
  }, []);

  const loadMunicipalities = async () => {
    try {
      setLoading(true);
      setError("");

      const municipalities = await municipalityService.getAll();
      setMunicipalities(municipalities);
    } catch (error) {
      console.error("Failed to load municipalities:", error);
      setError("Failed to load municipalities. Please try again.");
      setMunicipalities([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMunicipality = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError("");
      setSuccess("");

      await municipalityService.create({
        name: municipalityForm.name,
        province: municipalityForm.province,
        district: municipalityForm.district,
        wardCount: municipalityForm.wardCount,
        // Empty default configs - these will be managed by municipal officers
        valuationRulesConfig: {},
        taxSlabsConfig: [],
        exemptionRulesConfig: { rules: [] }
      });
      setSuccess("Municipality created successfully!");
      setShowCreateForm(false);
      resetForm();
      loadMunicipalities();
    } catch (error) {
      console.error("Failed to create municipality:", error);
      setError("Failed to create municipality. Please try again.");
    }
  };

  const handleUpdateMunicipality = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingMunicipality) return;

    try {
      setError("");
      setSuccess("");

      await municipalityService.update(editingMunicipality.municipalityId, {
        name: municipalityForm.name,
        province: municipalityForm.province,
        district: municipalityForm.district,
        wardCount: municipalityForm.wardCount,
        // Preserve existing tax configurations
        valuationRulesConfig: editingMunicipality.valuationRulesConfig,
        taxSlabsConfig: editingMunicipality.taxSlabsConfig,
        exemptionRulesConfig: editingMunicipality.exemptionRulesConfig,
      });
      setSuccess("Municipality updated successfully!");
      setEditingMunicipality(null);
      resetForm();
      loadMunicipalities();
    } catch (error) {
      console.error("Failed to update municipality:", error);
      setError("Failed to update municipality. Please try again.");
    }
  };

  const resetForm = () => {
    setMunicipalityForm({
      name: "",
      province: "",
      district: "",
      wardCount: 0
    });
  };

  const startEdit = (municipality: Municipality) => {
    setEditingMunicipality(municipality);
    setMunicipalityForm({
      name: municipality.name,
      province: municipality.province || "",
      district: municipality.district || "",
      wardCount: municipality.wardCount || 0
    });
  };

  const cancelEdit = () => {
    setEditingMunicipality(null);
    setShowCreateForm(false);
    resetForm();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setMunicipalityForm({ 
      ...municipalityForm, 
      [name]: name === 'wardCount' ? parseFloat(value) || 0 : value 
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const filteredMunicipalities = municipalities.filter((municipality) =>
    municipality.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <AdminLayout title="Municipality Management">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4 text-base-content/70">Loading municipalities...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="Municipality Management"
      subtitle="Manage municipalities and their basic details"
      className="max-w-7xl mx-auto"
    >
      {/* Breadcrumbs */}
      <AdminBreadcrumb 
        items={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Municipality Management" }
        ]}
        className="mb-4"
      />

      {/* Success/Error Messages */}
      {success && (
        <div className="alert alert-success mb-6">
          <span>{success}</span>
        </div>
      )}
      {error && (
        <div className="alert alert-error mb-6">
          <span>{error}</span>
        </div>
      )}

      {/* Tabs */}
      <AdminTabs
        tabs={[
          { key: "all", label: "All Municipalities", count: municipalities.length },
          { key: "settings", label: "Default Settings" },
          { key: "statistics", label: "Statistics" }
        ]}
        activeTab={activeTab}
        onTabChange={(tabKey) => setActiveTab(tabKey as "all" | "settings" | "statistics")}
        className="mb-6"
      />

      <div className="flex justify-end mb-8">
        <button
          onClick={() => setShowCreateForm(true)}
          className="btn btn-primary"
        >
          + Create Municipality
        </button>
      </div>

        {/* Tab Content */}
        {activeTab === "all" && (
          <>
            {/* Search */}
            <div className="mb-6">
              <input
                type="text"
                placeholder="Search municipalities..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input input-bordered w-full sm:w-80"
              />
            </div>

            {/* Municipality List */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {filteredMunicipalities.map((municipality) => (
                <div
                  key={municipality.municipalityId}
                  className="card bg-base-100 shadow-xl"
                >
                  <div className="card-body">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="card-title text-base-content">
                          {municipality.name}
                        </h3>
                        <div className="space-y-1 mt-2">
                          <p className="text-sm text-base-content/70">
                            Province: {municipality.province || "N/A"}
                          </p>
                          <p className="text-sm text-base-content/70">
                            District: {municipality.district || "N/A"}
                          </p>
                          <p className="text-sm text-base-content/70">
                            Ward Count: {municipality.wardCount || "N/A"}
                          </p>
                          <p className="text-sm text-base-content/50 mt-2">
                            Created: {municipality.createdAt ? formatDate(municipality.createdAt) : "N/A"}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => startEdit(municipality)}
                        className="btn btn-secondary btn-sm"
                      >
                        Edit
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredMunicipalities.length === 0 && (
              <div className="card bg-base-100 shadow-xl">
                <div className="card-body text-center py-12">
                  <div className="text-4xl mb-4">���️</div>
                  <h3 className="text-lg font-medium text-base-content mb-2">
                    No municipalities found
                  </h3>
                  <p className="text-base-content/70 mb-6">
                    {searchTerm
                      ? "No municipalities match your search."
                      : "Create your first municipality to get started."}
                  </p>
                  <button
                    onClick={() => setShowCreateForm(true)}
                    className="btn btn-primary"
                  >
                    Create Municipality
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {activeTab === "settings" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">Default Municipality Settings</h2>
              <p className="text-base-content/70 mb-4">
                Configure default settings that will be applied to newly created municipalities.
              </p>
              
              <div className="divider">Tax Configuration Defaults</div>
              
              <div className="form-control mb-4">
                <label className="label">
                  <span className="label-text font-medium">Default Penalty Rate (%)</span>
                </label>
                <input
                  type="number"
                  className="input input-bordered w-full max-w-xs"
                  placeholder="e.g. 5%"
                />
              </div>
              
              <div className="form-control mb-4">
                <label className="label">
                  <span className="label-text font-medium">Default Discount Rate (%)</span>
                </label>
                <input
                  type="number"
                  className="input input-bordered w-full max-w-xs"
                  placeholder="e.g. 10%"
                />
              </div>
              
              <div className="mt-6">
                <button className="btn btn-primary">Save Default Settings</button>
              </div>
            </div>
          </div>
        )}

        {activeTab === "statistics" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">Municipality Statistics</h2>
              
              <div className="stats shadow mt-4">
                <div className="stat">
                  <div className="stat-title">Total Municipalities</div>
                  <div className="stat-value">{municipalities.length}</div>
                </div>
                
                <div className="stat">
                  <div className="stat-title">Total Provinces</div>
                  <div className="stat-value">
                    {new Set(municipalities.map(m => m.province).filter(Boolean)).size}
                  </div>
                </div>
                
                <div className="stat">
                  <div className="stat-title">Total Districts</div>
                  <div className="stat-value">
                    {new Set(municipalities.map(m => m.district).filter(Boolean)).size}
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <h3 className="text-lg font-medium mb-4">Municipality Distribution</h3>
                <div className="overflow-x-auto">
                  <table className="table table-zebra w-full">
                    <thead>
                      <tr>
                        <th>Province</th>
                        <th>Number of Municipalities</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.from(
                        new Set(municipalities.map(m => m.province).filter(Boolean))
                      ).map(province => (
                        <tr key={province}>
                          <td>{province}</td>
                          <td>
                            {municipalities.filter(m => m.province === province).length}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Create/Edit Municipality Modal */}
        {(showCreateForm || editingMunicipality) && (
          <div className="modal modal-open">
            <div className="modal-box w-11/12 max-w-2xl">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold">
                  {editingMunicipality
                    ? `Edit Municipality: ${editingMunicipality.name}`
                    : "Create New Municipality"}
                </h3>
                <button
                  onClick={cancelEdit}
                  className="btn btn-ghost btn-sm btn-circle"
                >
                  ✕
                </button>
              </div>

              <form
                onSubmit={
                  editingMunicipality
                    ? handleUpdateMunicipality
                    : handleCreateMunicipality
                }
              >
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Municipality Name*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={municipalityForm.name}
                    onChange={handleInputChange}
                    className="input input-bordered"
                    required
                  />
                </div>

                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Province*</span>
                  </label>
                  <input
                    type="text"
                    name="province"
                    value={municipalityForm.province}
                    onChange={handleInputChange}
                    className="input input-bordered"
                    required
                  />
                </div>

                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">District*</span>
                  </label>
                  <input
                    type="text"
                    name="district"
                    value={municipalityForm.district}
                    onChange={handleInputChange}
                    className="input input-bordered"
                    required
                  />
                </div>

                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Ward Count*</span>
                  </label>
                  <input
                    type="number"
                    name="wardCount"
                    value={municipalityForm.wardCount}
                    onChange={handleInputChange}
                    className="input input-bordered"
                    min="1"
                    required
                  />
                </div>

                <div className="modal-action">
                  <button
                    type="button"
                    onClick={cancelEdit}
                    className="btn btn-ghost"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    {editingMunicipality ? "Update" : "Create"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
    </AdminLayout>
  );
};

export default MunicipalityManagement;