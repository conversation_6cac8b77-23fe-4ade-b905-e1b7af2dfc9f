using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

public class Program
{
    private static readonly HttpClient client = new HttpClient();
    
    public static async Task Main(string[] args)
    {
        try
        {
            // First, login as admin
            var loginRequest = new
            {
                email = "<EMAIL>",
                password = "Admin@123"
            };
            
            var loginJson = JsonSerializer.Serialize(loginRequest);
            var loginContent = new StringContent(loginJson, Encoding.UTF8, "application/json");
            
            Console.WriteLine("Testing login...");
            var loginResponse = await client.PostAsync("http://localhost:5094/api/auth/login", loginContent);
            
            if (loginResponse.IsSuccessStatusCode)
            {
                var loginResult = await loginResponse.Content.ReadAsStringAsync();
                Console.WriteLine($"Login successful: {loginResult}");
                
                // Extract token
                var loginData = JsonSerializer.Deserialize<JsonElement>(loginResult);
                var token = loginData.GetProperty("token").GetString();
                
                // Set authorization header
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                
                // Now test users endpoint
                Console.WriteLine("\nTesting users endpoint...");
                var usersResponse = await client.GetAsync("http://localhost:5094/api/users");
                
                if (usersResponse.IsSuccessStatusCode)
                {
                    var usersResult = await usersResponse.Content.ReadAsStringAsync();
                    Console.WriteLine($"Users endpoint successful: {usersResult}");
                }
                else
                {
                    Console.WriteLine($"Users endpoint failed: {usersResponse.StatusCode} - {await usersResponse.Content.ReadAsStringAsync()}");
                }
            }
            else
            {
                Console.WriteLine($"Login failed: {loginResponse.StatusCode} - {await loginResponse.Content.ReadAsStringAsync()}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
