# Land Tax System Developer Guide

## Project Overview

- **Purpose**: GIS-Based Property Tax System for managing land tax assessments, payments, and appeals
- **Backend**: .NET 9.0 API with Entity Framework Core and PostgreSQL/PostGIS
- **Frontend**: React 19 with TypeScript, Vite, and TailwindCSS
- **Architecture**: Clean architecture with Core, Infrastructure, and API layers

## Key Technologies

### Backend
- .NET 9.0 with ASP.NET Core
- Entity Framework Core with PostgreSQL
- PostGIS for GIS functionality
- JWT Authentication
- Identity Framework for user management
- Docker for containerization

### Frontend
- React 19 with TypeScript
- Vite for build tooling
- TailwindCSS for styling
- React Router for navigation
- React Hook Form for form handling
- Leaflet/React-Leaflet for GIS maps

## Project Structure

### Backend
- **LandTaxSystem.Core**: Contains domain entities, interfaces, DTOs, and business logic
- **LandTaxSystem.Infrastructure**: Contains implementations of interfaces, data access, and external services
- **LandTaxSystem.API**: Contains API controllers, middleware, and configuration

### Frontend
- **src/components**: Reusable UI components
- **src/context**: React context providers (Auth, Notifications)
- **src/pages**: Page components for different routes
- **src/services**: API service clients
- **src/types**: TypeScript type definitions
- **src/utils**: Utility functions

## Key Features

- User management with different roles (Citizen, Officer, CentralAdmin)
- Property registration with GIS data
- Tax assessment and calculation
- Payment processing
- Appeals and negotiations
- Fiscal year management
- Municipality management
- Notifications

## User Roles

- **Citizen**: Property owners who register properties and pay taxes
- **Officer**: Municipal staff who review properties, create assessments, and process appeals
- **CentralAdmin**: System administrators who manage municipalities and fiscal years

## Development Workflow

- Use Docker Compose to run the application locally
- Backend API runs on port 8080
- PostgreSQL database runs on port 5432
- MailHog for email testing runs on port 8025

### Running the Application

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

## Best Practices

### Code Organization
- Follow clean architecture principles
- Keep business logic in the Core layer
- Keep data access in the Infrastructure layer
- Keep API endpoints in the API layer
- Use DTOs for data transfer between layers

### API Development
- Use RESTful API design principles
- Use appropriate HTTP methods (GET, POST, PUT, DELETE)
- Return appropriate HTTP status codes
- Use DTOs for request and response models
- Validate input data

### Frontend Development
- Use functional components with hooks
- Use TypeScript for type safety
- Use React Context for state management
- Follow component-based architecture
- Use React Router for navigation

### Authentication & Authorization
- Use JWT tokens for authentication
- Store tokens in localStorage
- Include tokens in Authorization header
- Use role-based authorization
- Protect routes based on user roles

### Database
- Use migrations for schema changes
- Use seed data for development
- Use appropriate indexes for performance
- Use appropriate relationships between entities

## Taskmaster Development Workflow

The fundamental development cycle is:
1. **`list`**: Show what needs to be done
2. **`next`**: Decide what to work on
3. **`show <id>`**: Get details for a specific task
4. **`expand <id>`**: Break down complex tasks
5. **Implement**: Write code and tests
6. **`update-subtask`**: Log progress
7. **`set-status`**: Mark tasks as done
8. **Repeat**

## Rule Improvement Guidelines

- Add new rules when:
  - A new technology/pattern is used in 3+ files
  - Common bugs could be prevented by a rule
  - Code reviews repeatedly mention the same feedback
  - New security or performance patterns emerge

- Modify existing rules when:
  - Better examples exist in the codebase
  - Additional edge cases are discovered
  - Related rules have been updated
  - Implementation details have changed

## Rule Structure

```markdown
---
description: Clear, one-line description of what the rule enforces
globs: path/to/files/*.ext, other/path/**/*
alwaysApply: boolean
---

- **Main Points in Bold**
  - Sub-points with details
  - Examples and explanations
```

## Troubleshooting

- Check Docker container logs for errors
- Verify database connection string
- Check JWT token configuration
- Verify API endpoints using Swagger UI at http://localhost:8080/swagger
- Test email functionality using MailHog at http://localhost:8025