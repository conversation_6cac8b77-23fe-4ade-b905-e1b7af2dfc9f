import api from './api';
import type { OutstandingTaxReportRequest, OutstandingTaxReportResponse } from '../types/outstandingTaxReport';

export const reportService = {
  // Get outstanding tax report
  async getOutstandingTaxReport(params: OutstandingTaxReportRequest): Promise<OutstandingTaxReportResponse> {
    const response = await api.get('/Reports/outstanding-tax', { params });
    return response.data;
  },
  
  // Get municipalities for dropdown
  async getMunicipalities() {
    const response = await api.get('/Municipalities/list');
    return response.data;
  },
  
  // Get fiscal years for dropdown
  async getFiscalYears() {
    const response = await api.get('/FiscalYears/list');
    return response.data;
  },
  
  // Export to CSV
  async exportToCsv(params: OutstandingTaxReportRequest) {
    const response = await api.get('/Reports/outstanding-tax/export/csv', { 
      params,
      responseType: 'blob' 
    });
    
    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'outstanding-tax-report.csv');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  },
  
  // Export to PDF
  async exportToPdf(params: OutstandingTaxReportRequest) {
    const response = await api.get('/Reports/outstanding-tax/export/pdf', { 
      params,
      responseType: 'blob' 
    });
    
    // Create download link
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'outstanding-tax-report.pdf');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  }
};

export default reportService;
