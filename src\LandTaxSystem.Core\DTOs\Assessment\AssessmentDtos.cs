using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using LandTaxSystem.Core.DTOs.Assessment;
using LandTaxSystem.Core.Validation;

namespace LandTaxSystem.Core.DTOs.Assessment
{
    public class AssessmentCreateDto
    {
        [Required]
        public Guid PropertyId { get; set; }

        [Required]
        public Guid FiscalYearId { get; set; }

        [Required]
        public int AssessmentYear { get; set; }

        public decimal? OriginalAmount { get; set; }
        public decimal? OverriddenValue { get; set; }
        public string? OverrideReason { get; set; }

        // New fields for enhanced tax calculation
        [Required]
        public decimal UpperAssessment { get; set; }
        [Required]
        public decimal LowerAssessment { get; set; }
        [Required]
        public decimal ActualAssessment { get; set; }

        [Range(0, 100)]
        public decimal? PenaltyPercent { get; set; }

        [Range(0, 100)]
        public decimal? DiscountPercent { get; set; }

        [Required]
        public List<AssessmentLineItemCreateDto> LineItems { get; set; } = new();
        
        // Added to support migration from UnderpaidAssessmentCreateDto
        public List<Guid> PaymentIds { get; set; } = new List<Guid>();
    }

    public class AssessmentResponseDto
    {
        public Guid AssessmentId { get; set; }
        public Guid PropertyId { get; set; }
        public string PropertyAddress { get; set; } = string.Empty;
        public int AssessmentYear { get; set; }
        public Guid FiscalYearId { get; set; }
        public string FiscalYearName { get; set; } = string.Empty;
        public decimal? OriginalAmount { get; set; }

        // Location hierarchy fields for property identification
        public string Province { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public string Municipality { get; set; } = string.Empty;
        public int WardNumber { get; set; }
        public string Street { get; set; } = string.Empty;
        public string ParcelNumber { get; set; } = string.Empty;

        public decimal CalculatedValue { get; set; }
        public decimal? OverriddenValue { get; set; }
        public decimal FinalAssessedValue { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal UpperAssessment { get; set; }
        public decimal LowerAssessment { get; set; }
        public decimal ActualAssessment { get; set; }
        public decimal PenaltyPercent { get; set; }
        public decimal DiscountPercent { get; set; }
        public decimal TotalPayable { get; set; }

        public string? AssessedByOfficerId { get; set; }
        public string? AssessedByOfficerName { get; set; }
        public DateTime AssessmentDate { get; set; }
        public string? OverrideReason { get; set; }

        public object? ExemptionAppliedDetails { get; set; }

        public string PaymentStatus { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public List<AssessmentLineItemDto> LineItems { get; set; } = new();
    }

    public class AssessmentPaymentUpdateDto
    {
        [Required]
        public string PaymentStatus { get; set; } = "Pending";

        [Required]
        public Guid? PaymentId { get; set; }
    }

    public class AssessmentFilterDto
    {
        public Guid? MunicipalityId { get; set; }
        public string? PaymentStatus { get; set; }
        public int? Year { get; set; }
        public DateTime? CreatedAfter { get; set; }
        public DateTime? CreatedBefore { get; set; }
        public decimal? MinTaxAmount { get; set; }
        public decimal? MaxTaxAmount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}
