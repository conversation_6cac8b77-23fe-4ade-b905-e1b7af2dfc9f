import React, { useState, useEffect } from "react";
import type {
  TaxSlab,
  PenaltyRule,
  MunicipalityTaxConfig,
} from "../services/taxConfigService";
import {
  updateMunicipalityTaxConfig,
  finalizeMunicipalityTaxConfig,
} from "../services/taxConfigService";
import KeyValueTable, { type KeyValueEntry } from "./KeyValueTable";

interface TaxConfigEditorProps {
  taxConfig: MunicipalityTaxConfig;
  onUpdate: (updatedConfig: MunicipalityTaxConfig) => void;
  onFinalize: (finalizedConfig: MunicipalityTaxConfig) => void;
}

const TaxConfigEditor: React.FC<TaxConfigEditorProps> = ({
  taxConfig,
  onUpdate,
  onFinalize,
}) => {
  const [taxSlabs, setTaxSlabs] = useState<TaxSlab[]>(
    taxConfig.taxSlabsConfig || []
  );
  const [penaltyRules, setPenaltyRules] = useState<PenaltyRule[]>(
    taxConfig.penaltyRules || []
  );
  const [defaultPenaltyPercent, setDefaultPenaltyPercent] = useState<number>(
    taxConfig.defaultPenaltyPercent || 0
  );
  const [defaultDiscountPercent, setDefaultDiscountPercent] = useState<number>(
    taxConfig.defaultDiscountPercent || 0
  );
  // Valuation Rules state
  const [landMVREntries, setLandMVREntries] = useState<KeyValueEntry[]>([]);
  const [buildingBaseRateEntries, setBuildingBaseRateEntries] = useState<
    KeyValueEntry[]
  >([]);
  const [annualDepreciationRate, setAnnualDepreciationRate] = useState<number>(
    taxConfig.valuationRulesConfig?.annualDepreciationRate || 0
  );

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [taxDefaultsSuccess, setTaxDefaultsSuccess] = useState<string | null>(
    null
  );

  // Initialize valuation rules data from taxConfig
  useEffect(() => {
    if (taxConfig.valuationRulesConfig) {
      const config = taxConfig.valuationRulesConfig;

      // Convert landMVR object to KeyValueEntry array
      const landEntries: KeyValueEntry[] = Object.entries(
        config.landMVR || {}
      ).map(([key, value]) => ({
        id: `land-${key}-${Date.now()}`,
        key,
        value: value as number,
      }));
      setLandMVREntries(landEntries);

      // Convert buildingBaseRatePerSqm object to KeyValueEntry array
      const buildingEntries: KeyValueEntry[] = Object.entries(
        config.buildingBaseRatePerSqm || {}
      ).map(([key, value]) => ({
        id: `building-${key}-${Date.now()}`,
        key,
        value: value as number,
      }));
      setBuildingBaseRateEntries(buildingEntries);
    }
  }, [taxConfig]);

  // Handle adding a new tax slab
  const handleAddTaxSlab = () => {
    const newSlab: TaxSlab = {
      minValue: 0,
      maxValue: 0,
      rate: 0,
      description: "",
    };
    setTaxSlabs([...taxSlabs, newSlab]);
  };

  // Handle adding a new penalty rule
  const handleAddPenaltyRule = () => {
    const newRule: PenaltyRule = {
      daysAfterDue: 0,
      rate: 0,
      maxPenalty: 0,
      description: "",
    };
    setPenaltyRules([...penaltyRules, newRule]);
  };

  // Handle updating a tax slab
  const handleTaxSlabChange = (
    index: number,
    field: keyof TaxSlab,
    value: string | number
  ) => {
    const updatedSlabs = [...taxSlabs];
    updatedSlabs[index] = {
      ...updatedSlabs[index],
      [field]: field === "description" ? value : parseFloat(value.toString()),
    };
    setTaxSlabs(updatedSlabs);
  };

  // Handle updating a penalty rule
  const handlePenaltyRuleChange = (
    index: number,
    field: keyof PenaltyRule,
    value: string | number
  ) => {
    const updatedRules = [...penaltyRules];
    updatedRules[index] = {
      ...updatedRules[index],
      [field]: field === "description" ? value : parseFloat(value.toString()),
    };
    setPenaltyRules(updatedRules);
  };

  // Handle removing a tax slab
  const handleRemoveTaxSlab = (index: number) => {
    const updatedSlabs = [...taxSlabs];
    updatedSlabs.splice(index, 1);
    setTaxSlabs(updatedSlabs);
  };

  // Handle removing a penalty rule
  const handleRemovePenaltyRule = (index: number) => {
    const updatedRules = [...penaltyRules];
    updatedRules.splice(index, 1);
    setPenaltyRules(updatedRules);
  };

  // Save changes to the tax configuration
  const handleSaveChanges = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Validate tax slabs
      if (taxSlabs.length === 0) {
        setError("At least one tax slab is required");
        setIsSubmitting(false);
        return;
      }

      // Validate penalty rules
      if (penaltyRules.length === 0) {
        setError("At least one penalty rule is required");
        setIsSubmitting(false);
        return;
      }

      // Validate default penalty and discount percentages
      if (defaultPenaltyPercent < 0 || defaultPenaltyPercent > 100) {
        setError("Default penalty percentage must be between 0 and 100");
        setIsSubmitting(false);
        return;
      }

      if (defaultDiscountPercent < 0 || defaultDiscountPercent > 100) {
        setError("Default discount percentage must be between 0 and 100");
        setIsSubmitting(false);
        return;
      }

      // Validate annual depreciation rate
      if (annualDepreciationRate < 0 || annualDepreciationRate > 1) {
        setError("Annual depreciation rate must be between 0 and 1");
        setIsSubmitting(false);
        return;
      }

      // Validate land MVR entries
      for (const entry of landMVREntries) {
        if (!entry.key.trim()) {
          setError("All land use types must have a name");
          setIsSubmitting(false);
          return;
        }
        if (typeof entry.value !== "number" || entry.value < 0) {
          setError("All land MVR values must be non-negative numbers");
          setIsSubmitting(false);
          return;
        }
      }

      // Validate building base rate entries
      for (const entry of buildingBaseRateEntries) {
        if (!entry.key.trim()) {
          setError("All building types must have a name");
          setIsSubmitting(false);
          return;
        }
        if (typeof entry.value !== "number" || entry.value < 0) {
          setError(
            "All building base rate values must be non-negative numbers"
          );
          setIsSubmitting(false);
          return;
        }
      }

      // Check for overlapping tax slabs
      for (let i = 0; i < taxSlabs.length; i++) {
        for (let j = i + 1; j < taxSlabs.length; j++) {
          const slabA = taxSlabs[i];
          const slabB = taxSlabs[j];

          if (
            (slabA.minValue <= slabB.minValue &&
              slabA.maxValue >= slabB.minValue) ||
            (slabA.minValue <= slabB.maxValue &&
              slabA.maxValue >= slabB.maxValue) ||
            (slabA.minValue >= slabB.minValue &&
              slabA.maxValue <= slabB.maxValue)
          ) {
            setError(`Tax slabs ${i + 1} and ${j + 1} have overlapping ranges`);
            setIsSubmitting(false);
            return;
          }
        }
      } // Convert KeyValueEntry arrays to objects
      const landMVR: Record<string, number> = {};
      landMVREntries.forEach((entry) => {
        landMVR[entry.key] =
          typeof entry.value === "number"
            ? entry.value
            : parseFloat(entry.value.toString()) || 0;
      });

      const buildingBaseRatePerSqm: Record<string, number> = {};
      buildingBaseRateEntries.forEach((entry) => {
        buildingBaseRatePerSqm[entry.key] =
          typeof entry.value === "number"
            ? entry.value
            : parseFloat(entry.value.toString()) || 0;
      });

      const updatedConfig = await updateMunicipalityTaxConfig(
        taxConfig.municipalityTaxConfigId,
        {
          taxSlabsConfig: taxSlabs,
          penaltyRules: penaltyRules,
          defaultPenaltyPercent: defaultPenaltyPercent,
          defaultDiscountPercent: defaultDiscountPercent,
          valuationRules: {
            LandMVR: JSON.stringify(landMVR),
            BuildingBaseRatePerSqm: JSON.stringify(buildingBaseRatePerSqm),
            AnnualDepreciationRate: annualDepreciationRate.toString(),
          },
        }
      );

      setTaxDefaultsSuccess("Tax configuration updated successfully!");
      onUpdate(updatedConfig);
      setIsSubmitting(false);
    } catch (err: unknown) {
      console.error("Failed to update tax configuration:", err);
      setError("Failed to update tax configuration. Please try again.");
      setIsSubmitting(false);
    }
  };

  // Finalize the tax configuration
  const handleFinalize = async () => {
    if (
      window.confirm(
        "Are you sure you want to finalize this tax configuration? This action cannot be undone."
      )
    ) {
      try {
        setIsSubmitting(true);
        setError(null);

        const finalizedConfig = await finalizeMunicipalityTaxConfig(
          taxConfig.municipalityTaxConfigId
        );

        onFinalize(finalizedConfig);
        setIsSubmitting(false);
      } catch (err: unknown) {
        console.error("Failed to finalize tax configuration:", err);
        setError("Failed to finalize tax configuration. Please try again.");
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">
        Edit Tax Configuration for {taxConfig.municipalityName} -{" "}
        {taxConfig.fiscalYearName}
      </h2>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
          <p>{error}</p>
        </div>
      )}

      {taxDefaultsSuccess && (
        <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4">
          <p>{taxDefaultsSuccess}</p>
        </div>
      )}

      {/* Tax Slabs Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Tax Slabs</h3>
          <button
            type="button"
            onClick={handleAddTaxSlab}
            className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm"
            disabled={isSubmitting}
          >
            Add Tax Slab
          </button>
        </div>

        {taxSlabs.length === 0 ? (
          <p className="text-gray-500 italic">No tax slabs defined</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Min Value
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Max Value
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Rate (%)
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Description
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {taxSlabs.map((slab, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        value={slab.minValue}
                        onChange={(e) =>
                          handleTaxSlabChange(index, "minValue", e.target.value)
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        value={slab.maxValue}
                        onChange={(e) =>
                          handleTaxSlabChange(index, "maxValue", e.target.value)
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={slab.rate}
                        onChange={(e) =>
                          handleTaxSlabChange(index, "rate", e.target.value)
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="text"
                        value={slab.description}
                        onChange={(e) =>
                          handleTaxSlabChange(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <button
                        type="button"
                        onClick={() => handleRemoveTaxSlab(index)}
                        className="text-red-600 hover:text-red-800"
                        disabled={isSubmitting}
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Default Penalty and Discount Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">
            Default Assessment Percentages
          </h3>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg mb-4">
          <p className="text-sm text-blue-800 mb-2">
            These default percentages will be applied to property assessments
            when specific values are not provided.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Default Penalty Percentage
            </label>
            <div className="flex items-center">
              <input
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={defaultPenaltyPercent}
                onChange={(e) =>
                  setDefaultPenaltyPercent(parseFloat(e.target.value) || 0)
                }
                className={`border rounded px-2 py-1 w-full ${
                  defaultPenaltyPercent < 0 || defaultPenaltyPercent > 100
                    ? "border-red-500"
                    : ""
                }`}
                disabled={isSubmitting}
              />
              <span className="ml-2">%</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Default penalty percentage applied to assessments (0-100%)
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Default Discount Percentage
            </label>
            <div className="flex items-center">
              <input
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={defaultDiscountPercent}
                onChange={(e) =>
                  setDefaultDiscountPercent(parseFloat(e.target.value) || 0)
                }
                className={`border rounded px-2 py-1 w-full ${
                  defaultDiscountPercent < 0 || defaultDiscountPercent > 100
                    ? "border-red-500"
                    : ""
                }`}
                disabled={isSubmitting}
              />
              <span className="ml-2">%</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Default discount percentage applied to assessments (0-100%)
            </p>
          </div>
        </div>
      </div>

      {/* Penalty Rules Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Penalty Rules</h3>
          <button
            type="button"
            onClick={handleAddPenaltyRule}
            className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm"
            disabled={isSubmitting}
          >
            Add Penalty Rule
          </button>
        </div>

        {penaltyRules.length === 0 ? (
          <p className="text-gray-500 italic">No penalty rules defined</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Days After Due
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Rate (%)
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Max Penalty (%)
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Description
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {penaltyRules.map((rule, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        value={rule.daysAfterDue}
                        onChange={(e) =>
                          handlePenaltyRuleChange(
                            index,
                            "daysAfterDue",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={rule.rate}
                        onChange={(e) =>
                          handlePenaltyRuleChange(index, "rate", e.target.value)
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={rule.maxPenalty}
                        onChange={(e) =>
                          handlePenaltyRuleChange(
                            index,
                            "maxPenalty",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="text"
                        value={rule.description}
                        onChange={(e) =>
                          handlePenaltyRuleChange(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <button
                        type="button"
                        onClick={() => handleRemovePenaltyRule(index)}
                        className="text-red-600 hover:text-red-800"
                        disabled={isSubmitting}
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Valuation Rules Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Valuation Rules</h3>
        </div>

        <div className="bg-yellow-50 p-4 rounded-lg mb-4">
          <p className="text-sm text-yellow-800 mb-2">
            Configure valuation rules for property assessment. These rules will
            be used to calculate land market value reference (MVR) and building
            base rates.
          </p>
        </div>

        <div className="space-y-6">
          {/* Land MVR Section */}
          <div>
            <h4 className="text-md font-medium mb-2">
              Land Market Value Reference (MVR)
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              Define land market value reference rates by land use type (e.g.,
              Residential, Commercial, Industrial).
            </p>
            <KeyValueTable
              items={landMVREntries}
              onChange={setLandMVREntries}
              keyLabel="Land Use Type"
              valueLabel="MVR Rate"
              valueType="number"
              readOnly={taxConfig.isFinalized || isSubmitting}
              defaultEntries={[
                { key: "Residential", value: 0 },
                { key: "Commercial", value: 0 },
                { key: "Industrial", value: 0 },
              ]}
              addButtonLabel="Add Land Type"
            />
          </div>

          {/* Building Base Rate Section */}
          <div>
            <h4 className="text-md font-medium mb-2">
              Building Base Rate per Sqm
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              Define building base construction rates by building type (e.g.,
              Residential, RCC, Commercial, Industrial).
            </p>
            <KeyValueTable
              items={buildingBaseRateEntries}
              onChange={setBuildingBaseRateEntries}
              keyLabel="Building Type"
              valueLabel="Base Rate per Sqm"
              valueType="number"
              readOnly={taxConfig.isFinalized || isSubmitting}
              defaultEntries={[
                { key: "Residential", value: 0 },
                { key: "RCC", value: 0 },
                { key: "Commercial", value: 0 },
                { key: "Industrial", value: 0 },
              ]}
              addButtonLabel="Add Building Type"
            />
          </div>

          {/* Annual Depreciation Rate Section */}
          <div>
            <h4 className="text-md font-medium mb-2">
              Annual Depreciation Rate
            </h4>
            <p className="text-sm text-gray-600 mb-3">
              Set the annual depreciation rate for building valuation (as a
              decimal, e.g., 0.05 for 5%).
            </p>
            <div className="max-w-xs">
              <input
                type="number"
                min="0"
                max="1"
                step="0.01"
                value={annualDepreciationRate}
                onChange={(e) =>
                  setAnnualDepreciationRate(parseFloat(e.target.value) || 0)
                }
                className={`border rounded px-3 py-2 w-full ${
                  annualDepreciationRate < 0 || annualDepreciationRate > 1
                    ? "border-red-500"
                    : "border-gray-300"
                }`}
                disabled={taxConfig.isFinalized || isSubmitting}
                placeholder="0.05"
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter as decimal (0.05 = 5%, 0.10 = 10%)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 mt-6">
        <button
          type="button"
          onClick={handleSaveChanges}
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Saving..." : "Save Changes"}
        </button>
        {!taxConfig.isFinalized && (
          <button
            type="button"
            onClick={handleFinalize}
            className="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Processing..." : "Finalize Configuration"}
          </button>
        )}
      </div>
    </div>
  );
};

export default TaxConfigEditor;
