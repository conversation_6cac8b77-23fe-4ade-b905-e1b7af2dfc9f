using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using LandTaxSystem.Core.Models;

namespace LandTaxSystem.Core.DTOs.TaxConfig
{
    public class ValuationRulesConfigDto
    {
        public Dictionary<string, decimal> LandMVR { get; set; } = new();
        public Dictionary<string, decimal> BuildingBaseRatePerSqm { get; set; } = new();
        public decimal AnnualDepreciationRate { get; set; }
    }
    public class MunicipalityTaxConfigCreateDto
    {
        [Required]
        public Guid MunicipalityId { get; set; }

        [Required]
        public Guid FiscalYearId { get; set; }

        public List<TaxSlab>? TaxSlabsConfig { get; set; }

        public List<PenaltyRule>? PenaltyRules { get; set; }

        public ValuationRulesConfigDto? ValuationRulesConfig { get; set; }
    }
    public class MunicipalityTaxConfigResponseDto
    {
        public Guid MunicipalityTaxConfigId { get; set; }
        public Guid MunicipalityId { get; set; }
        public string MunicipalityName { get; set; } = string.Empty;
        public Guid FiscalYearId { get; set; }
        public string FiscalYearName { get; set; } = string.Empty;
        public List<TaxSlab> TaxSlabsConfig { get; set; } = new List<TaxSlab>();
        public List<PenaltyRule> PenaltyRules { get; set; } = new List<PenaltyRule>();
        public ValuationRulesConfigDto? ValuationRulesConfig { get; set; }
        public bool IsFinalized { get; set; }
        public DateTime? FinalizedAt { get; set; }
        public string? FinalizedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
    public class MunicipalityTaxConfigUpdateDto
    {
        public List<TaxSlab>? TaxSlabsConfig { get; set; }
        public List<PenaltyRule>? PenaltyRules { get; set; }
        public ValuationRulesConfigDto? ValuationRulesConfig { get; set; }
    }

    public class MunicipalityTaxConfigFinalizeDto
    {
        [Required]
        public bool Finalize { get; set; } = true;
    }
}
