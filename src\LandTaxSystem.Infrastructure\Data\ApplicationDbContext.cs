using System;
using LandTaxSystem.Core.Entities;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace LandTaxSystem.Infrastructure.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Province> Provinces { get; set; } = null!;
        public DbSet<District> Districts { get; set; } = null!;
        public DbSet<Municipality> Municipalities { get; set; } = null!;
        public DbSet<Property> Properties { get; set; } = null!;
        public DbSet<Assessment> Assessments { get; set; } = null!;
        public DbSet<Payment> Payments { get; set; } = null!;
        public DbSet<AuditLog> AuditLogs { get; set; } = null!;
        public DbSet<FiscalYear> FiscalYears { get; set; } = null!;
        public DbSet<MunicipalityTaxConfig> MunicipalityTaxConfigs { get; set; } = null!;
        public DbSet<Appeal> Appeals { get; set; } = null!;
        public DbSet<Negotiation> Negotiations { get; set; } = null!;
        public DbSet<Notification> Notifications { get; set; } = null!;
        public DbSet<TaxPeriod> TaxPeriods { get; set; } = null!;
        public DbSet<LandDetail> LandDetails { get; set; } = null!;
        public DbSet<ReturnFiling> ReturnFilings { get; set; } = null!;
        public DbSet<PreliminaryAssessment> PreliminaryAssessments { get; set; } = null!;
        public DbSet<PreliminaryAssessmentDetail> PreliminaryAssessmentDetails { get; set; } = null!;
        public DbSet<FinalAssessment> FinalAssessments { get; set; } = null!;
        public DbSet<FinalAssessmentDetail> FinalAssessmentDetails { get; set; } = null!;
        public DbSet<Rebate> Rebates { get; set; } = null!;
        public DbSet<RebateItem> RebateItems { get; set; } = null!;
        public DbSet<SpecialPenalty> SpecialPenalties { get; set; } = null!;
        public DbSet<PreliminaryAssessmentStatusHistory> PreliminaryAssessmentStatusHistories { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure entities based on the PRD

            // Province
            builder.Entity<Province>(entity =>
            {
                entity.HasKey(e => e.ProvinceId);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Code).HasMaxLength(20);

                // Configure relationship with Districts
                entity.HasMany(p => p.Districts)
                      .WithOne(d => d.Province)
                      .HasForeignKey(d => d.ProvinceId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // District
            builder.Entity<District>(entity =>
            {
                entity.HasKey(e => e.DistrictId);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Code).HasMaxLength(20);

                // Configure relationship with Province (already defined in Province entity)
                // Configure relationship with Municipalities
                entity.HasMany(d => d.Municipalities)
                      .WithOne(m => m.District)
                      .HasForeignKey(m => m.DistrictId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Municipality
            builder.Entity<Municipality>(entity =>
            {
                entity.HasKey(e => e.MunicipalityId);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);

                // Configure relationship with District (already defined in District entity)

                entity.Property(e => e.ValuationRulesConfigJson).HasColumnType("jsonb").HasDefaultValue("{}");
                entity.Property(e => e.TaxSlabsConfigJson).HasColumnType("jsonb").HasDefaultValue("[]");
                entity.Property(e => e.ExemptionRulesConfigJson).HasColumnType("jsonb").HasDefaultValue("{}");
            });

            // FiscalYear
            builder.Entity<FiscalYear>(entity =>
            {
                entity.HasKey(e => e.FiscalYearId);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                // StartDate and EndDate are now nullable
            });

            // MunicipalityTaxConfig
            builder.Entity<MunicipalityTaxConfig>(entity =>
            {
                entity.HasKey(e => e.MunicipalityTaxConfigId);
                entity.Property(e => e.TaxSlabsConfigJson).HasColumnType("jsonb").HasDefaultValue("[]");
                entity.Property(e => e.PenaltyRulesJson).HasColumnType("jsonb").HasDefaultValue("{}");

                // Configure relationships
                entity.HasOne(e => e.Municipality)
                    .WithMany(m => m.MunicipalityTaxConfigs)
                    .HasForeignKey(e => e.MunicipalityId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.FiscalYear)
                    .WithMany(f => f.MunicipalityTaxConfigs)
                    .HasForeignKey(e => e.FiscalYearId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Property
            builder.Entity<Property>(entity =>
            {
                entity.HasKey(e => e.PropertyId);
                entity.Property(e => e.Address).IsRequired().HasMaxLength(255);
                entity.Property(e => e.LandAreaSqM).HasPrecision(10, 2);
                entity.Property(e => e.UsageType).IsRequired().HasMaxLength(20);
                entity.Property(e => e.BuildingBuiltUpAreaSqM).HasPrecision(10, 2);
                entity.Property(e => e.BuildingConstructionType).HasMaxLength(20);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);

                // Configure GIS data column
                entity.Property(e => e.ParcelGeometry).HasColumnType("geometry(Polygon, 4326)");

                // Ignore computed properties (not mapped to database)
                entity.Ignore(e => e.EstimatedTaxAmount);

                // Configure relationships
                entity.HasOne(e => e.Owner)
                    .WithMany()
                    .HasForeignKey(e => e.OwnerUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Municipality)
                    .WithMany(m => m.Properties)
                    .HasForeignKey(e => e.MunicipalityId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // LandDetail
            builder.Entity<LandDetail>(entity =>
            {
                entity.HasKey(e => e.LandDetailId);

                // Configure relationship with Property
                entity.HasOne(ld => ld.Property)
                    .WithMany(p => p.LandDetails)
                    .HasForeignKey(ld => ld.PropertyId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Configure indexes
                entity.HasIndex(ld => ld.PropertyId);
                entity.HasIndex(ld => ld.KittaNo);
                entity.HasIndex(ld => ld.FiscalYear);
                entity.HasIndex(ld => ld.LastFyForInclusion);
            });

            // ReturnFiling
            builder.Entity<ReturnFiling>(entity =>
            {
                entity.HasKey(e => e.ReturnFilingId);

                // Configure relationship with Property
                entity.HasOne(rf => rf.Property)
                    .WithMany()
                    .HasForeignKey(rf => rf.PropertyId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Configure relationship with FiscalYear
                entity.HasOne(rf => rf.FiscalYear)
                    .WithMany()
                    .HasForeignKey(rf => rf.FiscalYearId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Configure relationship with User
                entity.HasOne(rf => rf.SubmittedByUser)
                    .WithMany()
                    .HasForeignKey(rf => rf.SubmittedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Create a unique constraint for PropertyId and FiscalYearId to prevent duplicate filings
                entity.HasIndex(rf => new { rf.PropertyId, rf.FiscalYearId }).IsUnique();

                // Additional indexes
                entity.HasIndex(rf => rf.SubmissionDate);
                entity.HasIndex(rf => rf.SubmittedByUserId);
            });

            // Assessment
            builder.Entity<Assessment>(entity =>
            {
                entity.HasKey(e => e.AssessmentId);
                entity.Property(e => e.CalculatedValue).HasPrecision(15, 2);
                entity.Property(e => e.OverriddenValue).HasPrecision(15, 2);
                entity.Property(e => e.FinalAssessedValue).HasPrecision(15, 2);
                entity.Property(e => e.TaxAmount).HasPrecision(15, 2);
                entity.Property(e => e.PaymentStatus).IsRequired().HasMaxLength(20);
                entity.Property(e => e.ExemptionAppliedDetailsJson).HasColumnType("jsonb").HasDefaultValue("{}");

                // Configure relationships
                entity.HasOne(e => e.Property)
                    .WithMany(p => p.Assessments)
                    .HasForeignKey(e => e.PropertyId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.AssessedByOfficer)
                    .WithMany()
                    .HasForeignKey(e => e.AssessedByOfficerId)
                    .OnDelete(DeleteBehavior.SetNull);

                // Create a unique constraint for PropertyId and AssessmentYear
                entity.HasIndex(e => new { e.PropertyId, e.AssessmentYear }).IsUnique();
            });

            // Payment
            builder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.PaymentId);
                entity.Property(e => e.AmountPaid).HasPrecision(15, 2);
                entity.Property(e => e.PaymentGateway).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TransactionId).HasMaxLength(100);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);

                // Configure indexes for performance
                entity.HasIndex(e => new { e.PropertyId, e.FiscalYearId })
                    .HasDatabaseName("IX_Payments_PropertyId_FiscalYearId");
                entity.HasIndex(e => e.Provisional)
                    .HasDatabaseName("IX_Payments_Provisional");

                // Configure relationships
                entity.HasOne(p => p.Assessment)
                    .WithMany(a => a.Payments)
                    .HasForeignKey(p => p.AssessmentId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(p => p.Property)
                    .WithMany()
                    .HasForeignKey(p => p.PropertyId)
                    .OnDelete(DeleteBehavior.Restrict);
                entity.HasOne(p => p.FiscalYear)
                    .WithMany()
                    .HasForeignKey(p => p.FiscalYearId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // AuditLog
            builder.Entity<AuditLog>(entity =>
            {
                entity.HasKey(e => e.LogId);
                entity.Property(e => e.LogId).UseIdentityColumn();
                entity.Property(e => e.ActionType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.EntityType).HasMaxLength(50);
                entity.Property(e => e.OldValueJson).HasColumnType("jsonb");
                entity.Property(e => e.NewValueJson).HasColumnType("jsonb");

                // Configure relationships
                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // ApplicationUser
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);

                // New configurations for PRD v1.2
                entity.Property(e => e.PAN).HasMaxLength(20); // Serves as TaxPayerID for login
                entity.HasIndex(e => e.PAN).IsUnique();

                entity.Property(e => e.SubmissionNumber).HasMaxLength(50); // Unique submission tracking number
                entity.HasIndex(e => e.SubmissionNumber).IsUnique();

                entity.Property(e => e.TwoGenerations).HasMaxLength(500); // Assuming max length for generational info

                entity.Property(e => e.Status).IsRequired().HasMaxLength(20); // e.g., PendingApproval, Active, Rejected
                // For PostgreSQL, add a check constraint for Status values
                entity.ToTable(tb => tb.HasCheckConstraint("CK_User_Status", "\"Status\" IN ('PendingApproval', 'Active', 'Rejected')"));

                // New properties for enhanced user registration
                entity.Property(e => e.DateOfBirth).IsRequired();
                entity.Property(e => e.Gender).IsRequired().HasMaxLength(10);
                entity.Property(e => e.CitizenshipNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.PermanentAddress).IsRequired().HasMaxLength(255);
                entity.Property(e => e.WardNumber).IsRequired().HasMaxLength(10);
                entity.Property(e => e.ToleStreet).IsRequired().HasMaxLength(100);
                entity.Property(e => e.DocumentPath).IsRequired().HasMaxLength(500);
            });

            // Appeal - Consolidated configuration
            builder.Entity<Appeal>(entity =>
            {
                entity.HasKey(e => e.AppealId);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Reason).IsRequired().HasMaxLength(500);

                // Add properties from ExtendedAppeal
                entity.Property(e => e.OfficeCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.LocationCode).HasMaxLength(50);
                entity.Property(e => e.TaxpayerName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.TaxpayerAddress).HasMaxLength(255);
                entity.Property(e => e.AppealSubject).IsRequired().HasMaxLength(255);
                entity.Property(e => e.TaxDeterminationOrderNumber).HasMaxLength(50);
                entity.Property(e => e.AppealAuthority).HasMaxLength(100);
                entity.Property(e => e.RegistrationNumber).HasMaxLength(50);
                entity.Property(e => e.OrderNumber).HasMaxLength(50);
                entity.Property(e => e.AppealDescription).HasMaxLength(1000);

                // Configure relationships
                entity.HasOne(e => e.Assessment)
                    .WithMany()
                    .HasForeignKey(e => e.AssessmentId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.TaxPayer)
                    .WithMany()
                    .HasForeignKey(e => e.TaxPayerId)
                    .OnDelete(DeleteBehavior.Restrict);

                // For PostgreSQL, add a check constraint for Status values
                entity.ToTable(tb => tb.HasCheckConstraint("CK_Appeal_Status", "\"Status\" IN ('Pending', 'Resolved')"));
            });

            // Negotiation
            builder.Entity<Negotiation>(entity =>
            {
                entity.HasKey(e => e.NegotiationId);
                entity.Property(e => e.NegotiatedAmount).HasPrecision(15, 2).IsRequired();

                // Configure relationships
                entity.HasOne(e => e.Appeal)
                    .WithOne(a => a.Negotiation)
                    .HasForeignKey<Negotiation>(e => e.AppealId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Officer)
                    .WithMany()
                    .HasForeignKey(e => e.OfficerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // TaxPeriod relationship with Appeal
            builder.Entity<Appeal>()
                .HasMany(a => a.TaxPeriods)
                .WithOne()
                .HasForeignKey(tp => tp.AppealId)
                .OnDelete(DeleteBehavior.Cascade);

            // TaxPeriod configuration
            builder.Entity<TaxPeriod>(entity =>
            {
                entity.HasKey(e => e.TaxPeriodId);

                // New fields for updated form
                entity.Property(e => e.FiscalYear).IsRequired().HasMaxLength(20);
                entity.Property(e => e.StartDate).IsRequired().HasMaxLength(20);
                entity.Property(e => e.EndDate).IsRequired().HasMaxLength(20);

                // Legacy fields kept for backward compatibility
                entity.Property(e => e.Year).HasMaxLength(20);
                entity.Property(e => e.Period).HasMaxLength(20);
                entity.Property(e => e.TaxPeriodValue).HasMaxLength(50);
                entity.Property(e => e.AppealSubject).HasMaxLength(255);
                entity.Property(e => e.AppealAmount).HasMaxLength(50);

                // Configure relationships - Update to reference Appeal
                entity.HasOne(e => e.Appeal)
                    .WithMany(a => a.TaxPeriods)
                    .HasForeignKey(e => e.AppealId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // PreliminaryAssessment configuration
            builder.Entity<PreliminaryAssessment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TaxpayerRegistration).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TaxpayerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Status).HasDefaultValue("Draft").HasMaxLength(20);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
                entity.Property(e => e.CreatedBy).IsRequired();
                entity.Property(e => e.MunicipalityId).IsRequired();

                entity.HasMany(e => e.TaxDetails)
                      .WithOne(e => e.PreliminaryAssessment)
                      .HasForeignKey(e => e.PreliminaryAssessmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.UpdatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.UpdatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Municipality)
                      .WithMany()
                      .HasForeignKey(e => e.MunicipalityId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ReturnFiling)
                      .WithMany()
                      .HasForeignKey(e => e.ReturnFilingId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // PreliminaryAssessmentDetail configuration
            builder.Entity<PreliminaryAssessmentDetail>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AssessedAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.AdditionalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Penalty).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Interest).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Total).HasColumnType("decimal(18,2)");
            });

            // FinalAssessment configuration
            builder.Entity<FinalAssessment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TaxpayerRegistration).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TaxpayerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Status).HasDefaultValue("Draft").HasMaxLength(20);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
                entity.Property(e => e.CreatedBy).IsRequired();
                entity.Property(e => e.FiscalYearId).IsRequired();
                entity.Property(e => e.MunicipalityId).IsRequired();

                entity.HasMany(e => e.TaxDetails)
                      .WithOne(e => e.FinalAssessment)
                      .HasForeignKey(e => e.FinalAssessmentId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.CreatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.UpdatedByUser)
                      .WithMany()
                      .HasForeignKey(e => e.UpdatedBy)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.FiscalYear)
                      .WithMany()
                      .HasForeignKey(e => e.FiscalYearId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Municipality)
                      .WithMany()
                      .HasForeignKey(e => e.MunicipalityId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ReturnFiling)
                      .WithMany()
                      .HasForeignKey(e => e.ReturnFilingId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.PreliminaryAssessment)
                      .WithMany()
                      .HasForeignKey(e => e.PreliminaryAssessmentId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // FinalAssessmentDetail configuration
            builder.Entity<FinalAssessmentDetail>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AssessedAmount).HasColumnType("decimal(15,2)");
                entity.Property(e => e.Penalty).HasColumnType("decimal(15,2)");
                entity.Property(e => e.AdditionalAmount).HasColumnType("decimal(15,2)");
                entity.Property(e => e.Interest).HasColumnType("decimal(15,2)");
                entity.Property(e => e.Total).HasColumnType("decimal(15,2)");
            });

            // Rebate configuration
            builder.Entity<Rebate>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OfficeCode).IsRequired();
                entity.Property(e => e.Pan).IsRequired();
                entity.Property(e => e.AccountType).IsRequired();
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.ExemptionDate).IsRequired();
                entity.Property(e => e.SerialNo).IsRequired();
                entity.Property(e => e.Scheme).IsRequired();
                entity.Property(e => e.Reason).IsRequired();

                entity.HasMany(e => e.RebateItems)
                      .WithOne(e => e.Rebate)
                      .HasForeignKey(e => e.RebateId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // RebateItem configuration
            builder.Entity<RebateItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FiscalYear).IsRequired();
                entity.Property(e => e.FilingPeriod).IsRequired();
                entity.Property(e => e.TaxPeriod).IsRequired();
                entity.Property(e => e.TotalExemptedAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountAmount).HasColumnType("decimal(18,2)");
            });

            // SpecialPenalty configuration
            builder.Entity<SpecialPenalty>(entity =>
            {
                entity.HasKey(e => e.SpecialPenaltyId);
                entity.Property(e => e.SpecialPenaltyNo).IsRequired().HasMaxLength(50);
                entity.Property(e => e.AccountType).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TaxYear).IsRequired();
                entity.Property(e => e.Reason).IsRequired().HasMaxLength(10);
                entity.Property(e => e.OtherReason).HasMaxLength(500);
                entity.Property(e => e.ReasonDetails).HasMaxLength(1000);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)").IsRequired();
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20).HasDefaultValue("Active");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("NOW()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("NOW()");

                // Configure relationships
                entity.HasOne(e => e.Taxpayer)
                    .WithMany()
                    .HasForeignKey(e => e.TaxpayerId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedById)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.UpdatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UpdatedById)
                    .OnDelete(DeleteBehavior.Restrict);

                // Add indexes for better performance
                entity.HasIndex(e => e.TaxpayerId);
                entity.HasIndex(e => e.TaxYear);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.SpecialPenaltyNo).IsUnique();
            });
        }
    }
}
