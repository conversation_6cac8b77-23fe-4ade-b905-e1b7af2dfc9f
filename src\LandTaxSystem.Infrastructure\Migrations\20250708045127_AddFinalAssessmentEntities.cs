﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddFinalAssessmentEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FinalAssessments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxpayerRegistration = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    FiscalYearId = table.Column<Guid>(type: "uuid", nullable: false),
                    MunicipalityId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReturnFilingId = table.Column<Guid>(type: "uuid", nullable: true),
                    PreliminaryAssessmentId = table.Column<Guid>(type: "uuid", nullable: true),
                    TaxpayerName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Address = table.Column<string>(type: "text", nullable: true),
                    Phone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    AssessmentPeriodFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AssessmentPeriodTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SectionsRules = table.Column<string>(type: "text", nullable: true),
                    BankName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BranchName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ReasonCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AppealNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ReasonDescription = table.Column<string>(type: "text", nullable: true),
                    InterestPenaltyCalculationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FinalAssessmentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Draft"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FinalAssessments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FinalAssessments_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FinalAssessments_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FinalAssessments_FiscalYears_FiscalYearId",
                        column: x => x.FiscalYearId,
                        principalTable: "FiscalYears",
                        principalColumn: "FiscalYearId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FinalAssessments_Municipalities_MunicipalityId",
                        column: x => x.MunicipalityId,
                        principalTable: "Municipalities",
                        principalColumn: "MunicipalityId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FinalAssessments_PreliminaryAssessments_PreliminaryAssessme~",
                        column: x => x.PreliminaryAssessmentId,
                        principalTable: "PreliminaryAssessments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_FinalAssessments_ReturnFilings_ReturnFilingId",
                        column: x => x.ReturnFilingId,
                        principalTable: "ReturnFilings",
                        principalColumn: "ReturnFilingId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FinalAssessmentDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FinalAssessmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    SerialNumber = table.Column<int>(type: "integer", nullable: false),
                    FilingPeriod = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TaxPeriod = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TaxYear = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AssessedAmount = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    Penalty = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    AdditionalAmount = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    Interest = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    Total = table.Column<decimal>(type: "numeric(15,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FinalAssessmentDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FinalAssessmentDetails_FinalAssessments_FinalAssessmentId",
                        column: x => x.FinalAssessmentId,
                        principalTable: "FinalAssessments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessmentDetails_FinalAssessmentId",
                table: "FinalAssessmentDetails",
                column: "FinalAssessmentId");

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessments_CreatedBy",
                table: "FinalAssessments",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessments_FiscalYearId",
                table: "FinalAssessments",
                column: "FiscalYearId");

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessments_MunicipalityId",
                table: "FinalAssessments",
                column: "MunicipalityId");

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessments_PreliminaryAssessmentId",
                table: "FinalAssessments",
                column: "PreliminaryAssessmentId");

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessments_ReturnFilingId",
                table: "FinalAssessments",
                column: "ReturnFilingId");

            migrationBuilder.CreateIndex(
                name: "IX_FinalAssessments_UpdatedBy",
                table: "FinalAssessments",
                column: "UpdatedBy");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FinalAssessmentDetails");

            migrationBuilder.DropTable(
                name: "FinalAssessments");
        }
    }
}
