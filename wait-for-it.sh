#!/bin/bash
# wait-for-it.sh script to wait for the database to be ready

set -e

host="$1"
shift
cmd="$@"

# Extract username and password from connection string in environment variable
# Default to postgres/postgres if not specified
DB_USER=${POSTGRES_USER:-postgres}
DB_PASS=${POSTGRES_PASSWORD:-postgres}

echo "Waiting for PostgreSQL to become available at $host..."
until PGPASSWORD=$DB_PASS psql -h "$host" -U "$DB_USER" -d "postgres" -c '\q'; do
  >&2 echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "PostgreSQL is up - attempting to connect to the database"

# Try to connect to the specific database
until PGPASSWORD=$DB_PASS psql -h "$host" -U "$DB_USER" -d "land_tax_system" -c '\q'; do
  >&2 echo "Database land_tax_system doesn't exist yet - creating..."
  PGPASSWORD=$DB_PASS psql -h "$host" -U "$DB_USER" -d "postgres" -c "CREATE DATABASE land_tax_system;" || true
  sleep 2
done

echo "Database is up - executing command"
exec $cmd
