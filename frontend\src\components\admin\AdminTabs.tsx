import React from 'react';

export interface TabItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  count?: number;
  disabled?: boolean;
}

interface AdminTabsProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabKey: string) => void;
  className?: string;
  variant?: 'bordered' | 'lifted' | 'boxed';
  size?: 'sm' | 'md' | 'lg';
}

const AdminTabs: React.FC<AdminTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
  variant = 'bordered',
  size = 'md',
}) => {
  if (!tabs || tabs.length === 0) {
    return null;
  }

  const sizeClass = {
    sm: 'text-sm',
    md: '',
    lg: 'text-lg',
  }[size];

  return (
    <div className={`tabs tabs-${variant} ${className}`}>
      {tabs.map((tab) => (
        <button
          key={tab.key}
          onClick={() => !tab.disabled && onTabChange(tab.key)}
          className={`tab ${sizeClass} ${
            activeTab === tab.key ? 'tab-active' : ''
          } ${tab.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          disabled={tab.disabled}
        >
          {tab.icon && <span className="mr-2">{tab.icon}</span>}
          {tab.label}
          {tab.count !== undefined && (
            <span className="badge badge-sm ml-2">{tab.count}</span>
          )}
        </button>
      ))}
    </div>
  );
};

export default AdminTabs;