using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Payment;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;

namespace LandTaxSystem.Infrastructure.Tests.Services
{
    public class PaymentServiceTests
    {
        private readonly Mock<ApplicationDbContext> _mockContext;
        private readonly Mock<GisService> _mockGisService;
        private readonly PaymentService _paymentService;

        public PaymentServiceTests()
        {
            _mockContext = new Mock<ApplicationDbContext>(new DbContextOptions<ApplicationDbContext>());
            _mockGisService = new Mock<GisService>();
            _paymentService = new PaymentService(_mockContext.Object, _mockGisService.Object);
        }

        [Fact]
        public async Task GetEligibleProperties_ReturnsEligibleProperties_WhenPaymentsUnderpaid()
        {
            // Arrange
            var municipalityId = Guid.NewGuid();
            
            // Setup mock data
            var payments = new List<Payment>
            {
                new Payment
                {
                    PaymentId = Guid.NewGuid(),
                    PropertyId = Guid.NewGuid(),
                    FiscalYearId = Guid.NewGuid(),
                    AmountPaid = 3000,
                    Status = "Success",
                    IsReconciled = false,
                    Property = new Property
                    {
                        PropertyId = Guid.NewGuid(),
                        MunicipalityId = municipalityId,
                        Address = "123 Test Street",
                        Municipality = new Municipality
                        {
                            TaxSlabsConfigJson = "{}",
                            ExemptionRulesConfigJson = "{}",
                            ValuationRulesConfigJson = "{}"
                        }
                    },
                    FiscalYear = new FiscalYear { Name = "2024-2025" }
                }
            }.AsQueryable();

            var mockPayments = MockDbSet(payments);
            _mockContext.Setup(x => x.Payments).Returns(mockPayments.Object);
            _mockGisService.Setup(x => x.CalculatePropertyValue(It.IsAny<Property>(), It.IsAny<string>())).Returns(1000000);
            _mockGisService.Setup(x => x.CalculateTaxAmount(It.IsAny<decimal>(), It.IsAny<string>(), It.IsAny<string>())).Returns(5000);

            // Act
            var result = await _paymentService.GetEligibleProperties(municipalityId);

            // Assert
            Assert.Single(result);
            Assert.Equal(2000, result[0].UnpaidBalance);
        }

        private static Mock<DbSet<T>> MockDbSet<T>(IQueryable<T> data) where T : class
        {
            var mockSet = new Mock<DbSet<T>>();
            mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(data.Provider);
            mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(data.Expression);
            mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(data.ElementType);
            mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(data.GetEnumerator());
            return mockSet;
        }
    }
}
