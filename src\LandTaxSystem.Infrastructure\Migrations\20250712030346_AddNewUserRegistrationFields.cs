﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddNewUserRegistrationFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CitizenshipIssueDate",
                table: "AspNetUsers",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CitizenshipIssueDistrict",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CitizenshipIssueOffice",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GrandmotherName",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MaritalStatus",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MotherName",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NationalIdDocumentPath",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "NationalIdIssueDate",
                table: "AspNetUsers",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NationalIdIssueDistrict",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NationalIdIssueOffice",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NationalIdNumber",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PanDocumentPath",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PanIssueDate",
                table: "AspNetUsers",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PanIssueDistrict",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PanIssueOffice",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Profession",
                table: "AspNetUsers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SpouseName",
                table: "AspNetUsers",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CitizenshipIssueDate",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "CitizenshipIssueDistrict",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "CitizenshipIssueOffice",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "GrandmotherName",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "MaritalStatus",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "MotherName",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "NationalIdDocumentPath",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "NationalIdIssueDate",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "NationalIdIssueDistrict",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "NationalIdIssueOffice",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "NationalIdNumber",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PanDocumentPath",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PanIssueDate",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PanIssueDistrict",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "PanIssueOffice",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "Profession",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "SpouseName",
                table: "AspNetUsers");
        }
    }
}
