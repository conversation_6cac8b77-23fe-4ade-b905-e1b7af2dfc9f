import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Polygon } from "react-leaflet";
import { toast } from "react-hot-toast";
import api from "../../services/api";
import type { Property } from "../../types";
import { useAuth } from "../../context/AuthContext";
import { AdminLayout } from "../../components/admin";
import {
  PropertyDetailsSection,
  LandDetailsSection,
  DocumentPreview,
} from "./components";
import "leaflet/dist/leaflet.css";

const PropertyReview: React.FC = () => {
  const { user } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [filter, setFilter] = useState<string>("PendingReview");
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "property" | "land" | "documents" | "map"
  >("property");

  // Define GeoJSON types for better type safety
  type GeoJSONCoordinate = number[];
  type GeoJSONPolygon = {
    type: string;
    coordinates: GeoJSONCoordinate[][];
  };

  // Extended Property type to handle backend response format
  interface ExtendedProperty {
    [key: string]: unknown; // Allow any properties from backend
    parcelGeoJson?: string | GeoJSONPolygon | Record<string, unknown>; // Backend returns parcelGeoJson (lowercase J)
    propertyId?: string; // Backend returns propertyId instead of id
    id?: string; // Frontend id field
    parcelGeoJSON?: string | GeoJSONPolygon | Record<string, unknown>; // Frontend expects parcelGeoJSON
  }

  // Helper function to parse GeoJSON data
  const parseGeoJSON = (
    property: Property | ExtendedProperty | null
  ): GeoJSONPolygon | null => {
    if (!property) return null;

    try {
      // Handle case where property has parcelGeoJson (from backend)
      const extendedProp = property as ExtendedProperty;
      if (extendedProp.parcelGeoJson) {
        console.log("Found parcelGeoJson property");
        if (typeof extendedProp.parcelGeoJson === "string") {
          console.log("Parsing string GeoJSON from parcelGeoJson");
          return JSON.parse(extendedProp.parcelGeoJson);
        } else if (typeof extendedProp.parcelGeoJson === "object") {
          console.log("Using object GeoJSON from parcelGeoJson");
          return extendedProp.parcelGeoJson as GeoJSONPolygon;
        }
      }

      // Handle case where parcelGeoJson is already an object
      if (
        property.parcelGeoJson &&
        typeof property.parcelGeoJson === "object"
      ) {
        console.log("Using object GeoJSON from parcelGeoJson");
        return property.parcelGeoJson as GeoJSONPolygon;
      }

      // Handle case where parcelGeoJson is a string
      if (
        property.parcelGeoJson &&
        typeof property.parcelGeoJson === "string"
      ) {
        console.log("Parsing string GeoJSON from parcelGeoJson");
        return JSON.parse(property.parcelGeoJson as string);
      }
    } catch (error) {
      console.error("Error parsing GeoJSON:", error);
      console.error("Property data:", property);
    }

    return null;
  };

  // Helper function to get map center from property
  const getMapCenter = (
    property: Property | ExtendedProperty | null
  ): [number, number] => {
    const geoJSON = parseGeoJSON(property);

    if (
      geoJSON &&
      geoJSON.coordinates &&
      geoJSON.coordinates[0] &&
      geoJSON.coordinates[0][0]
    ) {
      // Use first coordinate as center [lat, lng]
      return [geoJSON.coordinates[0][0][1], geoJSON.coordinates[0][0][0]];
    }

    return [27.7172, 85.324]; // Default center (Kathmandu)
  };

  // Helper function to render property polygon on the map
  const renderPropertyPolygon = () => {
    if (!selectedProperty) return null;

    const geoJSON = parseGeoJSON(selectedProperty);

    if (geoJSON && geoJSON.coordinates && geoJSON.coordinates[0]) {
      console.log(
        "Rendering polygon with coordinates:",
        geoJSON.coordinates[0]
      );
      return (
        <Polygon
          positions={geoJSON.coordinates[0].map((coord: number[]) => [
            coord[1],
            coord[0],
          ])}
          pathOptions={{ color: "blue", fillOpacity: 0.4 }}
        />
      );
    }

    console.warn("No valid coordinates found for polygon rendering");
    return null;
  };

  // Define loadPendingProperties with useCallback to prevent it from changing on every render
  const loadPendingProperties = useCallback(async () => {
    try {
      setLoading(true);

      // Get municipality ID from the authenticated user
      if (!user?.municipalityId) {
        console.error("User has no municipality ID");
        setLoading(false);
        return;
      }

      const response = await api.get(
        `/properties/municipality/${user.municipalityId}?status=${filter}`
      );

      // Log the first property to debug GeoJSON issues
      if (response.data && response.data.length > 0) {
        console.log("First property from API:", response.data[0]);
        if (response.data[0].parcelGeoJson) {
          console.log("parcelGeoJson found:", response.data[0].parcelGeoJson);
        }
        if (response.data[0].parcelGeoJSON) {
          console.log("parcelGeoJSON found:", response.data[0].parcelGeoJSON);
        }
      }

      // Map properties to ensure consistent property structure
      const mappedProperties = response.data.map((prop: ExtendedProperty) => {
        // Debug logging for the first property
        if (response.data.indexOf(prop) === 0) {
          console.log("Property from backend:", prop);
          console.log("Property ID:", prop.propertyId || prop.id);
          console.log(
            "GeoJSON data:",
            prop.parcelGeoJSON || prop.parcelGeoJson
          );
        }

        return {
          ...prop,
          id: prop.propertyId || prop.id,
          // Ensure parcelGeoJSON is available for frontend components
          parcelGeoJSON: prop.parcelGeoJSON || prop.parcelGeoJson,
        };
      });

      setProperties(mappedProperties);
    } catch (error) {
      console.error("Failed to load properties for review:", error);
      // For development, we'll keep some mock data as fallback
      setProperties([]);
    } finally {
      setLoading(false);
    }
  }, [filter, user]);

  useEffect(() => {
    loadPendingProperties();
  }, [loadPendingProperties]);

  // Reset selectedProperty when filter changes to avoid stale data
  useEffect(() => {
    setSelectedProperty(null);
  }, [filter]);

  const handleApprove = async (propertyId: string) => {
    try {
      setProcessing(true);

      // Use the status update endpoint
      await api.put(`/properties/${propertyId}/status`, {
        status: "Approved",
      });

      // Update local state
      setProperties((prev) => prev.filter((p) => p.id !== propertyId));
      setSelectedProperty(null);

      // Show success message
      toast.success("Property approved successfully!");
    } catch (error) {
      console.error("Failed to approve property:", error);
      toast.error("Failed to approve property. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async (propertyId: string) => {
    if (!rejectionReason.trim()) {
      toast.error("Please provide a reason for rejection.");
      return;
    }

    try {
      setProcessing(true);

      // Use the status update endpoint
      await api.put(`/properties/${propertyId}/status`, {
        status: "Rejected",
        reason: rejectionReason,
      });

      // Update local state
      setProperties((prev) => prev.filter((p) => p.id !== propertyId));
      setSelectedProperty(null);
      setShowRejectModal(false);
      setRejectionReason("");

      // Show success message
      toast.success("Property rejected successfully!");
    } catch (error) {
      console.error("Failed to reject property:", error);
      toast.error("Failed to reject property. Please try again.");
    } finally {
      setProcessing(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved":
        return "badge-success";
      case "PendingReview":
        return "badge-warning";
      case "Rejected":
        return "badge-error";
      default:
        return "badge-ghost";
    }
  };

  if (loading) {
    return (
      <AdminLayout title="Property Review Queue">
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Property Review Queue"
      subtitle="Review and approve/reject property registrations"
    >
      {/* Filters */}
      <div className="card bg-base-100 shadow mb-6">
        <div className="card-body">
          <div className="tabs tabs-boxed">
            <button
              onClick={() => setFilter("PendingReview")}
              className={`tab ${
                filter === "PendingReview" ? "tab-active" : ""
              }`}
            >
              Pending Review (
              {properties.filter((p) => p.status === "PendingReview").length})
            </button>
            <button
              onClick={() => setFilter("Approved")}
              className={`tab ${filter === "Approved" ? "tab-active" : ""}`}
            >
              Recently Approved
            </button>
            <button
              onClick={() => setFilter("Rejected")}
              className={`tab ${filter === "Rejected" ? "tab-active" : ""}`}
            >
              Recently Rejected
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Properties List */}
        <div className="card bg-base-100 shadow lg:col-span-1">
          <div className="card-body">
            <h2 className="card-title">
              Properties{" "}
              {filter === "PendingReview" ? "Awaiting Review" : `(${filter})`}
            </h2>
            <div className="max-h-96 overflow-y-auto">
              {properties.length === 0 ? (
                <div className="text-center py-8 text-base-content/70">
                  <div className="text-4xl mb-2">📋</div>
                  <p>No properties to review</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {properties.map((property) => (
                    <div
                      key={property.id}
                      className={`card bg-base-200 cursor-pointer hover:bg-base-300 transition-colors ${
                        selectedProperty?.id === property.id
                          ? "ring-2 ring-primary"
                          : ""
                      }`}
                      onClick={() => setSelectedProperty(property)}
                    >
                      <div className="card-body p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-medium">{property.address}</h3>
                            <p className="text-sm text-base-content/70 mt-1">
                              {property.usageType} • {property.landAreaSqM} sq.m
                            </p>
                            <p className="text-xs text-base-content/50 mt-1">
                              Submitted: {formatDate(property.registrationDate)}
                            </p>
                          </div>
                          <span
                            className={`badge ${getStatusColor(
                              property.status
                            )}`}
                          >
                            {property.status === "PendingReview"
                              ? "Pending"
                              : property.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Property Detail */}
        <div className="card bg-base-100 shadow lg:col-span-2">
          <div className="card-body">
            <h2 className="card-title">Property Details</h2>
            {selectedProperty ? (
              <div className="space-y-6">
                {/* Tab Navigation */}
                <div className="tabs tabs-bordered">
                  <button
                    onClick={() => setActiveTab("property")}
                    className={`tab ${
                      activeTab === "property" ? "tab-active" : ""
                    }`}
                  >
                    🏠 Property
                  </button>
                  <button
                    onClick={() => setActiveTab("land")}
                    className={`tab ${
                      activeTab === "land" ? "tab-active" : ""
                    }`}
                  >
                    🌾 Land Details
                  </button>
                  <button
                    onClick={() => setActiveTab("documents")}
                    className={`tab ${
                      activeTab === "documents" ? "tab-active" : ""
                    }`}
                  >
                    📄 Documents
                  </button>
                  <button
                    onClick={() => setActiveTab("map")}
                    className={`tab ${activeTab === "map" ? "tab-active" : ""}`}
                  >
                    🗺️ Map
                  </button>
                </div>

                {/* Tab Content */}
                <div className="min-h-[400px]">
                  {activeTab === "property" && (
                    <PropertyDetailsSection property={selectedProperty} />
                  )}

                  {activeTab === "land" && selectedProperty.landDetails ? (
                    <LandDetailsSection
                      landDetails={selectedProperty.landDetails}
                    />
                  ) : (
                    activeTab === "land" && (
                      <div className="card bg-base-200 shadow-sm">
                        <div className="card-body p-4">
                          <div className="text-center py-8 text-base-content/70">
                            <div className="text-4xl mb-2">🌾</div>
                            <p>No detailed land information available</p>
                            <p className="text-sm mt-1">
                              Land details were not provided during registration
                            </p>
                          </div>
                        </div>
                      </div>
                    )
                  )}

                  {activeTab === "documents" && (
                    <DocumentPreview property={selectedProperty} />
                  )}

                  {activeTab === "map" && (
                    <div className="card bg-base-200 shadow-sm">
                      <div className="card-body p-4">
                        <div className="flex items-center space-x-3 mb-4">
                          <div className="text-2xl">🗺️</div>
                          <div>
                            <h3 className="text-lg font-semibold text-base-content">
                              Property Location
                            </h3>
                            <p className="text-sm text-base-content/70">
                              Geographic boundaries and location on map
                            </p>
                          </div>
                        </div>
                        <div className="h-96 rounded-lg overflow-hidden border">
                          {/* Add key prop to force re-render when selectedProperty changes */}
                          <MapContainer
                            key={`map-${selectedProperty.id}`}
                            center={getMapCenter(selectedProperty)}
                            zoom={15}
                            style={{ height: "100%", width: "100%" }}
                          >
                            <TileLayer
                              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                            />
                            {renderPropertyPolygon()}
                          </MapContainer>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                {selectedProperty.status === "PendingReview" && (
                  <div className="card-actions justify-end pt-4 border-t">
                    <button
                      onClick={() => handleApprove(selectedProperty.id)}
                      disabled={processing}
                      className="btn btn-success"
                    >
                      {processing ? (
                        <>
                          <span className="loading loading-spinner loading-sm"></span>
                          Processing...
                        </>
                      ) : (
                        "Approve"
                      )}
                    </button>
                    <button
                      onClick={() => setShowRejectModal(true)}
                      disabled={processing}
                      className="btn btn-error"
                    >
                      Reject
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-base-content/70">
                <div className="text-4xl mb-2">👆</div>
                <p>Select a property from the list to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg">Reject Property Registration</h3>
            <p className="py-4 text-base-content/70">
              Please provide a reason for rejecting this property registration:
            </p>
            <div className="form-control">
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="textarea textarea-bordered"
                rows={4}
                placeholder="Enter rejection reason..."
              />
            </div>
            <div className="modal-action">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectionReason("");
                }}
                className="btn btn-ghost"
              >
                Cancel
              </button>
              <button
                onClick={() =>
                  selectedProperty && handleReject(selectedProperty.id)
                }
                disabled={!rejectionReason.trim() || processing}
                className="btn btn-error"
              >
                {processing ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Processing...
                  </>
                ) : (
                  "Reject Property"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default PropertyReview;
