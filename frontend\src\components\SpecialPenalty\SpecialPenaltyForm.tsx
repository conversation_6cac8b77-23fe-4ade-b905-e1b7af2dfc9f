import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import AdminLayout from '../admin/AdminLayout';
import AdminForm from '../admin/AdminForm';
import { specialPenaltyService, type CreateSpecialPenaltyDto } from '../../services/specialPenaltyService';
import { userService } from '../../services/api';

interface TaxpayerInfo {
  id: string;
  firstName: string;
  lastName: string;
  pan: string;
  address?: string;
  phoneNumber?: string;
  email: string;
}

interface SpecialPenaltyFormData {
  taxpayerId: string;
  penaltyType: string;
  amount: string;
  reason: string;
  dueDate: Date | undefined;
  status: string;
}

const SpecialPenaltyForm: React.FC = () => {
  const navigate = useNavigate();
  const [taxpayers, setTaxpayers] = useState<TaxpayerInfo[]>([]);
  const [selectedTaxpayer, setSelectedTaxpayer] = useState<TaxpayerInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  const [formData, setFormData] = useState<SpecialPenaltyFormData>({
    taxpayerId: '',
    penaltyType: '',
    amount: '',
    reason: '',
    dueDate: undefined,
    status: 'Pending'
  });

  // Load taxpayers on component mount
  useEffect(() => {
    const loadTaxpayers = async () => {
      try {
        setLoading(true);
        // Get all active citizens (taxpayers)
        const allTaxpayers = await userService.getAllCitizens();
        setTaxpayers(allTaxpayers);
      } catch (error) {
        console.error('Failed to load taxpayers:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTaxpayers();
  }, []);



  const handleChange = (field: keyof SpecialPenaltyFormData, value: string | Date | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Update selected taxpayer when taxpayerId changes
    if (field === 'taxpayerId' && typeof value === 'string') {
      const taxpayer = taxpayers.find(t => t.id === value);
      setSelectedTaxpayer(taxpayer || null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.taxpayerId || !formData.penaltyType || !formData.amount || !formData.reason || !formData.dueDate) {
      toast.error('Please fill in all required fields.');
      return;
    }

    try {
      setSubmitting(true);
      
      const createData: CreateSpecialPenaltyDto = {
        taxpayerId: formData.taxpayerId,
        penaltyType: formData.penaltyType,
        amount: parseFloat(formData.amount),
        reason: formData.reason,
        dueDate: formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : '',
        status: formData.status
      };
      
      await specialPenaltyService.createSpecialPenalty(createData);
      
      toast.success('Special penalty created successfully!');
      navigate('/special-penalties');
    } catch (error) {
      console.error('Error creating special penalty:', error);
      toast.error('Failed to create special penalty. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6 text-center">
          Special Penalty
        </h1>
        
        <AdminForm onSubmit={handleSubmit}>
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-lg font-semibold mb-4">
              Special Penalty Details
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Penalty Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Penalty Type *
                </label>
                <input
                  type="text"
                  value={formData.penaltyType}
                  onChange={(e) => handleChange('penaltyType', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter penalty type"
                  required
                />
              </div>
              
              {/* Due Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Due Date *
                </label>
                <input
                  type="date"
                  value={formData.dueDate ? formData.dueDate.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleChange('dueDate', e.target.value ? new Date(e.target.value) : undefined)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
              
              
              
              {/* Taxpayer Selection */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Taxpayer *
                </label>
                <select
                  value={formData.taxpayerId}
                  onChange={(e) => handleChange('taxpayerId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={loading}
                  required
                >
                  <option value="">Select a taxpayer...</option>
                  {taxpayers.map((taxpayer) => (
                    <option key={taxpayer.id} value={taxpayer.id}>
                      {taxpayer.firstName} {taxpayer.lastName} - {taxpayer.pan}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Selected Taxpayer Information */}
              {selectedTaxpayer && (
                <div className="md:col-span-2 bg-gray-50 p-4 rounded-md">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Selected Taxpayer Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex">
                      <span className="text-sm font-medium text-gray-500 w-32">Name:</span>
                      <span className="text-sm text-gray-900">{selectedTaxpayer.firstName} {selectedTaxpayer.lastName}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium text-gray-500 w-32">PAN:</span>
                      <span className="text-sm text-gray-900">{selectedTaxpayer.pan}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium text-gray-500 w-32">Email:</span>
                      <span className="text-sm text-gray-900">{selectedTaxpayer.email}</span>
                    </div>
                    {selectedTaxpayer.phoneNumber && (
                      <div className="flex">
                        <span className="text-sm font-medium text-gray-500 w-32">Phone:</span>
                        <span className="text-sm text-gray-900">{selectedTaxpayer.phoneNumber}</span>
                      </div>
                    )}
                    {selectedTaxpayer.address && (
                      <div className="flex md:col-span-2">
                        <span className="text-sm font-medium text-gray-500 w-32">Address:</span>
                        <span className="text-sm text-gray-900">{selectedTaxpayer.address}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount *
                </label>
                <input
                  type="number"
                  value={formData.amount}
                  onChange={(e) => handleChange('amount', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  min="0"
                  step="0.01"
                  placeholder="Enter penalty amount"
                  required
                />
              </div>
              
              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status:
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleChange('status', e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="Pending">Pending</option>
                  <option value="Active">Active</option>
                  <option value="Paid">Paid</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>
              
                            
              {/* Reason */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reason for Special Penalty *
                </label>
                <textarea
                  value={formData.reason}
                  onChange={(e) => handleChange('reason', e.target.value)}
                  rows={4}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Enter detailed reason for special penalty..."
                  required
                />
              </div>
            </div>
          </div>
          
          {/* Form Actions */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Creating...' : 'Create Special Penalty'}
            </button>
          </div>
        </AdminForm>
      </div>
    </AdminLayout>
  );
};

export default SpecialPenaltyForm;
