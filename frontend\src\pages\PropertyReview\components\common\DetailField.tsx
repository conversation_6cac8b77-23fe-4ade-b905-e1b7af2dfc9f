import React from "react";

interface DetailFieldProps {
  label: string;
  value?: string | number | boolean | null;
  type?: "text" | "number" | "currency" | "date" | "boolean";
  unit?: string;
}

export const DetailField: React.FC<DetailFieldProps> = ({ label, value, type = "text", unit }) => {
  const formatValue = () => {
    if (value === null || value === undefined || value === "") {
      return "N/A";
    }

    switch (type) {
      case "currency":
        return `Rs. ${Number(value).toLocaleString()}`;
      case "number":
        return `${Number(value).toLocaleString()}${unit ? ` ${unit}` : ""}`;
      case "date":
        return new Date(value as string).toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      case "boolean":
        return value ? "Yes" : "No";
      default:
        return String(value);
    }
  };

  return (
    <div className="space-y-1">
      <label className="label-text text-sm font-medium text-base-content/70">{label}</label>
      <p className="text-base text-base-content font-medium">{formatValue()}</p>
    </div>
  );
};
