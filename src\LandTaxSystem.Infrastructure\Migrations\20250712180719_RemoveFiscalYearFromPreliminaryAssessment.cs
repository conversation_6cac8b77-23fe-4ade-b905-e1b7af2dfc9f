﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveFiscalYearFromPreliminaryAssessment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PreliminaryAssessments_FiscalYears_FiscalYearId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropIndex(
                name: "IX_PreliminaryAssessments_FiscalYearId",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "FiscalYearId",
                table: "PreliminaryAssessments");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "FiscalYearId",
                table: "PreliminaryAssessments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessments_FiscalYearId",
                table: "PreliminaryAssessments",
                column: "FiscalYearId");

            migrationBuilder.AddForeignKey(
                name: "FK_PreliminaryAssessments_FiscalYears_FiscalYearId",
                table: "PreliminaryAssessments",
                column: "FiscalYearId",
                principalTable: "FiscalYears",
                principalColumn: "FiscalYearId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
