export interface OutstandingTaxReportRequest {
  municipalityId?: string;
  ward?: string;
  fiscalYear?: string;
  taxpayerNumber?: string;
}

export interface OutstandingTaxRecord {
  taxpayerNumber: string;
  taxpayerName: string;
  taxpayerAddress: string;
  taxpayerContacts: string;
  fiscalYear: string;
  ward: string;
  parcelNumber: string;
  outstandingDue: number;
}

export interface OutstandingTaxReportResponse {
  records: OutstandingTaxRecord[];
  totalCount: number;
}
