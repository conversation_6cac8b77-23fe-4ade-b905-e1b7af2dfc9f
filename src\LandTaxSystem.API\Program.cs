using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Core.Models;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Security.Claims;
using Microsoft.Extensions.Options;
using LandTaxSystem.API;
using LandTaxSystem.API.App_Start;
using AutoMapper;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Add database context
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
if (string.IsNullOrEmpty(connectionString))
{
    Console.WriteLine("WARNING: No connection string found in configuration. Using default localhost connection.");
    connectionString = "Host=localhost;Database=land_tax_system;Username=********;Password=********";
}
Console.WriteLine($"Using connection string: {connectionString}");

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(
        connectionString,
        npgsqlOptions => npgsqlOptions.UseNetTopologySuite()));

// Add Identity services
builder.Services.AddIdentityCore<ApplicationUser>(options =>
    {
        options.Password.RequireDigit = true;
        options.Password.RequireLowercase = true;
        options.Password.RequireUppercase = true;
        options.Password.RequireNonAlphanumeric = true;
        options.Password.RequiredLength = 8;
    })
    .AddRoles<IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager<SignInManager<ApplicationUser>>();

// Register JWT Key Service first
builder.Services.AddSingleton<JwtKeyService>();

// Add JWT Authentication using shared key service
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        // TokenValidationParameters will be configured after the app is built
    });

// Add authorization policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireCitizenRole", policy => policy.RequireRole("Citizen"));
    options.AddPolicy("RequireOfficerRole", policy => policy.RequireRole("Officer"));
    options.AddPolicy("RequireCentralAdminRole", policy => policy.RequireRole("CentralAdmin"));
});

// Configure EmailSettings
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));

// Register services
builder.Services.AddScoped<TokenService>();
builder.Services.AddScoped<FileService>();
builder.Services.AddScoped<GisService>();
builder.Services.AddScoped<TaxConfigResolver>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IPaymentService, PaymentService>();
builder.Services.AddScoped<ITaxCalculationService, TaxCalculationService>();
builder.Services.AddScoped<IPreliminaryAssessmentService, PreliminaryAssessmentService>();
builder.Services.AddScoped<IFinalAssessmentService, FinalAssessmentService>();
builder.Services.AddScoped<IRebateService, RebateService>();
builder.Services.AddScoped<IReportService, ReportService>();

// Add AutoMapper

// Add controllers
// Add AutoMapper
var mapperConfig = AutoMapperConfig.Configure();
IMapper mapper = mapperConfig.CreateMapper();
builder.Services.AddSingleton(mapper);
builder.Services.AddControllers();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
        builder.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

// Add Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Land Tax System API", Version = "v1" });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Land Tax System API v1"));
}

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseCors("AllowAll");

app.UseAuthentication();

// Configure JwtBearerOptions after the app is built and services are available
var jwtKeyService = app.Services.GetRequiredService<JwtKeyService>();
var jwtBearerOptions = app.Services.GetRequiredService<Microsoft.Extensions.Options.IOptionsMonitor<JwtBearerOptions>>().Get(JwtBearerDefaults.AuthenticationScheme);

jwtBearerOptions.TokenValidationParameters = new TokenValidationParameters
{
    ValidateIssuerSigningKey = true,
    IssuerSigningKey = jwtKeyService.GetKey(),
    ValidateIssuer = true,
    ValidIssuer = jwtKeyService.GetIssuer(),
    ValidateAudience = true,
    ValidAudience = jwtKeyService.GetAudience(),
    ValidateLifetime = true,
    ClockSkew = TimeSpan.Zero,
    RoleClaimType = ClaimTypes.Role,
    NameClaimType = ClaimTypes.NameIdentifier
};

jwtBearerOptions.Events = new JwtBearerEvents
{
    OnAuthenticationFailed = context =>
    {
        Console.WriteLine($"JWT Authentication failed: {context.Exception.Message}");
        Console.WriteLine($"Exception details: {context.Exception}");
        return Task.CompletedTask;
    },
    OnTokenValidated = context =>
    {
        return Task.CompletedTask;
    },
    OnChallenge = context =>
    {
        Console.WriteLine($"JWT Challenge: {context.Error} - {context.ErrorDescription}");
        return Task.CompletedTask;
    }
};

app.UseAuthorization();

app.MapControllers();

// Create uploads folder
var uploadsFolder = Path.Combine(app.Environment.ContentRootPath, "uploads");
if (!Directory.Exists(uploadsFolder))
    Directory.CreateDirectory(uploadsFolder);

// Seed the database
if (app.Environment.IsDevelopment())
{
    try
    {
        // Call the DbSeeder to seed the database
        await DbSeeder.SeedDatabase(app.Services);
        Console.WriteLine("Database seeded successfully.");

        // Seed tax configuration data
        using (var scope = app.Services.CreateScope())
        {
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Get all municipalities
            var municipalities = dbContext.Municipalities.ToList();

            if (municipalities.Any())
            {
                Console.WriteLine("Seeding tax configuration data for development...");
                foreach (var municipality in municipalities)
                {
                    SeedTaxConfig.SeedMunicipalityTaxConfig(app.Services, municipality.MunicipalityId);
                }
            }
            else
            {
                Console.WriteLine("No municipalities found in database. Skipping tax config seeding.");
            }
        }

        // Seed sample properties and assessments for testing
        SeedSampleData.SeedPropertiesAndAssessments(app.Services);

        // Seed demo data from provided files
        var parcelsPath = Path.Combine(Directory.GetCurrentDirectory(), "gis", "simulated_parcels.geojson");
        var buildingsPath = Path.Combine(Directory.GetCurrentDirectory(), "gis", "simulated_buildings.geojson");
        var taxRecordsPath = Path.Combine(Directory.GetCurrentDirectory(), "gis", "tax_pay_record.xlsx - Sheet1.csv");

        if (File.Exists(parcelsPath) && File.Exists(buildingsPath) && File.Exists(taxRecordsPath))
        {
            Console.WriteLine("Seeding demo data from provided files...");
            await SeedDemoData.SeedFromDemoFilesAsync(app.Services, parcelsPath, buildingsPath, taxRecordsPath);
        }
        else
        {
            Console.WriteLine("Demo data files not found. Checking paths:");
            Console.WriteLine($"Parcels: {parcelsPath} - {(File.Exists(parcelsPath) ? "Found" : "Not found")}");
            Console.WriteLine($"Buildings: {buildingsPath} - {(File.Exists(buildingsPath) ? "Found" : "Not found")}");
            Console.WriteLine($"Tax Records: {taxRecordsPath} - {(File.Exists(taxRecordsPath) ? "Found" : "Not found")}");
            Console.WriteLine("Skipping demo data seeding.");
        }

        // Seed cadastral data from GeoJSON file
        var geoJsonPath = "/Users/<USER>/Downloads/land-tax-system/gis/simulated_NeLIS_cadastral_data.geojson";
        if (File.Exists(geoJsonPath))
        {
            Console.WriteLine("Seeding cadastral data from GeoJSON file...");
            await SeedCadastralData.SeedFromGeoJsonAsync(app.Services, geoJsonPath);
        }
        else
        {
            Console.WriteLine($"GeoJSON file not found at: {geoJsonPath}");
            Console.WriteLine("Skipping cadastral data seeding.");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"An error occurred while seeding the database: {ex.Message}");
    }
}

app.Run();
