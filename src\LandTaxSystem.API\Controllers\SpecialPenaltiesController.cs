using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.API.DTOs.SpecialPenalty;
using System.Security.Claims;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SpecialPenaltiesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SpecialPenaltiesController> _logger;

        public SpecialPenaltiesController(ApplicationDbContext context, ILogger<SpecialPenaltiesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get all special penalties with optional filtering
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Officer,Admin")]
        public async Task<ActionResult<IEnumerable<SpecialPenaltyResponseDto>>> GetSpecialPenalties(
            [FromQuery] SpecialPenaltyListQueryDto query)
        {
            try
            {
                var specialPenaltiesQuery = _context.SpecialPenalties
                    .Include(sp => sp.Taxpayer)
                    .Include(sp => sp.CreatedBy)
                    .Include(sp => sp.UpdatedBy)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(query.Status))
                {
                    specialPenaltiesQuery = specialPenaltiesQuery.Where(sp => sp.Status == query.Status);
                }

                if (query.TaxYear.HasValue)
                {
                    specialPenaltiesQuery = specialPenaltiesQuery.Where(sp => sp.TaxYear == query.TaxYear.Value);
                }

                if (!string.IsNullOrEmpty(query.AccountType))
                {
                    specialPenaltiesQuery = specialPenaltiesQuery.Where(sp => sp.AccountType == query.AccountType);
                }

                if (!string.IsNullOrEmpty(query.SearchTerm))
                {
                    specialPenaltiesQuery = specialPenaltiesQuery.Where(sp => 
                        sp.Taxpayer.FullName.Contains(query.SearchTerm) ||
                        sp.Taxpayer.PAN.Contains(query.SearchTerm) ||
                        sp.Reason.Contains(query.SearchTerm) ||
                        (sp.OtherReason != null && sp.OtherReason.Contains(query.SearchTerm)) ||
                        sp.SpecialPenaltyNo.Contains(query.SearchTerm));
                }

                if (query.FromDate.HasValue)
                {
                    specialPenaltiesQuery = specialPenaltiesQuery.Where(sp => sp.CreatedAt >= query.FromDate.Value);
                }

                if (query.ToDate.HasValue)
                {
                    specialPenaltiesQuery = specialPenaltiesQuery.Where(sp => sp.CreatedAt <= query.ToDate.Value);
                }

                // Apply sorting
                specialPenaltiesQuery = query.SortBy?.ToLower() switch
                {
                    "taxpayername" => query.SortOrder?.ToLower() == "desc" 
                        ? specialPenaltiesQuery.OrderByDescending(sp => sp.Taxpayer.FullName)
                        : specialPenaltiesQuery.OrderBy(sp => sp.Taxpayer.FullName),
                    "amount" => query.SortOrder?.ToLower() == "desc" 
                        ? specialPenaltiesQuery.OrderByDescending(sp => sp.Amount)
                        : specialPenaltiesQuery.OrderBy(sp => sp.Amount),
                    "status" => query.SortOrder?.ToLower() == "desc" 
                        ? specialPenaltiesQuery.OrderByDescending(sp => sp.Status)
                        : specialPenaltiesQuery.OrderBy(sp => sp.Status),
                    "taxyear" => query.SortOrder?.ToLower() == "desc" 
                        ? specialPenaltiesQuery.OrderByDescending(sp => sp.TaxYear)
                        : specialPenaltiesQuery.OrderBy(sp => sp.TaxYear),
                    _ => query.SortOrder?.ToLower() == "desc" 
                        ? specialPenaltiesQuery.OrderByDescending(sp => sp.CreatedAt)
                        : specialPenaltiesQuery.OrderBy(sp => sp.CreatedAt)
                };

                // Apply pagination
                var totalCount = await specialPenaltiesQuery.CountAsync();
                var specialPenalties = await specialPenaltiesQuery
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .ToListAsync();

                var response = specialPenalties.Select(sp => new SpecialPenaltyResponseDto
                {
                    SpecialPenaltyId = sp.SpecialPenaltyId,
                    SpecialPenaltyNo = sp.SpecialPenaltyNo,
                    TaxpayerInfo = new TaxpayerInfoDto
                    {
                        TaxpayerId = sp.Taxpayer.Id,
                        FullName = sp.Taxpayer.FullName,
                        PAN = sp.Taxpayer.PAN,
                        PhoneNumber = sp.Taxpayer.PhoneNumber,
                        Email = sp.Taxpayer.Email
                    },
                    AccountType = sp.AccountType,
                    TaxYear = sp.TaxYear,
                    Reason = sp.Reason,
                    OtherReason = sp.OtherReason,
                    ReasonDetails = sp.ReasonDetails,
                    EffectiveDate = sp.EffectiveDate,
                    Amount = sp.Amount,
                    Status = sp.Status,
                    CreatedAt = sp.CreatedAt,
                    UpdatedAt = sp.UpdatedAt,
                    CreatedBy = sp.CreatedBy?.FullName,
                    UpdatedBy = sp.UpdatedBy?.FullName
                }).ToList();

                Response.Headers["X-Total-Count"] = totalCount.ToString();
                Response.Headers["X-Page"] = query.Page.ToString();
                Response.Headers["X-Page-Size"] = query.PageSize.ToString();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving special penalties");
                return StatusCode(500, "An error occurred while retrieving special penalties");
            }
        }

        /// <summary>
        /// Get a specific special penalty by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Officer,Admin")]
        public async Task<ActionResult<SpecialPenaltyResponseDto>> GetSpecialPenalty(Guid id)
        {
            try
            {
                var specialPenalty = await _context.SpecialPenalties
                    .Include(sp => sp.Taxpayer)
                    .Include(sp => sp.CreatedBy)
                    .Include(sp => sp.UpdatedBy)
                    .FirstOrDefaultAsync(sp => sp.SpecialPenaltyId == id);

                if (specialPenalty == null)
                {
                    return NotFound($"Special penalty with ID {id} not found");
                }

                var response = new SpecialPenaltyResponseDto
                {
                    SpecialPenaltyId = specialPenalty.SpecialPenaltyId,
                    SpecialPenaltyNo = specialPenalty.SpecialPenaltyNo,
                    TaxpayerInfo = new TaxpayerInfoDto
                    {
                        TaxpayerId = specialPenalty.Taxpayer.Id,
                        FullName = specialPenalty.Taxpayer.FullName,
                        PAN = specialPenalty.Taxpayer.PAN,
                        PhoneNumber = specialPenalty.Taxpayer.PhoneNumber,
                        Email = specialPenalty.Taxpayer.Email
                    },
                    AccountType = specialPenalty.AccountType,
                    TaxYear = specialPenalty.TaxYear,
                    Reason = specialPenalty.Reason,
                    OtherReason = specialPenalty.OtherReason,
                    ReasonDetails = specialPenalty.ReasonDetails,
                    EffectiveDate = specialPenalty.EffectiveDate,
                    Amount = specialPenalty.Amount,
                    Status = specialPenalty.Status,
                    CreatedAt = specialPenalty.CreatedAt,
                    UpdatedAt = specialPenalty.UpdatedAt,
                    CreatedBy = specialPenalty.CreatedBy?.FullName,
                    UpdatedBy = specialPenalty.UpdatedBy?.FullName
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving special penalty with ID {Id}", id);
                return StatusCode(500, "An error occurred while retrieving the special penalty");
            }
        }

        /// <summary>
        /// Create a new special penalty
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Officer,Admin")]
        public async Task<ActionResult<SpecialPenaltyResponseDto>> CreateSpecialPenalty(
            [FromBody] CreateSpecialPenaltyDto createDto)
        {
            try
            {
                // Validate taxpayer exists
                var taxpayer = await _context.Users.FindAsync(createDto.TaxpayerId);
                if (taxpayer == null)
                {
                    return BadRequest($"Taxpayer with ID {createDto.TaxpayerId} not found");
                }

                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value!;

                // Generate penalty number
                var year = DateTime.Now.Year;
                var count = await _context.SpecialPenalties.CountAsync(sp => sp.CreatedAt.Year == year) + 1;
                var penaltyNo = $"SP{year}{count:D4}";

                var specialPenalty = new SpecialPenalty
                {
                    SpecialPenaltyNo = penaltyNo,
                    TaxpayerId = createDto.TaxpayerId,
                    AccountType = createDto.AccountType,
                    TaxYear = createDto.TaxYear,
                    Reason = createDto.Reason,
                    OtherReason = createDto.OtherReason,
                    ReasonDetails = createDto.ReasonDetails,
                    EffectiveDate = createDto.EffectiveDate,
                    Amount = createDto.Amount,
                    Status = "Pending",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedById = currentUserId,
                    UpdatedById = currentUserId
                };

                _context.SpecialPenalties.Add(specialPenalty);
                await _context.SaveChangesAsync();

                // Reload with includes for response
                var createdSpecialPenalty = await _context.SpecialPenalties
                    .Include(sp => sp.Taxpayer)
                    .Include(sp => sp.CreatedBy)
                    .Include(sp => sp.UpdatedBy)
                    .FirstOrDefaultAsync(sp => sp.SpecialPenaltyId == specialPenalty.SpecialPenaltyId);

                var response = new SpecialPenaltyResponseDto
                {
                    SpecialPenaltyId = createdSpecialPenalty!.SpecialPenaltyId,
                    SpecialPenaltyNo = createdSpecialPenalty.SpecialPenaltyNo,
                    TaxpayerInfo = new TaxpayerInfoDto
                    {
                        TaxpayerId = createdSpecialPenalty.Taxpayer.Id,
                        FullName = createdSpecialPenalty.Taxpayer.FullName,
                        PAN = createdSpecialPenalty.Taxpayer.PAN,
                        PhoneNumber = createdSpecialPenalty.Taxpayer.PhoneNumber,
                        Email = createdSpecialPenalty.Taxpayer.Email
                    },
                    AccountType = createdSpecialPenalty.AccountType,
                    TaxYear = createdSpecialPenalty.TaxYear,
                    Reason = createdSpecialPenalty.Reason,
                    OtherReason = createdSpecialPenalty.OtherReason,
                    ReasonDetails = createdSpecialPenalty.ReasonDetails,
                    EffectiveDate = createdSpecialPenalty.EffectiveDate,
                    Amount = createdSpecialPenalty.Amount,
                    Status = createdSpecialPenalty.Status,
                    CreatedAt = createdSpecialPenalty.CreatedAt,
                    UpdatedAt = createdSpecialPenalty.UpdatedAt,
                    CreatedBy = createdSpecialPenalty.CreatedBy?.FullName,
                    UpdatedBy = createdSpecialPenalty.UpdatedBy?.FullName
                };

                return CreatedAtAction(nameof(GetSpecialPenalty), new { id = response.SpecialPenaltyId }, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating special penalty");
                return StatusCode(500, "An error occurred while creating the special penalty");
            }
        }

        /// <summary>
        /// Update an existing special penalty
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Officer,Admin")]
        public async Task<ActionResult<SpecialPenaltyResponseDto>> UpdateSpecialPenalty(
            Guid id, [FromBody] UpdateSpecialPenaltyDto updateDto)
        {
            try
            {
                var specialPenalty = await _context.SpecialPenalties
                    .Include(sp => sp.Taxpayer)
                    .FirstOrDefaultAsync(sp => sp.SpecialPenaltyId == id);

                if (specialPenalty == null)
                {
                    return NotFound($"Special penalty with ID {id} not found");
                }

                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value!;

                // Update fields
                if (!string.IsNullOrEmpty(updateDto.AccountType))
                    specialPenalty.AccountType = updateDto.AccountType;
                
                if (updateDto.TaxYear.HasValue)
                    specialPenalty.TaxYear = updateDto.TaxYear.Value;
                
                if (!string.IsNullOrEmpty(updateDto.Reason))
                    specialPenalty.Reason = updateDto.Reason;
                
                if (updateDto.OtherReason != null)
                    specialPenalty.OtherReason = updateDto.OtherReason;
                
                if (updateDto.ReasonDetails != null)
                    specialPenalty.ReasonDetails = updateDto.ReasonDetails;
                
                if (updateDto.EffectiveDate.HasValue)
                    specialPenalty.EffectiveDate = updateDto.EffectiveDate.Value;
                
                if (updateDto.Amount.HasValue)
                    specialPenalty.Amount = updateDto.Amount.Value;

                specialPenalty.UpdatedAt = DateTime.UtcNow;
                specialPenalty.UpdatedById = currentUserId;

                await _context.SaveChangesAsync();

                // Reload with includes for response
                var updatedSpecialPenalty = await _context.SpecialPenalties
                    .Include(sp => sp.Taxpayer)
                    .Include(sp => sp.CreatedBy)
                    .Include(sp => sp.UpdatedBy)
                    .FirstOrDefaultAsync(sp => sp.SpecialPenaltyId == id);

                var response = new SpecialPenaltyResponseDto
                {
                    SpecialPenaltyId = updatedSpecialPenalty!.SpecialPenaltyId,
                    SpecialPenaltyNo = updatedSpecialPenalty.SpecialPenaltyNo,
                    TaxpayerInfo = new TaxpayerInfoDto
                    {
                        TaxpayerId = updatedSpecialPenalty.Taxpayer.Id,
                        FullName = updatedSpecialPenalty.Taxpayer.FullName,
                        PAN = updatedSpecialPenalty.Taxpayer.PAN,
                        PhoneNumber = updatedSpecialPenalty.Taxpayer.PhoneNumber,
                        Email = updatedSpecialPenalty.Taxpayer.Email
                    },
                    AccountType = updatedSpecialPenalty.AccountType,
                    TaxYear = updatedSpecialPenalty.TaxYear,
                    Reason = updatedSpecialPenalty.Reason,
                    OtherReason = updatedSpecialPenalty.OtherReason,
                    ReasonDetails = updatedSpecialPenalty.ReasonDetails,
                    EffectiveDate = updatedSpecialPenalty.EffectiveDate,
                    Amount = updatedSpecialPenalty.Amount,
                    Status = updatedSpecialPenalty.Status,
                    CreatedAt = updatedSpecialPenalty.CreatedAt,
                    UpdatedAt = updatedSpecialPenalty.UpdatedAt,
                    CreatedBy = updatedSpecialPenalty.CreatedBy?.FullName,
                    UpdatedBy = updatedSpecialPenalty.UpdatedBy?.FullName
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating special penalty with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the special penalty");
            }
        }

        /// <summary>
        /// Update the status of a special penalty
        /// </summary>
        [HttpPatch("{id}/status")]
        [Authorize(Roles = "Officer,Admin")]
        public async Task<ActionResult> UpdateSpecialPenaltyStatus(
            Guid id, [FromBody] SpecialPenaltyStatusUpdateDto statusUpdateDto)
        {
            try
            {
                var specialPenalty = await _context.SpecialPenalties.FindAsync(id);
                if (specialPenalty == null)
                {
                    return NotFound($"Special penalty with ID {id} not found");
                }

                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value!;

                specialPenalty.Status = statusUpdateDto.Status;
                specialPenalty.UpdatedAt = DateTime.UtcNow;
                specialPenalty.UpdatedById = currentUserId;

                await _context.SaveChangesAsync();

                return Ok(new { message = "Status updated successfully", status = specialPenalty.Status });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating status for special penalty with ID {Id}", id);
                return StatusCode(500, "An error occurred while updating the status");
            }
        }

        /// <summary>
        /// Delete a special penalty
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> DeleteSpecialPenalty(Guid id)
        {
            try
            {
                var specialPenalty = await _context.SpecialPenalties.FindAsync(id);
                if (specialPenalty == null)
                {
                    return NotFound($"Special penalty with ID {id} not found");
                }

                _context.SpecialPenalties.Remove(specialPenalty);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Special penalty deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting special penalty with ID {Id}", id);
                return StatusCode(500, "An error occurred while deleting the special penalty");
            }
        }

        /// <summary>
        /// Get special penalties for a specific taxpayer
        /// </summary>
        [HttpGet("taxpayer/{taxpayerId}")]
        [Authorize(Roles = "Officer,Admin,Taxpayer")]
        public async Task<ActionResult<IEnumerable<SpecialPenaltyResponseDto>>> GetSpecialPenaltiesByTaxpayer(string taxpayerId)
        {
            try
            {
                // Check if current user can access this taxpayer's data
                var currentUserIdString = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                
                if (userRole == "Taxpayer" && currentUserIdString != taxpayerId)
                {
                    return Forbid("You can only access your own special penalties");
                }

                var specialPenalties = await _context.SpecialPenalties
                    .Include(sp => sp.Taxpayer)
                    .Include(sp => sp.CreatedBy)
                    .Include(sp => sp.UpdatedBy)
                    .Where(sp => sp.TaxpayerId == taxpayerId)
                    .OrderByDescending(sp => sp.CreatedAt)
                    .ToListAsync();

                var response = specialPenalties.Select(sp => new SpecialPenaltyResponseDto
                {
                    SpecialPenaltyId = sp.SpecialPenaltyId,
                    SpecialPenaltyNo = sp.SpecialPenaltyNo,
                    TaxpayerInfo = new TaxpayerInfoDto
                    {
                        TaxpayerId = sp.Taxpayer.Id,
                        FullName = sp.Taxpayer.FullName,
                        PAN = sp.Taxpayer.PAN,
                        PhoneNumber = sp.Taxpayer.PhoneNumber,
                        Email = sp.Taxpayer.Email
                    },
                    AccountType = sp.AccountType,
                    TaxYear = sp.TaxYear,
                    Reason = sp.Reason,
                    OtherReason = sp.OtherReason,
                    ReasonDetails = sp.ReasonDetails,
                    EffectiveDate = sp.EffectiveDate,
                    Amount = sp.Amount,
                    Status = sp.Status,
                    CreatedAt = sp.CreatedAt,
                    UpdatedAt = sp.UpdatedAt,
                    CreatedBy = sp.CreatedBy?.FullName,
                    UpdatedBy = sp.UpdatedBy?.FullName
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving special penalties for taxpayer {TaxpayerId}", taxpayerId);
                return StatusCode(500, "An error occurred while retrieving special penalties");
            }
        }
    }
}