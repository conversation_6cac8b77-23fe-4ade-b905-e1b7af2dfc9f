using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Models;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace LandTaxSystem.Infrastructure.Services
{
    public class GisService
    {
        public Polygon ConvertGeoJsonToGeometry(string geoJson)
        {
            if (string.IsNullOrEmpty(geoJson))
                throw new ArgumentException("GeoJSON is empty or null");

            try
            {
                // Parse and validate/fix the GeoJSON before processing
                var fixedGeoJson = EnsureValidPolygonGeoJson(geoJson);

                var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(4326);
                var reader = new GeoJsonReader();
                var geometry = reader.Read<Geometry>(fixedGeoJson);

                if (geometry is not Polygon polygon)
                    throw new ArgumentException("The GeoJSON does not represent a valid polygon");

                // Ensure the geometry has SRID 4326 (WGS84)
                polygon.SRID = 4326;

                return polygon;
            }
            catch (Exception ex) when (!(ex is ArgumentException))
            {
                throw new ArgumentException($"Failed to parse GeoJSON: {ex.Message}", ex);
            }
        }

        private string EnsureValidPolygonGeoJson(string geoJson)
        {
            try
            {
                var geoJsonObject = JsonSerializer.Deserialize<JsonElement>(geoJson);

                if (!geoJsonObject.TryGetProperty("type", out var typeElement) ||
                    typeElement.GetString() != "Polygon")
                {
                    throw new ArgumentException("GeoJSON must be a Polygon type");
                }

                if (!geoJsonObject.TryGetProperty("coordinates", out var coordinatesElement))
                {
                    throw new ArgumentException("GeoJSON Polygon must have coordinates property");
                }

                var coordinates = coordinatesElement.EnumerateArray().ToList();
                if (coordinates.Count == 0)
                {
                    throw new ArgumentException("GeoJSON Polygon coordinates cannot be empty");
                }

                // Fix the exterior ring (first coordinate array)
                var exteriorRing = coordinates[0].EnumerateArray().ToList();
                if (exteriorRing.Count < 4)
                {
                    throw new ArgumentException("GeoJSON Polygon exterior ring must have at least 4 coordinates");
                }

                // Check if the ring is closed (first and last coordinates are the same)
                var firstCoord = exteriorRing.First().EnumerateArray().Select(x => x.GetDouble()).ToArray();
                var lastCoord = exteriorRing.Last().EnumerateArray().Select(x => x.GetDouble()).ToArray();

                bool isClosed = firstCoord.Length == lastCoord.Length &&
                               firstCoord.Zip(lastCoord, (a, b) => Math.Abs(a - b) < 1e-10).All(x => x);

                if (!isClosed)
                {
                    // Auto-close the polygon by adding the first coordinate as the last one
                    var fixedExteriorRing = exteriorRing.ToList();
                    fixedExteriorRing.Add(exteriorRing.First());

                    // Rebuild the GeoJSON with the fixed coordinates
                    var fixedCoordinates = new List<object> { fixedExteriorRing.Select(coord => coord.EnumerateArray().Select(x => x.GetDouble()).ToArray()).ToArray() };

                    // Add any interior rings (holes) as-is for now
                    for (int i = 1; i < coordinates.Count; i++)
                    {
                        fixedCoordinates.Add(coordinates[i].EnumerateArray().Select(ring => ring.EnumerateArray().Select(x => x.GetDouble()).ToArray()).ToArray());
                    }

                    var fixedGeoJson = new
                    {
                        type = "Polygon",
                        coordinates = fixedCoordinates.ToArray()
                    };

                    return JsonSerializer.Serialize(fixedGeoJson);
                }

                return geoJson; // Already valid
            }
            catch (JsonException ex)
            {
                throw new ArgumentException($"Invalid JSON format: {ex.Message}", ex);
            }
        }
        public string ConvertGeometryToGeoJson(Geometry geometry)
        {
            var writer = new GeoJsonWriter();
            return writer.Write(geometry);
        }

        public decimal CalculatePropertyValue(Property property, string valuationRulesConfigJson)
        {
            // Legacy method - parse JSON internally
            ValuationRulesConfig valuationRules;
            
            // Handle null or empty JSON strings gracefully
            if (string.IsNullOrWhiteSpace(valuationRulesConfigJson))
            {
                // No hardcoded fallbacks - if valuation rules are missing, return 0
                return 0;
            }
            else
            {
                try
                {
                    valuationRules = JsonSerializer.Deserialize<ValuationRulesConfig>(valuationRulesConfigJson) ?? new ValuationRulesConfig();
                    if (valuationRules.LandMVR == null || 
                        !valuationRules.LandMVR.Any() || 
                        valuationRules.BuildingBaseRatePerSqm == null || 
                        !valuationRules.BuildingBaseRatePerSqm.Any())
                    {
                        // If any required configuration is missing, return 0
                        return 0;
                    }
                }
                catch (JsonException)
                {
                    // If JSON parsing fails, return 0
                    return 0;
                }
            }
            
            return CalculatePropertyValue(property, valuationRules);
        }
        
        public decimal CalculatePropertyValue(Property property, ValuationRulesConfig valuationRules)
        {

            // Calculate land value
            decimal landValue = 0;
            if (valuationRules.LandMVR.TryGetValue(property.UsageType, out var mvrRate))
            {
                landValue = property.LandAreaSqM * mvrRate;
            }
            else
            {
                // Default rate if usage type not found
                landValue = property.LandAreaSqM * 1000;
            }

            // Calculate building value if applicable
            decimal buildingValue = 0;
            if (property.BuildingBuiltUpAreaSqM.HasValue &&
                property.BuildingConstructionType != null &&
                property.BuildingConstructionYear.HasValue)
            {
                // Get building base rate
                decimal baseRate = 0;
                if (valuationRules.BuildingBaseRatePerSqm.TryGetValue(property.BuildingConstructionType, out var rate))
                {
                    baseRate = rate;
                }
                else
                {
                    // Default rate if construction type not found
                    baseRate = 5000;
                }

                // Calculate base building value
                decimal baseBuildingValue = property.BuildingBuiltUpAreaSqM.Value * baseRate;

                // Apply depreciation
                int buildingAge = DateTime.Now.Year - property.BuildingConstructionYear.Value;
                decimal depreciationFactor = buildingAge * valuationRules.AnnualDepreciationRate;

                // Cap depreciation at 60%
                if (depreciationFactor > 0.6m)
                    depreciationFactor = 0.6m;

                // Apply depreciation to building value
                buildingValue = baseBuildingValue * (1 - depreciationFactor);
            }

            // Total assessed value
            return landValue + buildingValue;
        }

        public decimal CalculateTaxAmount(decimal assessedValue, string taxSlabsConfigJson, string exemptionRulesConfigJson)
        {
            // Legacy method - parse JSON internally
            List<LegacyTaxSlab> taxSlabs;
            ExemptionRulesConfig exemptionRules;
            
            // Handle null or empty tax slabs JSON
            if (string.IsNullOrWhiteSpace(taxSlabsConfigJson))
            {
                // No hardcoded fallbacks - if tax slabs are missing, return 0
                return 0;
            }
            else
            {
                try
                {
                    taxSlabs = JsonSerializer.Deserialize<List<LegacyTaxSlab>>(taxSlabsConfigJson) ?? new List<LegacyTaxSlab>();
                    
                    // If deserialized successfully but list is empty, return 0
                    if (taxSlabs.Count == 0)
                    {
                        return 0;
                    }
                }
                catch (JsonException)
                {
                    // If JSON parsing fails, return 0
                    return 0;
                }
            }
            
            // Handle null or empty exemption rules JSON
            if (string.IsNullOrWhiteSpace(exemptionRulesConfigJson))
            {
                exemptionRules = new ExemptionRulesConfig
                {
                    Rules = new List<ExemptionRule>()
                };
            }
            else
            {
                try
                {
                    exemptionRules = JsonSerializer.Deserialize<ExemptionRulesConfig>(exemptionRulesConfigJson) ?? new ExemptionRulesConfig();
                }
                catch (JsonException)
                {
                    // If JSON parsing fails, use empty rules
                    exemptionRules = new ExemptionRulesConfig
                    {
                        Rules = new List<ExemptionRule>()
                    };
                }
            }
            
            return CalculateTaxAmount(assessedValue, taxSlabs, exemptionRules);
        }
        
        public decimal CalculateTaxAmount(decimal assessedValue, List<LegacyTaxSlab> taxSlabs, ExemptionRulesConfig exemptionRules)
        {

            // Find applicable tax slab
            var applicableSlab = taxSlabs
                .Where(slab => assessedValue >= slab.MinAssessedValue &&
                              (slab.MaxAssessedValue == 0 || assessedValue <= slab.MaxAssessedValue))
                .OrderBy(slab => slab.MinAssessedValue)
                .FirstOrDefault();

            if (applicableSlab == null)
            {
                // Default 1% tax if no slab found
                return assessedValue * 0.01m;
            }

            // Calculate tax based on slab
            decimal tax = applicableSlab.FixedAmount + ((assessedValue - applicableSlab.MinAssessedValue) * (applicableSlab.RatePercent / 100));

            // For MVP, we skip complex exemption rule evaluation (would require a rule engine)
            // In a real implementation, we would evaluate the exemption rule conditions here

            return tax;
        }
    }
}
