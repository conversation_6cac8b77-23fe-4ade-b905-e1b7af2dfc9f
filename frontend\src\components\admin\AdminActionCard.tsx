import React from 'react';
import { Link } from 'react-router-dom';

interface ActionItem {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
  to: string;
  badge?: {
    text: string;
    color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
  };
}

interface AdminActionCardProps {
  title: string;
  actions: ActionItem[];
  className?: string;
  columns?: 1 | 2 | 3 | 4;
}

const AdminActionCard: React.FC<AdminActionCardProps> = ({ 
  title, 
  actions, 
  className = '',
  columns = 3
}) => {
  // Determine the grid columns based on the columns prop
  const gridClass = `grid-cols-1 ${
    columns === 1 ? 'md:grid-cols-1' :
    columns === 2 ? 'md:grid-cols-2' :
    columns === 3 ? 'md:grid-cols-3' :
    'md:grid-cols-2 lg:grid-cols-4'
  }`;

  // Function to get color classes for background
  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'primary': return 'bg-primary/10 text-primary';
      case 'secondary': return 'bg-secondary/10 text-secondary';
      case 'accent': return 'bg-accent/10 text-accent';
      case 'info': return 'bg-info/10 text-info';
      case 'success': return 'bg-success/10 text-success';
      case 'warning': return 'bg-warning/10 text-warning';
      case 'error': return 'bg-error/10 text-error';
      default: return 'bg-primary/10 text-primary';
    }
  };

  // Function to get badge color classes
  const getBadgeColorClasses = (color?: string) => {
    switch (color) {
      case 'primary': return 'badge-primary';
      case 'secondary': return 'badge-secondary';
      case 'accent': return 'badge-accent';
      case 'info': return 'badge-info';
      case 'success': return 'badge-success';
      case 'warning': return 'badge-warning';
      case 'error': return 'badge-error';
      default: return 'badge-primary';
    }
  };

  return (
    <div className={`card bg-base-100 shadow-xl ${className}`}>
      <div className="card-body">
        <h3 className="card-title text-lg">{title}</h3>
        <div className={`grid ${gridClass} gap-4 mt-4`}>
          {actions.map((action, index) => (
            <Link
              key={index}
              to={action.to}
              className="card bg-base-200 hover:bg-base-300 transition-colors cursor-pointer"
            >
              <div className="card-body flex-row items-center p-4">
                {action.icon && (
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${getColorClasses(action.color)}`}>
                    {action.icon}
                  </div>
                )}
                <div className="flex-1">
                  <div className="flex items-center">
                    <h4 className="font-medium text-base-content">{action.title}</h4>
                    {action.badge && (
                      <span className={`badge ${getBadgeColorClasses(action.badge.color)} ml-2 badge-sm`}>
                        {action.badge.text}
                      </span>
                    )}
                  </div>
                  {action.description && (
                    <p className="text-sm text-base-content/70">{action.description}</p>
                  )}
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdminActionCard;