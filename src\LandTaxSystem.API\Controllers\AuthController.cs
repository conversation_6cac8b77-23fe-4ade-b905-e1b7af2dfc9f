using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Auth;
using LandTaxSystem.Core.DTOs.Municipality;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly TokenService _tokenService;
        private readonly ILogger<AuthController> _logger;
        private readonly IWebHostEnvironment _environment;
        private readonly ApplicationDbContext _context;
        private readonly IEmailService _emailService;

        public AuthController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            TokenService tokenService,
            ILogger<AuthController> logger,
            IWebHostEnvironment environment,
            ApplicationDbContext context,
            IEmailService emailService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _tokenService = tokenService;
            _logger = logger;
            _environment = environment;
            _context = context;
            _emailService = emailService;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromForm] RegisterDto registerDto, IFormFile citizenshipDocument, IFormFile? nationalIdDocument = null, IFormFile? panDocument = null)
        {
            if (await _userManager.FindByEmailAsync(registerDto.Email) != null)
            {
                return BadRequest("Email already in use");
            }
            
            // Validate citizenship document
            if (citizenshipDocument == null || citizenshipDocument.Length == 0)
            {
                return BadRequest("Citizenship document is required");
            }
            
            if (citizenshipDocument.Length > 5 * 1024 * 1024) // 5MB limit
            {
                return BadRequest("Citizenship document must be less than 5MB");
            }
            
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".pdf" };
            var fileExtension = Path.GetExtension(citizenshipDocument.FileName).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension))
            {
                return BadRequest("Only JPG, PNG and PDF files are allowed");
            }
            
            // Create directory if it doesn't exist
            var uploadsFolder = Path.Combine(_environment.ContentRootPath, "uploads", "documents");
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }
            
            // Generate unique filename for citizenship document
            var citizenshipFileName = $"citizenship_{Guid.NewGuid()}{fileExtension}";
            var citizenshipFilePath = Path.Combine(uploadsFolder, citizenshipFileName);
            
            // Save citizenship document
            using (var fileStream = new FileStream(citizenshipFilePath, FileMode.Create))
            {
                await citizenshipDocument.CopyToAsync(fileStream);
            }
            
            // Process National ID document if provided
            string? nationalIdFilePath = null;
            if (nationalIdDocument != null && nationalIdDocument.Length > 0)
            {
                if (nationalIdDocument.Length > 5 * 1024 * 1024) // 5MB limit
                {
                    return BadRequest("National ID document must be less than 5MB");
                }
                
                var nationalIdExtension = Path.GetExtension(nationalIdDocument.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(nationalIdExtension))
                {
                    return BadRequest("Only JPG, PNG and PDF files are allowed for National ID");
                }
                
                var nationalIdFileName = $"nationalid_{Guid.NewGuid()}{nationalIdExtension}";
                nationalIdFilePath = Path.Combine(uploadsFolder, nationalIdFileName);
                
                using (var fileStream = new FileStream(nationalIdFilePath, FileMode.Create))
                {
                    await nationalIdDocument.CopyToAsync(fileStream);
                }
            }
            
            // Process PAN document if provided
            string? panFilePath = null;
            if (panDocument != null && panDocument.Length > 0)
            {
                if (panDocument.Length > 5 * 1024 * 1024) // 5MB limit
                {
                    return BadRequest("PAN document must be less than 5MB");
                }
                
                var panExtension = Path.GetExtension(panDocument.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(panExtension))
                {
                    return BadRequest("Only JPG, PNG and PDF files are allowed for PAN");
                }
                
                var panFileName = $"pan_{Guid.NewGuid()}{panExtension}";
                panFilePath = Path.Combine(uploadsFolder, panFileName);
                
                using (var fileStream = new FileStream(panFilePath, FileMode.Create))
                {
                    await panDocument.CopyToAsync(fileStream);
                }
            }
            
            // Create TwoGenerations JSON string
            var twoGenerations = System.Text.Json.JsonSerializer.Serialize(new
            {
                FatherName = registerDto.FatherName,
                GrandfatherName = registerDto.GrandfatherName
            });
            
            // Create contact preferences JSON
            var contactPreferences = System.Text.Json.JsonSerializer.Serialize(new
            {
                PreferSMS = registerDto.PreferSMS,
                PreferEmail = registerDto.PreferEmail
            });
            
            var submissionNumber = $"SUB-{DateTime.UtcNow.Year}-{GenerateRandomAlphanumeric(6)}";
            
            // Generate a unique PAN number if not provided
            string panNumber = $"PAN-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString().Substring(0, 6)}".ToUpper();
            
            // Ensure all DateTime fields are in UTC format to avoid PostgreSQL timezone issues
            DateTime dateOfBirthUtc = DateTime.SpecifyKind(registerDto.DateOfBirth, DateTimeKind.Utc);
            DateTime? citizenshipIssueDateUtc = registerDto.CitizenshipIssueDate.HasValue 
                ? DateTime.SpecifyKind(registerDto.CitizenshipIssueDate.Value, DateTimeKind.Utc) 
                : null;
            DateTime? nationalIdIssueDateUtc = registerDto.NationalIdIssueDate.HasValue 
                ? DateTime.SpecifyKind(registerDto.NationalIdIssueDate.Value, DateTimeKind.Utc) 
                : null;
            DateTime? panIssueDateUtc = registerDto.PanIssueDate.HasValue 
                ? DateTime.SpecifyKind(registerDto.PanIssueDate.Value, DateTimeKind.Utc) 
                : null;
            
            // Construct full name from parts
            string fullName = registerDto.FirstName;
            if (!string.IsNullOrWhiteSpace(registerDto.MiddleName))
            {
                fullName += " " + registerDto.MiddleName;
            }
            fullName += " " + registerDto.LastName;
            
            // Construct permanent address
            var municipality = await _context.Municipalities.FindAsync(registerDto.PermanentMunicipalityId);
            string permanentAddress = $"{registerDto.PermanentTole}, Ward {registerDto.PermanentWardNumber}";
            if (municipality != null)
            {
                permanentAddress += $", {municipality.Name}";
            }
            if (!string.IsNullOrWhiteSpace(registerDto.PermanentHouseNumber))
            {
                permanentAddress += $", House #{registerDto.PermanentHouseNumber}";
            }

            // Construct temporary address if different from permanent
            string temporaryAddress = string.Empty;
            if (registerDto.TemporaryMunicipalityId.HasValue && 
                (registerDto.TemporaryMunicipalityId != registerDto.PermanentMunicipalityId ||
                 registerDto.TemporaryWardNumber != registerDto.PermanentWardNumber ||
                 registerDto.TemporaryTole != registerDto.PermanentTole))
            {
                var tempMunicipality = await _context.Municipalities.FindAsync(registerDto.TemporaryMunicipalityId);
                temporaryAddress = $"{registerDto.TemporaryTole}, Ward {registerDto.TemporaryWardNumber}";
                if (tempMunicipality != null)
                {
                    temporaryAddress += $", {tempMunicipality.Name}";
                }
                if (!string.IsNullOrWhiteSpace(registerDto.TemporaryHouseNumber))
                {
                    temporaryAddress += $", House #{registerDto.TemporaryHouseNumber}";
                }
            }
            
            // Determine if we need to use guardian information
            bool isMinor = registerDto.IsMinor || (registerDto.DateOfBirth > DateTime.UtcNow.AddYears(-18));
            
            var user = new ApplicationUser
            {
                FullName = fullName,
                Email = registerDto.Email,
                UserName = registerDto.Email,
                PhoneNumber = registerDto.Mobile,
                Telephone = registerDto.Telephone,
                DateOfBirth = dateOfBirthUtc,
                Gender = registerDto.Gender,
                Profession = registerDto.Profession,
                CitizenshipNumber = registerDto.CitizenshipNumber,
                FatherName = registerDto.FatherName,
                MotherName = registerDto.MotherName,
                GrandfatherName = registerDto.GrandfatherName,
                GrandmotherName = registerDto.GrandmotherName,
                MaritalStatus = registerDto.MaritalStatus,
                SpouseName = registerDto.SpouseName,
                Nationality = registerDto.Nationality,
                IsMinor = isMinor,
                GuardianName = isMinor ? registerDto.GuardianName : null,
                GuardianRelation = isMinor ? registerDto.GuardianRelation : null,
                TwoGenerations = twoGenerations,
                PermanentAddress = permanentAddress,
                TemporaryAddress = !string.IsNullOrEmpty(temporaryAddress) ? temporaryAddress : null,
                MunicipalityId = registerDto.PermanentMunicipalityId,
                WardNumber = registerDto.PermanentWardNumber.ToString(),
                ToleStreet = registerDto.PermanentTole,
                DocumentPath = citizenshipFilePath,
                
                // Document Metadata
                CitizenshipIssueDistrict = registerDto.CitizenshipIssueDistrict,
                CitizenshipIssueDate = citizenshipIssueDateUtc,
                CitizenshipIssueOffice = registerDto.CitizenshipIssueOffice,
                
                NationalIdNumber = registerDto.NationalIdNumber,
                NationalIdIssueDistrict = registerDto.NationalIdIssueDistrict,
                NationalIdIssueDate = nationalIdIssueDateUtc,
                NationalIdIssueOffice = registerDto.NationalIdIssueOffice,
                NationalIdDocumentPath = nationalIdFilePath,
                
                PanIssueDate = panIssueDateUtc,
                PanIssueDistrict = registerDto.PanIssueDistrict,
                PanIssueOffice = registerDto.PanIssueOffice,
                PanDocumentPath = panFilePath,
                
                PAN = !string.IsNullOrWhiteSpace(registerDto.PAN) ? registerDto.PAN : panNumber, // Use provided PAN or generate one
                SubmissionNumber = submissionNumber,
                Role = "Citizen",
                Status = "PendingApproval",
                ContactPreferences = contactPreferences,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            
            var result = await _userManager.CreateAsync(user, registerDto.Password);
            
            if (result.Succeeded)
            {
                // Log the submission for tracking
                _logger.LogInformation("New user registration with submission number {SubmissionNumber}", submissionNumber);
                
                // Send registration confirmation email
                try
                {
                    await _emailService.SendRegistrationConfirmationAsync(user.Email, user.FullName, Guid.Parse(user.Id), user.PAN);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send registration confirmation email to {Email}", user.Email);
                    // Don't fail the registration if email fails
                }
                
                // Create in-app notification for registration confirmation
                 var notification = new Notification
                 {
                     UserId = user.Id,
                     Message = "Your registration has been submitted successfully. Please wait for admin approval.",
                     Type = "registration",
                     IsRead = false,
                     RelatedEntityId = Guid.Parse(user.Id),
                     RelatedEntityType = "User"
                 };
                 _context.Notifications.Add(notification);
                 await _context.SaveChangesAsync();

                 // Return the submission number to the user for tracking their application
                 return CreatedAtAction(nameof(Register), new {
                     userId = user.Id,
                     submissionNumber = submissionNumber,
                     message = "Registration successful. Your account is pending approval. Use your submission number to track the status."
                 });
            }
            
            // Delete the uploaded files if user creation fails
            if (System.IO.File.Exists(citizenshipFilePath))
            {
                System.IO.File.Delete(citizenshipFilePath);
            }
            
            if (nationalIdFilePath != null && System.IO.File.Exists(nationalIdFilePath))
            {
                System.IO.File.Delete(nationalIdFilePath);
            }
            
            if (panFilePath != null && System.IO.File.Exists(panFilePath))
            {
                System.IO.File.Delete(panFilePath);
            }
            
            return BadRequest(result.Errors);
        }
        [HttpPost("login")]
        public async Task<IActionResult> Login(LoginDto loginDto)
        {
            // Validate that either Email or PAN is provided
            if (string.IsNullOrWhiteSpace(loginDto.Email) && string.IsNullOrWhiteSpace(loginDto.PAN))
            {
                return BadRequest("Either Email or PAN must be provided");
            }

            ApplicationUser? user = null;
            
            // Try to find user by Email
            if (!string.IsNullOrWhiteSpace(loginDto.Email))
            {
                user = await _userManager.FindByEmailAsync(loginDto.Email);
            }
            
            // If user not found by Email, try by PAN
            if (user == null && !string.IsNullOrWhiteSpace(loginDto.PAN))
            {
                user = await _context.Users.FirstOrDefaultAsync(u => u.PAN == loginDto.PAN);
            }

            if (user == null)
            {
                return Unauthorized("Invalid credentials");
            }

            if (user.Status != "Active")
            {
                // Accounts can be PendingApproval, Rejected, or (in future) other non-Active states.
                _logger.LogWarning("Login attempt for non-active user {UserEmail} with status {UserStatus}", loginDto.Email, user.Status);
                if (user.Status == "PendingApproval")
                {
                    return Unauthorized("Your account is pending approval. Please check your email or contact support.");
                }
                else if (user.Status == "Rejected")
                {
                    return Unauthorized("Your account has been rejected. Please contact support for more information.");
                }
                return Unauthorized("Your account is not active. Please contact support.");
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, loginDto.Password, false);

            if (result.Succeeded)
            {
                // Update LastLoginAt timestamp
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                var token = _tokenService.CreateToken(user);

                return Ok(new AuthResponseDto
                {
                    Token = token,
                    UserId = user.Id,
                    Role = user.Role
                });
            }

            return Unauthorized("Invalid email or password");
        }
        
        [HttpGet("registration-municipalities")]
        public async Task<ActionResult<IEnumerable<MunicipalityListItemDto>>> GetMunicipalitiesForRegistration()
        {
            var municipalities = await _context.Municipalities
                .Select(m => new MunicipalityListItemDto
                {
                    MunicipalityId = m.MunicipalityId,
                    Name = m.Name
                })
                .ToListAsync();

            return Ok(municipalities);
        }

        private string GenerateRandomAlphanumeric(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
