using System;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.User
{
    public class SubmissionStatusDto
    {
        public string SubmissionNumber { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // Registration, PropertyVerification, Appeal
        public DateTime SubmissionDate { get; set; }
        public string? Comments { get; set; }
        public string? RejectionReason { get; set; }
    }

    public class UserResponseDto
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty; // Assuming FirstName and LastName are combined or FullName is preferred
        public string? PhoneNumber { get; set; }
        public string? Telephone { get; set; }
        public string Role { get; set; } = string.Empty;
        public string? PAN { get; set; }
        public string? TwoGenerations { get; set; }
        public string? SubmissionNumber { get; set; }
        public string Status { get; set; } = string.Empty; // PendingApproval, Active, Rejected
        public Guid? MunicipalityId { get; set; }
        public string? MunicipalityName { get; set; }
        public string WardNumber { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        
        // Personal Information
        public DateTime DateOfBirth { get; set; }
        public string Gender { get; set; } = string.Empty;
        public string? Nationality { get; set; }
        public string? Profession { get; set; }
        public string CitizenshipNumber { get; set; } = string.Empty;
        
        // Family Information
        public string? FatherName { get; set; }
        public string? MotherName { get; set; }
        public string? GrandfatherName { get; set; }
        public string? GrandmotherName { get; set; }
        public string? MaritalStatus { get; set; }
        public string? SpouseName { get; set; }
        public bool IsMinor { get; set; }
        public string? GuardianName { get; set; }
        public string? GuardianRelation { get; set; }
        
        // Address Information
        public string? PermanentAddress { get; set; }
        public string? TemporaryAddress { get; set; }
        public string? ToleStreet { get; set; }
        
        // Document Information
        public string? DocumentPath { get; set; }
        
        // Document Metadata
        public string? CitizenshipIssueDistrict { get; set; }
        public DateTime? CitizenshipIssueDate { get; set; }
        public string? CitizenshipIssueOffice { get; set; }
        
        public string? NationalIdNumber { get; set; }
        public string? NationalIdIssueDistrict { get; set; }
        public DateTime? NationalIdIssueDate { get; set; }
        public string? NationalIdIssueOffice { get; set; }
        public string? NationalIdDocumentPath { get; set; }
        
        public DateTime? PanIssueDate { get; set; }
        public string? PanIssueDistrict { get; set; }
        public string? PanIssueOffice { get; set; }
        public string? PanDocumentPath { get; set; }
        
        // Contact Preferences
        public string? ContactPreferences { get; set; }
    }

    public class CreateUserDto
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }
        
        [StringLength(20)]
        public string? PAN { get; set; }

        [StringLength(500)]
        public string? TwoGenerations { get; set; }

        [Required]
        public string Role { get; set; } = string.Empty;

        public Guid? MunicipalityId { get; set; }
    }

    public class UpdateUserDto
    {
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Phone]
        public string? PhoneNumber { get; set; }
        [Required]
        public string Role { get; set; } = string.Empty;

        public Guid? MunicipalityId { get; set; }
        // IsActive is removed, status updates will be handled by a dedicated endpoint and DTO
    }

    public class ChangePasswordDto
    {
        [Required]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string NewPassword { get; set; } = string.Empty;
    }

    public class ResetPasswordDto
    {
        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string NewPassword { get; set; } = string.Empty;
    }
    public class UserListQueryDto
    {
        public string? Role { get; set; }
        public Guid? MunicipalityId { get; set; }
        public string? WardNumber { get; set; }
        public string? Status { get; set; } // Replaced IsActive with Status
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class UserStatusUpdateDto
    {
        [Required]
        [StringLength(20)] // Max length for 'PendingApproval', 'Active', 'Rejected'
        public string Status { get; set; } = null!;

        [StringLength(500)] // Optional reason, especially for 'Rejected' status
        public string? RejectionReason { get; set; }
    }
}
