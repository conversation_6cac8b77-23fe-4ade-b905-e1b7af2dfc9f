using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Appeal
{
    public class TaxPeriodDto
    {
        [Required]
        public string FiscalYear { get; set; } = null!;
        
        [Required]
        public string StartDate { get; set; } = null!;
        
        [Required]
        public string EndDate { get; set; } = null!;
        
        public string? Year { get; set; }
        
        public string? Period { get; set; }
        
        public string? TaxPeriodValue { get; set; }
        
        public string? AppealSubject { get; set; }
        
        [Required]
        public decimal AppealAmount { get; set; }
    }

    public class CreateAppealDto
    {
        [Required]
        public Guid AssessmentId { get; set; }
        
        [Required]
        [StringLength(500, MinimumLength = 10)]
        public string Reason { get; set; } = null!;
        
        [Required]
        public string OfficeCode { get; set; } = null!;
        
        public string? LocationCode { get; set; }
        
        [Required]
        public string TaxpayerName { get; set; } = null!;
        
        public string? TaxpayerAddress { get; set; }
        
        [Required]
        public DateTime AppealDate { get; set; }
        
        [Required]
        public string AppealSubject { get; set; } = null!;
        
        public string? TaxDeterminationOrderNumber { get; set; }
        
        public string? AppealAuthority { get; set; }
        
        public string? RegistrationNumber { get; set; }
        
        public string? OrderNumber { get; set; }
        
        public DateTime? OrderDate { get; set; }
        
        public string? AppealDescription { get; set; }
        
        [Required]
        public List<TaxPeriodDto> TaxPeriods { get; set; } = new List<TaxPeriodDto>();
    }

    public class AppealDto
    {
        public Guid AppealId { get; set; }
        public Guid AssessmentId { get; set; }
        public string TaxPayerId { get; set; } = null!;
        public string TaxPayerName { get; set; } = null!;
        public string Reason { get; set; } = null!;
        public DateTime SubmittedAt { get; set; }
        public string Status { get; set; } = null!;
        public DateTime? ResolvedAt { get; set; }
        
        // Assessment related information
        public decimal AssessmentAmount { get; set; }
        public string PropertyAddress { get; set; } = null!;
        
        // Negotiation information if resolved
        public decimal? NegotiatedAmount { get; set; }
        public DateTime? NegotiationDate { get; set; }
        public string? OfficerName { get; set; }
    }

    public class AppealListItemDto
    {
        public Guid AppealId { get; set; }
        public Guid AssessmentId { get; set; }
        public string PropertyAddress { get; set; } = null!;
        public decimal AssessmentAmount { get; set; }
        public string Status { get; set; } = null!;
        public DateTime SubmittedAt { get; set; }
        public DateTime? ResolvedAt { get; set; }
        public decimal? NegotiatedAmount { get; set; }
    }
}
