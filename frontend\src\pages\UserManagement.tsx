import React, { useState, useEffect, useCallback } from "react";
import { userService, municipalityService } from "../services/api";
import type { User, Municipality } from "../types";
import { 
  AdminLayout,
  AdminTable,
  AdminFilterBar,
  AdminModal,
  AdminForm,
  AdminPagination,
  AdminBreadcrumb,
  AdminTabs
} from "../components/admin";

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [municipalities, setMunicipalities] = useState<Municipality[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [newUser, setNewUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phoneNumber: "",
    role: "Citizen",
    municipalityId: "",
  });
  const [editUser, setEditUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    role: "",
    municipalityId: "",
  });

  const [newPassword, setNewPassword] = useState("");
  const [activeTab, setActiveTab] = useState<"all" | "active" | "inactive" | "pending">("all");

  const roles = ["Citizen", "Officer", "CentralAdmin"];
  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      // Note: Status filtering would need to be implemented in the backend API
      
      const response = await userService.getUsers(
        currentPage,
        10,
        searchTerm,
        selectedRole
      );
      setUsers(response.users || []);
      setTotalPages(response.totalPages || 1);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError("Failed to load users: " + errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, selectedRole]);

  const loadMunicipalities = useCallback(async () => {
    try {
      const response = await municipalityService.getAll();
      setMunicipalities(response);
    } catch (err) {
      console.error("Failed to load municipalities:", err);
    }
  }, []);

  useEffect(() => {
    loadUsers();
    loadMunicipalities();
  }, [loadUsers, loadMunicipalities]);

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await userService.createUser(newUser);
      setSuccess("User created successfully");
      setShowCreateModal(false);
      setNewUser({
        firstName: "",
        lastName: "",
        email: "",
        password: "",
        phoneNumber: "",
        role: "Citizen",
        municipalityId: "",
      });
      loadUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError("Failed to create user: " + errorMessage);
    }
  };

  const handleEditUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    try {
      await userService.updateUser(selectedUser.id, editUser);
      setSuccess("User updated successfully");
      setShowEditModal(false);
      setSelectedUser(null);
      loadUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError("Failed to update user: " + errorMessage);
    }
  };

  const handleDeleteUser = async (user: User) => {
    if (
      !confirm(
        `Are you sure you want to delete user "${
          user.firstName && user.lastName
            ? `${user.firstName} ${user.lastName}`
            : user.email
        }"?`
      )
    )
      return;

    try {
      await userService.deleteUser(user.id);
      setSuccess("User deleted successfully");
      loadUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError("Failed to delete user: " + errorMessage);
    }
  };

  const handleToggleUserStatus = async (user: User) => {
    try {
      await userService.toggleUserStatus(user.id);
      setSuccess(
        `User ${user.isActive ? "deactivated" : "activated"} successfully`
      );
      loadUsers();
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError("Failed to toggle user status: " + errorMessage);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    try {
      await userService.resetPassword(selectedUser.id, newPassword);
      setSuccess("Password reset successfully");
      setShowPasswordModal(false);
      setNewPassword("");
      setSelectedUser(null);
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "Unknown error occurred";
      setError("Failed to reset password: " + errorMessage);
    }
  };

  const openEditModal = (user: User) => {
    setSelectedUser(user);
    setEditUser({
      firstName: user.firstName || "",
      lastName: user.lastName || "",
      email: user.email,
      phoneNumber: user.phoneNumber || "",
      role: user.role,
      municipalityId: user.municipalityId ? String(user.municipalityId) : "",
    });
    setShowEditModal(true);
  };

  const openPasswordModal = (user: User) => {
    setSelectedUser(user);
    setShowPasswordModal(true);
  };

  const clearMessages = () => {
    setError("");
    setSuccess("");
  };

  return (
    <AdminLayout
      title="User Management"
      subtitle="Manage system users and their permissions"
      className="max-w-7xl mx-auto"
    >

        {/* Breadcrumbs */}
        <AdminBreadcrumb 
          items={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "User Management" }
          ]}
          className="mb-4"
        />

        {/* Error/Success Messages */}
        {error && (
          <div className="alert alert-error mb-4">
            <span>{error}</span>
            <button
              onClick={clearMessages}
              className="btn btn-ghost btn-sm btn-circle"
            >
              ×
            </button>
          </div>
        )}

        {success && (
          <div className="alert alert-success mb-4">
            <span>{success}</span>
            <button
              onClick={clearMessages}
              className="btn btn-ghost btn-sm btn-circle"
            >
              ×
            </button>
          </div>
        )}
        
        {/* Tabs */}
        <AdminTabs
          tabs={[
            { key: "all", label: "All Users", count: users.length },
            { key: "active", label: "Active", count: users.filter(u => u.isActive).length },
            { key: "inactive", label: "Inactive", count: users.filter(u => !u.isActive).length },
            { key: "pending", label: "Pending Approval", count: users.filter(u => u.status === "Pending").length }
          ]}
          activeTab={activeTab}
          onTabChange={(tabKey) => {
            setActiveTab(tabKey as "all" | "active" | "inactive" | "pending");
            setCurrentPage(1); // Reset to first page when changing tabs
          }}
          className="mb-6"
        />

        {/* Filters and Actions */}
        <AdminFilterBar
          className="mb-6"
          onSearch={(value) => setSearchTerm(value)}
          searchPlaceholder="Search users..."
          searchValue={searchTerm}
          filters={[
            {
              name: "role",
              value: selectedRole,
              onChange: setSelectedRole,
              options: roles.map(role => ({ label: role, value: role })),
              placeholder: "All Roles"
            }
          ]}
          actions={
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn btn-primary"
            >
              + Add User
            </button>
          }
        />

        {/* Users Table */}
        <div className="card bg-base-100 shadow-xl overflow-hidden">
          <div className="card-body p-0">
            <AdminTable
              data={users}
              keyField="id"
              isLoading={loading}
              emptyMessage="No users found"
              zebra
              hover
              columns={[
                {
                  header: "User",
                  accessor: (user) => (
                    <div>
                      <div className="font-medium text-base-content">
                        {user.firstName && user.lastName
                          ? `${user.firstName} ${user.lastName}`
                          : user.fullName || "N/A"}
                      </div>
                      <div className="text-sm text-base-content/70">
                        {user.email}
                      </div>
                      {user.phoneNumber && (
                        <div className="text-sm text-base-content/70">
                          {user.phoneNumber}
                        </div>
                      )}
                    </div>
                  )
                },
                {
                  header: "Role",
                  accessor: (user) => (
                    <span
                      className={`badge ${
                        user.role === "CentralAdmin"
                          ? "badge-secondary"
                          : user.role === "Officer"
                          ? "badge-info"
                          : "badge-success"
                      }`}
                    >
                      {user.role}
                    </span>
                  )
                },
                {
                  header: "Municipality",
                  accessor: (user) => user.municipalityName || "N/A"
                },
                {
                  header: "Status",
                  accessor: (user) => (
                    <span
                      className={`badge ${
                        user.isActive
                          ? "badge-success"
                          : "badge-error"
                      }`}
                    >
                      {user.isActive ? "Active" : "Inactive"}
                    </span>
                  )
                },
                {
                  header: "Created",
                  accessor: (user) => new Date(user.createdAt).toLocaleDateString()
                }
              ]}
              actions={(user) => (
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => openEditModal(user)}
                    className="btn btn-ghost btn-xs"
                    title="Edit User"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => openPasswordModal(user)}
                    className="btn btn-ghost btn-xs text-warning"
                    title="Reset Password"
                  >
                    Reset
                  </button>
                  <button
                    onClick={() => handleToggleUserStatus(user)}
                    className={`btn btn-ghost btn-xs ${
                      user.isActive
                        ? "text-error"
                        : "text-success"
                    }`}
                    title={
                      user.isActive
                        ? "Deactivate User"
                        : "Activate User"
                    }
                  >
                    {user.isActive ? "Deactivate" : "Activate"}
                  </button>
                  <button
                    onClick={() => handleDeleteUser(user)}
                    className="btn btn-ghost btn-xs text-error"
                    title="Delete User"
                  >
                    Delete
                  </button>
                </div>
              )}
            />
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="card-body border-t border-base-300">
              <div className="flex items-center justify-between">
                <div className="text-sm text-base-content/70">
                  Page <span className="font-medium">{currentPage}</span> of{" "}
                  <span className="font-medium">{totalPages}</span>
                </div>
                <AdminPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  showFirstLast
                />
              </div>
            </div>
          )}
        </div>

        {/* Create User Modal */}
        <AdminModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New User"
          size="md"
        >
              <AdminForm onSubmit={handleCreateUser}>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">First Name *</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={newUser.firstName}
                    onChange={(e) =>
                      setNewUser({ ...newUser, firstName: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Last Name *</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={newUser.lastName}
                    onChange={(e) =>
                      setNewUser({ ...newUser, lastName: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Email *</span>
                  </label>
                  <input
                    type="email"
                    required
                    value={newUser.email}
                    onChange={(e) =>
                      setNewUser({ ...newUser, email: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Password *</span>
                  </label>
                  <input
                    type="password"
                    required
                    value={newUser.password}
                    onChange={(e) =>
                      setNewUser({ ...newUser, password: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Phone Number</span>
                  </label>
                  <input
                    type="tel"
                    value={newUser.phoneNumber}
                    onChange={(e) =>
                      setNewUser({ ...newUser, phoneNumber: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Role *</span>
                  </label>
                  <select
                    required
                    value={newUser.role}
                    onChange={(e) =>
                      setNewUser({ ...newUser, role: e.target.value })
                    }
                    className="select select-bordered"
                  >
                    {roles.map((role) => (
                      <option key={role} value={role}>
                        {role}
                      </option>
                    ))}
                  </select>
                </div>
                {(newUser.role === "Officer" || newUser.role === "Citizen") && (
                  <div className="form-control mb-4">
                    <label className="label">
                      <span className="label-text font-medium">Municipality</span>
                    </label>
                    <select
                      value={newUser.municipalityId}
                      onChange={(e) =>
                        setNewUser({
                          ...newUser,
                          municipalityId: e.target.value,
                        })
                      }
                      className="select select-bordered"
                    >
                      <option value="">Select Municipality</option>
                      {municipalities.map((municipality) => (
                        <option
                          key={municipality.municipalityId}
                          value={municipality.municipalityId}
                        >
                          {municipality.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                <div className="modal-action">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="btn btn-ghost"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    Create User
                  </button>
                </div>
              </AdminForm>
        </AdminModal>

        {/* Edit User Modal */}
        <AdminModal
          isOpen={showEditModal && !!selectedUser}
          onClose={() => setShowEditModal(false)}
          title="Edit User"
          size="md"
        >
              <form onSubmit={handleEditUser}>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">First Name *</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={editUser.firstName}
                    onChange={(e) =>
                      setEditUser({ ...editUser, firstName: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Last Name *</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={editUser.lastName}
                    onChange={(e) =>
                      setEditUser({ ...editUser, lastName: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Email *</span>
                  </label>
                  <input
                    type="email"
                    required
                    value={editUser.email}
                    onChange={(e) =>
                      setEditUser({ ...editUser, email: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Phone Number</span>
                  </label>
                  <input
                    type="tel"
                    value={editUser.phoneNumber}
                    onChange={(e) =>
                      setEditUser({ ...editUser, phoneNumber: e.target.value })
                    }
                    className="input input-bordered"
                  />
                </div>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">Role *</span>
                  </label>
                  <select
                    required
                    value={editUser.role}
                    onChange={(e) =>
                      setEditUser({ ...editUser, role: e.target.value })
                    }
                    className="select select-bordered"
                  >
                    {roles.map((role) => (
                      <option key={role} value={role}>
                        {role}
                      </option>
                    ))}
                  </select>
                </div>
                {(editUser.role === "Officer" ||
                  editUser.role === "Citizen") && (
                  <div className="form-control mb-4">
                    <label className="label">
                      <span className="label-text font-medium">Municipality</span>
                    </label>
                    <select
                      value={editUser.municipalityId}
                      onChange={(e) =>
                        setEditUser({
                          ...editUser,
                          municipalityId: e.target.value,
                        })
                      }
                      className="select select-bordered"
                    >
                      <option value="">Select Municipality</option>
                      {municipalities.map((municipality) => (
                        <option
                          key={municipality.municipalityId}
                          value={municipality.municipalityId}
                        >
                          {municipality.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                <div className="modal-action">
                  <button
                    type="button"
                    onClick={() => setShowEditModal(false)}
                    className="btn btn-ghost"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    Update User
                  </button>
                </div>
              </form>
        </AdminModal>

        {/* Reset Password Modal */}
        <AdminModal
          isOpen={showPasswordModal && !!selectedUser}
          onClose={() => setShowPasswordModal(false)}
          title="Reset Password"
          size="sm"
        >
          {selectedUser && (
            <>
              <p className="text-base-content/70 mb-4">
                Reset password for:{" "}
                {selectedUser.firstName && selectedUser.lastName
                  ? `${selectedUser.firstName} ${selectedUser.lastName}`
                  : selectedUser.email}
              </p>
              <form onSubmit={handleResetPassword}>
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text font-medium">New Password *</span>
                  </label>
                  <input
                    type="password"
                    required
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="input input-bordered"
                    minLength={6}
                  />
                  <label className="label">
                    <span className="label-text-alt">Minimum 6 characters</span>
                  </label>
                </div>
                <div className="modal-action">
                  <button
                    type="button"
                    onClick={() => setShowPasswordModal(false)}
                    className="btn btn-ghost"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-error">
                    Reset Password
                  </button>
                </div>
              </form>
            </>
          )}
        </AdminModal>
    </AdminLayout>
  );
};

export default UserManagement;
