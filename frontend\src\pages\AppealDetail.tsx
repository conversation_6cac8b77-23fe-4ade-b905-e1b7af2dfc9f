import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { appealService, type AppealDto } from '../services/appealService';
import { format } from 'date-fns';
import { AdminLayout } from '../components/admin';

const AppealDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [appeal, setAppeal] = useState<AppealDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isOfficer = user?.role === 'Officer';

  useEffect(() => {
    if (id) {
      fetchAppeal(id);
    }
  }, [id]);

  const fetchAppeal = async (appealId: string) => {
    try {
      setLoading(true);
      const data = await appealService.getAppeal(appealId);
      setAppeal(data);
    } catch (err) {
      setError('Failed to fetch appeal details');
      console.error('Error fetching appeal:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNegotiation = () => {
    if (appeal) {
      navigate(`/negotiations/create/${appeal.appealId}`);
    }
  };

  const handleBack = () => {
    navigate('/appeals');
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'badge badge-warning';
      case 'approved':
        return 'badge badge-success';
      case 'rejected':
        return 'badge badge-error';
      case 'resolved':
        return 'badge badge-info';
      default:
        return 'badge badge-ghost';
    }
  };

  if (loading) {
    return (
      <AdminLayout title="Appeal Details">
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </AdminLayout>
    );
  }

  if (error || !appeal) {
    return (
      <AdminLayout title="Appeal Details">
        <div className="alert alert-error mb-4">
          {error || 'Appeal not found'}
        </div>
        <button onClick={handleBack} className="btn btn-ghost">
          Back to Appeals
        </button>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Appeal Details">
      <div className="flex justify-end mb-6">
        <div className="space-x-2">
          <button onClick={handleBack} className="btn btn-ghost btn-sm">
            Back to Appeals
          </button>
          {isOfficer && appeal.status.toLowerCase() === 'pending' && (
            <button onClick={handleCreateNegotiation} className="btn btn-primary btn-sm">
              Create Negotiation
            </button>
          )}
        </div>
      </div>

      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="flex justify-between items-start mb-6">
            <div className="text-xl font-semibold">
              Appeal #{appeal.appealId.slice(0, 8)}
            </div>
            <div className={getStatusBadgeClass(appeal.status)}>
              {appeal.status}
            </div>
          </div>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="card bg-base-200">
              <div className="card-body">
                <h3 className="card-title text-lg">Appeal Information</h3>
                <div className="space-y-3">
                  <div>
                    <div className="label-text text-base-content/70">Appeal ID</div>
                    <div className="font-medium">{appeal.appealId}</div>
                  </div>
                  <div>
                    <div className="label-text text-base-content/70">Status</div>
                    <div className="font-medium">{appeal.status}</div>
                  </div>
                  <div>
                    <div className="label-text text-base-content/70">Submitted Date</div>
                    <div className="font-medium">
                      {appeal.submittedAt ? format(new Date(appeal.submittedAt), 'MMMM dd, yyyy HH:mm') : 'N/A'}
                    </div>
                  </div>
                  {appeal.resolvedAt && (
                    <div>
                      <div className="label-text text-base-content/70">Resolved Date</div>
                      <div className="font-medium">
                        {appeal.resolvedAt ? format(new Date(appeal.resolvedAt), 'MMMM dd, yyyy HH:mm') : 'N/A'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="card bg-base-200">
              <div className="card-body">
                <h3 className="card-title text-lg">Property & Assessment</h3>
                <div className="space-y-3">
                  <div>
                    <div className="label-text text-base-content/70">Property Address</div>
                    <div className="font-medium">{appeal.propertyAddress}</div>
                  </div>
                  <div>
                    <div className="label-text text-base-content/70">Assessment ID</div>
                    <div className="font-medium">{appeal.assessmentId}</div>
                  </div>
                  <div>
                    <div className="label-text text-base-content/70">Assessment Amount</div>
                    <div className="font-medium text-lg text-success">
                      NPR {appeal.assessmentAmount ? appeal.assessmentAmount.toLocaleString() : '0'}
                    </div>
                  </div>
                  {isOfficer && (
                    <div>
                      <div className="label-text text-base-content/70">Taxpayer</div>
                      <div className="font-medium">{appeal.taxPayerName}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="card bg-base-200">
            <div className="card-body">
              <h3 className="card-title text-lg">Appeal Reason</h3>
              <div className="bg-base-300 p-4 rounded-lg">
                <p className="text-base-content">{appeal.reason}</p>
              </div>
            </div>
          </div>

          {appeal.responseMessage && (
            <div className="card bg-info/10 border border-info/20">
              <div className="card-body">
                <h3 className="card-title text-lg text-info">Official Response</h3>
                <div className="bg-info/5 p-4 rounded-lg border border-info/10">
                  <p className="text-info">{appeal.responseMessage}</p>
                </div>
              </div>
            </div>
          )}

          {isOfficer && appeal.status.toLowerCase() === 'pending' && (
            <div className="card bg-base-200">
              <div className="card-body">
                <h3 className="card-title text-lg">Actions</h3>
                <div className="card-actions justify-start">
                  <button onClick={handleCreateNegotiation} className="btn btn-primary">
                    Create Negotiation
                  </button>
                  <button className="btn btn-secondary">
                    Respond to Appeal
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AppealDetail;
