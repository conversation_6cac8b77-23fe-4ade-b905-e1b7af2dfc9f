using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Assessment;
using LandTaxSystem.Core.DTOs.Payment;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Enums;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AssessmentsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly GisService _gisService;
        private readonly IPaymentService _paymentService;
        private readonly TaxConfigResolver _taxConfigResolver;
        private readonly IEmailService _emailService;
        private readonly ILogger<AssessmentsController> _logger;

        public AssessmentsController(ApplicationDbContext context, GisService gisService, IPaymentService paymentService, TaxConfigResolver taxConfigResolver, IEmailService emailService, ILogger<AssessmentsController> logger)
        {
            _context = context;
            _gisService = gisService;
            _paymentService = paymentService;
            _taxConfigResolver = taxConfigResolver;
            _emailService = emailService;
            _logger = logger;
        }

        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<IEnumerable<AssessmentResponseDto>>> GetAssessmentsByProperty(Guid propertyId)
        {
            // Check if property exists
            var property = await _context.Properties.FindAsync(propertyId);
            if (property == null)
            {
                return NotFound("Property not found");
            }

            // Check if user is authorized to view this property's assessments
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);

            if (userRole == "Citizen" && property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to view assessments for this property");
            }

            if (userRole == "Officer")
            {
                var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
                if (officerMunicipalityId != property.MunicipalityId.ToString())
                {
                    return Forbid("You are not authorized to view assessments from this municipality");
                }
            }

            var assessments = await _context.Assessments
                .Include(a => a.Property)
                    .ThenInclude(p => p.Municipality)
                        .ThenInclude(m => m.District)
                            .ThenInclude(d => d.Province)
                .Include(a => a.AssessedByOfficer)
                .Where(a => a.PropertyId == propertyId)
                .OrderByDescending(a => a.AssessmentYear)
                .ToListAsync();

            return Ok(assessments.Select(a => MapToResponseDto(a)).ToList());
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AssessmentResponseDto>> GetAssessment(Guid id)
        {
            var assessment = await _context.Assessments
                .Include(a => a.Property)
                    .ThenInclude(p => p.Municipality)
                        .ThenInclude(m => m.District)
                            .ThenInclude(d => d.Province)
                .Include(a => a.AssessedByOfficer)
                .FirstOrDefaultAsync(a => a.AssessmentId == id);

            if (assessment == null)
            {
                return NotFound();
            }

            // Check authorization - only owner or officers from the property's municipality can view
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (assessment.Property.OwnerUserId != userId &&
                !(userRole == "Officer" && officerMunicipalityId == assessment.Property.MunicipalityId.ToString()) &&
                userRole != "CentralAdmin")
            {
                return Forbid();
            }

            return MapToResponseDto(assessment);
        }

        [HttpGet("eligible")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<EligiblePropertyDto>>> GetEligibleProperties()
        {
            // Get officer's municipality
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
            if (string.IsNullOrEmpty(officerMunicipalityId))
            {
                return BadRequest("Officer municipality not found");
            }

            var municipalityGuid = Guid.Parse(officerMunicipalityId);

            try
            {
                // Get eligible properties for the officer's municipality
                var eligibleProperties = await _paymentService.GetEligibleProperties(municipalityGuid);

                if (eligibleProperties.Count == 0)
                {
                    // Return sample data for testing
                    return Ok(new List<EligiblePropertyDto>
                    {
                        new EligiblePropertyDto
                        {
                            PropertyId = Guid.NewGuid(),
                            FiscalYearId = Guid.NewGuid(),
                            PropertyAddress = "123 Test Street, Sample City",
                            FiscalYearName = "2024-2025",
                            CalculatedTaxAmount = 5000,
                            AmountPaid = 3000,
                            UnpaidBalance = 2000,
                            PaymentIds = new List<Guid> { Guid.NewGuid() }
                        }
                    });
                }

                return Ok(eligibleProperties);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting eligible properties: {ex.Message}");
                return StatusCode(500, "An error occurred while retrieving eligible properties");
            }
        }

        [HttpPost("from-underpaid")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<AssessmentResponseDto>> CreateAssessmentFromUnderpaid(AssessmentCreateDto createDto)
        {
            // Find the property
            var property = await _context.Properties
                .Include(p => p.Municipality)
                    .ThenInclude(m => m.District)
                        .ThenInclude(d => d.Province)
                .FirstOrDefaultAsync(p => p.PropertyId == createDto.PropertyId);

            if (property == null)
            {
                return NotFound("Property not found");
            }

            // Check if officer belongs to the property's municipality
            var officerId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (officerMunicipalityId != property.MunicipalityId.ToString())
            {
                return Forbid("You are not authorized to assess properties from this municipality");
            }

            // Check if fiscal year exists
            var fiscalYear = await _context.FiscalYears.FindAsync(createDto.FiscalYearId);
            if (fiscalYear == null)
            {
                return BadRequest("Invalid fiscal year specified");
            }

            // Verify that the payment IDs exist and are underpaid
            var payments = await _context.Payments
                .Where(p => createDto.PaymentIds.Contains(p.PaymentId))
                .ToListAsync();

            if (payments.Count != createDto.PaymentIds.Count)
            {
                return BadRequest("One or more payment IDs are invalid");
            }

            // Verify that payments are for the specified property and fiscal year
            if (payments.Any(p => p.PropertyId != createDto.PropertyId || p.FiscalYearId != createDto.FiscalYearId))
            {
                return BadRequest("Payments must be for the specified property and fiscal year");
            }

            // Verify that payments are not already reconciled
            if (payments.Any(p => p.IsReconciled))
            {
                return BadRequest("One or more payments are already reconciled with an assessment");
            }

            // Calculate property value and tax amount using officer-configured settings
            decimal calculatedValue;
            decimal taxAmount;

            // Get the active tax configuration for the municipality
            var taxConfig = await _taxConfigResolver.GetActiveTaxConfigAsync(property.MunicipalityId);
            if (taxConfig == null)
            {
                // Fallback to legacy municipality config if no active tax config exists
                calculatedValue = _gisService.CalculatePropertyValue(property, property.Municipality?.ValuationRulesConfigJson ?? "{}");
                taxAmount = _gisService.CalculateTaxAmount(
                    calculatedValue,
                    property.Municipality?.TaxSlabsConfigJson ?? "[]",
                    property.Municipality?.ExemptionRulesConfigJson ?? "{}");
            }
            else
            {
                // Use the officer-configured tax settings
                var valuationRules = System.Text.Json.JsonSerializer.Deserialize<Core.Models.ValuationRulesConfig>(taxConfig.ValuationRulesConfigJson ?? property.Municipality?.ValuationRulesConfigJson ?? "{}") ?? new Core.Models.ValuationRulesConfig();
                var taxSlabs = System.Text.Json.JsonSerializer.Deserialize<List<Core.Models.LegacyTaxSlab>>(taxConfig.TaxSlabsConfigJson ?? property.Municipality?.TaxSlabsConfigJson ?? "[]") ?? new List<Core.Models.LegacyTaxSlab>();
                var exemptionRules = System.Text.Json.JsonSerializer.Deserialize<Core.Models.ExemptionRulesConfig>(taxConfig.ExemptionRulesConfigJson ?? property.Municipality?.ExemptionRulesConfigJson ?? "{}") ?? new Core.Models.ExemptionRulesConfig { Rules = new List<Core.Models.ExemptionRule>() };

                calculatedValue = _gisService.CalculatePropertyValue(property, valuationRules);
                taxAmount = _gisService.CalculateTaxAmount(calculatedValue, taxSlabs, exemptionRules);
            }

            // Calculate total paid amount
            var totalPaid = payments.Sum(p => p.AmountPaid);

            // Verify that the total paid amount is less than the calculated tax amount
            if (totalPaid >= taxAmount)
            {
                return BadRequest("Total paid amount is not less than the calculated tax amount");
            }

            var lineItems = new List<AssessmentLineItem>();
            if (createDto.LineItems != null)
            {
                lineItems = createDto.LineItems.Select(li => new AssessmentLineItem
                {
                    Id = Guid.NewGuid(),
                    SerialNumber = li.SerialNumber,
                    TaxDescription = li.TaxDescription ?? string.Empty,
                    FiscalYear = li.FiscalYear,
                    AssessedAmount = li.AssessedAmount,
                    PenaltyAmount = li.PenaltyAmount,
                    InterestAmount = li.InterestAmount,
                    TotalAmount = li.AssessedAmount + li.PenaltyAmount + li.InterestAmount,
                    CreatedAt = DateTime.UtcNow
                }).ToList();
            }

            // Create assessment
            var assessment = new Assessment
            {
                AssessmentId = Guid.NewGuid(),
                PropertyId = property.PropertyId,
                AssessmentYear = createDto.AssessmentYear,
                FiscalYearId = createDto.FiscalYearId,
                OriginalAmount = totalPaid,
                CalculatedValue = calculatedValue,
                FinalAssessedValue = calculatedValue,
                TaxAmount = taxAmount,
                AssessedByOfficerId = officerId,
                AssessmentDate = DateTime.UtcNow,
                OverrideReason = createDto.OverrideReason,
                PaymentStatus = "Underpaid",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                LineItems = lineItems
            };

            _context.Assessments.Add(assessment);

            // Update property status if needed
            if (property.Status == "Approved")
            {
                property.Status = "Assessed";
                property.UpdatedAt = DateTime.UtcNow;
            }

            // Reconcile payments with the assessment
            await _paymentService.ReconcilePayments(createDto.PaymentIds, assessment.AssessmentId);

            await _context.SaveChangesAsync();

            // Reload assessment with related entities
            var createdAssessment = await _context.Assessments
                .Include(a => a.Property)
                .Include(a => a.AssessedByOfficer)
                .FirstOrDefaultAsync(a => a.AssessmentId == assessment.AssessmentId);

            if (createdAssessment == null)
            {
                return NotFound("Assessment could not be found after creation.");
            }

            return CreatedAtAction(
                nameof(GetAssessment),
                new { id = createdAssessment.AssessmentId },
                MapToResponseDto(createdAssessment));
        }

        [HttpPost]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<AssessmentResponseDto>> CreateOrUpdateAssessment(AssessmentCreateDto createDto)
        {
            // Find the property
            var property = await _context.Properties
                .Include(p => p.Municipality)
                    .ThenInclude(m => m.District)
                        .ThenInclude(d => d.Province)
                .FirstOrDefaultAsync(p => p.PropertyId == createDto.PropertyId);

            if (property == null)
            {
                return NotFound("Property not found");
            }

            // Check if officer belongs to the property's municipality
            var officerId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (officerMunicipalityId != property.MunicipalityId.ToString())
            {
                return Forbid("You are not authorized to assess properties from this municipality");
            }

            // Check if fiscal year exists
            var fiscalYear = await _context.FiscalYears.FindAsync(createDto.FiscalYearId);
            if (fiscalYear == null)
            {
                return BadRequest("Invalid fiscal year specified");
            }

            // Check if an assessment already exists for this property and fiscal year
            var existingAssessment = await _context.Assessments
                .FirstOrDefaultAsync(a => a.PropertyId == createDto.PropertyId && a.FiscalYearId == createDto.FiscalYearId);

            if (existingAssessment != null)
            {
                // Only update if this is an override
                if (createDto.OverriddenValue.HasValue)
                {
                    existingAssessment.OverriddenValue = createDto.OverriddenValue;
                    existingAssessment.FinalAssessedValue = createDto.OverriddenValue.Value;
                    existingAssessment.OriginalAmount = createDto.OriginalAmount;
                    existingAssessment.OverrideReason = createDto.OverrideReason;
                    existingAssessment.AssessedByOfficerId = officerId;
                    existingAssessment.UpdatedAt = DateTime.UtcNow;
                    existingAssessment.UpperAssessment = createDto.UpperAssessment;
                    existingAssessment.LowerAssessment = createDto.LowerAssessment;
                    existingAssessment.ActualAssessment = createDto.ActualAssessment;
                    // Use municipal defaults if DTO values are null
                    existingAssessment.PenaltyPercent = createDto.PenaltyPercent ?? property.Municipality?.DefaultPenaltyPercent ?? 0;
                    existingAssessment.DiscountPercent = createDto.DiscountPercent ?? property.Municipality?.DefaultDiscountPercent ?? 0;

                    // Recalculate tax based on the new final assessed value using officer-configured settings
                    // Get the active tax configuration for the municipality
                    var taxConfig = await _taxConfigResolver.GetActiveTaxConfigAsync(property.MunicipalityId);
                    if (taxConfig == null)
                    {
                        // Fallback to legacy municipality config if no active tax config exists
                        existingAssessment.TaxAmount = _gisService.CalculateTaxAmount(
                            existingAssessment.FinalAssessedValue,
                            property.Municipality?.TaxSlabsConfigJson ?? "[]",
                            property.Municipality?.ExemptionRulesConfigJson ?? "{}");
                    }
                    else
                    {
                        // Use the officer-configured tax settings
                        var taxSlabs = System.Text.Json.JsonSerializer.Deserialize<List<Core.Models.LegacyTaxSlab>>(taxConfig.TaxSlabsConfigJson ?? property.Municipality?.TaxSlabsConfigJson ?? "[]") ?? new List<Core.Models.LegacyTaxSlab>();
                        var exemptionRules = System.Text.Json.JsonSerializer.Deserialize<Core.Models.ExemptionRulesConfig>(taxConfig.ExemptionRulesConfigJson ?? property.Municipality?.ExemptionRulesConfigJson ?? "{}") ?? new Core.Models.ExemptionRulesConfig { Rules = new List<Core.Models.ExemptionRule>() };

                        existingAssessment.TaxAmount = _gisService.CalculateTaxAmount(existingAssessment.FinalAssessedValue, taxSlabs, exemptionRules);
                    }

                    existingAssessment.TaxAmount = existingAssessment.TotalPayable; // Ensure TaxAmount reflects final payable

                    await _context.SaveChangesAsync();

                    // Create in-app notification for assessment update
                    var notification = new Notification
                    {
                        UserId = existingAssessment.Property.OwnerUserId,
                        Message = $"Your property assessment for {existingAssessment.Property.PropertyId} has been updated. New tax amount: NPR {existingAssessment.TaxAmount:N2}",
                        Type = "assessment_update",
                        IsRead = false,
                        RelatedEntityId = existingAssessment.AssessmentId,
                        RelatedEntityType = "Assessment"
                    };
                    _context.Notifications.Add(notification);
                    await _context.SaveChangesAsync();

                    // Reload to get related entities
                    var updatedAssessment = await _context.Assessments
                        .Include(a => a.Property)
                            .ThenInclude(p => p.Owner)
                        .Include(a => a.AssessedByOfficer)
                        .FirstOrDefaultAsync(a => a.AssessmentId == existingAssessment.AssessmentId);

                    if (updatedAssessment == null)
                    {
                        return NotFound("Assessment could not be found after update.");
                    }

                    // Send assessment notification email for updated assessment
                    try
                    {
                        await _emailService.SendAssessmentNotificationAsync(updatedAssessment.Property.Owner.Email, updatedAssessment.Property.Owner.FullName, updatedAssessment.AssessmentId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send assessment notification email to {Email}", updatedAssessment.Property.Owner.Email);
                        // Don't fail the assessment update if email fails
                    }

                    return Ok(MapToResponseDto(updatedAssessment));
                }
                else
                {
                    return BadRequest("Assessment already exists for this property and year");
                }
            }
            else
            {
                // Validate line items if present
                if (createDto.LineItems != null && createDto.LineItems.Any())
                {
                    // Check for required fields
                    foreach (var lineItem in createDto.LineItems)
                    {
                        if (lineItem.SerialNumber <= 0)
                            return BadRequest("Serial number must be positive");

                        if (string.IsNullOrWhiteSpace(lineItem.TaxDescription))
                            return BadRequest("Tax description is required");

                        if (lineItem.FiscalYear < 2000 || lineItem.FiscalYear > DateTime.Now.Year + 5)
                            return BadRequest("Fiscal year must be between 2000 and " + (DateTime.Now.Year + 5));

                        if (lineItem.AssessedAmount < 0)
                            return BadRequest("Assessed amount cannot be negative");

                        if (lineItem.PenaltyAmount < 0)
                            return BadRequest("Penalty amount cannot be negative");

                        if (lineItem.InterestAmount < 0)
                            return BadRequest("Interest amount cannot be negative");
                    }

                    // Check for duplicate serial numbers
                    var duplicateSerials = createDto.LineItems
                        .GroupBy(li => li.SerialNumber)
                        .Where(g => g.Count() > 1)
                        .Select(g => g.Key)
                        .ToList();

                    if (duplicateSerials.Any())
                        return BadRequest($"Duplicate serial numbers found: {string.Join(", ", duplicateSerials)}");
                }

                // Calculate property value using officer-configured settings
                decimal calculatedValue;
                var taxConfig = await _taxConfigResolver.GetActiveTaxConfigAsync(property.MunicipalityId);
                if (taxConfig == null)
                {
                    // Fallback to legacy municipality config if no active tax config exists
                    calculatedValue = _gisService.CalculatePropertyValue(property, property.Municipality?.ValuationRulesConfigJson ?? "{}");
                }
                else
                {
                    // Use the officer-configured valuation rules
                    var valuationRules = System.Text.Json.JsonSerializer.Deserialize<Core.Models.ValuationRulesConfig>(taxConfig.ValuationRulesConfigJson ?? property.Municipality?.ValuationRulesConfigJson ?? "{}") ?? new Core.Models.ValuationRulesConfig();
                    calculatedValue = _gisService.CalculatePropertyValue(property, valuationRules);
                }

                // Use overridden value if provided
                var finalAssessedValue = createDto.OverriddenValue ?? createDto.ActualAssessment;

                // Calculate tax amount using officer-configured settings
                decimal taxAmount;

                // The taxConfig is already fetched, so we can reuse it.
                if (taxConfig == null)
                {
                    // Fallback to legacy municipality config if no active tax config exists
                    taxAmount = _gisService.CalculateTaxAmount(
                        finalAssessedValue,
                        property.Municipality?.TaxSlabsConfigJson ?? "[]",
                        property.Municipality?.ExemptionRulesConfigJson ?? "{}");
                }
                else
                {
                    // Use the officer-configured tax settings
                    var taxSlabs = System.Text.Json.JsonSerializer.Deserialize<List<Core.Models.LegacyTaxSlab>>(taxConfig.TaxSlabsConfigJson ?? property.Municipality?.TaxSlabsConfigJson ?? "[]") ?? new List<Core.Models.LegacyTaxSlab>();
                    var exemptionRules = System.Text.Json.JsonSerializer.Deserialize<Core.Models.ExemptionRulesConfig>(taxConfig.ExemptionRulesConfigJson ?? property.Municipality?.ExemptionRulesConfigJson ?? "{}") ?? new Core.Models.ExemptionRulesConfig { Rules = new List<Core.Models.ExemptionRule>() };

                    taxAmount = _gisService.CalculateTaxAmount(finalAssessedValue, taxSlabs, exemptionRules);
                }

                // Find any provisional payments for this property and fiscal year
                var existingProvisionalPayments = await _context.Payments
                    .Where(p => p.PropertyId == property.PropertyId &&
                           p.FiscalYearId == createDto.FiscalYearId &&
                           p.Provisional == true)
                    .ToListAsync();

                var totalProvisionalPayments = existingProvisionalPayments.Sum(p => p.AmountPaid);

                // Determine payment status based on provisional payments
                string paymentStatus = "Pending";
                if (totalProvisionalPayments > 0)
                {
                    paymentStatus = totalProvisionalPayments >= taxAmount ? "Paid" : "Underpaid";
                }

                var lineItems = new List<AssessmentLineItem>();
                if (createDto.LineItems != null)
                {
                    lineItems = createDto.LineItems.Select(li => new AssessmentLineItem
                    {
                        Id = Guid.NewGuid(),
                        SerialNumber = li.SerialNumber,
                        TaxDescription = li.TaxDescription,
                        FiscalYear = li.FiscalYear,
                        AssessedAmount = li.AssessedAmount,
                        PenaltyAmount = li.PenaltyAmount,
                        InterestAmount = li.InterestAmount,
                        TotalAmount = li.AssessedAmount + li.PenaltyAmount + li.InterestAmount,
                        CreatedAt = DateTime.UtcNow
                    }).ToList();
                }

                var assessment = new Assessment
                {
                    AssessmentId = Guid.NewGuid(),
                    PropertyId = property.PropertyId,
                    AssessmentYear = createDto.AssessmentYear,
                    FiscalYearId = createDto.FiscalYearId,
                    OriginalAmount = createDto.OriginalAmount,
                    CalculatedValue = calculatedValue,
                    OverriddenValue = createDto.OverriddenValue,
                    FinalAssessedValue = finalAssessedValue,
                    UpperAssessment = createDto.UpperAssessment,
                    LowerAssessment = createDto.LowerAssessment,
                    ActualAssessment = createDto.ActualAssessment,
                    // Use municipal defaults if DTO values are null
                    PenaltyPercent = createDto.PenaltyPercent ?? property.Municipality?.DefaultPenaltyPercent ?? 0,
                    DiscountPercent = createDto.DiscountPercent ?? property.Municipality?.DefaultDiscountPercent ?? 0,
                    TaxAmount = taxAmount,
                    AssessedByOfficerId = officerId, // Always set the officer ID for manual assessments
                    AssessmentDate = DateTime.UtcNow,
                    OverrideReason = createDto.OverrideReason,
                    PaymentStatus = paymentStatus,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    LineItems = lineItems
                };

                _context.Assessments.Add(assessment);

                // Update property status if needed
                if (property.Status == "Approved")
                {
                    property.Status = "Assessed";
                    property.UpdatedAt = DateTime.UtcNow;
                }

                // Find any provisional payments for this property and fiscal year and link them to this assessment
                var provisionalPayments = await _context.Payments
                    .Where(p => p.PropertyId == property.PropertyId &&
                           p.FiscalYearId == createDto.FiscalYearId &&
                           p.Provisional &&
                           p.AssessmentId == null)
                    .ToListAsync();

                if (provisionalPayments.Any())
                {
                    foreach (var payment in provisionalPayments)
                    {
                        payment.AssessmentId = assessment.AssessmentId;
                        payment.UpdatedAt = DateTime.UtcNow;
                    }
                }

                assessment.TaxAmount = assessment.TotalPayable; // Ensure TaxAmount reflects final payable
                await _context.SaveChangesAsync();

                // Reload assessment with related entities
                var createdAssessment = await _context.Assessments
                    .Include(a => a.Property)
                        .ThenInclude(p => p.Owner)
                    .Include(a => a.AssessedByOfficer)
                    .FirstOrDefaultAsync(a => a.AssessmentId == assessment.AssessmentId);

                if (createdAssessment == null)
                {
                    return NotFound("Assessment could not be found after creation.");
                }

                // Send assessment notification email for new assessment
                try
                {
                    await _emailService.SendAssessmentNotificationAsync(createdAssessment.Property.Owner.Email, createdAssessment.Property.Owner.FullName, createdAssessment.AssessmentId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send assessment notification email to {Email}", createdAssessment.Property.Owner.Email);
                    // Don't fail the assessment creation if email fails
                }

                return CreatedAtAction(
                    nameof(GetAssessment),
                    new { id = createdAssessment.AssessmentId },
                    MapToResponseDto(createdAssessment));
            }
        }

        [HttpPut("{id}/payment-status")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<AssessmentResponseDto>> UpdatePaymentStatus(Guid id, AssessmentPaymentUpdateDto updateDto)
        {
            var assessment = await _context.Assessments
                .Include(a => a.Property)
                .Include(a => a.AssessedByOfficer)
                .FirstOrDefaultAsync(a => a.AssessmentId == id);

            if (assessment == null)
            {
                return NotFound();
            }

            // Check if officer belongs to the property's municipality
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (officerMunicipalityId != assessment.Property.MunicipalityId.ToString())
            {
                return Forbid("You are not authorized to update payment status for assessments from this municipality");
            }

            // Update payment status
            assessment.PaymentStatus = updateDto.PaymentStatus;
            assessment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return Ok(MapToResponseDto(assessment));
        }

        [HttpGet("filter")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<AssessmentResponseDto>>> FilterAssessments([FromQuery] AssessmentFilterDto filterDto)
        {
            // Get officer's municipality
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
            if (string.IsNullOrEmpty(officerMunicipalityId))
            {
                return BadRequest("Officer municipality not found");
            }

            var municipalityGuid = Guid.Parse(officerMunicipalityId);

            try
            {
                // Start with base query
                var query = _context.Assessments
                    .Include(a => a.Property)
                        .ThenInclude(p => p.Municipality)
                            .ThenInclude(m => m.District)
                                .ThenInclude(d => d.Province)
                    .Include(a => a.AssessedByOfficer)
                    .Include(a => a.FiscalYear) // Include FiscalYear navigation property
                                                // Only show assessments from the officer's municipality
                    .Where(a => a.Property.MunicipalityId == municipalityGuid);

                // Apply filters
                if (filterDto.Year.HasValue)
                {
                    query = query.Where(a => a.AssessmentYear == filterDto.Year.Value);
                }

                if (!string.IsNullOrEmpty(filterDto.PaymentStatus))
                {
                    query = query.Where(a => a.PaymentStatus == filterDto.PaymentStatus);
                }

                if (filterDto.CreatedAfter.HasValue)
                {
                    query = query.Where(a => a.CreatedAt >= filterDto.CreatedAfter.Value);
                }

                if (filterDto.CreatedBefore.HasValue)
                {
                    query = query.Where(a => a.CreatedAt <= filterDto.CreatedBefore.Value);
                }

                if (filterDto.MinTaxAmount.HasValue)
                {
                    query = query.Where(a => a.TaxAmount >= filterDto.MinTaxAmount.Value);
                }

                if (filterDto.MaxTaxAmount.HasValue)
                {
                    query = query.Where(a => a.TaxAmount <= filterDto.MaxTaxAmount.Value);
                }

                // Apply pagination
                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(totalCount / (double)filterDto.PageSize);

                var assessments = await query
                    .OrderByDescending(a => a.CreatedAt)
                    .Skip((filterDto.PageNumber - 1) * filterDto.PageSize)
                    .Take(filterDto.PageSize)
                    .ToListAsync();

                // Add pagination info to response headers
                Response.Headers["X-Total-Count"] = totalCount.ToString();
                Response.Headers["X-Total-Pages"] = totalPages.ToString();
                Response.Headers["X-Current-Page"] = filterDto.PageNumber.ToString();

                return Ok(assessments.Select(a => MapToResponseDto(a)).ToList());
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Error filtering assessments: {ex.Message}");

                // Return a sample list for testing purposes
                return Ok(new List<AssessmentResponseDto>
                {
                    new AssessmentResponseDto
                    {
                        AssessmentId = Guid.NewGuid(),
                        PropertyId = Guid.NewGuid(),
                        PropertyAddress = "123 Test Street, Sample City",
                        AssessmentYear = DateTime.Now.Year,
                        FiscalYearId = Guid.NewGuid(),
                        FiscalYearName = "2024-2025",
                        OriginalAmount = 5000,
                        CalculatedValue = 100000,
                        FinalAssessedValue = 100000,
                        TaxAmount = 5000,
                        AssessedByOfficerId = Guid.NewGuid().ToString(),
                        AssessedByOfficerName = "Test Officer",
                        AssessmentDate = DateTime.Now.AddDays(-30),
                        PaymentStatus = "Underpaid",
                        CreatedAt = DateTime.Now.AddDays(-30),
                        UpdatedAt = DateTime.Now.AddDays(-30)
                    }
                });
            }
        }

        [HttpGet("overdue")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<AssessmentResponseDto>>> GetOverdueAssessments([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            // Get officer's municipality
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
            if (string.IsNullOrEmpty(officerMunicipalityId))
            {
                return BadRequest("Officer municipality not found");
            }

            var municipalityGuid = Guid.Parse(officerMunicipalityId);

            // Find assessments that are overdue (payment status is "Overdue")
            var query = _context.Assessments
                .Include(a => a.Property)
                    .ThenInclude(p => p.Municipality)
                .Where(a => a.Property.MunicipalityId == municipalityGuid
                    && a.PaymentStatus == "Overdue");

            var totalCount = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

            var overdueAssessments = await query
                .OrderByDescending(a => a.AssessmentYear)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Add pagination info to response headers
            Response.Headers["X-Total-Count"] = totalCount.ToString();
            Response.Headers["X-Total-Pages"] = totalPages.ToString();
            Response.Headers["X-Current-Page"] = pageNumber.ToString();

            return Ok(overdueAssessments.Select(a => MapToResponseDto(a)).ToList());
        }

        private AssessmentResponseDto MapToResponseDto(Assessment assessment)
        {
            object? exemptionDetails = null;
            if (!string.IsNullOrEmpty(assessment.ExemptionAppliedDetailsJson))
            {
                try
                {
                    exemptionDetails = JsonSerializer.Deserialize<object>(assessment.ExemptionAppliedDetailsJson);
                }
                catch
                {
                    // Ignore deserialization errors
                }
            }

            // Get the district and province information through the municipality navigation property
            var municipality = assessment.Property?.Municipality;
            var district = municipality?.District;
            var province = district?.Province;

            return new AssessmentResponseDto
            {
                AssessmentId = assessment.AssessmentId,
                PropertyId = assessment.PropertyId,
                PropertyAddress = $"{assessment.Property?.Street ?? string.Empty}, {assessment.Property?.Municipality?.Name ?? string.Empty}",
                AssessmentYear = assessment.AssessmentYear,
                FiscalYearId = assessment.FiscalYearId,
                FiscalYearName = assessment.FiscalYear?.Name ?? string.Empty,
                OriginalAmount = assessment.OriginalAmount,

                // Location hierarchy fields
                Province = province?.Name ?? string.Empty,
                District = district?.Name ?? string.Empty,
                Municipality = municipality?.Name ?? string.Empty,
                WardNumber = assessment.Property?.WardNumber ?? 0,
                Street = assessment.Property?.Street ?? string.Empty,
                ParcelNumber = assessment.Property?.ParcelNumber ?? string.Empty,

                CalculatedValue = assessment.CalculatedValue,
                OverriddenValue = assessment.OverriddenValue,
                FinalAssessedValue = assessment.FinalAssessedValue,
                TaxAmount = assessment.TaxAmount,
                UpperAssessment = assessment.UpperAssessment,
                LowerAssessment = assessment.LowerAssessment,
                ActualAssessment = assessment.ActualAssessment,
                PenaltyPercent = assessment.PenaltyPercent,
                DiscountPercent = assessment.DiscountPercent,
                TotalPayable = assessment.TotalPayable,
                AssessedByOfficerId = assessment.AssessedByOfficerId,
                AssessedByOfficerName = assessment.AssessedByOfficer?.FullName,
                AssessmentDate = assessment.AssessmentDate,
                OverrideReason = assessment.OverrideReason,
                ExemptionAppliedDetails = exemptionDetails,
                PaymentStatus = assessment.PaymentStatus,
                CreatedAt = assessment.CreatedAt,
                UpdatedAt = assessment.UpdatedAt
            };
        }

        [HttpPut("{id}/bounds")]
        // [Authorize(Policy = "RequireExternalSystemRole")] // Placeholder for future system-to-system auth
        public async Task<ActionResult<AssessmentResponseDto>> UpdateAssessmentBounds(Guid id, AssessmentBoundsUpdateDto boundsDto)
        {
            var assessment = await _context.Assessments
                .Include(a => a.Property) // Include related entities if needed for MapToResponseDto
                    .ThenInclude(p => p.Municipality)
                        .ThenInclude(m => m.District)
                            .ThenInclude(d => d.Province)
                .Include(a => a.AssessedByOfficer)
                .Include(a => a.FiscalYear)
                .FirstOrDefaultAsync(a => a.AssessmentId == id);

            if (assessment == null)
            {
                return NotFound("Assessment not found");
            }

            // Validate that lower is not greater than upper
            if (boundsDto.LowerAssessment > boundsDto.UpperAssessment)
            {
                return BadRequest("LowerAssessment cannot be greater than UpperAssessment.");
            }

            assessment.UpperAssessment = boundsDto.UpperAssessment;
            assessment.LowerAssessment = boundsDto.LowerAssessment;
            assessment.UpdatedAt = DateTime.UtcNow;

            // Recalculate TaxAmount based on new bounds and existing ActualAssessment, Penalty, Discount
            assessment.TaxAmount = assessment.TotalPayable;

            await _context.SaveChangesAsync();

            return Ok(MapToResponseDto(assessment));
        }
    }
}
