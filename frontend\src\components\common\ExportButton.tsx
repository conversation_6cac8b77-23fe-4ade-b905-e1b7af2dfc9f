import React from 'react';

interface ExportButtonProps {
  onExport: () => void;
  loading?: boolean;
}

export const ExportButton: React.FC<ExportButtonProps> = ({
  onExport,
  loading = false,
}) => {
  return (
    <button
      className="btn btn-outline btn-sm"
      onClick={onExport}
      disabled={loading}
    >
      {loading ? (
        <>
          <span className="loading loading-spinner loading-xs"></span>
          Exporting...
        </>
      ) : (
        <>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export
        </>
      )}
    </button>
  );
};