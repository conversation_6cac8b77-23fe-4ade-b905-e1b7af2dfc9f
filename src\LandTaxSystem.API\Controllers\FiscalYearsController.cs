using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.TaxConfig;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FiscalYearsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public FiscalYearsController(ApplicationDbContext context)
        {
            _context = context;
        }

        private FiscalYearResponseDto MapToDto(FiscalYear fiscalYear)
        {
            return new FiscalYearResponseDto
            {
                FiscalYearId = fiscalYear.FiscalYearId,
                Name = fiscalYear.Name,
                StartDate = fiscalYear.StartDate,
                EndDate = fiscalYear.EndDate,
                IsActive = fiscalYear.IsActive,
                CreatedAt = fiscalYear.CreatedAt,
                UpdatedAt = fiscalYear.UpdatedAt
            };
        }

        [HttpGet]
        [AllowAnonymous] // Temporarily remove authorization for testing
        public async Task<ActionResult> GetFiscalYears()
        {
            try
            {
                // Get fiscal years directly with SQL to avoid EF Core mapping issues
                var fiscalYears = await _context.FiscalYears
                    .Select(f => new
                    {
                        f.FiscalYearId,
                        f.Name,
                        f.StartDate,
                        f.EndDate,
                        f.IsActive
                    })
                    .OrderByDescending(f => f.IsActive)
                    .ThenByDescending(f => f.StartDate)
                    .ToListAsync();

                // Convert to simple objects for the response
                var result = fiscalYears.Select(f => new
                {
                    fiscalYearId = f.FiscalYearId,
                    name = f.Name,
                    startDate = f.StartDate,
                    endDate = f.EndDate,
                    isActive = f.IsActive,
                    // Use current date for these fields since they might be missing in the database
                    createdAt = DateTime.UtcNow,
                    updatedAt = DateTime.UtcNow
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in GetFiscalYears: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return StatusCode(500, new { success = false, message = $"An error occurred while retrieving fiscal years: {ex.Message}" });
            }
        }

        [HttpGet("active")]
        [AllowAnonymous]
        public async Task<ActionResult<FiscalYearResponseDto>> GetActiveFiscalYear()
        {
            try
            {
                var activeFiscalYear = await _context.FiscalYears
                    .FirstOrDefaultAsync(f => f.IsActive);

                if (activeFiscalYear == null)
                {
                    return NotFound("No active fiscal year found");
                }

                return Ok(MapToDto(activeFiscalYear));
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while retrieving the active fiscal year: {ex.Message}");
            }
        }

        [HttpGet("list")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetFiscalYearsList()
        {
            try
            {
                var fiscalYears = await _context.FiscalYears
                    .Select(f => new
                    {
                        Id = f.FiscalYearId,
                        Name = f.Name
                    })
                    .OrderByDescending(f => f.Name)
                    .ToListAsync();

                return Ok(fiscalYears);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving fiscal years list", error = ex.Message });
            }
        }

        [HttpPost]
        [AllowAnonymous] // Temporarily remove authorization for testing
        public async Task<ActionResult> CreateFiscalYear([FromBody] FiscalYearCreateDto createDto)
        {
            Console.WriteLine("CreateFiscalYear method called");
            Console.WriteLine($"Request data: Name={createDto?.Name}, StartDate={createDto?.StartDate}, EndDate={createDto?.EndDate}, IsActive={createDto?.IsActive}");

            if (createDto == null)
            {
                Console.WriteLine("Request body is null");
                return BadRequest(new { success = false, message = "Request body is null" });
            }

            try
            {
                // Create fiscal year object with minimal required fields
                var fiscalYear = new FiscalYear
                {
                    FiscalYearId = Guid.NewGuid(),
                    Name = string.IsNullOrEmpty(createDto.Name) ? "Default Fiscal Year" : createDto.Name,
                    IsActive = createDto.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Only set dates if they are provided and valid
                if (createDto.StartDate.HasValue)
                {
                    fiscalYear.StartDate = createDto.StartDate.Value;
                }

                if (createDto.EndDate.HasValue)
                {
                    fiscalYear.EndDate = createDto.EndDate.Value;
                }

                Console.WriteLine($"Created fiscal year object with ID: {fiscalYear.FiscalYearId}, Name: {fiscalYear.Name}");
                Console.WriteLine($"StartDate: {fiscalYear.StartDate}, EndDate: {fiscalYear.EndDate}");

                // Add to database
                _context.FiscalYears.Add(fiscalYear);

                try
                {
                    // Save changes
                    Console.WriteLine("Calling SaveChangesAsync...");
                    int result = await _context.SaveChangesAsync();
                    Console.WriteLine($"SaveChangesAsync completed with result: {result}");

                    return Ok(new
                    {
                        success = true,
                        message = "Fiscal year created successfully",
                        fiscalYear = new
                        {
                            fiscalYearId = fiscalYear.FiscalYearId,
                            name = fiscalYear.Name,
                            startDate = fiscalYear.StartDate,
                            endDate = fiscalYear.EndDate,
                            isActive = fiscalYear.IsActive
                        }
                    });
                }
                catch (DbUpdateException dbEx)
                {
                    Console.WriteLine($"Database update exception: {dbEx.Message}");
                    Console.WriteLine($"Inner exception: {dbEx.InnerException?.Message}");
                    return StatusCode(500, new { success = false, message = $"Database error: {dbEx.InnerException?.Message ?? dbEx.Message}" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in CreateFiscalYear: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return StatusCode(500, new { success = false, message = $"Server error: {ex.Message}" });
            }
        }

        // Placeholder methods for update operations - not implemented
        [HttpPut("{id}")]
        public ActionResult<FiscalYearResponseDto> UpdateFiscalYear(Guid id, FiscalYearUpdateDto updateDto)
        {
            return Ok(new { message = "Update functionality is not implemented" });
        }

        [HttpPut("{id}/activate")]
        public ActionResult<FiscalYearResponseDto> SetActiveFiscalYear(Guid id)
        {
            return Ok(new { message = "Activate functionality is not implemented" });
        }
    }
}
