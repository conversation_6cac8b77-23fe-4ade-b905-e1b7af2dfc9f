import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import type { SubmitHandler } from 'react-hook-form';
import { reportService } from '../../services/reportService';
import type { OutstandingTaxReportRequest, OutstandingTaxReportResponse, OutstandingTaxRecord } from '../../types/outstandingTaxReport';
import { AdminLayout, AdminBreadcrumb } from '../../components/admin';

const OutstandingTaxReport: React.FC = () => {
  const [filters, setFilters] = useState<OutstandingTaxReportRequest>({});
  const [municipalities, setMunicipalities] = useState<{id: string, name: string}[]>([]);
  const [fiscalYears, setFiscalYears] = useState<{id: string, name: string}[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [reportData, setReportData] = useState<OutstandingTaxReportResponse | null>(null);
  const [isLoadingReport, setIsLoadingReport] = useState(false);
  const [reportError, setReportError] = useState<string | null>(null);

  const { control, handleSubmit, reset } = useForm<OutstandingTaxReportRequest>({
    defaultValues: {
      municipalityId: '',
      ward: '',
      fiscalYear: '',
      taxpayerNumber: ''
    }
  });

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [municipalitiesData, yearsData] = await Promise.all([
          reportService.getMunicipalities(),
          reportService.getFiscalYears(),
        ]);
        setMunicipalities(municipalitiesData);
        setFiscalYears(yearsData);
      } catch (error) {
        console.error('Error fetching initial data:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchInitialData();
  }, []);

  const fetchReportData = async (searchFilters: OutstandingTaxReportRequest) => {
    if (!searchFilters || Object.keys(searchFilters).length === 0) {
      setReportData(null);
      return;
    }

    setIsLoadingReport(true);
    setReportError(null);
    
    try {
      const data = await reportService.getOutstandingTaxReport(searchFilters);
      setReportData(data);
    } catch (error) {
      setReportError(error instanceof Error ? error.message : 'An unknown error occurred');
      setReportData(null);
    } finally {
      setIsLoadingReport(false);
    }
  };

  useEffect(() => {
    if (filters && Object.keys(filters).length > 0) {
      fetchReportData(filters);
    }
  }, [filters]);

  const handleSearch: SubmitHandler<OutstandingTaxReportRequest> = (formData) => {
    const activeFilters = Object.fromEntries(
      Object.entries(formData).filter(([, value]) => value !== '' && value !== null)
    );
    setFilters(activeFilters);
  };

  const handleReset = () => {
    reset();
    setFilters({});
    setReportData(null);
    setReportError(null);
  };

  const handleExportCsv = async () => {
    try {
      await reportService.exportToCsv(filters);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleExportPdf = async () => {
    try {
      await reportService.exportToPdf(filters);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-NP', {
      style: 'currency',
      currency: 'NPR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  if (isLoadingData) {
    return (
      <AdminLayout title="Outstanding Tax Report">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4 text-base-content/70">Loading initial data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout 
      title="Outstanding Tax Report"
      subtitle="View and export outstanding tax records"
    >
      <AdminBreadcrumb 
        items={[
          { label: 'Dashboard', href: '/dashboard' },
          { label: 'Reports' },
          { label: 'Outstanding Tax' }
        ]} 
        className="mb-4"
      />

      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <form onSubmit={handleSubmit(handleSearch)}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Municipality</span>
                </label>
                <Controller
                  name="municipalityId"
                  control={control}
                  render={({ field }) => (
                    <select {...field} className="select select-bordered">
                      <option value="">All Municipalities</option>
                      {municipalities.map((municipality) => (
                        <option key={municipality.id} value={municipality.id}>
                          {municipality.name}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Ward Number</span>
                </label>
                <Controller
                  name="ward"
                  control={control}
                  render={({ field }) => (
                    <input {...field} type="text" placeholder="e.g., 5" className="input input-bordered" />
                  )}
                />
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Fiscal Year</span>
                </label>
                <Controller
                  name="fiscalYear"
                  control={control}
                  render={({ field }) => (
                    <select {...field} className="select select-bordered">
                      <option value="">All Years</option>
                      {fiscalYears.map((year) => (
                        <option key={year.id} value={year.name}>
                          {year.name}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">Taxpayer Number</span>
                </label>
                <Controller
                  name="taxpayerNumber"
                  control={control}
                  render={({ field }) => (
                    <input {...field} type="text" placeholder="Enter taxpayer number" className="input input-bordered" />
                  )}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-4">
              <button type="button" onClick={handleReset} className="btn btn-ghost">
                Reset
              </button>
              <button type="submit" className="btn btn-primary">
                Search
              </button>
            </div>
          </form>
        </div>
      </div>

      <div className="card bg-base-100 shadow-xl mt-6">
        <div className="card-body">
          <div className="flex justify-between items-center mb-4">
            <h2 className="card-title">Report Results</h2>
            <div className="flex gap-2">
              <button onClick={handleExportCsv} className="btn btn-sm btn-outline">Export CSV</button>
              <button onClick={handleExportPdf} className="btn btn-sm btn-outline">Export PDF</button>
            </div>
          </div>

          {isLoadingReport ? (
            <div className="text-center py-8">
              <span className="loading loading-spinner loading-lg"></span>
              <p className="mt-4 text-base-content/70">Loading report...</p>
            </div>
          ) : reportError ? (
            <div role="alert" className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
              <span>Error! {reportError}</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table table-zebra">
                <thead>
                  <tr>
                    <th>Taxpayer No.</th>
                    <th>Name</th>
                    <th>Address</th>
                    <th>Contacts</th>
                    <th>Fiscal Year</th>
                    <th>Ward</th>
                    <th>Parcel No.</th>
                    <th className="text-right">Outstanding Due</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData?.records && reportData.records.length > 0 ? (
                    reportData.records.map((record: OutstandingTaxRecord, index: number) => (
                      <tr key={`${record.taxpayerNumber}-${index}`}>
                        <td>{record.taxpayerNumber}</td>
                        <td>{record.taxpayerName}</td>
                        <td>{record.taxpayerAddress}</td>
                        <td>{record.taxpayerContacts}</td>
                        <td>{record.fiscalYear}</td>
                        <td>{record.ward}</td>
                        <td>{record.parcelNumber}</td>
                        <td className="text-right">{formatCurrency(record.outstandingDue)}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <p className="text-base-content/70">No records found matching your criteria.</p>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default OutstandingTaxReport;
