using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class FinalAssessment
    {
        [Key]
        public Guid Id { get; set; }
        

        
        [Required]
        [MaxLength(50)]
        public string TaxpayerRegistration { get; set; } = string.Empty;
        
        [Required]
        public Guid FiscalYearId { get; set; }
        
        [Required]
        public Guid MunicipalityId { get; set; }
        
        public Guid? ReturnFilingId { get; set; }
        
        public Guid? PreliminaryAssessmentId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string TaxpayerName { get; set; } = string.Empty;
        
        public string? Address { get; set; }
        
        [MaxLength(20)]
        public string? Phone { get; set; }
        
        [Required]
        public DateTime AssessmentPeriodFrom { get; set; }
        
        [Required]
        public DateTime AssessmentPeriodTo { get; set; }
        
        public string? SectionsRules { get; set; }
        
        [MaxLength(100)]
        public string? BankName { get; set; }
        
        [MaxLength(100)]
        public string? BranchName { get; set; }
        
        [MaxLength(50)]
        public string? ReasonCode { get; set; }
        
        [MaxLength(50)]
        public string? AppealNumber { get; set; }
        
        public string? ReasonDescription { get; set; }
        
        public DateTime? InterestPenaltyCalculationDate { get; set; }
        
        public DateTime? FinalAssessmentDate { get; set; }
        
        [MaxLength(20)]
        public string Status { get; set; } = "Draft";
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        [Required]
        public string CreatedBy { get; set; } = string.Empty;
        
        public DateTime? UpdatedAt { get; set; }
        
        public string? UpdatedBy { get; set; }
        
        // Navigation properties
        public virtual ICollection<FinalAssessmentDetail> TaxDetails { get; set; } = new List<FinalAssessmentDetail>();
        
        [ForeignKey("FiscalYearId")]
        public virtual FiscalYear? FiscalYear { get; set; }
        
        [ForeignKey("MunicipalityId")]
        public virtual Municipality? Municipality { get; set; }
        
        [ForeignKey("ReturnFilingId")]
        public virtual ReturnFiling? ReturnFiling { get; set; }
        
        [ForeignKey("PreliminaryAssessmentId")]
        public virtual PreliminaryAssessment? PreliminaryAssessment { get; set; }
        
        [ForeignKey("CreatedBy")]
        public virtual ApplicationUser? CreatedByUser { get; set; }
        
        [ForeignKey("UpdatedBy")]
        public virtual ApplicationUser? UpdatedByUser { get; set; }
    }
}