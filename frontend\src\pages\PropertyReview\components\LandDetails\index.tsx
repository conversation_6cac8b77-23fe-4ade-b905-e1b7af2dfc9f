import React from "react";
import type { LandDetails } from "../../../../types";
import { DetailField } from "../common/DetailField";
import { SectionHeader } from "../common/SectionHeader";

interface LandDetailsSectionProps {
  landDetails: LandDetails;
}

export const LandDetailsSection: React.FC<LandDetailsSectionProps> = ({
  landDetails,
}) => {
  // Helper functions to check for meaningful values
  const hasValue = (value: string | null | undefined): boolean => {
    return value !== null && value !== undefined && value.trim() !== "";
  };

  const hasNumericValue = (value: number | null | undefined): boolean => {
    return value !== null && value !== undefined && value > 0;
  };

  return (
    <div className="space-y-6">
      {/* General Land Information */}
      {(hasValue(landDetails.oldVdc) ||
        hasValue(landDetails.oldWardNo) ||
        hasValue(landDetails.currentWardNo) ||
        hasValue(landDetails.kittaNo) ||
        hasValue(landDetails.mapNo) ||
        hasValue(landDetails.fiscalYear) ||
        hasValue(landDetails.measurementUnit) ||
        hasValue(landDetails.otherDetails)) && (
        <div className="card bg-base-200 shadow-sm">
          <div className="card-body p-4">
            <SectionHeader
              title="General Land Information"
              icon="📍"
              subtitle="Location and basic land details"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {hasValue(landDetails.oldVdc) && (
                <DetailField label="Old VDC" value={landDetails.oldVdc} />
              )}
              {hasValue(landDetails.oldWardNo) && (
                <DetailField
                  label="Old Ward Number"
                  value={landDetails.oldWardNo}
                />
              )}
              {hasValue(landDetails.currentWardNo) && (
                <DetailField
                  label="Current Ward Number"
                  value={landDetails.currentWardNo}
                />
              )}
              {hasValue(landDetails.kittaNo) && (
                <DetailField label="Kitta Number" value={landDetails.kittaNo} />
              )}
              {hasValue(landDetails.mapNo) && (
                <DetailField label="Map Number" value={landDetails.mapNo} />
              )}
              {hasValue(landDetails.fiscalYear) && (
                <DetailField label="Fiscal Year" value={landDetails.fiscalYear} />
              )}
              {hasValue(landDetails.measurementUnit) && (
                <DetailField
                  label="Measurement Unit"
                  value={landDetails.measurementUnit}
                />
              )}
            </div>

            {hasValue(landDetails.otherDetails) && (
              <div className="mt-4">
                <DetailField
                  label="Other Details"
                  value={landDetails.otherDetails}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Land Characteristics */}
      <div className="card bg-base-200 shadow-sm">
        <div className="card-body p-4">
          <SectionHeader
            title="Land Characteristics"
            icon="🌾"
            subtitle="Property characteristics and status"
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <DetailField
              label="Multi-rate Valuation"
              value={landDetails.isMultiRateValuation}
              type="boolean"
            />
            <DetailField
              label="Temporary House"
              value={landDetails.isTemporaryHouse}
              type="boolean"
            />
            <DetailField
              label="Cultivable Land"
              value={landDetails.isCultivable}
              type="boolean"
            />
            <DetailField
              label="Plot Deactivated"
              value={landDetails.deactivatePlot}
              type="boolean"
            />
            <DetailField
              label="Land Revenue Applicable"
              value={landDetails.landRevenueApplicable}
              type="boolean"
            />
            <DetailField
              label="Last FY for Inclusion"
              value={landDetails.lastFyForInclusion}
            />
          </div>
        </div>
      </div>

      {/* Area Measurements */}
      {(hasNumericValue(landDetails.areaBigha) ||
        hasNumericValue(landDetails.areaKattha) ||
        hasNumericValue(landDetails.areaDhur) ||
        hasNumericValue(landDetails.areaRopani) ||
        hasNumericValue(landDetails.areaAana) ||
        hasNumericValue(landDetails.areaPaisa) ||
        hasNumericValue(landDetails.areaDaam)) && (
        <div className="card bg-base-200 shadow-sm">
          <div className="card-body p-4">
            <SectionHeader
              title="Area Measurements"
              icon="📐"
              subtitle="Detailed area measurements in different units"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {hasNumericValue(landDetails.areaBigha) && (
                <DetailField
                  label="Bigha"
                  value={landDetails.areaBigha}
                  type="number"
                  unit="bigha"
                />
              )}
              {hasNumericValue(landDetails.areaKattha) && (
                <DetailField
                  label="Kattha"
                  value={landDetails.areaKattha}
                  type="number"
                  unit="kattha"
                />
              )}
              {hasNumericValue(landDetails.areaDhur) && (
                <DetailField
                  label="Dhur"
                  value={landDetails.areaDhur}
                  type="number"
                  unit="dhur"
                />
              )}
              {hasNumericValue(landDetails.areaRopani) && (
                <DetailField
                  label="Ropani"
                  value={landDetails.areaRopani}
                  type="number"
                  unit="ropani"
                />
              )}
              {hasNumericValue(landDetails.areaAana) && (
                <DetailField
                  label="Aana"
                  value={landDetails.areaAana}
                  type="number"
                  unit="aana"
                />
              )}
              {hasNumericValue(landDetails.areaPaisa) && (
                <DetailField
                  label="Paisa"
                  value={landDetails.areaPaisa}
                  type="number"
                  unit="paisa"
                />
              )}
              {hasNumericValue(landDetails.areaDaam) && (
                <DetailField
                  label="Daam"
                  value={landDetails.areaDaam}
                  type="number"
                  unit="daam"
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* Tax and Revenue Information */}
      {(hasNumericValue(landDetails.taxpayerPrice) ||
        hasNumericValue(landDetails.taxpayerLandRevenuePrice)) && (
        <div className="card bg-base-200 shadow-sm">
          <div className="card-body p-4">
            <SectionHeader
              title="Tax and Revenue Information"
              icon="💰"
              subtitle="Taxpayer declared values and revenue details"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {hasNumericValue(landDetails.taxpayerPrice) && (
                <DetailField
                  label="Taxpayer Declared Price"
                  value={landDetails.taxpayerPrice}
                  type="currency"
                />
              )}
              {hasNumericValue(landDetails.taxpayerLandRevenuePrice) && (
                <DetailField
                  label="Taxpayer Land Revenue Price"
                  value={landDetails.taxpayerLandRevenuePrice}
                  type="currency"
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* Structure Details */}
      {(hasNumericValue(landDetails.structureAreaLength) ||
        hasNumericValue(landDetails.structureAreaBreadth)) && (
        <div className="card bg-base-200 shadow-sm">
          <div className="card-body p-4">
            <SectionHeader
              title="Structure Details"
              icon="🏗️"
              subtitle="Building and structure information"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {hasNumericValue(landDetails.structureAreaLength) && (
                <DetailField
                  label="Structure Length"
                  value={landDetails.structureAreaLength}
                  type="number"
                  unit="m"
                />
              )}
              {hasNumericValue(landDetails.structureAreaBreadth) && (
                <DetailField
                  label="Structure Breadth"
                  value={landDetails.structureAreaBreadth}
                  type="number"
                  unit="m"
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
