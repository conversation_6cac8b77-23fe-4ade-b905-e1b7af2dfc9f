import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { propertyService } from '../services/api';
import TaxCalculator from '../components/TaxCalculator';
import DirectTaxPayment from '../components/DirectTaxPayment';
import type { Property, PaymentResponseDto } from '../types';

// Payment flow types
type PaymentFlow = 'select' | 'assessment' | 'property';

const TaxPaymentPageNew: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // Core state
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentFlow, setPaymentFlow] = useState<PaymentFlow>('select');
  
  // Property payment flow state
  const [showTaxCalculation, setShowTaxCalculation] = useState(false);
  const [showDirectPayment, setShowDirectPayment] = useState(false);
  const [calculatedTaxAmount, setCalculatedTaxAmount] = useState<number | null>(null);
  
  // Payment success state
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentResult, setPaymentResult] = useState<PaymentResponseDto | null>(null);

  // Load property data on mount
  const loadPropertyData = useCallback(async () => {
    if (!id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const propertyData = await propertyService.getById(id);
      setProperty(propertyData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load property data');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      loadPropertyData();
    } else {
      setError('Property ID is required');
      setLoading(false);
    }
  }, [id, loadPropertyData]);

  // Payment success handler
  const handlePaymentSuccess = (payment: PaymentResponseDto) => {
    setPaymentResult(payment);
    setPaymentSuccess(true);
  };

  // Payment error handler
  const handlePaymentError = (errorMessage: string) => {
    setError(errorMessage);
  };

  // Reset to flow selection
  const resetToFlowSelection = () => {
    setPaymentFlow('select');
    setShowTaxCalculation(false);
    setShowDirectPayment(false);
    setCalculatedTaxAmount(null);
    setPaymentSuccess(false);
    setPaymentResult(null);
    setError(null);
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </div>
    );
  }

  if (error && !property) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="alert alert-error">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <p className="mb-4">{error}</p>
          <button
            onClick={() => navigate('/properties')}
            className="btn btn-error"
          >
            Back to Properties
          </button>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="alert alert-warning">
          <h2 className="text-xl font-semibold mb-2">Property Not Found</h2>
          <p className="mb-4">The requested property could not be found.</p>
          <button
            onClick={() => navigate('/properties')}
            className="btn btn-primary"
          >
            Back to Properties
          </button>
        </div>
      </div>
    );
  }

  if (paymentSuccess && paymentResult) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="alert alert-success">
          <div className="text-center">
            <div className="text-4xl mb-4">✅</div>
            <h2 className="text-2xl font-bold mb-4">Payment Successful!</h2>
            
            <div className="card bg-base-100 shadow-xl p-6 mb-6">
              <h3 className="text-lg font-semibold mb-4">Payment Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-base-content/70">Payment ID:</span>
                  <p className="text-base-content">{paymentResult.paymentId}</p>
                </div>
                <div>
                  <span className="font-medium text-base-content/70">Amount Paid:</span>
                  <p className="text-base-content">NPR {paymentResult.amountPaid?.toLocaleString()}</p>
                </div>
                <div>
                  <span className="font-medium text-base-content/70">Payment Gateway:</span>
                  <p className="text-base-content">{paymentResult.paymentGateway}</p>
                </div>
                <div>
                  <span className="font-medium text-base-content/70">Status:</span>
                  <p className="text-success font-medium">{paymentResult.status}</p>
                </div>
              </div>
            </div>
            
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => navigate(`/property/${property.id}`)}
                className="btn btn-secondary"
              >
                Back to Property
              </button>
              <button
                onClick={() => navigate('/properties')}
                className="btn btn-primary"
              >
                My Properties
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="card bg-base-100 shadow-xl mb-6">
        <div className="card-body">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold">Tax Payment</h1>
              <p className="text-base-content/70 mt-1">{property.address}</p>
            </div>
            <button
              onClick={() => navigate(`/property/${property.id}`)}
              className="btn btn-ghost"
            >
              Back to Property
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="alert alert-error mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm">{error}</p>
            </div>
            <div className="ml-auto">
              <button
                onClick={() => setError(null)}
                className="btn btn-ghost btn-sm btn-circle"
              >
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Flow Selection */}
      {paymentFlow === 'select' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pay by Assessment */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center">
              <div className="text-4xl text-primary mb-4">📋</div>
              <h3 className="card-title justify-center mb-2">Pay by Assessment</h3>
              <p className="text-base-content/70 mb-6">
                Use existing tax assessments to calculate and pay your property tax.
              </p>
              <div className="card-actions justify-center">
                <button
                  onClick={() => setPaymentFlow('assessment')}
                  className="btn btn-primary"
                >
                  Choose Assessment Payment
                </button>
              </div>
            </div>
          </div>

          {/* Direct Property Payment */}
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center">
              <div className="text-4xl text-secondary mb-4">🏠</div>
              <h3 className="card-title justify-center mb-2">Direct Property Payment</h3>
              <p className="text-base-content/70 mb-6">
                Calculate tax directly from property details and make payment.
              </p>
              <div className="card-actions justify-center">
                <button
                  onClick={() => setPaymentFlow('property')}
                  className="btn btn-secondary"
                >
                  Choose Direct Payment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Assessment-Based Payment Flow */}
      {paymentFlow === 'assessment' && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex justify-between items-center mb-6">
              <h2 className="card-title">Assessment-Based Payment Flow</h2>
              <button
                onClick={resetToFlowSelection}
                className="btn btn-ghost"
              >
                ← Back to Payment Options
              </button>
            </div>
            <h3 className="text-lg font-medium mb-2">Assessment-Based Payment Flow</h3>
            <p className="text-base-content/70 mb-6">
              This will redirect you to the existing assessment-based payment system.
            </p>
            <button
              onClick={() => navigate(`/tax-payment-legacy/${property.id}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`)}
              className="btn btn-primary"
            >
              Continue to Assessment Payment
            </button>
          </div>
        </div>
      )}

      {/* Property-Based Payment Flow */}
      {paymentFlow === 'property' && (
        <div className="space-y-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <div className="flex justify-between items-center mb-6">
                <h2 className="card-title">Direct Property Tax Payment</h2>
                <button
                  onClick={resetToFlowSelection}
                  className="btn btn-ghost"
                >
                  ← Back to Payment Options
                </button>
              </div>

              {/* Property Information */}
              <div className="bg-base-200 rounded-lg p-4 mb-6">
                <h3 className="font-medium mb-2">Property Details</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-base-content/70">Address:</span>
                    <p className="text-base-content">{property.address}</p>
                  </div>
                  <div>
                    <span className="text-base-content/70">Land Area:</span>
                    <p className="text-base-content">{property.landArea} sq. meters</p>
                  </div>
                  <div>
                    <span className="text-base-content/70">Usage Type:</span>
                    <p className="text-base-content">{property.usageType}</p>
                  </div>
                  <div>
                    <span className="text-base-content/70">Status:</span>
                    <p className="text-success font-medium">{property.status}</p>
                  </div>
                </div>
              </div>

              {/* Step 1: Tax Calculation */}
              <div className="border-l-4 border-primary pl-6 mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-lg font-medium">Step 1: Calculate Tax</h3>
                  <button
                    onClick={() => setShowTaxCalculation(!showTaxCalculation)}
                    className="btn btn-primary"
                  >
                    {showTaxCalculation ? 'Hide Calculation' : 'Calculate Tax'}
                  </button>
                </div>
                
                {showTaxCalculation && (
                  <TaxCalculator
                    propertyId={property.id}
                    className="mt-4"
                  />
                )}
              </div>

              {/* Step 2: Payment */}
              <div className="border-l-4 border-success pl-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-lg font-medium">Step 2: Make Payment</h3>
                  <button
                    onClick={() => setShowDirectPayment(!showDirectPayment)}
                    disabled={!showTaxCalculation}
                    className="btn btn-success disabled:btn-disabled"
                  >
                    {showDirectPayment ? 'Hide Payment' : 'Proceed to Payment'}
                  </button>
                </div>
                
                {!showTaxCalculation && (
                  <p className="text-base-content/70 text-sm">Please calculate tax first before proceeding to payment.</p>
                )}
                
                {showDirectPayment && showTaxCalculation && (
                  <DirectTaxPayment
                    propertyId={property.id}
                    calculatedTaxAmount={calculatedTaxAmount || property.estimatedTax}
                    onPaymentSuccess={handlePaymentSuccess}
                    onPaymentError={handlePaymentError}
                    onCancel={() => setShowDirectPayment(false)}
                    className="mt-4"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaxPaymentPageNew;