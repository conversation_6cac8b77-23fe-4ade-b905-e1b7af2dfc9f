using AutoMapper;
using LandTaxSystem.Core.Mapping;

namespace LandTaxSystem.API.App_Start
{
    public class AutoMapperConfig
    {
        public static MapperConfiguration Configure()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<RebateMappingProfile>();
                cfg.AddProfile<LandDetailMappingProfile>();
            });

            return config;
        }
    }
}