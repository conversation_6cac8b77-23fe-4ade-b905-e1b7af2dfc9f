import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';

interface ReceiptPaymentsData {
  taxpayerRegistrationNumber: string;
  taxpayerName: string;
  province: string;
  district: string;
  municipality: string;
  ward: string;
  streetTole: string;
  houseNumber: string;
  email: string;
  mobile: string;
  phone: string;
  paymentDate: string;
  paymentStore: string;
  voucherNumber: string;
  accountNumber: string;
  bank: string;
  bankBranch: string;
  revenueAccount: string;
  amount: string;
  document: File | null;
  remarks: string;
}

const ReceiptPaymentsForm: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<ReceiptPaymentsData>({
    taxpayerRegistrationNumber: '',
    taxpayerName: '',
    province: '',
    district: '',
    municipality: '',
    ward: '',
    streetTole: '',
    houseNumber: '',
    email: '',
    mobile: '',
    phone: '',
    paymentDate: '',
    paymentStore: '',
    voucherNumber: '',
    accountNumber: '',
    bank: '',
    bankBranch: '',
    revenueAccount: '',
    amount: '',
    document: null,
    remarks: ''
  });

  const [errors, setErrors] = useState<Partial<ReceiptPaymentsData>>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name as keyof ReceiptPaymentsData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, document: file }));
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ReceiptPaymentsData> = {};

    if (!formData.taxpayerRegistrationNumber.trim()) {
      newErrors.taxpayerRegistrationNumber = 'Taxpayer Registration Number is required';
    }
    if (!formData.taxpayerName.trim()) {
      newErrors.taxpayerName = 'Taxpayer Name is required';
    }
    if (!formData.paymentDate) {
      newErrors.paymentDate = 'Payment Date is required';
    }
    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('कृपया सबै आवश्यक फिल्डहरू भर्नुहोस्');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // TODO: Implement API call to submit receipt payments data
      console.log('Receipt Payments Data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('भौचर भुक्तानी सफलतापूर्वक दर्ता भयो');
      navigate('/admin/dashboard');
    } catch (error) {
      console.error('Error submitting receipt payments:', error);
      toast.error('भौचर भुक्तानी दर्ता गर्न असफल भयो');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/dashboard');
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <h2 className="text-xl font-semibold mb-4">मुख्य विवरण (Main Details)</h2>
          
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Taxpayer Registration Number: *</span>
              </label>
              <input
                type="text"
                name="taxpayerRegistrationNumber"
                value={formData.taxpayerRegistrationNumber}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter registration number"
              />
              {errors.taxpayerRegistrationNumber && (
                <span className="text-error text-sm">{errors.taxpayerRegistrationNumber}</span>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Taxpayer Name: *</span>
              </label>
              <input
                type="text"
                name="taxpayerName"
                value={formData.taxpayerName}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter taxpayer name"
              />
              {errors.taxpayerName && (
                <span className="text-error text-sm">{errors.taxpayerName}</span>
              )}
            </div>
          </div>

          {/* Address Information */}
          <h3 className="text-lg font-medium mb-3">ठेगाना विवरण (Address Details)</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Province:</span>
              </label>
              <input
                type="text"
                name="province"
                value={formData.province}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter province"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">District:</span>
              </label>
              <input
                type="text"
                name="district"
                value={formData.district}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter district"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Municipality:</span>
              </label>
              <input
                type="text"
                name="municipality"
                value={formData.municipality}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter municipality"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Ward:</span>
              </label>
              <input
                type="text"
                name="ward"
                value={formData.ward}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter ward number"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Street/Tole:</span>
              </label>
              <input
                type="text"
                name="streetTole"
                value={formData.streetTole}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter street/tole"
              />
            </div>
          </div>

          <div className="form-control mb-6">
            <label className="label">
              <span className="label-text">House Number:</span>
            </label>
            <input
              type="text"
              name="houseNumber"
              value={formData.houseNumber}
              onChange={handleInputChange}
              className="input input-bordered input-sm"
              placeholder="Enter house number"
            />
          </div>

          {/* Contact Information */}
          <h3 className="text-lg font-medium mb-3">सम्पर्क विवरण (Contact Details)</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Email:</span>
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter email address"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Mobile No.:</span>
              </label>
              <input
                type="tel"
                name="mobile"
                value={formData.mobile}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter mobile number"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Phone:</span>
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter phone number"
              />
            </div>
          </div>
        </div>

        {/* Payment Information */}
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <h2 className="text-xl font-semibold mb-4">भुक्तानी विवरण (Payment Details)</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Payment Date: *</span>
              </label>
              <input
                type="date"
                name="paymentDate"
                value={formData.paymentDate}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
              />
              {errors.paymentDate && (
                <span className="text-error text-sm">{errors.paymentDate}</span>
              )}
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">रु. नं.:</span>
              </label>
              <input
                type="text"
                name="amount"
                value={formData.amount}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter amount"
              />
              {errors.amount && (
                <span className="text-error text-sm">{errors.amount}</span>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Payment Store:</span>
              </label>
              <select
                name="paymentStore"
                value={formData.paymentStore}
                onChange={handleInputChange}
                className="select select-bordered select-sm"
              >
                <option value="">.....Select Paymetn Store.....</option>
                <option value="store1">Store 1</option>
                <option value="store2">Store 2</option>
                <option value="store3">Store 3</option>
              </select>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">भौचर नं.:</span>
              </label>
              <input
                type="text"
                name="voucherNumber"
                value={formData.voucherNumber}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter voucher number"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Account No.:</span>
              </label>
              <select
                name="accountNumber"
                value={formData.accountNumber}
                onChange={handleInputChange}
                className="select select-bordered select-sm"
              >
                <option value="">.....Account No.....</option>
                <option value="acc1">Account 1</option>
                <option value="acc2">Account 2</option>
                <option value="acc3">Account 3</option>
              </select>
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Bank:</span>
              </label>
              <input
                type="text"
                name="bank"
                value={formData.bank}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter bank name"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="form-control">
              <label className="label">
                <span className="label-text">Bank Branch:</span>
              </label>
              <input
                type="text"
                name="bankBranch"
                value={formData.bankBranch}
                onChange={handleInputChange}
                className="input input-bordered input-sm"
                placeholder="Enter bank branch"
              />
            </div>

            <div className="form-control">
              <label className="label">
                <span className="label-text">Revenue Account:</span>
              </label>
              <select
                name="revenueAccount"
                value={formData.revenueAccount}
                onChange={handleInputChange}
                className="select select-bordered select-sm"
              >
                <option value="">.....Revenue Account.....</option>
                <option value="rev1">Revenue Account 1</option>
                <option value="rev2">Revenue Account 2</option>
                <option value="rev3">Revenue Account 3</option>
              </select>
            </div>
          </div>
        </div>

        {/* Document Upload */}
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <h2 className="text-xl font-semibold mb-4">कागजात (Documents)</h2>
          
          <div className="form-control mb-4">
            <label className="label">
              <span className="label-text">Document:</span>
            </label>
            <input
              type="file"
              name="document"
              onChange={handleFileChange}
              className="file-input file-input-bordered file-input-sm w-full"
              accept=".pdf,.jpg,.jpeg,.png"
            />
            {formData.document ? (
              <div className="mt-2">
                <span className="text-success text-sm">File selected: {formData.document.name}</span>
                <div className="mt-2">
                  <a href="#" className="text-red-600 underline text-sm">Document View</a>
                </div>
              </div>
            ) : (
              <span className="text-gray-500 text-sm mt-1">No file selected.</span>
            )}
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">केफियत:</span>
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              className="textarea textarea-bordered textarea-sm"
              rows={4}
              placeholder="Enter remarks"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
          </div>
        </div>
    </form>
  );
};

export default ReceiptPaymentsForm;