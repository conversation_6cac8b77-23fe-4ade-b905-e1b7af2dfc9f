using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using LandTaxSystem.Core.DTOs.FinalAssessment;
using LandTaxSystem.Core.Interfaces;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FinalAssessmentsController : ControllerBase
    {
        private readonly IFinalAssessmentService _finalAssessmentService;

        public FinalAssessmentsController(IFinalAssessmentService finalAssessmentService)
        {
            _finalAssessmentService = finalAssessmentService;
        }

        /// <summary>
        /// Get all final assessments with pagination
        /// </summary>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>List of final assessments</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<FinalAssessmentListDto>>> GetAll(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var assessments = await _finalAssessmentService.GetAllAsync(page, pageSize);
                var totalCount = await _finalAssessmentService.GetTotalCountAsync();
                
                Response.Headers["X-Total-Count"] = totalCount.ToString();
                Response.Headers["X-Page"] = page.ToString();
                Response.Headers["X-Page-Size"] = pageSize.ToString();
                
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get a specific final assessment by ID
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Final assessment details</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<FinalAssessmentResponseDto>> GetById(Guid id)
        {
            try
            {
                var assessment = await _finalAssessmentService.GetByIdAsync(id);
                if (assessment == null)
                {
                    return NotFound(new { message = "Assessment not found" });
                }
                return Ok(assessment);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Get final assessments by municipality
        /// </summary>
        /// <param name="municipalityId">Municipality ID</param>
        /// <returns>List of final assessments for the municipality</returns>
        [HttpGet("by-municipality/{municipalityId}")]
        public async Task<ActionResult<IEnumerable<FinalAssessmentListDto>>> GetByMunicipality(Guid municipalityId)
        {
            try
            {
                var assessments = await _finalAssessmentService.GetByMunicipalityAsync(municipalityId);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get final assessments by fiscal year
        /// </summary>
        /// <param name="fiscalYearId">Fiscal year ID</param>
        /// <returns>List of final assessments for the fiscal year</returns>
        [HttpGet("by-fiscal-year/{fiscalYearId}")]
        public async Task<ActionResult<IEnumerable<FinalAssessmentListDto>>> GetByFiscalYear(Guid fiscalYearId)
        {
            try
            {
                var assessments = await _finalAssessmentService.GetByFiscalYearAsync(fiscalYearId);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get final assessments by taxpayer registration
        /// </summary>
        /// <param name="taxpayerRegistration">Taxpayer registration number</param>
        /// <returns>List of final assessments for the taxpayer</returns>
        [HttpGet("by-taxpayer/{taxpayerRegistration}")]
        public async Task<ActionResult<IEnumerable<FinalAssessmentListDto>>> GetByTaxpayerRegistration(string taxpayerRegistration)
        {
            try
            {
                var assessments = await _finalAssessmentService.GetByTaxpayerRegistrationAsync(taxpayerRegistration);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get final assessments by preliminary assessment
        /// </summary>
        /// <param name="preliminaryAssessmentId">Preliminary assessment ID</param>
        /// <returns>List of final assessments for the preliminary assessment</returns>
        [HttpGet("by-preliminary/{preliminaryAssessmentId}")]
        public async Task<ActionResult<IEnumerable<FinalAssessmentListDto>>> GetByPreliminaryAssessment(Guid preliminaryAssessmentId)
        {
            try
            {
                var assessments = await _finalAssessmentService.GetByPreliminaryAssessmentAsync(preliminaryAssessmentId);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Search final assessments
        /// </summary>
        /// <param name="taxpayerRegistration">Taxpayer registration filter</param>
        /// <param name="taxpayerName">Taxpayer name filter</param>
        /// <param name="fiscalYearId">Fiscal year filter</param>
        /// <param name="municipalityId">Municipality filter</param>
        /// <param name="status">Status filter</param>
        /// <param name="createdFrom">Created from date filter</param>
        /// <param name="createdTo">Created to date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paginated list of matching final assessments</returns>
        [HttpGet("search")]
        public async Task<ActionResult<FinalAssessmentListResponse>> Search(
            [FromQuery] string? taxpayerRegistration = null,
            [FromQuery] string? taxpayerName = null,
            [FromQuery] Guid? fiscalYearId = null,
            [FromQuery] Guid? municipalityId = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? createdFrom = null,
            [FromQuery] DateTime? createdTo = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var searchParams = new FinalAssessmentSearchParams
                {
                    TaxpayerRegistration = taxpayerRegistration,
                    TaxpayerName = taxpayerName,
                    FiscalYearId = fiscalYearId,
                    MunicipalityId = municipalityId,
                    Status = status,
                    CreatedFrom = createdFrom,
                    CreatedTo = createdTo,
                    Page = page,
                    PageSize = pageSize
                };
                
                var result = await _finalAssessmentService.SearchAsync(searchParams);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while searching assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Create a new final assessment
        /// </summary>
        /// <param name="dto">Final assessment creation data</param>
        /// <returns>Created final assessment</returns>
        [HttpPost]
        public async Task<ActionResult<FinalAssessmentResponseDto>> Create([FromBody] FinalAssessmentCreateDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if taxpayer registration already exists
                var exists = await _finalAssessmentService.ExistsAsync(dto.TaxpayerRegistration);
                if (exists)
                {
                    return Conflict(new { message = "Final assessment for this taxpayer registration already exists" });
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "User not authenticated" });
                }

                var assessment = await _finalAssessmentService.CreateAsync(dto, userId);
                return CreatedAtAction(nameof(GetById), new { id = assessment.Id }, assessment);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while creating the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Update an existing final assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="dto">Final assessment update data</param>
        /// <returns>Updated final assessment</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<FinalAssessmentResponseDto>> Update(Guid id, [FromBody] FinalAssessmentUpdateDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "User not authenticated" });
                }

                var assessment = await _finalAssessmentService.UpdateAsync(id, dto, userId);
                return Ok(assessment);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while updating the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Delete a final assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _finalAssessmentService.DeleteAsync(id);
                if (!result)
                {
                    return NotFound(new { message = "Assessment not found" });
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Check if taxpayer registration exists
        /// </summary>
        /// <param name="taxpayerRegistration">Taxpayer registration number</param>
        /// <returns>Existence status</returns>
        [HttpGet("exists/{taxpayerRegistration}")]
        public async Task<ActionResult<bool>> Exists(string taxpayerRegistration)
        {
            try
            {
                var exists = await _finalAssessmentService.ExistsAsync(taxpayerRegistration);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while checking assessment existence", error = ex.Message });
            }
        }
    }
}