using LandTaxSystem.Core.DTOs.Rebate;
using LandTaxSystem.Core.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class RebatesController : ControllerBase
    {
        private readonly IRebateService _rebateService;

        public RebatesController(IRebateService rebateService)
        {
            _rebateService = rebateService;
        }

        [HttpPost]
        public async Task<IActionResult> CreateRebate([FromBody] CreateRebateDto createRebateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var rebate = await _rebateService.CreateRebateAsync(createRebateDto);
            return CreatedAtAction(nameof(GetRebate), new { id = rebate.Id }, rebate);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetRebate(int id)
        {
            var rebate = await _rebateService.GetRebateAsync(id);
            if (rebate == null)
            {
                return NotFound();
            }
            return Ok(rebate);
        }

        [HttpGet]
        public async Task<IActionResult> GetRebates()
        {
            var rebates = await _rebateService.GetRebatesAsync();
            return Ok(rebates);
        }
    }
}