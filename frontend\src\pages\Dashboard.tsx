import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import api, { propertyService, municipalityService, userService, paymentService } from "../services/api";
import { fiscalYearService } from "../services/fiscalYearService";
import { AdminLayout, AdminStats, AdminActionCard, AdminMetricCard, AdminSummaryCard } from "../components/admin";
import type { Property } from "../types";
import type { FiscalYear } from "../services/taxConfigService";

// Import the map component
import PropertiesMap from "../components/PropertiesMap";

interface DashboardStats {
  totalProperties: number;
  pendingReviews: number;
  totalAssessments: number;
  outstandingTax: number;
  totalTaxPaid: number;
  totalTaxpayers?: number;
  todaysTaxCollection?: number;
  monthlyTaxCollection?: number;
}

// Calculate dynamic zoom level based on property coordinates
const calculateDynamicZoom = (coordinates: [number, number][]): number => {
  if (coordinates.length === 0) return 14; // Default zoom
  if (coordinates.length === 1) return 16; // Single property - closer zoom

  // Calculate bounding box for multiple properties
  const lats = coordinates.map(coord => coord[0]);
  const lngs = coordinates.map(coord => coord[1]);

  const minLat = Math.min(...lats);
  const maxLat = Math.max(...lats);
  const minLng = Math.min(...lngs);
  const maxLng = Math.max(...lngs);

  // Calculate the distance span
  const latSpan = maxLat - minLat;
  const lngSpan = maxLng - minLng;
  const maxSpan = Math.max(latSpan, lngSpan);

  // Determine zoom based on span (smaller span = higher zoom)
  let zoom = 14;
  if (maxSpan < 0.001) zoom = 18; // Very close properties
  else if (maxSpan < 0.005) zoom = 16; // Close properties
  else if (maxSpan < 0.01) zoom = 15; // Moderate spread
  else if (maxSpan < 0.05) zoom = 13; // Wide spread
  else zoom = 12; // Very wide spread

  // Ensure zoom is within reasonable bounds
  return Math.max(12, Math.min(18, zoom));
};

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalProperties: 0,
    pendingReviews: 0,
    totalAssessments: 0,
    outstandingTax: 0,
    totalTaxPaid: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [municipalityName, setMunicipalityName] = useState<string>("");
  const [properties, setProperties] = useState<Property[]>([]);
  const [mapCenter, setMapCenter] = useState<[number, number]>([27.7172, 85.324]); // Default to Kathmandu
  const [mapZoom, setMapZoom] = useState<number>(14); // Dynamic zoom level
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [selectedFiscalYear, setSelectedFiscalYear] = useState<string>("");
  const [officerProperties, setOfficerProperties] = useState<Property[]>([]);
  const [officerMapCenter, setOfficerMapCenter] = useState<[number, number]>([27.7172, 85.324]);
  const [officerMapZoom, setOfficerMapZoom] = useState<number>(13);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError("");

        if (user?.role === "Citizen") {
          // Fetch citizen dashboard data
          const properties = await propertyService.getMyProperties();
          console.log("Fetched properties for citizen:", properties.length);
          console.log("Sample property data:", properties[0]);
          setProperties(properties);

          // Calculate stats for citizen
          const totalProps = properties.length;
          const pendingProps = properties.filter(p => p.status === "PendingReview").length;

          // Calculate total outstanding tax, total tax paid, and find property with coordinates
          let outstandingTax = 0;
          let totalTaxPaid = 0;
          const validCoordinates: [number, number][] = [];

          properties.forEach(property => {
            // Sum up outstanding tax and total tax paid if available
            if (property.taxDue !== undefined) {
              outstandingTax += property.taxDue;
            }
            if (property.taxPaid !== undefined) {
              totalTaxPaid += property.taxPaid;
            }

            // Collect all valid coordinates for map center calculation
            if (property.parcelGeoJson) {
              try {
                console.log("Property parcelGeoJson:", property.parcelGeoJson);
                const geoJson =
                  typeof property.parcelGeoJson === "string"
                    ? JSON.parse(property.parcelGeoJson)
                    : property.parcelGeoJson;

                if (geoJson.coordinates?.[0]?.[0]) {
                  const [longitude, latitude] = geoJson.coordinates[0][0];
                  console.log("Extracted coordinates:", { longitude, latitude });
                  if (latitude && longitude && !isNaN(latitude) && !isNaN(longitude)) {
                    validCoordinates.push([Number(latitude), Number(longitude)]);
                    console.log("Added valid coordinate:", [Number(latitude), Number(longitude)]);
                  }
                }
              } catch (error) {
                console.error("Error parsing coordinates for property:", property.id || "unknown", error);
              }
            } else {
              console.log("No coordinates found for property:", property.id || "unknown");
            }
          });

          // Calculate center and zoom based on all valid coordinates
          if (validCoordinates.length > 0) {
            const avgLat = validCoordinates.reduce((sum, coord) => sum + coord[0], 0) / validCoordinates.length;
            const avgLng = validCoordinates.reduce((sum, coord) => sum + coord[1], 0) / validCoordinates.length;
            const dynamicZoom = calculateDynamicZoom(validCoordinates);

            console.log("Calculated map center for citizen:", [avgLat, avgLng]);
            console.log("Calculated dynamic zoom:", dynamicZoom);
            console.log("Valid coordinates found:", validCoordinates.length);
            console.log("Sample coordinates:", validCoordinates.slice(0, 3));

            setMapCenter([avgLat, avgLng]);
            setMapZoom(dynamicZoom);
          } else {
            console.log("No valid coordinates found for citizen properties");
            setMapZoom(14); // Default zoom when no coordinates
          }

          // Count properties with assessments
          const totalAssessments = properties.filter(p => p.status === "Assessed").length;

          setStats({
            totalProperties: totalProps,
            pendingReviews: pendingProps,
            totalAssessments,
            outstandingTax,
            totalTaxPaid,
          });
        } else if (user?.role === "Officer") {
          // Fetch officer dashboard data
          if (user.municipalityId) {
            // Load fiscal years and set active one as default
            let activeFiscalYearId = "";
            try {
              const allFiscalYears = await fiscalYearService.getAll();
              setFiscalYears(allFiscalYears);
              const activeFiscalYear = allFiscalYears.find(fy => fy.isActive);
              if (activeFiscalYear) {
                activeFiscalYearId = activeFiscalYear.fiscalYearId;
                setSelectedFiscalYear(activeFiscalYear.fiscalYearId);
              }
            } catch (error) {
              console.error("Failed to load fiscal years:", error);
            }

            const properties = await propertyService.getByMunicipality(user.municipalityId, "PendingReview");

            // Calculate total tax paid for officer view if needed
            const totalTaxPaid = 0; // This would be fetched from the API in a real implementation

            // Fetch total taxpayers for the municipality
            const taxpayers = await userService.getTaxpayers(user.municipalityId);

            // Fetch today's tax collection
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // Get all properties for the municipality with the correct fiscal year
            const allProperties = await api.get(`/properties/municipality/${user.municipalityId}?fiscalYearId=${activeFiscalYearId}`);
            const propertiesData = allProperties.data;

            setProperties(propertiesData);
            setOfficerProperties(propertiesData);

            // Calculate map center for officer properties
            const validCoordinates: [number, number][] = [];
            propertiesData.forEach((property: Property) => {
              if (property.parcelGeoJson) {
                try {
                  console.log("Officer property parcelGeoJson:", property.parcelGeoJson);
                  const geoJson =
                    typeof property.parcelGeoJson === "string"
                      ? JSON.parse(property.parcelGeoJson)
                      : property.parcelGeoJson;

                  if (geoJson.coordinates?.[0]?.[0]) {
                    const [longitude, latitude] = geoJson.coordinates[0][0];
                    console.log("Officer extracted coordinates:", { longitude, latitude });
                    if (latitude && longitude && !isNaN(latitude) && !isNaN(longitude)) {
                      validCoordinates.push([Number(latitude), Number(longitude)]);
                      console.log("Officer added valid coordinate:", [Number(latitude), Number(longitude)]);
                    }
                  }
                } catch (error) {
                  console.error("Error parsing coordinates for property:", property.id || "unknown", error);
                }
              } else {
                console.log("Officer no coordinates found for property:", property.id || "unknown");
              }
            });

            if (validCoordinates.length > 0) {
              const avgLat = validCoordinates.reduce((sum, coord) => sum + coord[0], 0) / validCoordinates.length;
              const avgLng = validCoordinates.reduce((sum, coord) => sum + coord[1], 0) / validCoordinates.length;
              const dynamicZoom = calculateDynamicZoom(validCoordinates);

              console.log("Calculated map center for officer:", [avgLat, avgLng]);
              console.log("Calculated dynamic zoom for officer:", dynamicZoom);
              console.log("Valid coordinates found for officer:", validCoordinates.length);
              console.log("Sample coordinates for officer:", validCoordinates.slice(0, 3));

              setOfficerMapCenter([avgLat, avgLng]);
              setOfficerMapZoom(dynamicZoom);
            } else {
              console.log("No valid coordinates found for officer properties");
              setOfficerMapZoom(14); // Default zoom when no coordinates
            }

            // Get payments for each property and sum up today's and this month's collection
            let todaysTaxCollection = 0;
            let monthlyTaxCollection = 0;
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            for (const property of propertiesData) {
              try {
                // Skip properties without valid IDs
                if (!property.id) {
                  console.warn("Property without ID found, skipping payment history fetch:", property);
                  continue;
                }

                const paymentHistory = await paymentService.getPaymentHistory(property.id);

                // Process payments for today's collection
                const todayPayments = paymentHistory.payments.filter(payment => {
                  const paymentDate = new Date(payment.date);
                  return paymentDate >= today && paymentDate < new Date(today.getTime() + 24 * 60 * 60 * 1000);
                });

                // Process payments for this month's collection
                const monthlyPayments = paymentHistory.payments.filter(payment => {
                  const paymentDate = new Date(payment.date);
                  return paymentDate >= firstDayOfMonth && paymentDate <= today;
                });

                todayPayments.forEach(payment => {
                  todaysTaxCollection += payment.amount;
                });

                monthlyPayments.forEach(payment => {
                  monthlyTaxCollection += payment.amount;
                });
              } catch (error) {
                console.error(`Error fetching payments for property ${property.id || "unknown"}:`, error);
              }
            }

            setStats({
              totalProperties: propertiesData.length,
              pendingReviews: properties.length,
              totalAssessments: 0,
              outstandingTax: 0,
              totalTaxPaid,
              totalTaxpayers: taxpayers.length,
              todaysTaxCollection,
              monthlyTaxCollection,
            });
          }
        } else if (user?.role === "CentralAdmin") {
          // Fetch admin dashboard data

          setStats({
            totalProperties: 0, // TODO: Get total properties across all municipalities
            pendingReviews: 0,
            totalAssessments: 0,
            outstandingTax: 0,
            totalTaxPaid: 0,
          });
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load dashboard data");
        console.error("Dashboard error:", err);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  // Handle fiscal year change
  const handleFiscalYearChange = async (fiscalYearId: string) => {
    setSelectedFiscalYear(fiscalYearId);

    if (fiscalYearId && user?.role === "Officer" && user?.municipalityId) {
      try {
        // Reload properties for the selected fiscal year
        const propertiesResponse = await api.get(`/properties/municipality/${user.municipalityId}?fiscalYearId=${fiscalYearId}`);
        const properties = propertiesResponse.data;

        setOfficerProperties(properties);

        // Update map center and zoom based on properties
        if (properties.length > 0) {
          const validCoords = properties
            .map((p: Property) => {
              try {
                const geoJson = typeof p.parcelGeoJson === "string" ? JSON.parse(p.parcelGeoJson) : p.parcelGeoJson;
                const coordinates = geoJson?.coordinates?.[0]?.[0];
                if (Array.isArray(coordinates) && coordinates.length >= 2) {
                  return [parseFloat(coordinates[1]), parseFloat(coordinates[0])];
                }
              } catch (error) {
                console.error("Error parsing coordinates:", error);
              }
              return null;
            })
            .filter((coord: [number, number] | null): coord is [number, number] => coord !== null);

          if (validCoords.length > 0) {
            const avgLat =
              validCoords.reduce((sum: number, coord: [number, number]) => sum + coord[0], 0) / validCoords.length;
            const avgLng =
              validCoords.reduce((sum: number, coord: [number, number]) => sum + coord[1], 0) / validCoords.length;
            setOfficerMapCenter([avgLat, avgLng]);
            setOfficerMapZoom(13);
          }
        }
      } catch (error) {
        console.error("Error loading properties for fiscal year:", error);
      }
    }
  };

  // Fetch municipality name for officers
  useEffect(() => {
    const fetchMunicipalityName = async () => {
      if (user?.municipalityId) {
        try {
          const municipality = await municipalityService.getById(user.municipalityId);
          setMunicipalityName(municipality.name);
        } catch (err) {
          console.error("Error fetching municipality name:", err);
          setMunicipalityName("Unknown Municipality");
        }
      }
    };

    fetchMunicipalityName();
  }, [user?.municipalityId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="loading loading-spinner loading-lg text-blue-500"></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="stroke-current shrink-0 h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span>{error}</span>
      </div>
    );
  }

  const renderCitizenDashboard = () => {
    // Define stat items
    const statItems = [
      {
        title: "Total Properties",
        value: stats.totalProperties,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        ),
        color: "primary" as const,
        trend: {
          value: 12,
          isUpward: true,
        },
      },
      {
        title: "Pending Reviews",
        value: stats.pendingReviews,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        ),
        color: "warning" as const,
      },
      {
        title: "Assessments",
        value: stats.totalAssessments,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        ),
        color: "success" as const,
      },
      {
        title: "Outstanding Tax",
        value: `NPR ${stats.outstandingTax.toLocaleString()}`,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
        ),
        color: "error" as const,
      },
      {
        title: "Total Tax Paid",
        value: `NPR ${stats.totalTaxPaid.toLocaleString()}`,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
            />
          </svg>
        ),
        color: "success" as const,
      },
    ];

    // Define quick actions
    const quickActions = [
      {
        title: "Register Property",
        description: "Add a new property",
        to: "/properties/register",
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        ),
        color: "primary" as const,
      },
      {
        title: "My Properties",
        description: "View your properties",
        to: "/properties",
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        ),
        color: "secondary" as const,
        badge: {
          text: stats.totalProperties.toString(),
          color: "secondary" as const,
        },
      },
      {
        title: "Pay Taxes",
        description: "Make tax payments",
        to: "/payments",
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
        ),
        color: "success" as const,
      },
    ];

    return (
      <div className="space-y-6">
        {/* Welcome Card */}
        <AdminSummaryCard
          title={`Welcome back, ${user?.fullName}!`}
          description="Manage your properties and tax payments from your dashboard."
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
              />
            </svg>
          }
          color="primary"
        />

        {/* Stats Section */}
        <AdminStats items={statItems} />

        {/* Properties Map */}
        {properties.length > 0 && (
          <div className="card bg-base-100 shadow-xl mb-6">
            <div className="card-body">
              <h2 className="card-title flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                My Properties Map
                <div className="badge badge-primary">{properties.length}</div>
              </h2>
              <p className="text-base-content/70 mb-4">
                Interactive map showing all your registered properties with status indicators
              </p>
              <div className="h-96 w-full">
                <PropertiesMap
                  key={`${mapCenter[0]}-${mapCenter[1]}-${mapZoom}`}
                  properties={properties}
                  height="100%"
                  width="100%"
                  zoom={mapZoom}
                  center={mapCenter}
                />
              </div>
              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-base-content/60">
                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-3 border-2 border-green-500 bg-green-500 bg-opacity-30"></div>
                      <span>Approved Parcels ({properties.filter(p => p.status === "Approved").length})</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-3 border-2 border-yellow-500 bg-yellow-500 bg-opacity-30"></div>
                      <span>Pending Review ({properties.filter(p => p.status === "PendingReview").length})</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-3 border-2 border-red-500 bg-red-500 bg-opacity-30"></div>
                      <span>Rejected Parcels ({properties.filter(p => p.status === "Rejected").length})</span>
                    </div>
                  </div>
                </div>
                <button className="btn btn-primary btn-sm" onClick={() => (window.location.href = "/properties")}>
                  View All Properties
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <AdminActionCard title="Quick Actions" actions={quickActions} />
      </div>
    );
  };

  const renderOfficerDashboard = () => {
    // Define metric cards for officer dashboard
    const metricCards = [
      {
        title: "Total Taxpayers",
        value: stats.totalTaxpayers || 0,
        description: "Registered taxpayers",
        to: "/users/pending",
        color: "primary" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
        ),
      },
      {
        title: "Total Properties",
        value: stats.totalProperties || 0,
        description: "Registered properties",
        to: "/properties/review",
        color: "secondary" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        ),
      },
      {
        title: "Today's Tax Collection",
        value: `NPR ${(stats.todaysTaxCollection || 0).toLocaleString()}`,
        description: "Collected today",
        to: "/reports/outstanding-tax",
        color: "success" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        ),
      },
      {
        title: "This Month's Collection",
        value: `NPR ${(stats.monthlyTaxCollection || 0).toLocaleString()}`,
        description: `Collected in ${new Date().toLocaleString("default", { month: "long" })}`,
        to: "/reports/outstanding-tax",
        color: "info" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        ),
      },
      {
        title: "Property Reviews",
        value: stats.pendingReviews,
        description: "Pending reviews",
        to: "/properties/review",
        color: "warning" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
        ),
      },
      {
        title: "Tax Configuration",
        description: "Manage tax slabs and penalties",
        to: "/tax-config",
        color: "success" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
        ),
      },
      {
        title: "Assessment Management",
        description: "Manage property assessments",
        to: "/assessments",
        color: "primary" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
            />
          </svg>
        ),
      },
      {
        title: "User Approvals",
        description: "Verify pending user registrations",
        to: "/users/pending",
        color: "info" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
        ),
      },
    ] as const;

    // Define settings actions
    const settingsActions = [
      {
        title: "Fiscal Year Management",
        description: "Manage fiscal years for your municipality",
        to: "/fiscal-years",
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        ),
        color: "info" as const,
      },
      {
        title: "Appeals Management",
        description: "Manage citizen appeals",
        to: "/appeals",
        icon: (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        ),
        color: "secondary" as const,
      },
    ];

    return (
      <div className="space-y-6">
        {/* Welcome Card */}
        <AdminSummaryCard
          title="Officer Dashboard"
          description="Manage property reviews, tax configurations, and assessments for your municipality."
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
          }
          color="primary"
        >
          {user?.municipalityId && (
            <div className="mt-2 text-sm text-primary font-medium">
              Managing Municipality: {municipalityName || "Loading..."}
            </div>
          )}
        </AdminSummaryCard>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {metricCards.map((card, index) => (
            <AdminMetricCard
              key={index}
              title={card.title}
              value={"value" in card ? card.value : undefined}
              description={card.description}
              icon={card.icon}
              color={card.color}
              to={card.to}
            />
          ))}
        </div>

        {/* Fiscal Year Selection */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title text-primary">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              Fiscal Year Selection
            </h2>
            <p className="text-base-content/70 mb-4">Select a fiscal year to view property tax payment status</p>
            <div className="form-control w-full max-w-xs">
              <label className="label">
                <span className="label-text font-medium">Active Fiscal Year</span>
              </label>
              <select
                className="select select-bordered w-full max-w-xs"
                value={selectedFiscalYear}
                onChange={e => handleFiscalYearChange(e.target.value)}
              >
                <option value="">Select Fiscal Year</option>
                {fiscalYears.map(fy => (
                  <option key={fy.fiscalYearId} value={fy.fiscalYearId}>
                    {fy.name} {fy.isActive ? "(Active)" : ""}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Properties Map */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title text-primary">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
                />
              </svg>
              Municipality Properties Map
              <div className="badge badge-primary">{officerProperties.length}</div>
            </h2>
            <p className="text-base-content/70 mb-4">
              Interactive map showing all properties in your municipality with tax payment status
            </p>
            <div className="h-96 w-full">
              <PropertiesMap
                key={`officer-${officerMapCenter[0]}-${officerMapCenter[1]}-${officerMapZoom}-${selectedFiscalYear}`}
                properties={officerProperties}
                height="100%"
                width="100%"
                zoom={officerMapZoom}
                center={officerMapCenter}
                showTaxPaymentStatus={true}
              />
            </div>
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-base-content/60">
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-3 border-2 border-green-500 bg-green-500 bg-opacity-30"></div>
                    <span>
                      Tax Paid / No Tax Due (
                      {
                        officerProperties.filter(
                          p => p.ownershipType !== 'Government' && !(p.taxDue && p.taxDue > 0) && p.assessedValue && p.assessedValue > 0
                        ).length
                      }
                      )
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-3 border-2 border-yellow-500 bg-yellow-500 bg-opacity-30"></div>
                    <span>
                      Pending Assessment (
                      {officerProperties.filter(p => p.ownershipType !== 'Government' && !(p.assessedValue && p.assessedValue > 0)).length})
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-3 border-2 border-red-500 bg-red-500 bg-opacity-30"></div>
                    <span>Tax Unpaid ({officerProperties.filter(p => p.ownershipType !== 'Government' && p.taxDue && p.taxDue > 0).length})</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-3 border-2 border-gray-500 bg-gray-500 bg-opacity-30"></div>
                    <span>
                      Exempted (Government) (
                      {officerProperties.filter(p => p.ownershipType === 'Government').length})
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Settings Section */}
        <AdminActionCard title="Municipality Settings" actions={settingsActions} columns={2} />
      </div>
    );
  };

  const renderAdminDashboard = () => {
    // Define admin metric cards
    const adminMetricCards = [
      {
        title: "Municipality Management",
        description: "Manage municipalities and rules",
        to: "/municipalities",
        color: "secondary" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        ),
        badge: {
          text: "Admin",
          color: "secondary" as const,
        },
      },
      {
        title: "System Reports",
        description: "View system-wide analytics",
        to: "/reports",
        color: "info" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 00-2 2h-2a2 2 0 00-2-2z"
            />
          </svg>
        ),
      },
      {
        title: "User Management",
        description: "Manage system users",
        to: "/users",
        color: "primary" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        ),
      },
      {
        title: "Fiscal Year Management",
        description: "Manage fiscal years",
        to: "/fiscal-years",
        color: "success" as const,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        ),
      },
    ] as const;

    // Define system stats
    const systemStats = [
      {
        title: "Total Municipalities",
        value: 25,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
        ),
        color: "secondary" as const,
        trend: {
          value: 5,
          isUpward: true,
        },
      },
      {
        title: "Total Users",
        value: 1250,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
        ),
        color: "primary" as const,
        trend: {
          value: 12,
          isUpward: true,
        },
      },
      {
        title: "Total Properties",
        value: 5280,
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
        ),
        color: "accent" as const,
        trend: {
          value: 8,
          isUpward: true,
        },
      },
      {
        title: "Total Revenue",
        value: "NPR 12.5M",
        icon: (
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
            />
          </svg>
        ),
        color: "success" as const,
        trend: {
          value: 15,
          isUpward: true,
        },
      },
    ];

    return (
      <div className="space-y-6">
        {/* Welcome Card */}
        <AdminSummaryCard
          title="Central Admin Dashboard"
          description="Manage municipalities and oversee the entire system."
          icon={
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
          color="accent"
        />

        {/* System Stats */}
        <AdminStats items={systemStats} />

        {/* Admin Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {adminMetricCards.map((card, index) => (
            <AdminMetricCard
              key={index}
              title={card.title}
              description={card.description}
              icon={card.icon}
              color={card.color}
              to={card.to}
              badge={"badge" in card ? card.badge : undefined}
            />
          ))}
        </div>
      </div>
    );
  };

  const getDashboardSubtitle = () => {
    switch (user?.role) {
      case "Citizen":
        return "Welcome to your property management dashboard";
      case "Officer":
        return "Municipal officer dashboard";
      case "CentralAdmin":
        return "Central administration dashboard";
      default:
        return "Welcome to the Land Tax System";
    }
  };

  return (
    <AdminLayout title="Dashboard" subtitle={getDashboardSubtitle()} className="max-w-7xl mx-auto">
      {user?.role === "Citizen" && renderCitizenDashboard()}
      {user?.role === "Officer" && renderOfficerDashboard()}
      {user?.role === "CentralAdmin" && renderAdminDashboard()}
    </AdminLayout>
  );
};

export default Dashboard;
