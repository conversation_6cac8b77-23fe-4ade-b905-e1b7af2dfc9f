import React, { useState, useEffect, useCallback } from "react";

// Interface for key-value entries
export interface KeyValueEntry {
  id: string;
  key: string;
  value: string | number;
}

// Props interface for the component
export interface KeyValueTableProps {
  items?: KeyValueEntry[];
  onChange: (items: KeyValueEntry[]) => void;
  keyLabel?: string;
  valueLabel?: string;
  valueType?: "text" | "number";
  readOnly?: boolean;
  defaultEntries?: { key: string; value: string | number }[];
  allowedKeys?: string[];
  addButtonLabel?: string;
  removeButtonLabel?: string;
}

const KeyValueTable: React.FC<KeyValueTableProps> = ({
  items,
  onChange,
  keyLabel = "Key",
  valueLabel = "Value",
  valueType = "text",
  readOnly = false,
  defaultEntries = [],
  allowedKeys,
  addButtonLabel = "Add Row",
  removeButtonLabel = "Remove",
}) => {
  const [localItems, setLocalItems] = useState<KeyValueEntry[]>([]);

  // Generate unique ID for new entries
  const generateId = useCallback(() => {
    return `kvt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Initialize local state
  useEffect(() => {
    if (items && items.length > 0) {
      // Use provided items
      setLocalItems(items);
    } else if (defaultEntries.length > 0) {
      // Use default entries
      const initialItems = defaultEntries.map((entry) => ({
        id: generateId(),
        key: entry.key,
        value: entry.value,
      }));
      setLocalItems(initialItems);
    } else {
      // Empty state
      setLocalItems([]);
    }
  }, [items, defaultEntries, generateId]);

  // Notify parent component of changes
  const notifyChange = useCallback(
    (newItems: KeyValueEntry[]) => {
      onChange(newItems);
    },
    [onChange]
  );

  // Update local state and notify parent
  const updateItems = useCallback(
    (newItems: KeyValueEntry[]) => {
      setLocalItems(newItems);
      notifyChange(newItems);
    },
    [notifyChange]
  );
  return (
    <div className="card bg-base-100 shadow-sm">
      <div className="card-body p-0">
        <div className="overflow-x-auto">
          <table className="table table-zebra">
            <thead>
              <tr>
                <th>{keyLabel}</th>
                <th>{valueLabel}</th>
                {!readOnly && <th>Actions</th>}
              </tr>
            </thead>
            <tbody>
              {localItems.map((item, index) => (
                <tr key={item.id}>
                  <td>
                    {/* Key input/display */}
                    {readOnly ? (
                      <span>{item.key}</span>
                    ) : allowedKeys ? (
                      <select
                        value={item.key}
                        onChange={(e) => {
                          const newItems = [...localItems];
                          newItems[index] = { ...item, key: e.target.value };
                          updateItems(newItems);
                        }}
                        className="select select-bordered select-sm w-full"
                      >
                        <option value="">Select {keyLabel}</option>
                        {allowedKeys.map((key) => (
                          <option key={key} value={key}>
                            {key}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <input
                        type="text"
                        value={item.key}
                        onChange={(e) => {
                          const newItems = [...localItems];
                          newItems[index] = { ...item, key: e.target.value };
                          updateItems(newItems);
                        }}
                        className="input input-bordered input-sm w-full"
                        placeholder={`Enter ${keyLabel.toLowerCase()}`}
                      />
                    )}
                  </td>

                  <td>
                    {/* Value input/display */}
                    {readOnly ? (
                      <span>{item.value}</span>
                    ) : (
                      <input
                        type={valueType}
                        value={item.value}
                        onChange={(e) => {
                          let newValue: string | number = e.target.value;

                          // Handle numeric validation
                          if (valueType === "number") {
                            const numValue = parseFloat(e.target.value);
                            if (!isNaN(numValue) && numValue >= 0) {
                              newValue = numValue;
                            } else if (e.target.value === "") {
                              newValue = "";
                            } else {
                              // Invalid numeric input, don't update
                              return;
                            }
                          }

                          const newItems = [...localItems];
                          newItems[index] = { ...item, value: newValue };
                          updateItems(newItems);
                        }}
                        className="input input-bordered input-sm w-full"
                        placeholder={`Enter ${valueLabel.toLowerCase()}`}
                        min={valueType === "number" ? 0 : undefined}
                      />
                    )}
                  </td>

                  {!readOnly && (
                    <td>
                      <button
                        type="button"
                        onClick={() => {
                          const newItems = localItems.filter((_, i) => i !== index);
                          updateItems(newItems);
                        }}
                        className="btn btn-error btn-sm btn-circle"
                        title={removeButtonLabel}
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {!readOnly && (
          <div className="card-actions justify-start p-4">
            <button
              type="button"
              onClick={() => {
                const newItem: KeyValueEntry = {
                  id: generateId(),
                  key: "",
                  value: valueType === "number" ? 0 : "",
                };
                updateItems([...localItems, newItem]);
              }}
              className="btn btn-primary btn-sm"
            >
              + {addButtonLabel}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default KeyValueTable;
