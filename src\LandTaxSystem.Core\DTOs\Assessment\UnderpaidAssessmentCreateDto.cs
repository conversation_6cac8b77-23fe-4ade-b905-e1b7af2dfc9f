using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Assessment
{
    [Obsolete("Use AssessmentCreateDto instead - all assessments are now unified")]
    public class UnderpaidAssessmentCreateDto
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        public int AssessmentYear { get; set; }
        
        [Required]
        public Guid FiscalYearId { get; set; }
        
        [Required]
        public List<Guid> PaymentIds { get; set; } = new List<Guid>();
    }
}
