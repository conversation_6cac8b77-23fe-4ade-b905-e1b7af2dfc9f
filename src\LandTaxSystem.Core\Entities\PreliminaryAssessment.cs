using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class PreliminaryAssessment
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string TaxpayerRegistration { get; set; } = string.Empty;

        [Required]
        public Guid MunicipalityId { get; set; }

        public Guid? ReturnFilingId { get; set; }

        [Required]
        [MaxLength(200)]
        public string TaxpayerName { get; set; } = string.Empty;

        public string? Address { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(50)]
        public string? AccountNumber { get; set; }

        [Required]
        public DateTime AssessmentPeriodFrom { get; set; }

        [Required]
        public DateTime AssessmentPeriodTo { get; set; }

        [MaxLength(100)]
        public string? ActSection { get; set; }

        [MaxLength(100)]
        public string? Rule { get; set; }

        [MaxLength(100)]
        public string? Bank { get; set; }

        [MaxLength(100)]
        public string? Branch { get; set; }

        [MaxLength(500)]
        public string? ReasonForAssessment { get; set; }

        [MaxLength(50)]
        public string? AppealNumber { get; set; }

        [MaxLength(500)]
        public string? OtherReasonDescription { get; set; }

        public DateTime? InterestCalculationDate { get; set; }

        public DateTime? PreliminaryAssessmentDate { get; set; }

        public string? Reason { get; set; }

        public string? Regulations { get; set; }

        [EmailAddress]
        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(20)]
        public string? MobileNumber { get; set; }

        [MaxLength(50)]
        public string? AssessmentPeriod { get; set; }

        [MaxLength(20)]
        public string Status { get; set; } = "Draft";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? UpdatedAt { get; set; }

        public string? UpdatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<PreliminaryAssessmentDetail> TaxDetails { get; set; } = new List<PreliminaryAssessmentDetail>();

        [ForeignKey("MunicipalityId")]
        public virtual Municipality? Municipality { get; set; }

        [ForeignKey("ReturnFilingId")]
        public virtual ReturnFiling? ReturnFiling { get; set; }

        [ForeignKey("CreatedBy")]
        public virtual ApplicationUser? CreatedByUser { get; set; }

        [ForeignKey("UpdatedBy")]
        public virtual ApplicationUser? UpdatedByUser { get; set; }
    }
}
