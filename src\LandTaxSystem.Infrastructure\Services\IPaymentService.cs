using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Payment;

namespace LandTaxSystem.Infrastructure.Services
{
    public interface IPaymentService
    {
        /// <summary>
        /// Gets a list of properties eligible for assessment in a specific municipality
        /// </summary>
        /// <param name="municipalityId">The municipality ID to filter by</param>
        /// <returns>List of eligible properties with payment details</returns>
        Task<List<EligiblePropertyDto>> GetEligibleProperties(Guid municipalityId);

        /// <summary>
        /// Reconciles payments with an assessment
        /// </summary>
        /// <param name="paymentIds">List of payment IDs to reconcile</param>
        /// <param name="assessmentId">The assessment ID to link the payments to</param>
        Task ReconcilePayments(List<Guid> paymentIds, Guid assessmentId);
    }
}
