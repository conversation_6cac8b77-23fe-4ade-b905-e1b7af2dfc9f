import React, { type ReactNode } from 'react';

// Form field types
export type FieldType = 
  | 'text'
  | 'email'
  | 'password'
  | 'number'
  | 'date'
  | 'textarea'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'custom';

// Option type for select, radio, etc.
export interface FieldOption {
  value: string | number | boolean;
  label: string;
}

// Base field properties
export interface BaseFieldProps {
  name: string;
  label: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  helpText?: string;
  error?: string;
  labelClassName?: string;
  containerClassName?: string;
}

// Input field properties
export interface InputFieldProps extends BaseFieldProps {
  type: Exclude<FieldType, 'select' | 'textarea' | 'checkbox' | 'radio' | 'custom'>;
  placeholder?: string;
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  min?: number | string;
  max?: number | string;
  step?: number | string;
  autoComplete?: string;
}

// Textarea field properties
export interface TextareaFieldProps extends BaseFieldProps {
  type: 'textarea';
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  rows?: number;
}

// Select field properties
export interface SelectFieldProps extends BaseFieldProps {
  type: 'select';
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  options: FieldOption[];
  placeholder?: string;
}

// Checkbox field properties
export interface CheckboxFieldProps extends BaseFieldProps {
  type: 'checkbox';
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

// Radio field properties
export interface RadioFieldProps extends BaseFieldProps {
  type: 'radio';
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  options: FieldOption[];
}

// Custom field properties
export interface CustomFieldProps extends BaseFieldProps {
  type: 'custom';
  render: () => ReactNode;
}

// Union type for all field props
export type FieldProps = 
  | InputFieldProps
  | TextareaFieldProps
  | SelectFieldProps
  | CheckboxFieldProps
  | RadioFieldProps
  | CustomFieldProps;

// Form Field component
export const FormField: React.FC<FieldProps> = (props) => {
  const { 
    name, 
    label, 
    required, 
    disabled, 
    className = '', 
    helpText, 
    error,
    labelClassName = '',
    containerClassName = ''
  } = props;

  // Common input classes
  const inputClasses = `${className} ${error ? 'input-error' : ''}`;
  
  // Common label element
  const labelElement = (
    <label htmlFor={name} className={`label ${labelClassName}`}>
      <span className="label-text font-medium">
        {label}
        {required && <span className="text-error ml-1">*</span>}
      </span>
    </label>
  );

  // Error message element
  const errorElement = error && (
    <label className="label">
      <span className="label-text-alt text-error">{error}</span>
    </label>
  );

  // Help text element
  const helpTextElement = helpText && !error && (
    <label className="label">
      <span className="label-text-alt">{helpText}</span>
    </label>
  );

  // Render based on field type
  const renderField = () => {
    switch (props.type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'date':
        return (
          <>
            {labelElement}
            <input
              id={name}
              name={name}
              type={props.type}
              value={props.value}
              onChange={props.onChange}
              placeholder={props.placeholder}
              required={required}
              disabled={disabled}
              className={`input input-bordered w-full ${inputClasses}`}
              min={props.min}
              max={props.max}
              step={props.step}
              autoComplete={props.autoComplete}
            />
            {errorElement || helpTextElement}
          </>
        );

      case 'textarea':
        return (
          <>
            {labelElement}
            <textarea
              id={name}
              name={name}
              value={props.value}
              onChange={props.onChange}
              placeholder={props.placeholder}
              required={required}
              disabled={disabled}
              className={`textarea textarea-bordered w-full ${inputClasses}`}
              rows={props.rows || 3}
            />
            {errorElement || helpTextElement}
          </>
        );

      case 'select':
        return (
          <>
            {labelElement}
            <select
              id={name}
              name={name}
              value={props.value}
              onChange={props.onChange}
              required={required}
              disabled={disabled}
              className={`select select-bordered w-full ${inputClasses}`}
            >
              {props.placeholder && (
                <option value="" disabled>
                  {props.placeholder}
                </option>
              )}
              {props.options.map((option) => (
                <option key={String(option.value)} value={String(option.value)}>
                  {option.label}
                </option>
              ))}
            </select>
            {errorElement || helpTextElement}
          </>
        );

      case 'checkbox':
        return (
          <div className="flex items-center">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                id={name}
                name={name}
                type="checkbox"
                checked={props.checked}
                onChange={props.onChange}
                disabled={disabled}
                className={`checkbox ${inputClasses}`}
              />
              <span className="label-text">{label}</span>
            </label>
            {errorElement}
          </div>
        );

      case 'radio':
        return (
          <>
            {labelElement}
            <div className="flex flex-col gap-2">
              {props.options.map((option) => (
                <label key={String(option.value)} className="label cursor-pointer justify-start gap-4">
                  <input
                    type="radio"
                    name={name}
                    value={String(option.value)}
                    checked={props.value === option.value}
                    onChange={props.onChange}
                    disabled={disabled}
                    className={`radio ${inputClasses}`}
                  />
                  <span className="label-text">{option.label}</span>
                </label>
              ))}
            </div>
            {errorElement || helpTextElement}
          </>
        );

      case 'custom':
        return (
          <>
            {labelElement}
            {props.render()}
            {errorElement || helpTextElement}
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`form-control w-full ${containerClassName}`}>
      {renderField()}
    </div>
  );
};

// Form component
interface AdminFormProps {
  children: ReactNode;
  onSubmit: (e: React.FormEvent) => void;
  className?: string;
}

const AdminForm: React.FC<AdminFormProps> = ({ 
  children, 
  onSubmit, 
  className = '' 
}) => {
  return (
    <form onSubmit={onSubmit} className={className}>
      {children}
    </form>
  );
};

export default AdminForm;