import React, { useState, useEffect, useCallback } from "react";
import { paymentService } from "../services/api";
import type { Payment } from "../types";

interface TaxPaymentHistoryProps {
  propertyId: string;
}

const TaxPaymentHistory: React.FC<TaxPaymentHistoryProps> = ({ propertyId }) => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  const loadPaymentHistory = useCallback(async () => {
    try {
      setLoading(true);
      setError("");
      // Use the payment service to get payment history with the correct endpoint
      const response = await paymentService.getPaymentHistory(propertyId);
      setPayments((response.payments || []) as unknown as Payment[]);
    } catch (error) {
      console.error("Failed to load payment history:", error);
      
      // For demo, use mock data
      const mockPayments: Payment[] = [
        {
          id: "1",
          assessmentId: "101",
          amountPaid: 75000,
          paymentDate: "2024-05-15T09:30:00Z",
          paymentGateway: "Card Payment",
          transactionId: "TXN_1621234567",
          status: "Success"
        },
        {
          id: "2",
          assessmentId: "102",
          amountPaid: 82500,
          paymentDate: "2023-05-20T14:45:00Z",
          paymentGateway: "Bank Transfer",
          transactionId: "TXN_1621234890",
          status: "Success"
        }
      ];
      setPayments(mockPayments);
    } finally {
      setLoading(false);
    }
  }, [propertyId]);  // Dependency array for useCallback
  
  useEffect(() => {
    loadPaymentHistory();
  }, [loadPaymentHistory]);

  const formatCurrency = (amount: number) => {
    return `NPR ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-6">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-error">
        <span>{error}</span>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className="alert alert-info">
        <span>No payment records found for this property.</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Payment History</h3>
      <div className="overflow-x-auto">
        <table className="table table-zebra">
          <thead>
            <tr>
              <th>Date</th>
              <th>Amount</th>
              <th>Payment Method</th>
              <th>Transaction ID</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {payments.map((payment) => (
              <tr key={payment.id}>
                <td>{formatDate(payment.paymentDate)}</td>
                <td className="font-medium">{formatCurrency(payment.amountPaid)}</td>
                <td className="text-base-content/70">{payment.paymentGateway}</td>
                <td className="text-base-content/70">{payment.transactionId}</td>
                <td>
                  <div className={`badge ${
                    payment.status === "Success" 
                      ? "badge-success" 
                      : "badge-error"
                  }`}>
                    {payment.status}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TaxPaymentHistory;
