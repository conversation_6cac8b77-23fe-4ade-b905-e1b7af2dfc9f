import React, { useState, useEffect } from "react";
import { fiscalYearService } from "../services/fiscalYearService";
import type { FiscalYear } from "../services/taxConfigService";
import {
  AdminLayout,
  AdminTabs,
  AdminBreadcrumb,
} from "../components/admin";

interface FiscalYearForm {
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
}

const FiscalYearManagement: React.FC = () => {
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingFiscalYear, setEditingFiscalYear] = useState<FiscalYear | null>(
    null
  );
  const [activeTab, setActiveTab] = useState<
    "all" | "active" | "archived" | "timeline"
  >("all");

  // Form state
  const [fiscalYearForm, setFiscalYearForm] = useState<FiscalYearForm>({
    name: "",
    startDate: "",
    endDate: "",
    isActive: false,
  });

  useEffect(() => {
    loadFiscalYears();
  }, []);

  const loadFiscalYears = async () => {
    try {
      setLoading(true);
      setError("");

      const years = await fiscalYearService.getAll();
      setFiscalYears(years);
    } catch (error) {
      console.error("Failed to load fiscal years:", error);
      setError("Failed to load fiscal years. Please try again.");
      setFiscalYears([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFiscalYear = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");

    try {
      // Default dates if not provided
      const currentDate = new Date().toISOString().split("T")[0];
      const nextYearDate = new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      )
        .toISOString()
        .split("T")[0];

      console.log("Creating fiscal year with data:", {
        name: fiscalYearForm.name,
        startDate: fiscalYearForm.startDate || currentDate,
        endDate: fiscalYearForm.endDate || nextYearDate,
        isActive: fiscalYearForm.isActive,
      });

      const result = await fiscalYearService.create({
        name: fiscalYearForm.name,
        startDate: fiscalYearForm.startDate || currentDate,
        endDate: fiscalYearForm.endDate || nextYearDate,
        isActive: fiscalYearForm.isActive,
      });

      console.log("Create fiscal year response:", result);
      setSuccess("Fiscal year created successfully!");
      setShowCreateForm(false);
      resetForm();
      loadFiscalYears();
    } catch (err: unknown) {
      console.error("Error creating fiscal year:", err);
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setError(`Failed to create fiscal year: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateFiscalYear = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingFiscalYear) return;

    try {
      setLoading(true);
      setError("");
      setSuccess("");

      // Only include fields that have values
      const updateData: {
        name: string;
        isActive: boolean;
        startDate?: string;
        endDate?: string;
      } = {
        name: fiscalYearForm.name,
        isActive: fiscalYearForm.isActive,
      };

      if (fiscalYearForm.startDate) {
        updateData.startDate = fiscalYearForm.startDate;
      }

      if (fiscalYearForm.endDate) {
        updateData.endDate = fiscalYearForm.endDate;
      }

      console.log("Updating fiscal year with data:", updateData);

      await fiscalYearService.update(
        editingFiscalYear.fiscalYearId,
        updateData
      );
      setSuccess("Fiscal year updated successfully!");
      setEditingFiscalYear(null);
      resetForm();
      loadFiscalYears();
    } catch (err: unknown) {
      console.error("Failed to update fiscal year:", err);
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setError(`Failed to update fiscal year: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSetActive = async (id: string) => {
    try {
      setError("");
      setSuccess("");

      await fiscalYearService.setActive(id);
      setSuccess("Fiscal year set as active successfully!");
      loadFiscalYears();
    } catch (error) {
      console.error("Failed to set fiscal year as active:", error);
      setError("Failed to set fiscal year as active. Please try again.");
    }
  };

  const resetForm = () => {
    setFiscalYearForm({
      name: "",
      startDate: "",
      endDate: "",
      isActive: false,
    });
  };

  const startEdit = (fiscalYear: FiscalYear) => {
    setEditingFiscalYear(fiscalYear);
    setFiscalYearForm({
      name: fiscalYear.name,
      startDate: fiscalYear.startDate.split("T")[0], // Format date for input field
      endDate: fiscalYear.endDate.split("T")[0], // Format date for input field
      isActive: fiscalYear.isActive,
    });
  };

  const cancelEdit = () => {
    setEditingFiscalYear(null);
    setShowCreateForm(false);
    resetForm();
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFiscalYearForm({
      ...fiscalYearForm,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    });
  };

  // Convert English date to Nepali date format (YYYY-MM-DD)
  const toNepaliDate = (englishDate: string) => {
    // This is a placeholder. In a real implementation, you would use a Nepali date conversion library
    // For now, we'll just return the English date as is
    return englishDate;
  };

  // Note: In a real implementation, we would also need a function to convert
  // Nepali dates to English dates for API communication

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <AdminLayout title="Fiscal Year Management">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4 text-base-content/70">Loading fiscal years...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Fiscal Year Management"
      subtitle="Manage Nepali fiscal years for the tax system"
      className="max-w-7xl mx-auto"
    >
      <>
        {/* Breadcrumbs */}
        <AdminBreadcrumb
          items={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Fiscal Year Management" },
          ]}
          className="mb-4"
        />

        {/* Success/Error Messages */}
        {success && (
          <div className="alert alert-success mb-6">
            <span>{success}</span>
          </div>
        )}
        {error && (
          <div className="alert alert-error mb-6">
            <span>{error}</span>
          </div>
        )}

        {/* Tabs */}
        <AdminTabs
          tabs={[
            {
              key: "all",
              label: "All Fiscal Years",
              count: fiscalYears.length,
            },
            {
              key: "active",
              label: "Active",
              count: fiscalYears.filter((fy) => fy.isActive).length,
            },
            {
              key: "archived",
              label: "Archived",
              count: fiscalYears.filter((fy) => !fy.isActive).length,
            },
            { key: "timeline", label: "Timeline" },
          ]}
          activeTab={activeTab}
          onTabChange={(tabKey) =>
            setActiveTab(tabKey as "all" | "active" | "archived" | "timeline")
          }
          className="mb-6"
        />

        <div className="flex justify-end mb-8">
          <button
            onClick={() => setShowCreateForm(true)}
            className="btn btn-primary"
          >
            + Create Fiscal Year
          </button>
        </div>

        {/* Tab Content */}
        {(activeTab === "all" ||
          activeTab === "active" ||
          activeTab === "archived") && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <div className="overflow-x-auto">
                <table className="table table-zebra">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Start Date</th>
                      <th>End Date</th>
                      <th>Status</th>
                      <th className="text-right">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {fiscalYears
                      .filter((year) => {
                        if (activeTab === "all") return true;
                        if (activeTab === "active") return year.isActive;
                        if (activeTab === "archived") return !year.isActive;
                        return true;
                      })
                      .map((fiscalYear) => (
                        <tr key={fiscalYear.fiscalYearId}>
                          <td>
                            <div className="font-medium">{fiscalYear.name}</div>
                          </td>
                          <td>
                            <div className="text-sm">
                              {formatDate(fiscalYear.startDate)}
                            </div>
                            <div className="text-xs text-base-content/50">
                              {toNepaliDate(fiscalYear.startDate.split("T")[0])}{" "}
                              (Nepali)
                            </div>
                          </td>
                          <td>
                            <div className="text-sm">
                              {formatDate(fiscalYear.endDate)}
                            </div>
                            <div className="text-xs text-base-content/50">
                              {toNepaliDate(fiscalYear.endDate.split("T")[0])}{" "}
                              (Nepali)
                            </div>
                          </td>
                          <td>
                            <div
                              className={`badge ${
                                fiscalYear.isActive
                                  ? "badge-success"
                                  : "badge-ghost"
                              }`}
                            >
                              {fiscalYear.isActive ? "Active" : "Inactive"}
                            </div>
                          </td>
                          <td>
                            <button
                              onClick={() => startEdit(fiscalYear)}
                              className="btn btn-ghost btn-sm mr-2"
                            >
                              Edit
                            </button>
                            {!fiscalYear.isActive && (
                              <button
                                onClick={() =>
                                  handleSetActive(fiscalYear.fiscalYearId)
                                }
                                className="btn btn-success btn-sm"
                              >
                                Set Active
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {fiscalYears.length === 0 && activeTab !== "timeline" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center py-12">
              <h3 className="card-title justify-center mb-2">
                No fiscal years found
              </h3>
              <p className="text-base-content/70 mb-6">
                Create your first fiscal year to get started.
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="btn btn-primary"
              >
                Create Fiscal Year
              </button>
            </div>
          </div>
        )}

        {/* Timeline View */}
        {activeTab === "timeline" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title mb-6">Fiscal Year Timeline</h2>

              {fiscalYears.length > 0 ? (
                <ul className="timeline timeline-vertical">
                  {fiscalYears
                    .sort(
                      (a, b) =>
                        new Date(a.startDate).getTime() -
                        new Date(b.startDate).getTime()
                    )
                    .map((year, index) => (
                      <li key={year.fiscalYearId}>
                        <div
                          className={`timeline-start ${
                            year.isActive ? "text-success font-bold" : ""
                          }`}
                        >
                          {year.name}
                        </div>
                        <div
                          className={`timeline-middle ${
                            year.isActive ? "text-success" : ""
                          }`}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            className="w-5 h-5"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="timeline-end timeline-box">
                          <div className="font-medium">
                            {formatDate(year.startDate)} to{" "}
                            {formatDate(year.endDate)}
                          </div>
                          <div className="text-sm text-base-content/70 mt-1">
                            {year.isActive ? "Current active fiscal year" : ""}
                          </div>
                        </div>
                        {index < fiscalYears.length - 1 && <hr />}
                      </li>
                    ))}
                </ul>
              ) : (
                <div className="text-center py-12">
                  <p className="text-base-content/70 mb-6">
                    No fiscal years available to display in timeline.
                  </p>
                  <button
                    onClick={() => setShowCreateForm(true)}
                    className="btn btn-primary"
                  >
                    Create Fiscal Year
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Create/Edit Fiscal Year Modal */}
        {(showCreateForm || editingFiscalYear) && (
          <div className="modal modal-open">
            <div className="modal-box">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-bold text-lg">
                  {editingFiscalYear
                    ? `Edit Fiscal Year: ${editingFiscalYear.name}`
                    : "Create New Fiscal Year"}
                </h3>
                <button
                  onClick={cancelEdit}
                  className="btn btn-ghost btn-sm btn-circle"
                >
                  <span className="text-xl">&times;</span>
                </button>
              </div>

              <form
                onSubmit={
                  editingFiscalYear
                    ? handleUpdateFiscalYear
                    : handleCreateFiscalYear
                }
              >
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text">Fiscal Year Name*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    placeholder="e.g., 2080/81"
                    value={fiscalYearForm.name}
                    onChange={handleInputChange}
                    className="input input-bordered w-full"
                    required
                  />
                </div>

                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text">Start Date (Nepali)*</span>
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={fiscalYearForm.startDate}
                    onChange={handleInputChange}
                    className="input input-bordered w-full"
                  />
                  <label className="label">
                    <span className="label-text-alt">
                      Format: YYYY-MM-DD (e.g., 2080-04-01)
                    </span>
                  </label>
                </div>

                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text">End Date (Nepali)*</span>
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    value={fiscalYearForm.endDate}
                    onChange={handleInputChange}
                    className="input input-bordered w-full"
                  />
                  <label className="label">
                    <span className="label-text-alt">
                      Format: YYYY-MM-DD (e.g., 2081-03-31)
                    </span>
                  </label>
                </div>

                <div className="form-control mb-4">
                  <label className="label cursor-pointer">
                    <span className="label-text">
                      Set as active fiscal year
                    </span>
                    <input
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      checked={fiscalYearForm.isActive}
                      onChange={handleInputChange}
                      className="checkbox checkbox-primary"
                    />
                  </label>
                  <label className="label">
                    <span className="label-text-alt">
                      Note: Setting this fiscal year as active will deactivate
                      any currently active fiscal year.
                    </span>
                  </label>
                </div>

                <div className="modal-action">
                  <button
                    type="button"
                    onClick={cancelEdit}
                    className="btn btn-ghost"
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    {editingFiscalYear ? "Update" : "Create"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </>
    </AdminLayout>
  );
};

export default FiscalYearManagement;
