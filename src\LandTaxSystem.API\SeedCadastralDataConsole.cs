using System;
using System.IO;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Core.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;

namespace LandTaxSystem.API
{
    /// <summary>
    /// Console application to seed cadastral data from GeoJSON file
    /// Usage: dotnet run --project SeedCadastralDataConsole.cs [path-to-geojson]
    /// </summary>
    public class SeedCadastralDataConsole
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== Land Tax System - Cadastral Data Seeder ===");
            Console.WriteLine();
            
            // Default GeoJSON path
            var geoJsonPath = args.Length > 0 
                ? args[0] 
                : "/Users/<USER>/Downloads/land-tax-system/gis/simulated_NeLIS_cadastral_data.geojson";
                
            if (!File.Exists(geoJsonPath))
            {
                Console.WriteLine($"❌ GeoJSON file not found: {geoJsonPath}");
                Console.WriteLine("Please provide a valid path to the GeoJSON file.");
                Console.WriteLine("Usage: dotnet run [path-to-geojson-file]");
                return;
            }
            
            Console.WriteLine($"📁 GeoJSON file: {geoJsonPath}");
            Console.WriteLine();
            
            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .AddJsonFile("appsettings.Development.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();
                
                // Setup services
                var services = new ServiceCollection();
                
                // Add database context
                var connectionString = configuration.GetConnectionString("DefaultConnection") 
                    ?? "Host=localhost;Database=land_tax_system;Username=********;Password=********";
                    
                Console.WriteLine($"🔗 Database: {connectionString.Split(';')[0]}");
                
                services.AddDbContext<ApplicationDbContext>(options =>
                    options.UseNpgsql(
                        connectionString,
                        npgsqlOptions => npgsqlOptions.UseNetTopologySuite()));
                
                // Add Identity services
                services.AddIdentityCore<ApplicationUser>(options =>
                    {
                        options.Password.RequireDigit = true;
                        options.Password.RequireLowercase = true;
                        options.Password.RequireUppercase = true;
                        options.Password.RequireNonAlphanumeric = true;
                        options.Password.RequiredLength = 8;
                    })
                    .AddRoles<IdentityRole>()
                    .AddEntityFrameworkStores<ApplicationDbContext>();
                
                // Build service provider
                var serviceProvider = services.BuildServiceProvider();
                
                Console.WriteLine("🚀 Starting cadastral data seeding...");
                Console.WriteLine();
                
                // Ensure database exists and is migrated
                using (var scope = serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    
                    Console.WriteLine("📊 Ensuring database is created and migrated...");
                    await dbContext.Database.EnsureCreatedAsync();
                    await dbContext.Database.MigrateAsync();
                }
                
                // Seed cadastral data
                await SeedCadastralData.SeedFromGeoJsonAsync(serviceProvider, geoJsonPath);
                
                Console.WriteLine();
                Console.WriteLine("✅ Cadastral data seeding completed successfully!");
                Console.WriteLine();
                
                // Display summary
                using (var scope = serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    
                    var totalProperties = await dbContext.Properties.CountAsync();
                    var cadastralProperties = await dbContext.Properties
                        .Where(p => p.SheetNo == "1000")
                        .CountAsync();
                    var totalAssessments = await dbContext.Assessments.CountAsync();
                    var totalPayments = await dbContext.Payments.CountAsync();
                    var paidProperties = await dbContext.Assessments
                        .Where(a => a.PaymentStatus == "Paid")
                        .CountAsync();
                    var partiallyPaidProperties = await dbContext.Assessments
                        .Where(a => a.PaymentStatus == "Partial")
                        .CountAsync();
                    var unpaidProperties = await dbContext.Assessments
                        .Where(a => a.PaymentStatus == "Pending")
                        .CountAsync();
                    
                    Console.WriteLine("📈 Database Summary:");
                    Console.WriteLine($"   • Total Properties: {totalProperties}");
                    Console.WriteLine($"   • Cadastral Properties (from GeoJSON): {cadastralProperties}");
                    Console.WriteLine($"   • Total Assessments: {totalAssessments}");
                    Console.WriteLine($"   • Total Payments: {totalPayments}");
                    Console.WriteLine($"   • Fully Paid Properties: {paidProperties}");
                    Console.WriteLine($"   • Partially Paid Properties: {partiallyPaidProperties}");
                    Console.WriteLine($"   • Unpaid Properties: {unpaidProperties}");
                }
                
                Console.WriteLine();
                Console.WriteLine("🎉 All done! The cadastral data has been successfully seeded into the database.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error occurred during seeding: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }
        }
    }
}