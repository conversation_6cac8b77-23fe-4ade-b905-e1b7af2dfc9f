using System.ComponentModel;

namespace LandTaxSystem.Core.Enums
{
    public enum PreliminaryAssessmentStatus
    {
        [Description("Draft")]
        Draft = 0,
        
        [Description("Pending Review")]
        PendingReview = 1,
        
        [Description("Under Review")]
        UnderReview = 2,
        
        [Description("Approved")]
        Approved = 3,
        
        [Description("Rejected")]
        Rejected = 4,
        
        [Description("Finalized")]
        Finalized = 5,
        
        [Description("Cancelled")]
        Cancelled = 6
    }
    
    public static class PreliminaryAssessmentStatusExtensions
    {
        public static string GetDescription(this PreliminaryAssessmentStatus status)
        {
            var field = status.GetType().GetField(status.ToString());
            var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field!, typeof(DescriptionAttribute));
            return attribute?.Description ?? status.ToString();
        }
        
        public static bool CanTransitionTo(this PreliminaryAssessmentStatus currentStatus, PreliminaryAssessmentStatus newStatus)
        {
            return currentStatus switch
            {
                PreliminaryAssessmentStatus.Draft => newStatus is PreliminaryAssessmentStatus.PendingReview or PreliminaryAssessmentStatus.Cancelled,
                PreliminaryAssessmentStatus.PendingReview => newStatus is PreliminaryAssessmentStatus.UnderReview or PreliminaryAssessmentStatus.Draft or PreliminaryAssessmentStatus.Cancelled,
                PreliminaryAssessmentStatus.UnderReview => newStatus is PreliminaryAssessmentStatus.Approved or PreliminaryAssessmentStatus.Rejected or PreliminaryAssessmentStatus.PendingReview,
                PreliminaryAssessmentStatus.Approved => newStatus is PreliminaryAssessmentStatus.Finalized or PreliminaryAssessmentStatus.UnderReview,
                PreliminaryAssessmentStatus.Rejected => newStatus is PreliminaryAssessmentStatus.Draft or PreliminaryAssessmentStatus.UnderReview,
                PreliminaryAssessmentStatus.Finalized => false, // Final state
                PreliminaryAssessmentStatus.Cancelled => false, // Final state
                _ => false
            };
        }
        
        public static bool IsEditable(this PreliminaryAssessmentStatus status)
        {
            return status is PreliminaryAssessmentStatus.Draft or PreliminaryAssessmentStatus.Rejected;
        }
        
        public static bool IsFinalState(this PreliminaryAssessmentStatus status)
        {
            return status is PreliminaryAssessmentStatus.Finalized or PreliminaryAssessmentStatus.Cancelled;
        }
    }
}