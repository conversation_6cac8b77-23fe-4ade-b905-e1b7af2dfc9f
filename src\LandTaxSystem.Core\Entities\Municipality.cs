using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.Entities
{
    public class Municipality
    {
        public Guid MunicipalityId { get; set; }
        public string Name { get; set; } = string.Empty;
        
        // Location hierarchy
        public Guid DistrictId { get; set; }
        public int WardCount { get; set; } = 0;

        [JsonIgnore]
        public string ValuationRulesConfigJson { get; set; } = "{}";

        [JsonIgnore]
        public string TaxSlabsConfigJson { get; set; } = "[]";

        [JsonIgnore]
        public string ExemptionRulesConfigJson { get; set; } = "[]";

        [Range(0, 100)]
        public decimal DefaultPenaltyPercent { get; set; } = 0;

        [Range(0, 100)]
        public decimal DefaultDiscountPercent { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual District District { get; set; } = null!;
        public virtual ICollection<Property> Properties { get; set; } = new List<Property>();
        public virtual ICollection<ApplicationUser> Officers { get; set; } = new List<ApplicationUser>();
        public virtual ICollection<MunicipalityTaxConfig> MunicipalityTaxConfigs { get; set; } = new List<MunicipalityTaxConfig>();
    }
}
