import React, { useState, useEffect } from 'react';
import { taxCalculationService } from '../services/taxCalculationService';
import type { TaxCalculationResult } from '../types';

interface TaxCalculatorProps {
  propertyId?: string;
  fiscalYearId?: string;
  taxCalculationData?: TaxCalculationResult;
  showRecalculateButton?: boolean;
  className?: string;
}

const TaxCalculator: React.FC<TaxCalculatorProps> = ({
  propertyId,
  fiscalYearId,
  taxCalculationData,
  showRecalculateButton = false,
  className = ''
}) => {
  const [calculationData, setCalculationData] = useState<TaxCalculationResult | null>(taxCalculationData || null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);  // Fetch tax calculation data if not provided and propertyId is available
  const fetchTaxCalculation = async () => {
    if (!propertyId) return;

    setLoading(true);
    setError(null);

    try {
      let result: TaxCalculationResult;
      if (fiscalYearId) {
        // Use specific fiscal year if provided
        result = await taxCalculationService.getTaxCalculation(propertyId, fiscalYearId);
      } else {
        // Use current active fiscal year if no specific fiscal year provided
        result = await taxCalculationService.getCurrentTaxCalculation(propertyId);
      }
      setCalculationData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to calculate tax');
      console.error('Error fetching tax calculation:', err);
    } finally {
      setLoading(false);
    }
  };  useEffect(() => {
    if (!taxCalculationData && propertyId) {
      fetchTaxCalculation();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propertyId, fiscalYearId, taxCalculationData]);

  const handleRecalculate = () => {
    fetchTaxCalculation();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NP', {
      style: 'currency',
      currency: 'NPR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Tax Calculation</h3>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
          </div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Calculation Error</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          {showRecalculateButton && (
            <button
              onClick={handleRecalculate}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    );
  }
  if (!calculationData) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <p>No tax calculation data available</p>
          {propertyId && (
            <button
              onClick={fetchTaxCalculation}
              className="mt-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Calculate Tax
            </button>
          )}
        </div>
      </div>
    );
  }

  const { valueBreakdown, taxBreakdown } = calculationData;

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Tax Calculation</h3>
        {showRecalculateButton && (
          <button
            onClick={handleRecalculate}
            disabled={loading}
            className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Calculating...' : 'Recalculate'}
          </button>
        )}
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-1">Property Value</h4>
          <p className="text-2xl font-bold text-blue-800">{formatCurrency(calculationData.propertyValue)}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-green-900 mb-1">Tax Amount</h4>
          <p className="text-2xl font-bold text-green-800">{formatCurrency(calculationData.taxAmount)}</p>
        </div>
      </div>

      {/* Property Value Breakdown */}
      <div className="mb-6">
        <h4 className="text-md font-semibold text-gray-900 mb-3">Property Value Breakdown</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Land Area:</span>
            <span>{valueBreakdown.landAreaSqM.toLocaleString()} sq.m</span>
          </div>
          <div className="flex justify-between">
            <span>Land Rate per sq.m:</span>
            <span>{formatCurrency(valueBreakdown.landMVRPerSqM)}</span>
          </div>
          <div className="flex justify-between">
            <span>Land Value:</span>
            <span>{formatCurrency(valueBreakdown.landValue)}</span>
          </div>
          {valueBreakdown.buildingAreaSqM && (
            <>
              <div className="flex justify-between">
                <span>Building Area:</span>
                <span>{valueBreakdown.buildingAreaSqM.toLocaleString()} sq.m</span>
              </div>
              <div className="flex justify-between">
                <span>Building Rate per sq.m:</span>
                <span>{valueBreakdown.buildingBaseRatePerSqM ? formatCurrency(valueBreakdown.buildingBaseRatePerSqM) : 'N/A'}</span>
              </div>
              {valueBreakdown.buildingAgeYears && (
                <div className="flex justify-between">
                  <span>Building Age:</span>
                  <span>{valueBreakdown.buildingAgeYears} years</span>
                </div>
              )}
              {valueBreakdown.depreciationFactor && (
                <div className="flex justify-between">
                  <span>Depreciation Factor:</span>
                  <span>{(valueBreakdown.depreciationFactor * 100).toFixed(1)}%</span>
                </div>
              )}
              <div className="flex justify-between">
                <span>Building Value:</span>
                <span>{formatCurrency(valueBreakdown.buildingValue)}</span>
              </div>
            </>
          )}
          <div className="flex justify-between font-semibold border-t pt-2 mt-2">
            <span>Total Property Value:</span>
            <span>{formatCurrency(valueBreakdown.totalValue)}</span>
          </div>
        </div>
      </div>

      {/* Tax Calculation Breakdown */}
      <div className="mb-6">
        <h4 className="text-md font-semibold text-gray-900 mb-3">Tax Calculation Breakdown</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Assessed Value:</span>
            <span>{formatCurrency(taxBreakdown.assessedValue)}</span>
          </div>          {taxBreakdown.applicableTaxSlab && (
            <div className="flex justify-between">
              <span>Applicable Tax Slab:</span>
              <span>
                {formatCurrency(taxBreakdown.applicableTaxSlab.minAssessedValue)} - {formatCurrency(taxBreakdown.applicableTaxSlab.maxAssessedValue)}
              </span>
            </div>
          )}
          <div className="flex justify-between">
            <span>Fixed Amount:</span>
            <span>{formatCurrency(taxBreakdown.fixedAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Variable Amount:</span>
            <span>{formatCurrency(taxBreakdown.variableAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Gross Tax Amount:</span>
            <span>{formatCurrency(taxBreakdown.grossTaxAmount)}</span>
          </div>
          {taxBreakdown.exemptionAmount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>Exemption/Discount:</span>
              <span>-{formatCurrency(taxBreakdown.exemptionAmount)}</span>
            </div>
          )}
          <div className="flex justify-between font-semibold border-t pt-2 mt-2">
            <span>Net Tax Amount:</span>
            <span>{formatCurrency(taxBreakdown.netTaxAmount)}</span>
          </div>
        </div>
      </div>

      {/* Rules Used */}
      <div className="text-xs text-gray-500">
        <p>
          Calculation performed on:{' '}
          {new Date(calculationData.calculationTimestamp).toLocaleDateString('en-NP', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </p>
      </div>
    </div>
  );
};

export default TaxCalculator;
