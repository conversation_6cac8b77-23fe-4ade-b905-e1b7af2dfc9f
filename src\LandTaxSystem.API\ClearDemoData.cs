using System;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace LandTaxSystem.API
{
    public class ClearDemoData
    {
        public static async Task ClearDemoDataAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            Console.WriteLine("Clearing existing demo data...");

            // Delete demo properties and related data
            var demoProperties = await dbContext.Properties
                .Where(p => p.ParcelNumber != null && p.ParcelNumber.StartsWith("pokhara_metropolitan_city_15_1000_"))
                .ToListAsync();

            if (demoProperties.Any())
            {
                // Delete related payments first
                var propertyIds = demoProperties.Select(p => p.PropertyId).ToList();
                var demoPayments = await dbContext.Payments
                    .Where(p => propertyIds.Contains(p.PropertyId))
                    .ToListAsync();

                if (demoPayments.Any())
                {
                    dbContext.Payments.RemoveRange(demoPayments);
                    Console.WriteLine($"Deleted {demoPayments.Count} demo payment records.");
                }

                // Delete properties
                dbContext.Properties.RemoveRange(demoProperties);
                Console.WriteLine($"Deleted {demoProperties.Count} demo properties.");

                await dbContext.SaveChangesAsync();
                Console.WriteLine("Demo data cleared successfully.");
            }
            else
            {
                Console.WriteLine("No demo data found to clear.");
            }
        }
    }
}
