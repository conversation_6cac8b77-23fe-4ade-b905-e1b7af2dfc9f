using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Core.DTOs.PreliminaryAssessment;
using LandTaxSystem.Core.Enums;
using LandTaxSystem.Core.Services;
using LandTaxSystem.Infrastructure.Data;

namespace LandTaxSystem.Infrastructure.Services
{
    public class PreliminaryAssessmentValidationService : IPreliminaryAssessmentValidationService
    {
        private readonly ApplicationDbContext _context;
        
        public PreliminaryAssessmentValidationService(ApplicationDbContext context)
        {
            _context = context;
        }
        
        public async Task<ValidationResult> ValidateStatusTransitionAsync(Guid assessmentId, PreliminaryAssessmentStatus newStatus, string userId)
        {
            var assessment = await _context.PreliminaryAssessments.FindAsync(assessmentId);
            if (assessment == null)
                return ValidationResult.Failure("Assessment not found");
            
            if (!Enum.TryParse<PreliminaryAssessmentStatus>(assessment.Status, out var currentStatus))
                return ValidationResult.Failure("Invalid current status");
            
            if (!currentStatus.CanTransitionTo(newStatus))
                return ValidationResult.Failure($"Cannot transition from {currentStatus.GetDescription()} to {newStatus.GetDescription()}");
            
            // Additional business rules for specific transitions
            switch (newStatus)
            {
                case PreliminaryAssessmentStatus.Approved:
                    if (assessment.TaxDetails?.Any() != true)
                        return ValidationResult.Failure("Cannot approve assessment without tax details");
                    break;
                    
                case PreliminaryAssessmentStatus.Finalized:
                    // Check if there are any pending final assessments
                    var hasPendingFinalAssessments = await _context.FinalAssessments
                        .AnyAsync(fa => fa.PreliminaryAssessmentId == assessmentId && fa.Status != "Completed");
                    if (hasPendingFinalAssessments)
                        return ValidationResult.Failure("Cannot finalize preliminary assessment with pending final assessments");
                    break;
            }
            
            return ValidationResult.Success();
        }
        
        public async Task<ValidationResult> ValidateAssessmentPeriodAsync(string taxpayerRegistration, DateTime periodFrom, DateTime periodTo, Guid? excludeAssessmentId = null)
        {
            if (periodFrom >= periodTo)
                return ValidationResult.Failure("Assessment period 'From' date must be before 'To' date");
            
            // Check for overlapping periods for the same taxpayer
            var query = _context.PreliminaryAssessments
                .Where(a => a.TaxpayerRegistration == taxpayerRegistration &&
                           a.Status != PreliminaryAssessmentStatus.Cancelled.ToString() &&
                           a.Status != PreliminaryAssessmentStatus.Rejected.ToString());
            
            if (excludeAssessmentId.HasValue)
                query = query.Where(a => a.Id != excludeAssessmentId.Value);
            
            var overlappingAssessments = await query
                .Where(a => (a.AssessmentPeriodFrom < periodTo && a.AssessmentPeriodTo > periodFrom))
                .ToListAsync();
            
            if (overlappingAssessments.Any())
            {
                var overlappingIds = string.Join(", ", overlappingAssessments.Select(a => a.Id));
                return ValidationResult.Failure($"Assessment period overlaps with existing assessments: {overlappingIds}");
            }
            
            // Warn if period is more than 5 years
            if ((periodTo - periodFrom).TotalDays > 365 * 5)
                return ValidationResult.Warning("Assessment period spans more than 5 years");
            
            return ValidationResult.Success();
        }
        
        public ValidationResult ValidateTaxCalculation(PreliminaryAssessmentCreateDto dto)
        {
            var errors = new List<string>();
            
            if (dto.TaxDetails == null || !dto.TaxDetails.Any())
            {
                errors.Add("At least one tax detail is required");
                return ValidationResult.Failure(errors.ToArray());
            }
            
            foreach (var detail in dto.TaxDetails)
            {
                // Validate individual tax detail calculations
                var calculatedTotal = detail.AssessedAmount + detail.Penalty + detail.AdditionalAmount + detail.Interest;
                if (Math.Abs(detail.Total - calculatedTotal) > 0.01m) // Allow for minor rounding differences
                {
                    errors.Add($"Tax detail {detail.SerialNumber}: Total ({detail.Total}) does not match calculated total ({calculatedTotal})");
                }
                
                // Validate amounts are non-negative
                if (detail.AssessedAmount < 0 || detail.Penalty < 0 || detail.AdditionalAmount < 0 || detail.Interest < 0)
                {
                    errors.Add($"Tax detail {detail.SerialNumber}: All amounts must be non-negative");
                }
                
                // Validate tax year format
                if (!IsValidTaxYear(detail.TaxYear))
                {
                    errors.Add($"Tax detail {detail.SerialNumber}: Invalid tax year format '{detail.TaxYear}'");
                }
            }
            
            return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
        }
        
        public async Task<ValidationResult> ValidateCreateAsync(PreliminaryAssessmentCreateDto dto)
        {
            var errors = new List<string>();
            
            // Validate taxpayer registration exists
            var taxpayerExists = await _context.Users.AnyAsync(u => u.UserName == dto.TaxpayerRegistration);
            if (!taxpayerExists)
                errors.Add("Taxpayer registration not found");
            
            // Validate municipality exists
            var municipalityExists = await _context.Municipalities.AnyAsync(m => m.MunicipalityId == dto.MunicipalityId);
            if (!municipalityExists)
                errors.Add("Municipality not found");
            
            // Validate return filing if provided
            if (dto.ReturnFilingId.HasValue)
            {
                var returnFilingExists = await _context.ReturnFilings.AnyAsync(rf => rf.ReturnFilingId == dto.ReturnFilingId.Value);
                if (!returnFilingExists)
                    errors.Add("Return filing not found");
            }
            
            // Validate assessment period
            var periodValidation = await ValidateAssessmentPeriodAsync(dto.TaxpayerRegistration, dto.AssessmentPeriodFrom, dto.AssessmentPeriodTo);
            if (!periodValidation.IsValid)
                errors.AddRange(periodValidation.Errors);
            
            // Validate tax calculation
            var taxValidation = ValidateTaxCalculation(dto);
            if (!taxValidation.IsValid)
                errors.AddRange(taxValidation.Errors);
            
            return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
        }
        
        public async Task<ValidationResult> ValidateUpdateAsync(Guid assessmentId, PreliminaryAssessmentUpdateDto dto)
        {
            var errors = new List<string>();
            
            var assessment = await _context.PreliminaryAssessments.FindAsync(assessmentId);
            if (assessment == null)
                return ValidationResult.Failure("Assessment not found");
            
            // Check if assessment is in an editable state
            if (Enum.TryParse<PreliminaryAssessmentStatus>(assessment.Status, out var status) && !status.IsEditable())
                return ValidationResult.Failure($"Cannot edit assessment in {status.GetDescription()} status");
            
            // Validate municipality exists if changed
            if (dto.MunicipalityId != Guid.Empty)
            {
                var municipalityExists = await _context.Municipalities.AnyAsync(m => m.MunicipalityId == dto.MunicipalityId);
                if (!municipalityExists)
                    errors.Add("Municipality not found");
            }
            
            // Validate return filing if provided
            if (dto.ReturnFilingId.HasValue)
            {
                var returnFilingExists = await _context.ReturnFilings.AnyAsync(rf => rf.ReturnFilingId == dto.ReturnFilingId.Value);
                if (!returnFilingExists)
                    errors.Add("Return filing not found");
            }
            
            // Validate assessment period if changed
            if (dto.AssessmentPeriodFrom != default && dto.AssessmentPeriodTo != default)
            {
                var periodValidation = await ValidateAssessmentPeriodAsync(dto.TaxpayerRegistration ?? assessment.TaxpayerRegistration, 
                    dto.AssessmentPeriodFrom, dto.AssessmentPeriodTo, assessmentId);
                if (!periodValidation.IsValid)
                    errors.AddRange(periodValidation.Errors);
            }
            
            return errors.Any() ? ValidationResult.Failure(errors.ToArray()) : ValidationResult.Success();
        }
        
        public async Task<ValidationResult> ValidateDeleteAsync(Guid assessmentId)
        {
            var assessment = await _context.PreliminaryAssessments.FindAsync(assessmentId);
            if (assessment == null)
                return ValidationResult.Failure("Assessment not found");
            
            // Check if assessment has related final assessments
            var hasFinalAssessments = await _context.FinalAssessments.AnyAsync(fa => fa.PreliminaryAssessmentId == assessmentId);
            if (hasFinalAssessments)
                return ValidationResult.Failure("Cannot delete assessment with related final assessments");
            
            // Check if assessment is in a final state
            if (Enum.TryParse<PreliminaryAssessmentStatus>(assessment.Status, out var status) && status.IsFinalState())
                return ValidationResult.Failure($"Cannot delete assessment in {status.GetDescription()} status");
            
            return ValidationResult.Success();
        }
        
        private static bool IsValidTaxYear(string taxYear)
        {
            // Validate tax year format (e.g., "2023-24" or "2023")
            if (string.IsNullOrWhiteSpace(taxYear))
                return false;
            
            // Check for YYYY-YY format
            if (taxYear.Contains('-'))
            {
                var parts = taxYear.Split('-');
                if (parts.Length == 2 && 
                    int.TryParse(parts[0], out var year1) && 
                    int.TryParse(parts[1], out var year2) &&
                    year1 >= 2000 && year1 <= 2100 &&
                    year2 == (year1 + 1) % 100)
                {
                    return true;
                }
            }
            
            // Check for YYYY format
            if (int.TryParse(taxYear, out var year) && year >= 2000 && year <= 2100)
            {
                return true;
            }
            
            return false;
        }
    }
}