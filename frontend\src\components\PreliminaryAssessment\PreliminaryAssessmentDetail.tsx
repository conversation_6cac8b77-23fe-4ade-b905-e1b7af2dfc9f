import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
// Using DaisyUI alerts instead of react-hot-toast
import type { PreliminaryAssessmentDto, PreliminaryAssessmentDetailDto } from '../../types/preliminaryAssessment';
import { preliminaryAssessmentService } from '../../services/preliminaryAssessmentService';
import AdminLayout from '../admin/AdminLayout';
import AdminTable, { type Column } from '../admin/AdminTable';
import AdminModal from '../admin/AdminModal';
import StatusManagement from './StatusManagement';

export const PreliminaryAssessmentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [assessment, setAssessment] = useState<PreliminaryAssessmentDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [generatingPdf, setGeneratingPdf] = useState(false);
  const [alert, setAlert] = useState<{type: 'success' | 'error', message: string} | null>(null);

  const loadAssessment = useCallback(async (assessmentId: string) => {
    try {
      setLoading(true);
      const data = await preliminaryAssessmentService.getById(assessmentId);
      setAssessment(data);
    } catch (error) {
      setAlert({type: 'error', message: 'Failed to load preliminary assessment'});
      console.error('Error loading assessment:', error);
      navigate('/preliminary-assessments');
    } finally {
      setLoading(false);
    }
  }, [navigate]);

  useEffect(() => {
    if (id) {
      loadAssessment(id);
    }
  }, [id, loadAssessment]);

  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await preliminaryAssessmentService.delete(id);
      setAlert({type: 'success', message: 'Preliminary assessment deleted successfully'});
      navigate('/preliminary-assessments');
    } catch (error) {
      setAlert({type: 'error', message: 'Failed to delete preliminary assessment'});
      console.error('Error deleting assessment:', error);
    }
    setDeleteConfirm(false);
  };

  const handleStatusUpdate = (newStatus: string) => {
    if (assessment) {
      setAssessment({ ...assessment, status: newStatus });
      setAlert({ type: 'success', message: `Status updated to ${newStatus}` });
    }
  };

  const handleStatusError = (message: string) => {
    setAlert({ type: 'error', message });
  };

  const handleGeneratePdf = async () => {
    if (!id || !assessment) return;
    
    try {
      setGeneratingPdf(true);
      const blob = await preliminaryAssessmentService.generatePdfReport(id);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `preliminary-assessment-${assessment.taxpayerName.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      setAlert({type: 'success', message: 'PDF generated successfully'});
    } catch (error) {
      setAlert({type: 'error', message: 'Failed to generate PDF'});
      console.error('Error generating PDF:', error);
    } finally {
      setGeneratingPdf(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <div className="loading loading-spinner loading-lg"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!assessment) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Assessment Not Found</h3>
          <p className="text-gray-600 mb-4">The requested preliminary assessment could not be found.</p>
          <Link
            to="/preliminary-assessments"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Assessments
          </Link>
        </div>
      </AdminLayout>
    );
  }

  const taxDetailColumns: Column<PreliminaryAssessmentDetailDto>[] = [
    { 
      header: 'S.N.', 
      accessor: 'serialNumber' as keyof PreliminaryAssessmentDetailDto
    },
    { 
      header: 'Filing Period', 
      accessor: 'filingPeriod' as keyof PreliminaryAssessmentDetailDto
    },
    { 
      header: 'Period', 
      accessor: 'period' as keyof PreliminaryAssessmentDetailDto
    },
    { 
      header: 'Tax Year', 
      accessor: 'taxYear' as keyof PreliminaryAssessmentDetailDto
    },
    { 
      header: 'Assessed Amount', 
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.assessedAmount.toLocaleString()}`
    },
    { 
      header: 'Penalty Amount', 
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.penalty.toLocaleString()}`
    },
    { 
      header: 'Additional Amount', 
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.additionalAmount.toLocaleString()}`
    },
    { 
      header: 'Interest Amount', 
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.interest.toLocaleString()}`
    },
    { 
      header: 'Total Amount', 
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.total.toLocaleString()}`
    }
  ];

  const grandTotal = assessment.grandTotal || 0;

  return (
    <AdminLayout 
      title={`Assessment: ${assessment.taxpayerRegistration}`}
      subtitle="Preliminary Assessment Details"
    >
      
      {/* Action Buttons */}
      <div className="mb-6 flex flex-wrap gap-2">
        <button
          onClick={handleGeneratePdf}
          disabled={generatingPdf}
          className="btn btn-primary"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {generatingPdf ? 'Generating...' : 'Download PDF'}
        </button>
        <Link
          to={`/preliminary-assessments/${assessment.id}/edit`}
          className="btn btn-success"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit
        </Link>
        <button
          onClick={() => setDeleteConfirm(true)}
          className="btn btn-error"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete
        </button>
      </div>

      {/* Alert Messages */}
      {alert && (
        <div className={`alert ${alert.type === 'success' ? 'alert-success' : 'alert-error'} mb-6`}>
          <span>{alert.message}</span>
          <button 
            onClick={() => setAlert(null)}
            className="btn btn-ghost btn-sm btn-circle"
          >
            ✕
          </button>
        </div>
      )}

      <div className="space-y-6">
        {/* Status Management */}
        <StatusManagement
          assessmentId={assessment.id}
          currentStatus={assessment.status}
          onStatusUpdate={handleStatusUpdate}
          onError={handleStatusError}
        />
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

            <div>
              <label className="block text-sm font-medium text-gray-500">Taxpayer Registration</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.taxpayerRegistration}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Taxpayer Name</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.taxpayerName}</p>
            </div>
            {assessment.phone && (
              <div>
                <label className="block text-sm font-medium text-gray-500">Phone</label>
                <p className="mt-1 text-sm text-gray-900">{assessment.phone}</p>
              </div>
            )}
            {assessment.accountNumber && (
              <div>
                <label className="block text-sm font-medium text-gray-500">Account Number</label>
                <p className="mt-1 text-sm text-gray-900">{assessment.accountNumber}</p>
              </div>
            )}
          </div>
          {assessment.address && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-500">Address</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.address}</p>
            </div>
          )}
        </div>

        {/* Assessment Period */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Period</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">From Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.assessmentPeriodFrom).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">To Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.assessmentPeriodTo).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        {(assessment.reason || assessment.regulations) && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
            <div className="space-y-4">
              {assessment.reason && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Reason</label>
                  <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{assessment.reason}</p>
                </div>
              )}
              {assessment.regulations && (
                <div>
                  <label className="block text-sm font-medium text-gray-500">Regulations</label>
                  <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{assessment.regulations}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Tax Details */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Tax Details</h3>
          {assessment.taxDetails.length > 0 ? (
            <div>
              <AdminTable
                columns={taxDetailColumns}
                data={assessment.taxDetails}
                keyField="serialNumber"
              />
              <div className="mt-4 border-t pt-4">
                <div className="flex justify-end">
                  <div className="text-lg font-semibold text-gray-900">
                    Grand Total: Rs. {grandTotal.toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">No tax details available</p>
          )}
        </div>

        {/* Metadata */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Record Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Created At</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.createdAt).toLocaleString()}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Last Updated</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.updatedAt).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AdminModal
        isOpen={deleteConfirm}
        onClose={() => setDeleteConfirm(false)}
        title="Delete Preliminary Assessment"
        footer={
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setDeleteConfirm(false)}
              className="btn btn-ghost"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              className="btn btn-error"
            >
              Delete
            </button>
          </div>
        }
      >
        <p className="text-gray-600">
          Are you sure you want to delete the preliminary assessment "{assessment.taxpayerRegistration} - {assessment.taxpayerName}"? This action cannot be undone.
        </p>
      </AdminModal>
    </AdminLayout>
  );
};