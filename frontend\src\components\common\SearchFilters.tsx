import React, { useState } from 'react';

interface SearchField {
  name: string;
  label: string;
  type: 'text' | 'date' | 'select';
  placeholder?: string;
  options?: { value: string; label: string }[];
}

interface SearchFiltersProps {
  fields: SearchField[];
  onSearch: (filters: Record<string, string>) => void;
  onReset: () => void;
}

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  fields,
  onSearch,
  onReset,
}) => {
  const [filters, setFilters] = useState<Record<string, string>>({});

  const handleInputChange = (name: string, value: string) => {
    const newFilters = {
      ...filters,
      [name]: value,
    };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  const handleReset = () => {
    setFilters({});
    onReset();
  };

  return (
    <div className="card bg-base-100 shadow-sm border border-base-300 mb-4">
      <div className="card-body p-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 items-end">
          {fields.map((field) => (
            <div key={field.name} className="form-control">
              <label className="label">
                <span className="label-text">{field.label}</span>
              </label>
              {field.type === 'select' ? (
                <select
                  className="select select-bordered w-full"
                  value={filters[field.name] || ''}
                  onChange={(e) => handleInputChange(field.name, e.target.value)}
                >
                  <option value="">{field.placeholder}</option>
                  {field.options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type={field.type}
                  placeholder={field.placeholder}
                  className="input input-bordered w-full"
                  value={filters[field.name] || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleInputChange(field.name, e.target.value)}
                />
              )}
            </div>
          ))}

          <div className="form-control">
            <button
              className="btn btn-outline"
              onClick={handleReset}
            >
              Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};