﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateAssessmentPayment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Payments_AssessmentId",
                table: "Payments");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_AssessmentId",
                table: "Payments",
                column: "AssessmentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Payments_AssessmentId",
                table: "Payments");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_AssessmentId",
                table: "Payments",
                column: "AssessmentId",
                unique: true);
        }
    }
}
