using System;

namespace LandTaxSystem.Core.Entities
{
    public class AuditLog
    {
        public long LogId { get; set; }
        public string? UserId { get; set; }
        public string ActionType { get; set; } = string.Empty;
        public string? EntityType { get; set; }
        public Guid? EntityId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? OldValue<PERSON>son { get; set; }
        public string? NewValueJson { get; set; }
        public string? Description { get; set; }

        // Navigation properties
        public virtual ApplicationUser? User { get; set; }
    }
}
