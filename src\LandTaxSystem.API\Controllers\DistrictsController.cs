using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DistrictsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public DistrictsController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetDistricts()
        {
            try
            {
                var districts = await _context.Districts
                    .OrderBy(d => d.Name)
                    .Select(d => new
                    {
                        d.DistrictId,
                        d.Name,
                        d.Code,
                        d.ProvinceId
                    })
                    .ToListAsync();

                return Ok(districts);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving districts", error = ex.Message });
            }
        }

        [HttpGet("{id}/municipalities")]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<object>>> GetMunicipalitiesByDistrict(Guid id)
        {
            try
            {
                var municipalities = await _context.Municipalities
                    .Where(m => m.DistrictId == id)
                    .OrderBy(m => m.Name)
                    .Select(m => new
                    {
                        m.MunicipalityId,
                        m.Name,
                        m.DistrictId,
                        m.WardCount
                    })
                    .ToListAsync();

                return Ok(municipalities);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving municipalities", error = ex.Message });
            }
        }
    }
}
