import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import * as api from '../services/api';
import { finalAssessmentService } from '../services/finalAssessmentService';
import type { FinalAssessmentDto } from '../types/finalAssessment';

interface TaxPeriodItem {
  id: string;
  fiscalYear: string;
  startDate: string;
  endDate: string;
  year: string;
  period: string;
  taxPeriod: string;
  appealSubject: string;
  appealAmount: number;
}

interface AppealFormProps {
  onSubmit?: () => void;
  onCancel?: () => void;
}

const AppealForm: React.FC<AppealFormProps> = ({
  onSubmit,
  onCancel
}) => {
  const navigate = useNavigate();

  // Helper function to validate GUID format
  const isValidGuid = (guid: string): boolean => {
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return guidRegex.test(guid);
  };

  // Function to load available assessments
  const loadAssessments = async () => {
    try {
      setLoadingAssessments(true);
      const response = await finalAssessmentService.getAll({ pageSize: 100 });
      setAvailableAssessments(response.data || []);
    } catch (error) {
      console.error('Error loading assessments:', error);
    } finally {
      setLoadingAssessments(false);
    }
  };

  // Load assessments on component mount
  useEffect(() => {
    loadAssessments();
  }, []);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  
  // Form fields
  const [assessmentId, setAssessmentId] = useState('');
  const [availableAssessments, setAvailableAssessments] = useState<FinalAssessmentDto[]>([]);
  const [loadingAssessments, setLoadingAssessments] = useState(false);
  const [appealDate, setAppealDate] = useState('');
  const [appealSubject, setAppealSubject] = useState('');
  const [taxDeterminationOrderNumber, setTaxDeterminationOrderNumber] = useState('');
  const [appealAuthority, setAppealAuthority] = useState('');
  const [registrationNumber, setRegistrationNumber] = useState('');
  const [orderNumber, setOrderNumber] = useState('');
  const [orderDate, setOrderDate] = useState('');
  const [appealDescription, setAppealDescription] = useState('');
  
  // Tax period table
  const [taxPeriods, setTaxPeriods] = useState<TaxPeriodItem[]>([
    { id: '1', fiscalYear: '', startDate: '', endDate: '', year: '', period: '', taxPeriod: '', appealSubject: '', appealAmount: 0 }
  ]);

  const handleAddTaxPeriod = () => {
    const newId = (taxPeriods.length + 1).toString();
    setTaxPeriods([...taxPeriods, { 
      id: newId, 
      fiscalYear: '', 
      startDate: '', 
      endDate: '', 
      year: '', 
      period: '', 
      taxPeriod: '', 
      appealSubject: '', 
      appealAmount: 0 
    }]);
  };

  const handleRemoveTaxPeriod = (id: string) => {
    if (taxPeriods.length > 1) {
      setTaxPeriods(taxPeriods.filter(item => item.id !== id));
    }
  };

  const handleTaxPeriodChange = (id: string, field: keyof TaxPeriodItem, value: string | number) => {
    setTaxPeriods(taxPeriods.map(item => 
      item.id === id ? { ...item, [field]: value } : item
    ));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!assessmentId || !appealDate || !appealSubject) {
      setError('Please fill in all required fields');
      return;
    }

    // Validate GUID format for assessmentId
    if (!isValidGuid(assessmentId)) {
      setError('Assessment ID must be a valid GUID format (e.g., 12345678-1234-1234-1234-123456789abc)');
      return;
    }

    // Validate tax periods
    const invalidTaxPeriods = taxPeriods.some(
      period => !period.fiscalYear || !period.startDate || !period.endDate || period.appealAmount <= 0
    );
    
    if (invalidTaxPeriods) {
      setError('Please fill in all tax period details including fiscal year, start date, and end date');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const appealData = {
        assessmentId: assessmentId, // Backend expects GUID format
        appealDate,
        appealSubject,
        taxDeterminationOrderNumber,
        appealAuthority,
        registrationNumber,
        orderNumber,
        orderDate: orderDate || null,
        appealDescription,
        taxPeriods: taxPeriods.map(period => ({
          fiscalYear: period.fiscalYear,
          startDate: period.startDate,
          endDate: period.endDate,
          year: period.year,
          period: period.period,
          taxPeriodValue: period.taxPeriod,
          appealSubject: period.appealSubject,
          appealAmount: period.appealAmount
        }))
      };

      await api.post('/appeals', appealData);
      
      setShowSuccessAlert(true);
      
      if (onSubmit) {
        onSubmit();
      } else {
        navigate('/appeals');
      }
      
      setTimeout(() => {
        setShowSuccessAlert(false);
        setAssessmentId('');
        setAppealDate('');
        setAppealSubject('');
        setTaxDeterminationOrderNumber('');
        setAppealAuthority('');
        setRegistrationNumber('');
        setOrderNumber('');
        setOrderDate('');
        setAppealDescription('');
        setTaxPeriods([{ id: '1', fiscalYear: '', startDate: '', endDate: '', year: '', period: '', taxPeriod: '', appealSubject: '', appealAmount: 0 }]);
      }, 5000);
    } catch (err) {
      console.error('Failed to submit appeal:', err);
      setError('Failed to submit appeal. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">Appeal Information</h2>
        
        {error && (
          <div className="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>{error}</span>
          </div>
        )}
        
        {showSuccessAlert && (
          <div className="alert alert-success">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>Your appeal has been submitted successfully.</span>
          </div>
        )}
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="form-control">
            <label className="label">
              <span className="label-text font-medium">Assessment ID: *</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={assessmentId}
              onChange={(e) => setAssessmentId(e.target.value)}
              required
            >
              <option value="">Select an assessment</option>
              {loadingAssessments ? (
                <option disabled>Loading assessments...</option>
              ) : (
                availableAssessments.map((assessment) => (
                  <option key={assessment.id} value={assessment.id}>
                    {assessment.taxpayerName} - {assessment.taxpayerRegistration} ({assessment.id.substring(0, 8)}...)
                  </option>
                ))
              )}
            </select>
            {assessmentId && (
              <div className="text-xs text-gray-500 mt-1">
                Selected ID: {assessmentId}
              </div>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Appeal Date:
            </label>
            <input
              type="date"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={appealDate}
              onChange={(e) => setAppealDate(e.target.value)}
              required
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Appeal Subject:
            </label>
            <input
              type="text"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={appealSubject}
              onChange={(e) => setAppealSubject(e.target.value)}
              required
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Tax Determination Order No:
            </label>
            <input
              type="text"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={taxDeterminationOrderNumber}
              onChange={(e) => setTaxDeterminationOrderNumber(e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Appeal Authority:
            </label>
            <input
              type="text"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={appealAuthority}
              onChange={(e) => setAppealAuthority(e.target.value)}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Registration No:
            </label>
            <input
              type="text"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={registrationNumber}
              onChange={(e) => setRegistrationNumber(e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Order No:
            </label>
            <input
              type="text"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={orderNumber}
              onChange={(e) => setOrderNumber(e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Order Date:
            </label>
            <input
              type="date"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={orderDate}
              onChange={(e) => setOrderDate(e.target.value)}
            />
          </div>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2">
            Appeal Description:
          </label>
          <textarea
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={4}
            value={appealDescription}
            onChange={(e) => setAppealDescription(e.target.value)}
          />
        </div>
        
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-semibold">Tax Period</h3>
            <button
              type="button"
              onClick={handleAddTaxPeriod}
              className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-sm"
            >
              Add Row
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-300">
              <thead>
                <tr>
                  <th className="border px-4 py-2 text-sm">S.N.</th>
                  <th className="border px-4 py-2 text-sm">Fiscal Year *</th>
                  <th className="border px-4 py-2 text-sm">Start Date *</th>
                  <th className="border px-4 py-2 text-sm">End Date *</th>
                  <th className="border px-4 py-2 text-sm">Appeal Subject</th>
                  <th className="border px-4 py-2 text-sm">Appeal Amount *</th>
                  <th className="border px-4 py-2 text-sm">Action</th>
                </tr>
              </thead>
              <tbody>
                {taxPeriods.map((period, index) => (
                  <tr key={period.id}>
                    <td className="border px-4 py-2 text-center">{index + 1}</td>
                    <td className="border px-4 py-2">
                      <input
                        type="text"
                        className="w-full p-1 border rounded"
                        value={period.fiscalYear}
                        onChange={(e) => handleTaxPeriodChange(period.id, 'fiscalYear', e.target.value)}
                        placeholder="e.g., 2080/81"
                        required
                      />
                    </td>
                    <td className="border px-4 py-2">
                      <input
                        type="date"
                        className="w-full p-1 border rounded"
                        value={period.startDate}
                        onChange={(e) => handleTaxPeriodChange(period.id, 'startDate', e.target.value)}
                        required
                      />
                    </td>
                    <td className="border px-4 py-2">
                      <input
                        type="date"
                        className="w-full p-1 border rounded"
                        value={period.endDate}
                        onChange={(e) => handleTaxPeriodChange(period.id, 'endDate', e.target.value)}
                        required
                      />
                    </td>

                    <td className="border px-4 py-2">
                      <select
                        className="w-full p-1 border rounded bg-white"
                        value={period.appealSubject}
                        onChange={(e) => handleTaxPeriodChange(period.id, 'appealSubject', e.target.value)}
                        required
                      >
                        <option value="">Select an option</option>
                        <option value="MA">MA</option>
                        <option value="AF">AF</option>
                        <option value="SP">SP</option>
                      </select>
                    </td>
                    <td className="border px-4 py-2">
                      <input
                        type="number"
                        className="w-full p-1 border rounded"
                        value={period.appealAmount}
                        onChange={(e) => handleTaxPeriodChange(period.id, 'appealAmount', parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.01"
                        required
                      />
                    </td>
                    <td className="border px-4 py-2 text-center">
                      <button
                        type="button"
                        onClick={() => handleRemoveTaxPeriod(period.id)}
                        className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-sm"
                        disabled={taxPeriods.length === 1}
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        
        <div className="card-actions justify-end mt-6">
          <button
            type="button"
            onClick={onCancel || (() => navigate(-1))}
            className="btn btn-ghost"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="btn btn-primary"
          >
            {loading ? 
              <span className="loading loading-spinner loading-sm"></span> : 
              'Submit Appeal'
            }
          </button>
        </div>
      </form>
      </div>
    </div>
  );
};

export default AppealForm;
