using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Negotiation;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Enums;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class NegotiationsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<NegotiationsController> _logger;
        private readonly IEmailService _emailService;

        public NegotiationsController(ApplicationDbContext context, ILogger<NegotiationsController> logger, IEmailService emailService)
        {
            _context = context;
            _logger = logger;
            _emailService = emailService;
        }

        // GET: api/negotiations
        [HttpGet]
        [Authorize(Roles = "Citizen,Officer")]
        public async Task<ActionResult<IEnumerable<NegotiationDto>>> GetAllNegotiations()
        {
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var currentUser = await _context.Users.FindAsync(currentUserId);

            if (currentUser == null)
            {
                return Unauthorized();
            }

            IQueryable<Negotiation> query = _context.Negotiations
                .Include(n => n.Appeal)
                    .ThenInclude(a => a.Assessment)
                        .ThenInclude(a => a.Property)
                .Include(n => n.Appeal)
                    .ThenInclude(a => a.TaxPayer)
                .Include(n => n.Officer);

            // If user is a Citizen, only return their negotiations
            if (User.IsInRole("Citizen"))
            {
                query = query.Where(n => n.Appeal.TaxPayerId == currentUserId);
            }
            // If user is an Officer, only return negotiations for their municipality
            else if (User.IsInRole("Officer") && currentUser.MunicipalityId.HasValue)
            {
                query = query.Where(n => n.Appeal.Assessment.Property.MunicipalityId == currentUser.MunicipalityId);
            }

            var negotiations = await query.ToListAsync();

            var result = negotiations.Select(n => new NegotiationDto
            {
                NegotiationId = n.NegotiationId,
                AppealId = n.AppealId,
                OfficerId = n.OfficerId,
                OfficerName = n.Officer?.FullName ?? "Unknown Officer",
                NegotiatedAmount = n.NegotiatedAmount,
                NegotiationDate = n.NegotiationDate,
                Status = n.Appeal?.Status ?? "Unknown",
                AppealReason = n.Appeal?.Reason ?? "No reason provided",
                AppealSubmittedAt = n.Appeal?.SubmittedAt ?? DateTime.UtcNow,
                AssessmentId = n.Appeal?.AssessmentId ?? Guid.Empty,
                OriginalAssessmentAmount = n.Appeal?.Assessment?.TaxAmount ?? 0,
                PropertyId = n.Appeal?.Assessment?.PropertyId ?? Guid.Empty,
                PropertyAddress = n.Appeal?.Assessment?.Property?.Address ?? "Unknown Address",
                TaxPayerId = n.Appeal?.TaxPayerId ?? string.Empty,
                TaxPayerName = n.Appeal?.TaxPayer?.FullName ?? "Unknown Taxpayer",
                // Map additional fields
                PANNumber = n.PANNumber ?? string.Empty,
                TaxOfficeAddress = n.TaxOfficeAddress ?? string.Empty,
                AppealBody = n.AppealBody,
                DecisionNumber = n.DecisionNumber ?? string.Empty,
                DecisionDate = n.DecisionDate,
                OriginalTax = n.OriginalTax,
                OriginalPenalty = n.OriginalPenalty,
                OriginalFee = n.OriginalFee,
                OriginalInterest = n.OriginalInterest,
                DecidedTax = n.DecidedTax,
                DecidedPenalty = n.DecidedPenalty,
                DecidedFee = n.DecidedFee,
                DecidedInterest = n.DecidedInterest,
                IsWithdrawn = n.IsWithdrawn,
                // Map tax periods
                TaxPeriods = n.TaxPeriods?.Select(tp => new TaxPeriodDto
                {
                    TaxPeriodId = tp.TaxPeriodId,
                    // Map new fields
                    FiscalYear = tp.FiscalYear,
                    StartDate = tp.StartDate,
                    EndDate = tp.EndDate,
                    // Map legacy fields
                    Year = tp.Year,
                    Period = tp.Period,
                    TaxPeriodValue = tp.TaxPeriodValue,
                    AppealSubject = tp.AppealSubject
                }).ToList() ?? new List<TaxPeriodDto>()
            }).ToList();

            return Ok(result);
        }

        // POST: api/negotiations
        [HttpPost]
        [Authorize(Roles = "Officer")]
        public async Task<ActionResult<NegotiationDto>> CreateNegotiation(CreateNegotiationDto createNegotiationDto)
        {
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Check if the appeal exists and is pending
            var appeal = await _context.Appeals
                .Include(a => a.Assessment)
                .Include(a => a.Assessment.Property)
                .Include(a => a.TaxPayer)
                .FirstOrDefaultAsync(a => a.AppealId == createNegotiationDto.AppealId);

            if (appeal == null)
            {
                return NotFound("Appeal not found");
            }

            if (appeal.Status != "Pending")
            {
                return BadRequest("Appeal is not in pending status");
            }

            // Check if the officer belongs to the same municipality as the property
            var officer = await _context.Users.FirstOrDefaultAsync(u => u.Id == currentUserId);
            if (officer == null || !officer.MunicipalityId.HasValue || 
                officer.MunicipalityId != appeal.Assessment.Property.MunicipalityId)
            {
                return Forbid();
            }

            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                // 1. Create negotiation record
                var negotiation = new Negotiation
                {
                    NegotiationId = Guid.NewGuid(),
                    AppealId = createNegotiationDto.AppealId,
                    OfficerId = currentUserId != null ? currentUserId : throw new InvalidOperationException("Current user ID is null"),
                    NegotiatedAmount = createNegotiationDto.NegotiatedAmount,
                    NegotiationDate = DateTime.UtcNow,
                    // New fields
                    TaxpayerName = createNegotiationDto.TaxpayerName,
                    PANNumber = createNegotiationDto.PANNumber,
                    TaxOfficeAddress = createNegotiationDto.TaxOfficeAddress,
                    AppealBody = createNegotiationDto.AppealBody,
                    DecisionNumber = createNegotiationDto.DecisionNumber,
                    DecisionDate = createNegotiationDto.DecisionDate,
                    OriginalTax = createNegotiationDto.OriginalTax,
                    OriginalPenalty = createNegotiationDto.OriginalPenalty,
                    OriginalFee = createNegotiationDto.OriginalFee,
                    OriginalInterest = createNegotiationDto.OriginalInterest,
                    DecidedTax = createNegotiationDto.DecidedTax,
                    DecidedPenalty = createNegotiationDto.DecidedPenalty,
                    DecidedFee = createNegotiationDto.DecidedFee,
                    DecidedInterest = createNegotiationDto.DecidedInterest,
                    IsWithdrawn = createNegotiationDto.IsWithdrawn
                };

                // Add tax periods if any
                if (createNegotiationDto.TaxPeriods != null && createNegotiationDto.TaxPeriods.Any())
                {
                    foreach (var periodDto in createNegotiationDto.TaxPeriods)
                    {
                        var taxPeriod = new TaxPeriod
                        {
                            TaxPeriodId = periodDto.TaxPeriodId,
                            // Map new fields
                            FiscalYear = periodDto.FiscalYear,
                            StartDate = periodDto.StartDate,
                            EndDate = periodDto.EndDate,
                            // Map legacy fields for backward compatibility
                            Year = periodDto.Year,
                            Period = periodDto.Period,
                            TaxPeriodValue = periodDto.TaxPeriodValue,
                            AppealSubject = periodDto.AppealSubject
                        };
                        negotiation.TaxPeriods.Add(taxPeriod);
                    }
                }

                _context.Negotiations.Add(negotiation);

                // 2. Mark appeal as resolved
                appeal.Status = "Resolved";
                appeal.ResolvedAt = DateTime.UtcNow;
                appeal.UpdatedAt = DateTime.UtcNow;

                // 3. Create a new assessment record with negotiated amount
                var originalAssessment = appeal.Assessment;
                var newAssessment = new Assessment
                {
                    AssessmentId = Guid.NewGuid(),
                    PropertyId = originalAssessment.PropertyId,
                    AssessmentYear = originalAssessment.AssessmentYear,
                    FiscalYearId = originalAssessment.FiscalYearId,
                    OriginalAmount = originalAssessment.TaxAmount, // Store the original tax amount
                    CalculatedValue = originalAssessment.CalculatedValue,
                    OverriddenValue = createNegotiationDto.NegotiatedAmount,
                    FinalAssessedValue = originalAssessment.FinalAssessedValue,
                    TaxAmount = createNegotiationDto.NegotiatedAmount, // Set the negotiated amount as the new tax amount
                    AssessedByOfficerId = currentUserId,
                    AssessmentDate = DateTime.UtcNow,
                    OverrideReason = $"Negotiated after appeal (Appeal ID: {appeal.AppealId})",
                    PaymentStatus = "Pending",
                    PreviousAssessmentId = originalAssessment.AssessmentId // Reference to the original assessment
                };

                _context.Assessments.Add(newAssessment);

                // 4. Create audit logs
                var negotiationAuditLog = new AuditLog
                {
                    ActionType = "Create",
                    EntityType = "Negotiation",
                    EntityId = negotiation.NegotiationId,
                    UserId = currentUserId,
                    Timestamp = DateTime.UtcNow,
                    NewValueJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        negotiation.NegotiationId,
                        negotiation.AppealId,
                        negotiation.OfficerId,
                        negotiation.NegotiatedAmount,
                        negotiation.NegotiationDate
                    })
                };

                _context.AuditLogs.Add(negotiationAuditLog);

                var appealAuditLog = new AuditLog
                {
                    ActionType = "Update",
                    EntityType = "Appeal",
                    EntityId = appeal.AppealId,
                    UserId = currentUserId,
                    Timestamp = DateTime.UtcNow,
                    OldValueJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        appeal.Status,
                        ResolvedAt = (DateTime?)null
                    }),
                    NewValueJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        Status = "Resolved",
                        ResolvedAt = DateTime.UtcNow
                    })
                };

                _context.AuditLogs.Add(appealAuditLog);

                var assessmentAuditLog = new AuditLog
                {
                    ActionType = "Create",
                    EntityType = "Assessment",
                    EntityId = newAssessment.AssessmentId,
                    UserId = currentUserId,
                    Timestamp = DateTime.UtcNow,
                    NewValueJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        newAssessment.AssessmentId,
                        newAssessment.PropertyId,
                        newAssessment.AssessmentYear,
                        newAssessment.FiscalYearId,
                        newAssessment.OriginalAmount,
                        newAssessment.CalculatedValue,
                        newAssessment.OverriddenValue,
                        newAssessment.FinalAssessedValue,
                        newAssessment.TaxAmount,
                        newAssessment.AssessedByOfficerId,
                        newAssessment.AssessmentDate,
                        newAssessment.OverrideReason,
                        newAssessment.PaymentStatus,
                        newAssessment.PreviousAssessmentId
                    })
                };

                _context.AuditLogs.Add(assessmentAuditLog);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                // Send email notification to taxpayer about the negotiation result and new assessment
                try
                {
                    // Use null conditional operator to safely access TaxPayer.Email
                    string? taxpayerEmail = appeal.TaxPayer?.Email;
                    string? taxpayerId = appeal.TaxPayerId;
                    
                    if (!string.IsNullOrEmpty(taxpayerEmail))
                    {
                        // Update the email service call to include additional fields if needed
                    await _emailService.SendNegotiationNotificationToTaxPayer(
                            negotiation,
                            taxpayerEmail);
                        
                        // Create in-app notification for taxpayer
                        if (!string.IsNullOrEmpty(taxpayerId))
                        {
                            var notification = new Notification
                            {
                                UserId = taxpayerId,
                                Message = $"Your appeal has been reviewed. A negotiation has been proposed with a new tax amount of NPR {negotiation.NegotiatedAmount:N2}.",
                                Type = "negotiation",
                                IsRead = false,
                                RelatedEntityId = negotiation.NegotiationId,
                                RelatedEntityType = "Negotiation"
                            };
                            
                            _context.Notifications.Add(notification);
                            await _context.SaveChangesAsync();
                        }
                        
                        _logger.LogInformation($"Negotiation notification sent to taxpayer {appeal.TaxPayerId} for appeal {appeal.AppealId}");
                    }
                    else
                    {
                        _logger.LogWarning($"Could not send negotiation notification: taxpayer email not found for appeal {appeal.AppealId}");
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the negotiation process
                    _logger.LogError(ex, $"Error sending taxpayer notification for negotiation {negotiation.NegotiationId}: {ex.Message}");
                }

                // Return the negotiation details
                return Ok(new NegotiationDto
                {
                    NegotiationId = negotiation.NegotiationId,
                    AppealId = negotiation.AppealId,
                    OfficerId = negotiation.OfficerId,
                    OfficerName = officer.FullName,
                    NegotiatedAmount = negotiation.NegotiatedAmount,
                    NegotiationDate = negotiation.NegotiationDate,
                    AppealReason = appeal.Reason,
                    AppealSubmittedAt = appeal.SubmittedAt,
                    AssessmentId = newAssessment.AssessmentId,
                    OriginalAssessmentAmount = originalAssessment.TaxAmount,
                    PropertyId = originalAssessment.PropertyId,
                    PropertyAddress = appeal.Assessment?.Property?.Address ?? "Unknown Address",
                    TaxPayerId = appeal.TaxPayerId,
                    TaxPayerName = appeal.TaxPayer?.FullName ?? "Unknown Taxpayer"
                });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating negotiation");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing the negotiation");
            }
        }

// GET: api/negotiations/{id}
        [HttpGet("{id}")]
        [Authorize(Roles = "Citizen,Officer")]
        public async Task<ActionResult<NegotiationDto>> GetNegotiation(Guid id)
        {
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            var negotiation = await _context.Negotiations
                .Include(n => n.Appeal)
                .Include(n => n.Appeal.Assessment)
                .Include(n => n.Appeal.Assessment.Property)
                .Include(n => n.Appeal.TaxPayer)
                .Include(n => n.Officer)
                .FirstOrDefaultAsync(n => n.NegotiationId == id);

            if (negotiation == null)
            {
                return NotFound();
            }

            // Check if the officer belongs to the same municipality as the property
            var officer = await _context.Users.FirstOrDefaultAsync(u => u.Id == currentUserId);
            if (officer == null || !officer.MunicipalityId.HasValue || 
                negotiation.Appeal?.Assessment?.Property == null ||
                officer.MunicipalityId != negotiation.Appeal.Assessment.Property.MunicipalityId)
            {
                return Forbid();
            }

            // Find the new assessment created after negotiation
            var newAssessment = await _context.Assessments
                .FirstOrDefaultAsync(a => a.PreviousAssessmentId == negotiation.Appeal.AssessmentId);

            return new NegotiationDto
            {
                NegotiationId = negotiation.NegotiationId,
                AppealId = negotiation.AppealId,
                OfficerId = negotiation.OfficerId,
                OfficerName = negotiation.Officer?.FullName ?? "Unknown Officer",
                NegotiatedAmount = negotiation.NegotiatedAmount,
                NegotiationDate = negotiation.NegotiationDate,
                AppealReason = negotiation.Appeal?.Reason ?? "No reason provided",
                AppealSubmittedAt = negotiation.Appeal?.SubmittedAt ?? DateTime.UtcNow,
                AssessmentId = newAssessment?.AssessmentId ?? negotiation.Appeal?.AssessmentId ?? Guid.Empty,
                OriginalAssessmentAmount = negotiation.Appeal?.Assessment?.TaxAmount ?? 0,
                PropertyId = negotiation.Appeal?.Assessment?.PropertyId ?? Guid.Empty,
                PropertyAddress = negotiation.Appeal?.Assessment?.Property?.Address ?? "Unknown Address",
                TaxPayerId = negotiation.Appeal?.TaxPayerId ?? string.Empty,
                TaxPayerName = negotiation.Appeal?.TaxPayer?.FullName ?? "Unknown Taxpayer"
            };
        }
    }
}
