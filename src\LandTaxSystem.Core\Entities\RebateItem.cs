using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class RebateItem
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string? FiscalYear { get; set; }

        [Required]
        public string? FilingPeriod { get; set; }

        [Required]
        public string? TaxPeriod { get; set; }

        [Required]
        public decimal TotalExemptedAmount { get; set; }

        [Required]
        public decimal DiscountAmount { get; set; }

        public int RebateId { get; set; }
        [ForeignKey("RebateId")]
        public Rebate? Rebate { get; set; }
    }
}