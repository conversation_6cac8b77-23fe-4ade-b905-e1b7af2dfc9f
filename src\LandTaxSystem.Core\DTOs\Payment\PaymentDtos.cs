using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Payment
{
    public class PaymentAgainstAssessmentDto
    {
        [Required]
        public Guid AssessmentId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0.")]
        public decimal Amount { get; set; }
    }

    public class DirectPaymentDto
    {
        [Required]
        public Guid AssessmentId { get; set; }
        
        [Required]
        [RegularExpression("^(card|bank|digital)$", 
            ErrorMessage = "PaymentMethod must be one of: card, bank, digital")]
        public string PaymentMethod { get; set; } = "card";
    }
    
    public class BulkPaymentDto
    {
        [Required]
        [MinLength(1, ErrorMessage = "At least one assessment must be provided")]
        public List<Guid> AssessmentIds { get; set; } = new List<Guid>();
        
        [Required]
        [RegularExpression("^(card|bank|digital)$", 
            ErrorMessage = "PaymentMethod must be one of: card, bank, digital")]
        public string PaymentMethod { get; set; } = "card";
    }

    public class PaymentGatewayResponseDto
    {
        public Guid InternalPaymentId { get; set; }
        public string PaymentGatewayUrl { get; set; } = string.Empty;
    }

    public class PaymentCallbackDto
    {
        public Guid InternalPaymentId { get; set; }
        public Guid AssessmentId { get; set; }

        [Required]
        [RegularExpression("^(Success|Failed)$",
            ErrorMessage = "Status must be one of: Success, Failed")]
        public string Status { get; set; } = string.Empty;

        public string TransactionId { get; set; } = string.Empty;
    }    public class PaymentResponseDto
    {
        public Guid PaymentId { get; set; }
        public Guid? AssessmentId { get; set; } // Made nullable to support provisional payments
        public Guid PropertyId { get; set; } // Added to support provisional payments
        public Guid FiscalYearId { get; set; } // Added to support provisional payments
        public decimal AmountPaid { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentGateway { get; set; } = string.Empty;
        public string? TransactionId { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool Provisional { get; set; } // Added to indicate provisional payments
        public bool Partial { get; set; } // Added to indicate partial payments
        public DateTime CreatedAt { get; set; }
        
        // Additional fields for underpayment scenarios
        public string? Message { get; set; } // Information message about the payment status
        public decimal? ExpectedTaxAmount { get; set; } // Expected tax amount for comparison
        public decimal? DeficitAmount { get; set; } // Amount still owed in case of underpayment
        public bool? AssessmentCreated { get; set; } // Whether an assessment was created for underpayment
    }
    
    public class BulkPaymentResponseDto
    {
        public Guid BatchPaymentId { get; set; }
        public List<PaymentResponseDto> Payments { get; set; } = new List<PaymentResponseDto>();
        public decimal TotalAmountPaid { get; set; }
        public DateTime PaymentDate { get; set; }
        public string PaymentGateway { get; set; } = string.Empty;
        public string? TransactionId { get; set; }
        public string Status { get; set; } = string.Empty;
    }
    
    public class PaymentHistoryDto
    {
        public Guid PropertyId { get; set; }
        public List<PaymentResponseDto> Payments { get; set; } = new List<PaymentResponseDto>();
    }
    
    public class ReceiptDto
    {
        public Guid PaymentId { get; set; }
        public string PropertyAddress { get; set; } = string.Empty;
        public string OwnerName { get; set; } = string.Empty;
        public int AssessmentYear { get; set; }
        public decimal AmountPaid { get; set; }
        public DateTime PaymentDate { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string MunicipalityName { get; set; } = string.Empty;
    }
    
    public class ProvisionalPaymentDto
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0.")]
        public decimal Amount { get; set; }
        
        [Required]
        [RegularExpression("^(card|bank|digital)$", 
            ErrorMessage = "PaymentMethod must be one of: card, bank, digital")]
        public string PaymentMethod { get; set; } = "card";
    }

    public class PropertyTaxPaymentDto
    {
        [Required]
        public Guid PropertyId { get; set; }

        [Required]
        public Guid FiscalYearId { get; set; }

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0.")]
        public decimal AmountPaid { get; set; }

        [Required]
        [RegularExpression("^(card|bank|digital)$", 
            ErrorMessage = "PaymentMethod must be one of: card, bank, digital")]
        public string PaymentMethod { get; set; } = "card";

        /// <summary>
        /// External transaction ID from payment gateway (optional for mock payments)
        /// </summary>
        public string? TransactionId { get; set; }
    }
}
