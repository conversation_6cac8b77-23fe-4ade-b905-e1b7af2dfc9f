﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddPreliminaryAssessmentEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PreliminaryAssessments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OfficeCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    AssessmentOrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TaxpayerRegistration = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TaxpayerName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Address = table.Column<string>(type: "text", nullable: true),
                    Phone = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    AccountNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AssessmentPeriodFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AssessmentPeriodTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: true),
                    Regulations = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Draft"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PreliminaryAssessments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PreliminaryAssessments_AspNetUsers_CreatedBy",
                        column: x => x.CreatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PreliminaryAssessments_AspNetUsers_UpdatedBy",
                        column: x => x.UpdatedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PreliminaryAssessmentDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PreliminaryAssessmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    SerialNumber = table.Column<int>(type: "integer", nullable: false),
                    HouseProperty = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ConstructionCost = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    LandValue = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    Penalty = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    Interest = table.Column<decimal>(type: "numeric(15,2)", nullable: false),
                    Total = table.Column<decimal>(type: "numeric(15,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PreliminaryAssessmentDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PreliminaryAssessmentDetails_PreliminaryAssessments_Prelimi~",
                        column: x => x.PreliminaryAssessmentId,
                        principalTable: "PreliminaryAssessments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessmentDetails_PreliminaryAssessmentId",
                table: "PreliminaryAssessmentDetails",
                column: "PreliminaryAssessmentId");

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessments_CreatedBy",
                table: "PreliminaryAssessments",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessments_UpdatedBy",
                table: "PreliminaryAssessments",
                column: "UpdatedBy");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PreliminaryAssessmentDetails");

            migrationBuilder.DropTable(
                name: "PreliminaryAssessments");
        }
    }
}
