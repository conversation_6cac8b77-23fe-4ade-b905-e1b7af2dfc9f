using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class LandDetail
    {
        [Key]
        public Guid LandDetailId { get; set; }

        [Required]
        public Guid PropertyId { get; set; }

        // General Information
        public string? OldVdc { get; set; }
        public string? OldWardNo { get; set; }
        public string? CurrentWardNo { get; set; }
        public string? KittaNo { get; set; }
        public string? MapNo { get; set; }
        public string? FiscalYear { get; set; }
        public string? OtherDetails { get; set; }

        // Status Flags
        public bool IsMultiRateValuation { get; set; }
        public bool IsTemporaryHouse { get; set; }
        public bool IsCultivable { get; set; }

        // Measurement Units
        public string? MeasurementUnit { get; set; } // "ropani-aana-paisa-daam" or "bigha-kattha-dhur"

        // Area Measurements
        public decimal? AreaBigha { get; set; }
        public decimal? AreaKattha { get; set; }
        public decimal? AreaDhur { get; set; }
        public decimal? AreaRopani { get; set; }
        public decimal? AreaAana { get; set; }
        public decimal? AreaPaisa { get; set; }
        public decimal? AreaDaam { get; set; }

        // Tax and Revenue
        public decimal? TaxpayerPrice { get; set; }
        public decimal? TaxpayerLandRevenuePrice { get; set; }
        public bool DeactivatePlot { get; set; }
        public string? LastFyForInclusion { get; set; }
        public string? LandRevenueApplicable { get; set; } // "no", "full", or "partial"

        // Structure Details
        public decimal? StructureAreaLength { get; set; }
        public decimal? StructureAreaBreadth { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Property
        [ForeignKey("PropertyId")]
        public virtual Property? Property { get; set; }
    }
}
