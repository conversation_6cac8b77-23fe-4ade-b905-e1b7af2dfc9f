/**
 * Authentication utility functions for the Land Tax System
 */

/**
 * Gets the authentication header with the JWT token
 * @returns The authorization header object or empty object if no token exists
 */
export const getAuthHeader = () => {
  const token = localStorage.getItem('token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};

/**
 * Saves the JWT token to local storage
 * @param token The JWT token to save
 */
export const saveToken = (token: string) => {
  localStorage.setItem('token', token);
};

/**
 * Removes the JWT token from local storage
 */
export const removeToken = () => {
  localStorage.removeItem('token');
};

/**
 * Checks if a user is authenticated by verifying token existence
 * @returns True if authenticated, false otherwise
 */
export const isAuthenticated = () => {
  return !!localStorage.getItem('token');
};
