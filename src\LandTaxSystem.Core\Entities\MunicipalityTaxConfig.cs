using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace LandTaxSystem.Core.Entities
{
    public class MunicipalityTaxConfig
    {
        public Guid MunicipalityTaxConfigId { get; set; }
        public Guid MunicipalityId { get; set; }
        public Guid FiscalYearId { get; set; }
        
        [JsonIgnore]
        public string TaxSlabsConfigJson { get; set; } = "[]";
        
        [JsonIgnore]
        public string PenaltyRulesJson { get; set; } = "{}";

        // Officer-configured valuation rules (per fiscal year)
        [JsonIgnore]
        public string ValuationRulesConfigJson { get; set; } = "{}";

        // Officer-configured exemption rules (per fiscal year)
        [JsonIgnore]
        public string ExemptionRulesConfigJson { get; set; } = "[]";
        
        public bool IsFinalized { get; set; } = false;
        public DateTime? FinalizedAt { get; set; }
        public string? FinalizedBy { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Municipality Municipality { get; set; } = null!;
        public virtual FiscalYear FiscalYear { get; set; } = null!;
    }
}
