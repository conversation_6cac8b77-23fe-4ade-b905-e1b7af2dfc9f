using System;

namespace LandTaxSystem.Core.Entities
{
    public class Notification
    {
        public Guid NotificationId { get; set; }
        public required string UserId { get; set; }
        public required string Message { get; set; }
        public required string Type { get; set; } // payment, appeal, negotiation, assessment, system
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public Guid? RelatedEntityId { get; set; }
        public string? RelatedEntityType { get; set; }
    }
}
