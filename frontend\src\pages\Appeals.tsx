import React, { useState } from 'react';
import AppealForm from '../components/AppealForm';
import { AdminLayout } from '../components/admin';

const Appeals: React.FC = () => {
  const [submissionSuccess, setSubmissionSuccess] = useState(false);

  const handleSubmitSuccess = () => {
    setSubmissionSuccess(true);
    // Clear the success message after 5 seconds
    setTimeout(() => setSubmissionSuccess(false), 5000);
  };

  return (
    <AdminLayout title="पुनरावेदन दर्ता फारम">
      {submissionSuccess && (
        <div className="alert alert-success mb-6">
          आफ्नो पुनरावेदन सफलतापूर्वक पेश गरिएको छ।
        </div>
      )}
      
      <div className="card bg-base-100 shadow">
        <div className="card-body">
          <AppealForm 
            onSubmit={handleSubmitSuccess}
          />
        </div>
      </div>
    </AdminLayout>
  );
};

export default Appeals;
