using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Payment;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Enums;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.Infrastructure.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly ApplicationDbContext _context;
        private readonly GisService _gisService;

        public PaymentService(ApplicationDbContext context, GisService gisService)
        {
            _context = context;
            _gisService = gisService;
        }

        /// <summary>
        /// Gets a list of properties eligible for assessment in a specific municipality
        /// </summary>
        /// <param name="municipalityId">The municipality ID to filter by</param>
        /// <returns>List of eligible properties with payment details</returns>
        public async Task<List<EligiblePropertyDto>> GetEligibleProperties(Guid municipalityId)
        {
            try
            {
                // Get all payments that are not reconciled and belong to the specified municipality
                // Include Municipality to avoid null reference exceptions
                var unreconciled = await _context.Payments
                    .Include(p => p.Property)
                        .ThenInclude(p => p.Municipality)
                    .Include(p => p.FiscalYear)
                    .Where(p => 
                        !p.IsReconciled && 
                        p.Status == "Success" && 
                        p.Property != null && 
                        p.Property.MunicipalityId == municipalityId)
                    .ToListAsync();

                // Filter out any payments with null properties or fiscal years
                unreconciled = unreconciled
                    .Where(p => p.Property != null && p.FiscalYear != null)
                    .ToList();

                if (!unreconciled.Any())
                {
                    // Return empty list if no eligible payments found
                    return new List<EligiblePropertyDto>();
                }

                // Group payments by property and fiscal year
                var groupedPayments = unreconciled
                    .GroupBy(p => new { p.PropertyId, p.FiscalYearId })
                    .Select(g => new
                    {
                        PropertyId = g.Key.PropertyId,
                        FiscalYearId = g.Key.FiscalYearId,
                        TotalPaid = g.Sum(p => p.AmountPaid),
                        Property = g.First().Property,
                        FiscalYear = g.First().FiscalYear,
                        Payments = g.ToList()
                    })
                    .ToList(); // Force evaluation to catch any errors

                var result = new List<EligiblePropertyDto>();

                foreach (var group in groupedPayments)
                {
                    try
                    {
                        // Skip if property or fiscal year is null
                        if (group.Property == null || group.FiscalYear == null)
                        {
                            Console.WriteLine($"Skipping property {group.PropertyId} due to null property or fiscal year");
                            continue;
                        }

                        // Calculate tax amount for this property
                        var calculatedValue = _gisService.CalculatePropertyValue(
                            group.Property,
                            group.Property.Municipality?.ValuationRulesConfigJson ?? "{}");

                        var taxAmount = _gisService.CalculateTaxAmount(
                            calculatedValue,
                            group.Property.Municipality?.TaxSlabsConfigJson ?? "{}",
                            group.Property.Municipality?.ExemptionRulesConfigJson ?? "{}");

                        if (group.TotalPaid < taxAmount)
                        {
                            result.Add(new EligiblePropertyDto
                            {
                                PropertyId = group.PropertyId,
                                FiscalYearId = group.FiscalYearId,
                                PropertyAddress = group.Property.Address ?? "Unknown Address",
                                FiscalYearName = group.FiscalYear.Name ?? "Unknown Fiscal Year",
                                CalculatedTaxAmount = taxAmount,
                                AmountPaid = group.TotalPaid,
                                UnpaidBalance = taxAmount - group.TotalPaid,
                                PaymentIds = group.Payments.Select(p => p.PaymentId).ToList()
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue processing other properties
                        Console.WriteLine($"Error processing property {group.PropertyId}: {ex.Message}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetEligibleProperties: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                // Return empty list instead of throwing to prevent 500 errors
                return new List<EligiblePropertyDto>();
            }
        }

        /// <summary>
        /// Reconciles payments with an assessment
        /// </summary>
        /// <param name="paymentIds">List of payment IDs to reconcile</param>
        /// <param name="assessmentId">The assessment ID to link the payments to</param>
        public async Task ReconcilePayments(List<Guid> paymentIds, Guid assessmentId)
        {
            var payments = await _context.Payments
                .Where(p => paymentIds.Contains(p.PaymentId))
                .ToListAsync();

            foreach (var payment in payments)
            {
                payment.AssessmentId = assessmentId;
                payment.IsReconciled = true;
                payment.UpdatedAt = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
        }
    }
}
