import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../services/api";
import type { Property, Assessment } from "../types";
import type { MockProperty, MockAssessment } from "../types/mock";
import { toFullProperty, toFullAssessment } from "../types/mock";
import { AdminLayout } from "../components/admin";

interface PropertyWithAssessment extends Property {
  assessment?: Assessment;
  selected: boolean;
}

const BulkTaxPayment: React.FC = () => {
  const [properties, setProperties] = useState<PropertyWithAssessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [error, setError] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    loadProperties();
  }, []);

  const loadProperties = async () => {
    try {
      setLoading(true);
      // Get properties that are approved and have tax due
      const response = await api.get("/properties/mine");

      // Filter properties that are approved and have tax due
      const filteredProperties = response.data
        .filter(
          (prop: Property) =>
            prop.status === "Approved" && prop.taxDue && prop.taxDue > 0
        )
        .map((prop: Property) => ({
          ...prop,
          id: prop.id,
          landArea: prop.landAreaSqM || prop.landArea,
          builtUpArea: prop.buildingBuiltUpAreaSqM || prop.builtUpArea,
          selected: false,
        }));

      // For each property, get the latest pending assessment
      const propertiesWithAssessments = await Promise.all(
        filteredProperties.map(async (property: PropertyWithAssessment) => {
          try {
            // Use the existing endpoint to get all assessments for the property
            const assessmentResponse = await api.get(
              `/assessments/property/${property.id}`
            );

            // Filter for pending assessments and get the most recent one
            const pendingAssessments = assessmentResponse.data
              .filter(
                (assessment: Assessment) =>
                  assessment.paymentStatus === "Pending"
              )
              .sort(
                (a: Assessment, b: Assessment) =>
                  new Date(b.assessmentDate).getTime() -
                  new Date(a.assessmentDate).getTime()
              );

            return {
              ...property,
              assessment:
                pendingAssessments.length > 0
                  ? pendingAssessments[0]
                  : undefined,
            };
          } catch (error) {
            console.error(
              `Failed to load assessment for property ${property.id}:`,
              error
            );
            // If no assessment is found, we'll just return the property without an assessment
            return property;
          }
        })
      );

      setProperties(propertiesWithAssessments);
    } catch (error) {
      console.error("Failed to load properties:", error);
      setError("Failed to load your properties. Please try again later.");

      // For demo, create mock data
      const mockData = [
        {
          id: "1",
          ownerUserId: "1",
          municipalityId: "1",
          municipalityName: "Kathmandu Metropolitan City",
          address: "123 Main Street, Ward 5, Kathmandu",
          landAreaSqM: 500.5,
          landArea: 500.5,
          usageType: "Residential",
          builtUpArea: 300.25,
          registrationDate: "2024-01-15T10:30:00Z",
          status: "Approved",
          isDefaulter: false,
          assessedValue: 15000000,
          taxDue: 75000,
          selected: false,
          mockAssessment: {
            id: "1",
            propertyId: "1",
            assessmentYear: 2024,
            calculatedValue: 15000000,
            finalAssessedValue: 15000000,
            taxAmount: 75000,
            assessmentDate: "2024-01-20T14:00:00Z",
            paymentStatus: "Pending",
          },
        },
        {
          id: "2",
          ownerUserId: "1",
          municipalityId: "1",
          municipalityName: "Kathmandu Metropolitan City",
          address: "456 Park Avenue, Ward 7, Kathmandu",
          landAreaSqM: 750.0,
          landArea: 750.0,
          usageType: "Commercial",
          builtUpArea: 500.0,
          registrationDate: "2024-02-10T09:15:00Z",
          status: "Approved",
          isDefaulter: false,
          assessedValue: 25000000,
          taxDue: 125000,
          selected: false,
          mockAssessment: {
            id: "2",
            propertyId: "2",
            assessmentYear: 2024,
            calculatedValue: 25000000,
            finalAssessedValue: 25000000,
            taxAmount: 125000,
            assessmentDate: "2024-02-15T11:30:00Z",
            paymentStatus: "Pending",
          },
        },
      ];

      const mockProperties: PropertyWithAssessment[] = mockData.map((item) => ({
        ...toFullProperty(item as MockProperty),
        selected: item.selected,
        assessment: item.mockAssessment
          ? toFullAssessment(item.mockAssessment as MockAssessment)
          : undefined,
      }));

      setProperties(mockProperties);
    } finally {
      setLoading(false);
    }
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setProperties(
      properties.map((property) => ({
        ...property,
        selected: newSelectAll,
      }))
    );
  };

  const togglePropertySelection = (propertyId: string) => {
    setProperties(
      properties.map((property) =>
        property.id === propertyId
          ? { ...property, selected: !property.selected }
          : property
      )
    );

    // Update selectAll state based on if all properties are selected
    const allSelected = properties.every((p) =>
      p.id === propertyId ? !p.selected : p.selected
    );
    setSelectAll(allSelected);
  };

  const calculateTotalAmount = () => {
    return properties
      .filter((property) => property.selected)
      .reduce(
        (total, property) =>
          total + (property.assessment?.taxAmount || property.taxDue || 0),
        0
      );
  };

  const handlePayment = async () => {
    const selectedProperties = properties.filter(
      (property) => property.selected
    );

    if (selectedProperties.length === 0) {
      setError("Please select at least one property to pay tax for.");
      return;
    }

    setProcessing(true);
    setError("");

    try {
      // Mock payment processing
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // In a real application, this would integrate with a payment gateway
      const totalAmount = calculateTotalAmount();
      const paymentData = {
        propertyIds: selectedProperties.map((property) => property.id),
        assessmentIds: selectedProperties
          .filter((property) => property.assessment)
          .map((property) => property.assessment?.id),
        amount: totalAmount,
        paymentMethod,
        // Mock transaction details
        transactionId: `TXN_${Date.now()}`,
        gateway:
          paymentMethod === "card" ? "Mock Card Gateway" : "Mock Bank Transfer",
      };

      // Submit payment to backend
      // In a real implementation, we would call the API:
      // await api.post("/payments/bulk", paymentData);
      console.log("Payment data:", paymentData);

      setPaymentSuccess(true);
    } catch (error: unknown) {
      setError(
        error instanceof Error
          ? error.message
          : "Payment processing failed. Please try again."
      );
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `NPR ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <AdminLayout title="Bulk Tax Payment">
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </AdminLayout>
    );
  }

  if (paymentSuccess) {
    return (
      <AdminLayout title="Payment Successful">
        <div className="max-w-2xl mx-auto">
          <div className="alert alert-success">
            <div className="text-center">
              <div className="text-success text-6xl mb-4">✓</div>
              <h2 className="text-2xl font-bold mb-2">
                Bulk Payment Successful!
              </h2>
              <p className="mb-6">
                Your tax payments have been processed successfully for{" "}
                {properties.filter((p) => p.selected).length} properties. You
                should receive a confirmation receipt shortly.
              </p>
            </div>
            <div className="card bg-base-100 shadow-xl p-4 mb-6 text-left">
              <h3 className="font-semibold mb-3">Payment Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Properties Paid:</span>
                  <span className="text-gray-900">
                    {properties.filter((p) => p.selected).length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Assessment Year:</span>
                  <span className="text-gray-900">
                    {new Date().getFullYear()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Amount Paid:</span>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(calculateTotalAmount())}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="text-gray-900">TXN_{Date.now()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Date:</span>
                  <span className="text-gray-900">
                    {formatDate(new Date().toISOString())}
                  </span>
                </div>
              </div>
            </div>
            <div className="space-x-4">
              <Link to="/properties" className="btn btn-primary">
                Back to Properties
              </Link>
              <button
                onClick={() => window.print()}
                className="btn btn-secondary"
              >
                Print Receipt
              </button>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error && properties.length === 0) {
    return (
      <AdminLayout title="Bulk Tax Payment">
        <div className="alert alert-error">
          <h2 className="text-xl font-semibold mb-2">
            Unable to Load Properties
          </h2>
          <p>{error}</p>
          <Link to="/properties" className="btn btn-primary mt-4 inline-block">
            Back to Properties
          </Link>
        </div>
      </AdminLayout>
    );
  }

  if (properties.length === 0) {
    return (
      <AdminLayout title="Bulk Tax Payment">
        <div className="alert alert-warning">
          <h2 className="text-xl font-semibold mb-2">
            No Properties with Pending Tax
          </h2>
          <p>You don't have any properties with pending tax payments.</p>
          <Link to="/properties" className="btn btn-primary mt-4 inline-block">
            Back to Properties
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Bulk Tax Payment"
      subtitle="Pay tax for multiple properties at once"
    >
      <div className="card-body">
        <div className="grid grid-cols-1 gap-8">
          {/* Property Selection */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Select Properties</h2>
              <div className="form-control">
                <label className="label cursor-pointer">
                  <span className="label-text mr-2">Select All</span>
                  <input
                    type="checkbox"
                    id="selectAll"
                    checked={selectAll}
                    onChange={toggleSelectAll}
                    className="checkbox checkbox-primary"
                  />
                </label>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              {properties.map((property) => (
                <div
                  key={property.id}
                  className={`card ${
                    property.selected
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300"
                  } border`}
                >
                  <div className="card-body p-4">
                    <div className="flex items-start">
                      <input
                        type="checkbox"
                        id={`property-${property.id}`}
                        checked={property.selected}
                        onChange={() => togglePropertySelection(property.id)}
                        className="checkbox checkbox-primary mt-1 mr-3"
                      />
                      <div className="flex-1">
                        <label
                          htmlFor={`property-${property.id}`}
                          className="block font-medium mb-1 cursor-pointer"
                        >
                          {property.address}
                        </label>
                        <div className="grid grid-cols-2 gap-4 text-sm text-base-content/70">
                          <div>
                            <span className="font-medium">Municipality:</span>
                            <div>{property.municipalityName}</div>
                          </div>
                          <div>
                            <span className="font-medium">Land Area:</span>
                            <div>{property.landArea} sq.m</div>
                          </div>
                          <div>
                            <span className="font-medium">Usage Type:</span>
                            <div>{property.usageType}</div>
                          </div>
                          <div>
                            <span className="font-medium">Tax Due:</span>
                            <div className="font-semibold text-error">
                              {formatCurrency(
                                property.assessment?.taxAmount ||
                                  property.taxDue ||
                                  0
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Total Amount */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-blue-500">
                  Total Tax Amount:
                </span>
                <span className="text-lg font-bold text-blue-500">
                  {formatCurrency(calculateTotalAmount())}
                </span>
              </div>
            </div>

            {/* Payment Method */}
            <h2 className="text-lg font-semibold mb-4">Payment Method</h2>

            <div className="space-y-4 mb-6">
              <div>
                <label className="flex items-center space-x-3 p-4 border border-base-300 rounded-lg cursor-pointer hover:bg-base-200">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="card"
                    checked={paymentMethod === "card"}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="radio radio-primary"
                  />
                  <div>
                    <div className="font-medium">Credit/Debit Card</div>
                    <div className="text-sm text-base-content/70">
                      Pay securely with your card
                    </div>
                  </div>
                </label>
              </div>

              <div>
                <label className="flex items-center space-x-3 p-4 border border-base-300 rounded-lg cursor-pointer hover:bg-base-200">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="bank"
                    checked={paymentMethod === "bank"}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="radio radio-primary"
                  />
                  <div>
                    <div className="font-medium">Bank Transfer</div>
                    <div className="text-sm text-base-content/70">
                      Direct transfer from your bank account
                    </div>
                  </div>
                </label>
              </div>

              <div>
                <label className="flex items-center space-x-3 p-4 border border-base-300 rounded-lg cursor-pointer hover:bg-base-200">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="wallet"
                    checked={paymentMethod === "wallet"}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="radio radio-primary"
                  />
                  <div>
                    <div className="font-medium">Digital Wallet</div>
                    <div className="text-sm text-base-content/70">
                      Pay with eSewa, Khalti, or IME Pay
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {paymentMethod === "card" && (
              <div className="space-y-4 mb-6">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Card Number</span>
                  </label>
                  <input
                    type="text"
                    placeholder="1234 5678 9012 3456"
                    className="input input-bordered"
                    disabled
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Expiry Date</span>
                    </label>
                    <input
                      type="text"
                      placeholder="MM/YY"
                      className="input input-bordered"
                      disabled
                    />
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">CVV</span>
                    </label>
                    <input
                      type="text"
                      placeholder="123"
                      className="input input-bordered"
                      disabled
                    />
                  </div>
                </div>
                <div className="form-control">
                  <label className="label">
                    <span className="label-text">Cardholder Name</span>
                  </label>
                  <input
                    type="text"
                    placeholder="John Doe"
                    className="input input-bordered"
                    disabled
                  />
                </div>
              </div>
            )}

            {/* Payment Notice */}
            <div className="alert alert-warning mb-6">
              <div className="flex">
                <div className="mr-3">⚠️</div>
                <div>
                  <h4 className="font-medium">Important Notice</h4>
                  <p className="text-sm mt-1">
                    This is a mock payment system for demonstration purposes. No
                    actual charges will be made. In a real system, this would
                    integrate with licensed payment gateways.
                  </p>
                </div>
              </div>
            </div>

            {error && (
              <div className="alert alert-error mb-4">
                <p>{error}</p>
              </div>
            )}

            <div className="flex space-x-4">
              <Link to="/properties" className="flex-1 btn btn-ghost">
                Cancel
              </Link>
              <button
                onClick={handlePayment}
                disabled={processing || calculateTotalAmount() === 0}
                className="flex-1 btn btn-primary"
              >
                {processing ? (
                  <div className="flex items-center justify-center">
                    <span className="loading loading-spinner loading-sm mr-2"></span>
                    Processing...
                  </div>
                ) : (
                  `Pay ${formatCurrency(calculateTotalAmount())}`
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default BulkTaxPayment;
