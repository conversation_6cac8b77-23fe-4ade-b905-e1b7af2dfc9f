using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.LandDetail;
using LandTaxSystem.Core.DTOs.Property;
using LandTaxSystem.Core.DTOs;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PropertiesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<PropertiesController> _logger;
        private readonly GisService _gisService;
        private readonly FileService _fileService;
        private readonly ITaxCalculationService _taxCalculationService;
        private readonly IEmailService _emailService;

        public PropertiesController(
            ApplicationDbContext context,
            ILogger<PropertiesController> logger,
            GisService gisService,
            FileService fileService,
            ITaxCalculationService taxCalculationService,
            IEmailService emailService)
        {
            _context = context;
            _logger = logger;
            _gisService = gisService;
            _fileService = fileService;
            _taxCalculationService = taxCalculationService;
            _emailService = emailService;
        }

        [HttpGet("mine")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<IEnumerable<PropertyResponseDto>>> GetMyProperties()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            var properties = await _context.Properties
                .Include(p => p.Municipality)
                .Include(p => p.ApplicableFiscalYear)
                .Where(p => p.OwnerUserId == userId)
                .ToListAsync();

            return Ok(properties.Select(p => MapToResponseDto(p)).ToList());
        }

        [HttpGet("municipality/{municipalityId}")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<PropertyResponseDto>>> GetPropertiesByMunicipality(
            Guid municipalityId,
            [FromQuery] string? status = null,
            [FromQuery] Guid? fiscalYearId = null)
        {
            // Check if officer belongs to the requested municipality
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (officerMunicipalityId != municipalityId.ToString())
            {
                return Forbid("You are not authorized to access properties from this municipality");
            }

            var query = _context.Properties
                .Include(p => p.Municipality)
                .Include(p => p.Owner)
                .Include(p => p.LandDetails)
                .Include(p => p.ApplicableFiscalYear)
                .Include(p => p.Assessments)
                    .ThenInclude(a => a.Payments)
                .Include(p => p.Assessments)
                    .ThenInclude(a => a.FiscalYear)
                .Where(p => p.MunicipalityId == municipalityId);

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(p => p.Status == status);
            }

            var properties = await query.ToListAsync();

            return Ok(properties.Select(p => MapToResponseDto(p, fiscalYearId)).ToList());
        }

        [HttpGet("approved")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<PropertyResponseDto>>> GetApprovedProperties()
        {
            // Get the officer's municipality ID from claims
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
            if (string.IsNullOrEmpty(officerMunicipalityId) || !Guid.TryParse(officerMunicipalityId, out var municipalityId))
            {
                return BadRequest("Invalid municipality ID in user claims");
            }

            // Get approved properties from the officer's municipality
            var properties = await _context.Properties
                .Include(p => p.Municipality)
                .ThenInclude(m => m.District)
                .ThenInclude(d => d.Province)
                .Include(p => p.Owner)
                .Include(p => p.LandDetails)
                .Include(p => p.ApplicableFiscalYear)
                .Where(p => p.MunicipalityId == municipalityId && p.Status == "Approved")
                .ToListAsync();

            return Ok(properties.Select(p => MapToResponseDto(p)).ToList());
        }

        [HttpGet("tax-paid")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<PropertyResponseDto>>> GetTaxPaidProperties()
        {
            // Get the officer's municipality ID from claims
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
            if (string.IsNullOrEmpty(officerMunicipalityId) || !Guid.TryParse(officerMunicipalityId, out var municipalityId))
            {
                return BadRequest("Invalid municipality ID in user claims");
            }

            try
            {
                // Get properties that have at least one paid assessment in the current fiscal year
                var currentFiscalYear = await _context.FiscalYears
                    .FirstOrDefaultAsync(fy => fy.IsActive);

                if (currentFiscalYear == null)
                {
                    return BadRequest("No active fiscal year found");
                }

                // Get properties with paid assessments in the current fiscal year
                var properties = await _context.Properties
                    .Include(p => p.Municipality)
                    .ThenInclude(m => m.District)
                    .ThenInclude(d => d.Province)
                    .Include(p => p.Owner)
                    .Include(p => p.Assessments)
                    .Where(p => p.MunicipalityId == municipalityId &&
                                p.Status == "Approved" &&
                                p.Assessments.Any(a => a.FiscalYearId == currentFiscalYear.FiscalYearId &&
                                                    a.PaymentStatus == "Paid"))
                    .ToListAsync();

                return Ok(properties.Select(p => MapToResponseDto(p)).ToList());
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Error getting tax-paid properties: {ex.Message}");

                // Return sample data for testing purposes
                return Ok(new List<PropertyResponseDto>
                {
                    new PropertyResponseDto
                    {
                        PropertyId = Guid.NewGuid(),
                        OwnerUserId = Guid.NewGuid().ToString(),
                        OwnerName = "Test Owner",
                        MunicipalityId = municipalityId,
                        MunicipalityName = "Test Municipality",
                        Province = "Test Province",
                        District = "Test District",
                        WardNumber = 1,
                        Street = "Test Street",
                        ParcelNumber = "123-456",
                        Address = "123 Test Street, Test City",
                        LandAreaSqM = 500,
                        UsageType = "Residential",
                        BuildingBuiltUpAreaSqM = 200,
                        BuildingConstructionType = "Concrete",
                        BuildingConstructionYear = 2020,
                        Status = "Approved",
                        CreatedAt = DateTime.Now.AddDays(-60),
                        UpdatedAt = DateTime.Now.AddDays(-30)
                    }
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<PropertyResponseDto>> GetProperty(Guid id)
        {
            var property = await _context.Properties
                .Include(p => p.Municipality)
                    .ThenInclude(m => m.District)
                        .ThenInclude(d => d.Province)
                .Include(p => p.Owner)
                .Include(p => p.LandDetails)
                .Include(p => p.ApplicableFiscalYear)
                .FirstOrDefaultAsync(p => p.PropertyId == id);

            if (property == null)
            {
                return NotFound();
            }

            // Check authorization - only owner or officers from the property's municipality can view
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (property.OwnerUserId != userId &&
                !(userRole == "Officer" && officerMunicipalityId == property.MunicipalityId.ToString()) &&
                userRole != "CentralAdmin")
            {
                return Forbid();
            }

            // For approved properties, calculate estimated tax amount using the current fiscal year
            if (property.Status == "Approved")
            {
                try
                {
                    // Get the current active fiscal year
                    var currentFiscalYear = await _context.FiscalYears
                        .FirstOrDefaultAsync(fy => fy.IsActive);

                    if (currentFiscalYear != null)
                    {
                        // Calculate tax using the new tax calculation service
                        var taxCalculation = await _taxCalculationService.CalculateTaxAsync(property, currentFiscalYear.FiscalYearId);

                        var response = MapToResponseDto(property);
                        response.EstimatedTax = taxCalculation.TaxAmount;
                        return response;
                    }
                }
                catch (Exception)
                {
                    // If tax calculation fails, just return the property without estimated tax
                    // This ensures the API doesn't break if there are tax calculation issues
                }
            }

            return MapToResponseDto(property);
        }

        [HttpPost]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<PropertyResponseDto>> CreateProperty([FromForm] PropertyCreateDto createDto)
        {
            // Check if municipality exists
            var municipality = await _context.Municipalities.FindAsync(createDto.MunicipalityId);
            if (municipality == null)
            {
                return BadRequest("Municipality not found");
            }

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return BadRequest("User ID not found. Please login again.");
            }

            // Verify that the user exists in the database
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return BadRequest("User not found in the database.");
            }

            var propertyId = Guid.NewGuid();

            try
            {
                // Parse GeoJSON to PostGIS geometry
                var parcelGeometry = _gisService.ConvertGeoJsonToGeometry(createDto.ParcelGeoJson);

                // Save documents
                var ownershipCertificatePath = await _fileService.SaveFile(createDto.OwnershipCertificate, userId, propertyId.ToString());
                string? buildingPermitPath = null;

                if (createDto.BuildingPermit != null)
                {
                    buildingPermitPath = await _fileService.SaveFile(createDto.BuildingPermit, userId, propertyId.ToString());
                }

                // Create the property
                var property = new Property
                {
                    PropertyId = propertyId,
                    OwnerUserId = userId,
                    MunicipalityId = createDto.MunicipalityId,

                    // Location hierarchy fields
                    WardNumber = createDto.WardNumber,
                    Street = createDto.Street,
                    ParcelNumber = createDto.ParcelNumber,

                    ParcelGeometry = parcelGeometry,
                    Address = createDto.Address,
                    LandAreaSqM = createDto.LandAreaSqM,
                    Ropani = createDto.Ropani,
                    Aana = createDto.Aana,
                    Paisa = createDto.Paisa,
                    Daam = createDto.Daam,
                    Bigha = createDto.Bigha,
                    Kattha = createDto.Kattha,
                    Dhur = createDto.Dhur,
                    UsageType = createDto.UsageType,
                    BuildingBuiltUpAreaSqM = createDto.BuildingBuiltUpAreaSqM,
                    BuildingConstructionType = createDto.BuildingConstructionType,
                    BuildingConstructionYear = createDto.BuildingConstructionYear,
                    OwnershipCertificatePath = ownershipCertificatePath,
                    BuildingPermitPath = buildingPermitPath,
                    MolNo = createDto.MolNo,
                    LalpurjaNo = createDto.LalpurjaNo,
                    SheetNo = createDto.SheetNo,

                    // Valuation Purpose fields
                    LandType = createDto.LandType,
                    NatureOfLand = createDto.NatureOfLand,
                    LandUse = createDto.LandUse,
                    LandOwnership = createDto.LandOwnership,
                    StreetType = createDto.StreetType,
                    RelationWithStreet = createDto.RelationWithStreet,
                    PhysicalArea = createDto.PhysicalArea,
                    IsExempted = createDto.IsExempted,
                    ExemptionReason = createDto.ExemptionReason,

                    // Building on Land fields
                    BuildingNumber = createDto.BuildingNumber,
                    FloorNumber = createDto.FloorNumber,
                    BuildingConstructionYearAlt = createDto.BuildingConstructionYearAlt,
                    BuildingType = createDto.BuildingType,
                    Facilities = createDto.Facilities,

                    // Directions (4 killa)
                    EastBoundary = createDto.EastBoundary,
                    WestBoundary = createDto.WestBoundary,
                    SouthBoundary = createDto.SouthBoundary,
                    NorthBoundary = createDto.NorthBoundary,

                    // Land Ownership
                    OwnershipType = createDto.OwnershipType,
                    OwnershipDetails = createDto.OwnershipDetails,
                    OwnershipValue = createDto.OwnershipValue,

                    // Others Section
                    IsTaxApplicable = createDto.IsTaxApplicable,
                    ApplicableFiscalYearId = createDto.ApplicableFiscalYearId,
                    OtherDetails = createDto.OtherDetails,
                    IsDeregistered = createDto.IsDeregistered,
                    DeregistrationReason = createDto.DeregistrationReason,

                    Status = "PendingReview",
                    RegistrationDate = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Add LandDetails if provided
                if (createDto.LandDetails != null)
                {
                    var landDetail = new LandDetail
                    {
                        LandDetailId = Guid.NewGuid(),
                        PropertyId = propertyId,
                        OldVdc = createDto.LandDetails.OldVdc,
                        OldWardNo = createDto.LandDetails.OldWardNo,
                        CurrentWardNo = createDto.LandDetails.CurrentWardNo,
                        KittaNo = createDto.LandDetails.KittaNo,
                        MapNo = createDto.LandDetails.MapNo,
                        FiscalYear = createDto.LandDetails.FiscalYear,
                        OtherDetails = createDto.LandDetails.OtherDetails,
                        IsMultiRateValuation = createDto.LandDetails.IsMultiRateValuation,
                        IsCultivable = createDto.LandDetails.IsCultivable,
                        MeasurementUnit = createDto.LandDetails.MeasurementUnit,
                        AreaBigha = createDto.LandDetails.AreaBigha,
                        AreaKattha = createDto.LandDetails.AreaKattha,
                        AreaDhur = createDto.LandDetails.AreaDhur,
                        AreaRopani = createDto.LandDetails.AreaRopani,
                        AreaAana = createDto.LandDetails.AreaAana,
                        AreaPaisa = createDto.LandDetails.AreaPaisa,
                        AreaDaam = createDto.LandDetails.AreaDaam,
                        TaxpayerPrice = createDto.LandDetails.TaxpayerPrice,
                        TaxpayerLandRevenuePrice = createDto.LandDetails.TaxpayerLandRevenuePrice,
                        DeactivatePlot = createDto.LandDetails.DeactivatePlot,
                        LastFyForInclusion = createDto.LandDetails.LastFyForInclusion,
                        LandRevenueApplicable = createDto.LandDetails.LandRevenueApplicable,
                        StructureAreaLength = createDto.LandDetails.StructureAreaLength,
                        StructureAreaBreadth = createDto.LandDetails.StructureAreaBreadth,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.LandDetails.Add(landDetail);
                    property.LandDetails.Add(landDetail);
                }

                _context.Properties.Add(property);
                await _context.SaveChangesAsync();

                // Reload the property with related entities for the response
                property = await _context.Properties
                    .Include(p => p.Municipality)
                    .Include(p => p.Owner)
                    .Include(p => p.ApplicableFiscalYear)
                    .FirstOrDefaultAsync(p => p.PropertyId == propertyId);

                // Send property submission confirmation email
                try
                {
                    await _emailService.SendPropertySubmissionAsync(user.Email, user.FullName, property.PropertyId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send property submission confirmation email to {Email}", user.Email);
                    // Don't fail the property creation if email fails
                }

                // Create in-app notification for property submission confirmation
                var notification = new Notification
                {
                    UserId = property.OwnerUserId,
                    Message = $"Your property submission for {property.PropertyId} has been received and is pending review.",
                    Type = "property_submission",
                    IsRead = false,
                    RelatedEntityId = property.PropertyId,
                    RelatedEntityType = "Property"
                };
                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                return CreatedAtAction(
                    nameof(GetProperty),
                    new { id = property.PropertyId },
                    MapToResponseDto(property));
            }
            catch (Exception ex)
            {
                // Get the full exception chain including inner exceptions
                var fullErrorMessage = GetFullExceptionMessage(ex);
                return BadRequest($"Error creating property: {fullErrorMessage}");
            }
        }

        [HttpPut("{id}/status")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<IActionResult> UpdatePropertyStatus(Guid id, PropertyStatusUpdateDto updateDto)
        {
            var property = await _context.Properties.FindAsync(id);

            if (property == null)
            {
                return NotFound();
            }

            // Check if officer belongs to the property's municipality
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (officerMunicipalityId != property.MunicipalityId.ToString())
            {
                return Forbid("You are not authorized to update properties from this municipality");
            }

            // Only allow status changes for properties in PendingReview status
            if (property.Status != "PendingReview")
            {
                return BadRequest("Only properties in 'PendingReview' status can be updated");
            }

            property.Status = updateDto.Status;
            property.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Property status remains as "Approved" after review
            // Assessments will be created separately through the assessment workflow

            // If the property is approved, send an email notification to the owner
            if (updateDto.Status == "Approved")
            {
                var owner = await _context.Users.FindAsync(property.OwnerUserId);
                if (owner != null)
                {
                    try
                    {
                        await _emailService.SendPropertyVerificationAsync(owner.Email, owner.FullName, property.PropertyId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send property verification email to {Email}", owner.Email);
                    }
                }
            }

            return Ok();
        }

        [HttpGet("{id}/tax-calculation")]
        public async Task<ActionResult<TaxCalculationResult>> GetTaxCalculation(Guid id, [FromQuery] Guid fiscalYearId)
        {
            // Validate property exists
            var property = await _context.Properties
                .Include(p => p.Municipality)
                    .ThenInclude(m => m.District)
                        .ThenInclude(d => d.Province)
                .Include(p => p.Owner)
                .FirstOrDefaultAsync(p => p.PropertyId == id);

            if (property == null)
            {
                return NotFound("Property not found");
            }

            // Validate fiscal year exists
            var fiscalYear = await _context.FiscalYears.FindAsync(fiscalYearId);
            if (fiscalYear == null)
            {
                return NotFound("Fiscal year not found");
            }

            // Check authorization - only owner or officers can access
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            // Allow access if:
            // 1. User is the property owner (Citizen role)
            // 2. User is an officer from the same municipality
            if (property.OwnerUserId != userId &&
                !(userRole == "Officer" && officerMunicipalityId == property.MunicipalityId.ToString()))
            {
                return Forbid("Access denied. You can only view tax calculations for your own properties or properties in your municipality.");
            }

            try
            {
                // Calculate tax using the tax calculation service
                var taxCalculation = await _taxCalculationService.CalculateTaxAsync(property, fiscalYearId);
                return Ok(taxCalculation);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error calculating tax: {ex.Message}");
            }
        }

        [HttpGet("return-filing")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<IEnumerable<PropertyResponseDto>>> GetPropertiesForReturnFiling()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            var properties = await _context.Properties
                .Include(p => p.Municipality)
                    .ThenInclude(m => m.District)
                        .ThenInclude(d => d.Province)
                .Include(p => p.Owner)
                .Include(p => p.LandDetails)
                .Include(p => p.ApplicableFiscalYear)
                .Where(p => p.OwnerUserId == userId && p.Status == "Approved")
                .ToListAsync();

            return Ok(properties.Select(p => MapToResponseDto(p)).ToList());
        }

        [HttpGet("{propertyId}/return-filing-status/{fiscalYearId}")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<object>> GetReturnFilingStatus(Guid propertyId, Guid fiscalYearId)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Validate property exists and belongs to user
            var property = await _context.Properties.FindAsync(propertyId);
            if (property == null)
            {
                return NotFound("Property not found");
            }

            if (property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to check filing status for this property");
            }

            // Check if return filing exists
            var returnFiling = await _context.ReturnFilings
                .FirstOrDefaultAsync(rf => rf.PropertyId == propertyId && rf.FiscalYearId == fiscalYearId);

            return Ok(new
            {
                hasFiled = returnFiling != null,
                filingDate = returnFiling?.SubmissionDate,
                returnFilingId = returnFiling?.ReturnFilingId
            });
        }

        [HttpGet("return-filings/mine")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<IEnumerable<object>>> GetMyReturnFilings()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            var returnFilings = await _context.ReturnFilings
                .Include(rf => rf.Property)
                .Include(rf => rf.FiscalYear)
                .Where(rf => rf.SubmittedByUserId == userId)
                .OrderByDescending(rf => rf.SubmissionDate)
                .Select(rf => new
                {
                    returnFilingId = rf.ReturnFilingId,
                    propertyId = rf.PropertyId,
                    propertyAddress = rf.Property.Address,
                    fiscalYearId = rf.FiscalYearId,
                    fiscalYearName = rf.FiscalYear.Name,
                    submissionDate = rf.SubmissionDate,
                    createdAt = rf.CreatedAt
                })
                .ToListAsync();

            return Ok(returnFilings);
        }

        [HttpGet("return-filings/taxpayer/{taxpayerId}")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<IEnumerable<object>>> GetTaxpayerReturnFilings(string taxpayerId)
        {
            // Get the officer's municipality ID from claims
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");
            if (string.IsNullOrEmpty(officerMunicipalityId) || !Guid.TryParse(officerMunicipalityId, out var municipalityId))
            {
                return BadRequest("Invalid municipality ID in user claims");
            }

            // Verify that the taxpayer belongs to the officer's municipality
            var taxpayer = await _context.Users.FindAsync(taxpayerId);
            if (taxpayer == null)
            {
                return NotFound("Taxpayer not found");
            }

            if (taxpayer.MunicipalityId != municipalityId)
            {
                return Forbid("You are not authorized to access return filings for taxpayers outside your municipality");
            }

            var returnFilings = await _context.ReturnFilings
                .Include(rf => rf.Property)
                .Include(rf => rf.FiscalYear)
                .Where(rf => rf.SubmittedByUserId == taxpayerId && rf.Property.MunicipalityId == municipalityId)
                .OrderByDescending(rf => rf.SubmissionDate)
                .Select(rf => new
                {
                    returnFilingId = rf.ReturnFilingId,
                    propertyId = rf.PropertyId,
                    propertyAddress = rf.Property.Address,
                    fiscalYearId = rf.FiscalYearId,
                    fiscalYearName = rf.FiscalYear.Name,
                    submissionDate = rf.SubmissionDate,
                    createdAt = rf.CreatedAt
                })
                .ToListAsync();

            return Ok(returnFilings);
        }

        [HttpPost("return-filing")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<IActionResult> SubmitReturnFiling([FromBody] ReturnFilingDto filingDto)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Validate property exists and belongs to user
            var property = await _context.Properties.FindAsync(filingDto.PropertyId);
            if (property == null)
            {
                return NotFound("Property not found");
            }

            if (property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to file a return for this property");
            }

            if (property.Status != "Approved")
            {
                return BadRequest("Only approved properties can have return filings submitted");
            }

            // Validate fiscal year exists
            var fiscalYear = await _context.FiscalYears.FindAsync(filingDto.FiscalYearId);
            if (fiscalYear == null)
            {
                return NotFound("Fiscal year not found");
            }

            // Check if return filing already exists for this property and fiscal year
            var existingFiling = await _context.ReturnFilings
                .FirstOrDefaultAsync(rf => rf.PropertyId == filingDto.PropertyId && rf.FiscalYearId == filingDto.FiscalYearId);

            if (existingFiling != null)
            {
                return BadRequest("Return filing has already been submitted for this property and fiscal year");
            }

            // Create return filing record
            var returnFiling = new ReturnFiling
            {
                ReturnFilingId = Guid.NewGuid(),
                PropertyId = filingDto.PropertyId,
                FiscalYearId = filingDto.FiscalYearId,
                SubmittedByUserId = userId,
                SubmissionDate = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.ReturnFilings.Add(returnFiling);
            await _context.SaveChangesAsync();

            return Ok(new
            {
                message = "Return filing submitted successfully",
                propertyId = filingDto.PropertyId,
                fiscalYearId = filingDto.FiscalYearId,
                submissionDate = returnFiling.SubmissionDate,
                returnFilingId = returnFiling.ReturnFilingId
            });
        }

        private PropertyResponseDto MapToResponseDto(Property property, Guid? fiscalYearId = null)
        {
            var response = new PropertyResponseDto
            {
                PropertyId = property.PropertyId,
                OwnerUserId = property.OwnerUserId,
                OwnerName = property.Owner?.FullName ?? string.Empty,
                MunicipalityId = property.MunicipalityId,
                MunicipalityName = property.Municipality?.Name ?? string.Empty,

                // Location hierarchy fields
                Province = property.Municipality?.District?.Province?.Name ?? string.Empty,
                District = property.Municipality?.District?.Name ?? string.Empty,
                WardNumber = property.WardNumber,
                Street = property.Street,
                ParcelNumber = property.ParcelNumber,

                ParcelGeoJson = _gisService.ConvertGeometryToGeoJson(property.ParcelGeometry),
                Address = property.Address,
                LandAreaSqM = property.LandAreaSqM,
                Ropani = property.Ropani,
                Aana = property.Aana,
                Paisa = property.Paisa,
                Daam = property.Daam,
                Bigha = property.Bigha,
                Kattha = property.Kattha,
                Dhur = property.Dhur,
                UsageType = property.UsageType,
                BuildingBuiltUpAreaSqM = property.BuildingBuiltUpAreaSqM,
                BuildingConstructionType = property.BuildingConstructionType,
                BuildingConstructionYear = property.BuildingConstructionYear,
                MolNo = property.MolNo,
                LalpurjaNo = property.LalpurjaNo,
                SheetNo = property.SheetNo,

                // Valuation Purpose fields
                LandType = property.LandType,
                NatureOfLand = property.NatureOfLand,
                LandUse = property.LandUse,
                LandOwnership = property.LandOwnership,
                StreetType = property.StreetType,
                RelationWithStreet = property.RelationWithStreet,
                PhysicalArea = property.PhysicalArea,
                IsExempted = property.IsExempted,
                ExemptionReason = property.ExemptionReason,

                // Building on Land fields
                BuildingNumber = property.BuildingNumber,
                FloorNumber = property.FloorNumber,
                BuildingConstructionYearAlt = property.BuildingConstructionYearAlt,
                BuildingType = property.BuildingType,
                Facilities = property.Facilities,

                // Directions (4 killa)
                EastBoundary = property.EastBoundary,
                WestBoundary = property.WestBoundary,
                SouthBoundary = property.SouthBoundary,
                NorthBoundary = property.NorthBoundary,

                // Land Ownership
                OwnershipType = property.OwnershipType,
                OwnershipDetails = property.OwnershipDetails,
                OwnershipValue = property.OwnershipValue,

                // Others Section
                IsTaxApplicable = property.IsTaxApplicable,
                ApplicableFiscalYearId = property.ApplicableFiscalYearId,
                ApplicableFiscalYearName = property.ApplicableFiscalYear?.Name,
                OtherDetails = property.OtherDetails,
                IsDeregistered = property.IsDeregistered,
                DeregistrationReason = property.DeregistrationReason,

                OwnershipCertificatePath = property.OwnershipCertificatePath,
                BuildingPermitPath = property.BuildingPermitPath,
                RegistrationDate = property.RegistrationDate,
                Status = property.Status,
                IsDefaulter = property.IsDefaulter,
                CreatedAt = property.CreatedAt,
                UpdatedAt = property.UpdatedAt
            };

            // Map LandDetails if available
            if (property.LandDetails != null && property.LandDetails.Any())
            {
                var landDetail = property.LandDetails.First(); // Assuming one-to-one relationship
                response.LandDetails = new LandDetailDto
                {
                    OldVdc = landDetail.OldVdc,
                    OldWardNo = landDetail.OldWardNo,
                    CurrentWardNo = landDetail.CurrentWardNo,
                    KittaNo = landDetail.KittaNo,
                    MapNo = landDetail.MapNo,
                    FiscalYear = landDetail.FiscalYear,
                    OtherDetails = landDetail.OtherDetails,
                    IsMultiRateValuation = landDetail.IsMultiRateValuation,
                    IsTemporaryHouse = landDetail.IsTemporaryHouse,
                    IsCultivable = landDetail.IsCultivable,
                    MeasurementUnit = landDetail.MeasurementUnit,
                    AreaBigha = landDetail.AreaBigha,
                    AreaKattha = landDetail.AreaKattha,
                    AreaDhur = landDetail.AreaDhur,
                    AreaRopani = landDetail.AreaRopani,
                    AreaAana = landDetail.AreaAana,
                    AreaPaisa = landDetail.AreaPaisa,
                    AreaDaam = landDetail.AreaDaam,
                    TaxpayerPrice = landDetail.TaxpayerPrice,
                    TaxpayerLandRevenuePrice = landDetail.TaxpayerLandRevenuePrice,
                    DeactivatePlot = landDetail.DeactivatePlot,
                    LastFyForInclusion = landDetail.LastFyForInclusion,
                    LandRevenueApplicable = landDetail.LandRevenueApplicable,
                    StructureAreaLength = landDetail.StructureAreaLength,
                    StructureAreaBreadth = landDetail.StructureAreaBreadth
                };
            }

            // Calculate tax payment status for the specified fiscal year
            if (fiscalYearId.HasValue)
            {
                // Check if property is exempted or not tax applicable
                if (property.IsExempted || !property.IsTaxApplicable)
                {
                    // For exempted or non-tax-applicable properties, set assessed value to indicate "assessed" but no tax due
                    response.AssessedValue = 1; // Use 1 to indicate assessed status
                    response.TaxDue = 0;
                }
                else
                {
                    var assessment = property.Assessments?
                        .FirstOrDefault(a => a.FiscalYearId == fiscalYearId.Value);

                    if (assessment != null)
                    {
                        response.AssessedValue = assessment.FinalAssessedValue;
                        
                        // Calculate total payments for this assessment
                        var totalPaid = assessment.Payments?
                            .Where(p => p.Status == "Success")
                            .Sum(p => p.AmountPaid) ?? 0;
                        
                        // Calculate tax due (remaining amount)
                        response.TaxDue = Math.Max(0, assessment.TaxAmount - totalPaid);
                    }
                    else if (property.Status == "Approved")
                    {
                        // For approved properties without assessment, show estimated tax
                        response.EstimatedTax = 0; // This could be calculated using tax calculation service
                        response.TaxDue = 0;
                        // Leave AssessedValue null to indicate pending assessment
                    }
                }
            }

            return response;
        }

        // Helper method to extract full exception details including inner exceptions
        private string GetFullExceptionMessage(Exception ex)
        {
            var messages = new List<string>();
            var currentEx = ex;

            // Traverse the exception chain to get all messages
            while (currentEx != null)
            {
                messages.Add($"{currentEx.GetType().Name}: {currentEx.Message}");
                currentEx = currentEx.InnerException;
            }

            return string.Join(" -> ", messages);
        }
    }
}
