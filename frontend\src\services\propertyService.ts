import api from './api';
import type { ReturnFiling } from './api';

export interface Property {
    id: string;
    address: string;
    ownerName: string;
    registrationNumber: string;
    landArea: number;
    buildingArea?: number;
    propertyType: string;
    zone: string;
    municipality: string;
    district: string;
    province: string;
    createdAt: Date;
    updatedAt: Date;
}

export interface PropertyResponse {
    data: Property[];
    total: number;
    page: number;
    limit: number;
}

export interface PropertySearchParams {
    page?: number;
    limit?: number;
    search?: string;
    municipality?: string;
    district?: string;
    province?: string;
    propertyType?: string;
}

class PropertyService {
    async getAll(params?: PropertySearchParams): Promise<PropertyResponse> {
        const response = await api.get('/properties', { params });
        return response.data;
    }

    async getById(id: string): Promise<Property> {
        const response = await api.get(`/properties/${id}`);
        return response.data;
    }

    async getByRegistrationNumber(registrationNumber: string): Promise<Property> {
        const response = await api.get(`/properties/registration/${registrationNumber}`);
        return response.data;
    }

    async create(propertyData: Partial<Property>): Promise<Property> {
        const response = await api.post('/properties', propertyData);
        return response.data;
    }

    async update(id: string, propertyData: Partial<Property>): Promise<Property> {
        const response = await api.put(`/properties/${id}`, propertyData);
        return response.data;
    }

    async delete(id: string): Promise<void> {
        await api.delete(`/properties/${id}`);
    }

    async getTaxpayerReturnFilings(taxpayerId: string): Promise<ReturnFiling[]> {
        const response = await api.get(`/properties/return-filings/taxpayer/${taxpayerId}`);
        return response.data;
    }
}

const propertyService = new PropertyService();
export default propertyService;