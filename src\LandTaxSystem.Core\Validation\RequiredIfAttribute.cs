using System;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace LandTaxSystem.Core.Validation
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
    public class RequiredIfAttribute : ValidationAttribute
    {
        private string PropertyName { get; set; }
        private object DesiredValue { get; set; }

        public RequiredIfAttribute(string propertyName, object desiredValue = null)
        {
            PropertyName = propertyName;
            DesiredValue = desiredValue ?? true;
            ErrorMessage = "The {0} field is required when {1} is " + (desiredValue?.ToString() ?? "true");
        }

        protected override ValidationResult IsValid(object value, ValidationContext context)
        {
            var instance = context.ObjectInstance;
            var type = instance.GetType();
            
            var propertyValue = type.GetProperty(PropertyName)?.GetValue(instance, null);
            
            if (propertyValue != null && propertyValue.Equals(DesiredValue))
            {
                if (value == null || (value is string strValue && string.IsNullOrWhiteSpace(strValue)))
                {
                    return new ValidationResult(
                        string.Format(ErrorMessageString, context.DisplayName, PropertyName));
                }
            }

            return ValidationResult.Success;
        }
    }
}
