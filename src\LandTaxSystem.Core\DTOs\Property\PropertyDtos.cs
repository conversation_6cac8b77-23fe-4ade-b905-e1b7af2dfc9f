using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using LandTaxSystem.Core.DTOs.LandDetail;
using Microsoft.AspNetCore.Http;
using NetTopologySuite.Geometries;

namespace LandTaxSystem.Core.DTOs.Property
{
    public class PropertyCreateDto
    {
        [Required]
        public Guid MunicipalityId { get; set; }

        // Location hierarchy fields
        [Required, Range(1, 50)]
        public int WardNumber { get; set; }

        [MaxLength(100)]
        public string Street { get; set; } = string.Empty; // Street/Tole

        [Required, MaxLength(50)]
        public string ParcelNumber { get; set; } = string.Empty; // Kitta No

        [Required, MaxLength(255)]
        public string Address { get; set; } = string.Empty;

        [Required, Range(0.01, double.MaxValue)]
        public decimal LandAreaSqM { get; set; }

        // Nepali land measurement units
        public decimal? Ropani { get; set; }
        public decimal? Aana { get; set; }
        public decimal? Paisa { get; set; }
        public decimal? Daam { get; set; }
        public decimal? Bigha { get; set; }
        public decimal? Kattha { get; set; }
        public decimal? Dhur { get; set; }

        [Required]
        [RegularExpression("^(Residential|Commercial|Industrial|Agricultural)$",
            ErrorMessage = "Usage type must be one of: Residential, Commercial, Industrial, Agricultural")]
        public string UsageType { get; set; } = string.Empty;

        // Building details (optional)
        [Range(0.01, double.MaxValue)]
        public decimal? BuildingBuiltUpAreaSqM { get; set; }

        [RegularExpression("^(RCC|BrickMud|Other)$",
            ErrorMessage = "Construction type must be one of: RCC, BrickMud, Other")]
        public string? BuildingConstructionType { get; set; }

        [Range(1900, 2100)]
        public int? BuildingConstructionYear { get; set; }


        // Document reference numbers
        [MaxLength(100)]
        public string? MolNo { get; set; } // MOL (Ministry of Land) number

        [MaxLength(100)]
        public string? LalpurjaNo { get; set; } // Lalpurja number

        [MaxLength(50)]
        public string? SheetNo { get; set; } // Sheet number from land records

        // Valuation Purpose fields
        [MaxLength(100)]
        public string? LandType { get; set; }

        [MaxLength(100)]
        public string? NatureOfLand { get; set; }

        [MaxLength(100)]
        public string? LandUse { get; set; }

        [MaxLength(100)]
        public string? LandOwnership { get; set; }

        [MaxLength(100)]
        public string? StreetType { get; set; }

        [MaxLength(100)]
        public string? RelationWithStreet { get; set; }

        [MaxLength(100)]
        public string? PhysicalArea { get; set; }

        public bool IsExempted { get; set; } = false;

        [MaxLength(500)]
        public string? ExemptionReason { get; set; }

        // Building on Land fields
        [MaxLength(50)]
        public string? BuildingNumber { get; set; }

        [MaxLength(50)]
        public string? FloorNumber { get; set; }

        [Range(1900, 2100)]
        public int? BuildingConstructionYearAlt { get; set; }

        [MaxLength(100)]
        public string? BuildingType { get; set; }

        // Facilities in Land (JSON string)
        public string? Facilities { get; set; }

        // Directions (4 killa)
        [MaxLength(200)]
        public string? EastBoundary { get; set; }

        [MaxLength(200)]
        public string? WestBoundary { get; set; }

        [MaxLength(200)]
        public string? SouthBoundary { get; set; }

        [MaxLength(200)]
        public string? NorthBoundary { get; set; }

        // Land Ownership
        [MaxLength(100)]
        public string? OwnershipType { get; set; }

        [MaxLength(500)]
        public string? OwnershipDetails { get; set; }

        [MaxLength(100)]
        public string? OwnershipValue { get; set; }

        // Others Section
        public bool IsTaxApplicable { get; set; } = true;

        public Guid? ApplicableFiscalYearId { get; set; }

        [MaxLength(1000)]
        public string? OtherDetails { get; set; }

        public bool IsDeregistered { get; set; } = false;

        [MaxLength(500)]
        public string? DeregistrationReason { get; set; }

        // GIS parcel data
        [Required]
        public string ParcelGeoJson { get; set; } = string.Empty;

        // Documents
        [Required]
        public IFormFile OwnershipCertificate { get; set; } = null!;

        public IFormFile? BuildingPermit { get; set; }

        // Land details
        [Required]
        public CreateLandDetailDto LandDetails { get; set; } = null!;
    }

    public class PropertyResponseDto
    {
        public Guid PropertyId { get; set; }
        public string OwnerUserId { get; set; } = string.Empty;
        public string OwnerName { get; set; } = string.Empty;
        public Guid MunicipalityId { get; set; }
        public string MunicipalityName { get; set; } = string.Empty;
        
        // Location hierarchy fields
        public string Province { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public int WardNumber { get; set; }
        public string Street { get; set; } = string.Empty; // Street/Tole
        public string ParcelNumber { get; set; } = string.Empty; // Kitta No

        public string ParcelGeoJson { get; set; } = string.Empty;

        public string Address { get; set; } = string.Empty;
        public decimal LandAreaSqM { get; set; }

        // Nepali land measurement units
        public decimal? Ropani { get; set; }
        public decimal? Aana { get; set; }
        public decimal? Paisa { get; set; }
        public decimal? Daam { get; set; }
        public decimal? Bigha { get; set; }
        public decimal? Kattha { get; set; }
        public decimal? Dhur { get; set; }

        public string UsageType { get; set; } = string.Empty;

        // Building details
        public decimal? BuildingBuiltUpAreaSqM { get; set; }
        public string? BuildingConstructionType { get; set; }
        public int? BuildingConstructionYear { get; set; }

        // Document reference numbers
        public string? MolNo { get; set; } // MOL (Ministry of Land) number
        public string? LalpurjaNo { get; set; } // Lalpurja number
        public string? SheetNo { get; set; } // Sheet number from land records

        // Valuation Purpose fields
        public string? LandType { get; set; }
        public string? NatureOfLand { get; set; }
        public string? LandUse { get; set; }
        public string? LandOwnership { get; set; }
        public string? StreetType { get; set; }
        public string? RelationWithStreet { get; set; }
        public string? PhysicalArea { get; set; }
        public bool IsExempted { get; set; }
        public string? ExemptionReason { get; set; }

        // Building on Land fields
        public string? BuildingNumber { get; set; }
        public string? FloorNumber { get; set; }
        public int? BuildingConstructionYearAlt { get; set; }
        public string? BuildingType { get; set; }
        public string? Facilities { get; set; }

        // Directions (4 killa)
        public string? EastBoundary { get; set; }
        public string? WestBoundary { get; set; }
        public string? SouthBoundary { get; set; }
        public string? NorthBoundary { get; set; }

        // Land Ownership
        public string? OwnershipType { get; set; }
        public string? OwnershipDetails { get; set; }
        public string? OwnershipValue { get; set; }

        // Others Section
        public bool IsTaxApplicable { get; set; }
        public Guid? ApplicableFiscalYearId { get; set; }
        public string? ApplicableFiscalYearName { get; set; }
        public string? OtherDetails { get; set; }
        public bool IsDeregistered { get; set; }
        public string? DeregistrationReason { get; set; }

        // Document paths
        public string? OwnershipCertificatePath { get; set; }
        public string? BuildingPermitPath { get; set; }

        public DateTime RegistrationDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool IsDefaulter { get; set; }
        
        // Tax information
        public decimal? AssessedValue { get; set; }
        public decimal? TaxDue { get; set; }
        public decimal? EstimatedTax { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        // Land details
        public LandDetailDto? LandDetails { get; set; }
    }

    public class PropertyStatusUpdateDto
    {
        [Required]
        [RegularExpression("^(Approved|Rejected)$",
            ErrorMessage = "Status must be one of: Approved, Rejected")]
        public string Status { get; set; } = string.Empty;

        public string? Reason { get; set; }
    }
}
