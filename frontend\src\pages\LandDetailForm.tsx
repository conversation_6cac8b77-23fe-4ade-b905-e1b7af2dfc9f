import React from "react";
import type { FiscalYear } from "../services/taxConfigService";
import "./LandDetailForm.css"; // We can create this file for styling

export interface LandDetailFormState {
  oldVdc: string;
  oldWardNo: string;
  currentWardNo: string;
  kittaNo: string;
  molNo: string;
  mapNo: string;
  fiscalYear: string;
  otherDetails: string;
  isMultiRateValuation: boolean;
  isTemporaryHouse: boolean;
  isCultivable: boolean;
  measurementUnit: string;
  areaBigha: string;
  areaKattha: string;
  areaDhur: string;
  areaRopani: string;
  areaAana: string;
  areaPaisa: string;
  areaDaam: string;
  taxpayerPrice: string;
  taxpayerLandRevenuePrice: string;
  deactivatePlot: boolean;
  lastFyForInclusion: string;
  landRevenueApplicable: "no" | "full" | "partial";
  structureAreaLength: string; // Assuming तम्बाई is length
  structureAreaBreadth: string; // Assuming जोडाई is breadth
  // Valuation Details - Placeholder for now as it looks like a list
  // Malpot Details - Placeholder for now as it looks like a list
}

interface LandDetailFormProps {
  formData: LandDetailFormState;
  onFormDataChange: (fieldName: keyof LandDetailFormState, value: string | number | boolean) => void;
  fiscalYears: FiscalYear[];
}

const LandDetailForm: React.FC<LandDetailFormProps> = ({
  formData,
  onFormDataChange,
  fiscalYears,
}) => {
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      onFormDataChange(
        name as keyof LandDetailFormState,
        (e.target as HTMLInputElement).checked
      );
    } else {
      onFormDataChange(name as keyof LandDetailFormState, value);
    }
  };

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFormDataChange(
      "landRevenueApplicable",
      e.target.value as LandDetailFormState["landRevenueApplicable"]
    );
  };

  return (
    <div className="land-detail-form-container">
      <h2>जग्गाको विवरण प्रविष्टि</h2>
      <fieldset>
        <legend>सामान्य जानकारी</legend>
        <div className="form-grid">
          <div>
            <label htmlFor="oldVdc">पुरानो गा.बि.स.</label>
            <input
              type="text"
              id="oldVdc"
              name="oldVdc"
              value={formData.oldVdc}
              onChange={handleChange}
            />
          </div>
          <div>
            <label htmlFor="oldWardNo">पुरानो वडा नम्बर</label>
            <input
              type="text"
              id="oldWardNo"
              name="oldWardNo"
              value={formData.oldWardNo}
              onChange={handleChange}
            />
          </div>
          <div>
            <label htmlFor="currentWardNo">हालको वडा नम्बर</label>
            <input
              type="text"
              id="currentWardNo"
              name="currentWardNo"
              value={formData.currentWardNo}
              onChange={handleChange}
            />
          </div>
          <div>
            <label htmlFor="kittaNo">कित्ता नम्बर</label>
            <input
              type="text"
              id="kittaNo"
              name="kittaNo"
              value={formData.kittaNo}
              onChange={handleChange}
            />
          </div>
          <div>
            <label htmlFor="molNo">मोल नं</label>
            <input
              type="text"
              id="molNo"
              name="molNo"
              value={formData.molNo}
              onChange={handleChange}
            />
          </div>
          <div>
            <label htmlFor="mapNo">नक्सा नं</label>
            <input
              type="text"
              id="mapNo"
              name="mapNo"
              value={formData.mapNo}
              onChange={handleChange}
            />
          </div>
          <div>
            <label htmlFor="fiscalYear">आर्थिक वर्ष</label>
            <select
              id="fiscalYear"
              name="fiscalYear"
              value={formData.fiscalYear}
              onChange={handleChange}
            >
              <option value="">Select Fiscal Year</option>
              {fiscalYears.map((fy) => (
                <option key={fy.fiscalYearId} value={fy.name}>
                  {fy.name}
                </option>
              ))}
            </select>
          </div>
          <div className="form-grid-colspan-2">
            <label htmlFor="otherDetails">अन्य विशेष केहि भए</label>
            <textarea
              id="otherDetails"
              name="otherDetails"
              value={formData.otherDetails}
              onChange={handleChange}
              rows={3}
            ></textarea>
          </div>
        </div>
        <div className="checkbox-group">
          <label>
            <input
              type="checkbox"
              name="isMultiRateValuation"
              checked={formData.isMultiRateValuation}
              onChange={handleChange}
            />
            सम्पत्ति कर बहुदरमा मूल्यांकन हुने ?
          </label>
          <label>
            <input
              type="checkbox"
              name="isTemporaryHouse"
              checked={formData.isTemporaryHouse}
              onChange={handleChange}
            />
            कच्ची घर हो ?
          </label>
          <label>
            <input
              type="checkbox"
              name="isCultivable"
              checked={formData.isCultivable}
              onChange={handleChange}
            />
            खेती योग्य हो ?
          </label>
        </div>
      </fieldset>

      <fieldset>
        <legend>जग्गाको जम्मा क्षेत्रफल</legend>
        <div>
          <label htmlFor="measurementUnit">मापन एकाइ</label>
          <select
            id="measurementUnit"
            name="measurementUnit"
            value={formData.measurementUnit}
            onChange={handleChange}
          >
            <option value="ropani-aana-paisa-daam">रोपनी-आना-पैसा-दाम</option>
            <option value="bigha-kattha-dhur">बिघा-कठ्ठा-धुर</option>
            {/* Add other units */}
          </select>
        </div>
        <div className="area-inputs">
          {/* These are complex and might need specific handling based on selected unit */}
          <p>
            <strong>कुल क्षेत्रफल:</strong>
          </p>
          <div>
            <label>बिघा:</label>
            <input
              type="number"
              name="areaBigha"
              value={formData.areaBigha}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>कठ्ठा:</label>
            <input
              type="number"
              name="areaKattha"
              value={formData.areaKattha}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>धुर:</label>
            <input
              type="number"
              name="areaDhur"
              value={formData.areaDhur}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>रोपनी:</label>
            <input
              type="number"
              name="areaRopani"
              value={formData.areaRopani}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>आना:</label>
            <input
              type="number"
              name="areaAana"
              value={formData.areaAana}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>पैसा:</label>
            <input
              type="text"
              name="areaPaisa"
              value={formData.areaPaisa}
              onChange={handleChange}
            />
          </div>
          <div>
            <label>दाम:</label>
            <input
              type="text"
              name="areaDaam"
              value={formData.areaDaam}
              onChange={handleChange}
            />
          </div>
        </div>
        {/* Simplified representation of कुल क्षेत्रफल, मूल्यांकन हुने, मूल्यांकन नहुने */}
        <p>
          <em>
            नोट: कुल क्षेत्रफल, मूल्यांकन हुने, मूल्यांकन नहुने क्षेत्रका लागि
            विस्तृत इनपुट आवश्यक छ।
          </em>
        </p>

        <div>
          <label htmlFor="taxpayerPrice">
            करदाताले तोकेको मूल्य (प्रति रोपनी)
          </label>
          <input
            type="number"
            step="0.01"
            id="taxpayerPrice"
            name="taxpayerPrice"
            value={formData.taxpayerPrice}
            onChange={handleChange}
          />
        </div>
        <div>
          <label htmlFor="taxpayerLandRevenuePrice">
            करदाताले तोकेको मालपोत मूल्य (प्रति रोपनी)
          </label>
          <input
            type="number"
            step="0.01"
            id="taxpayerLandRevenuePrice"
            name="taxpayerLandRevenuePrice"
            value={formData.taxpayerLandRevenuePrice}
            onChange={handleChange}
          />
        </div>
        <div className="checkbox-group">
          <label>
            <input
              type="checkbox"
              name="deactivatePlot"
              checked={formData.deactivatePlot}
              onChange={handleChange}
            />
            कित्ता निस्कृय गर्ने हो ?
          </label>
        </div>
        <div>
          <label htmlFor="lastFyForInclusion">
            सम्पति करमा समावेश हुने अन्तिम आ.व.
          </label>
          <select
            id="lastFyForInclusion"
            name="lastFyForInclusion"
            value={formData.lastFyForInclusion}
            onChange={handleChange}
          >
            <option value="">-- छान्नुहोस् --</option>
            <option value="२०८०/२०८१">२०८०/२०८१</option>
            {/* Add more fiscal years */}
          </select>
        </div>
        <div className="radio-group">
          <p>मालपोत कर लाग्ने हो ?</p>
          <label>
            <input
              type="radio"
              name="landRevenueApplicable"
              value="no"
              checked={formData.landRevenueApplicable === "no"}
              onChange={handleRadioChange}
            />{" "}
            छैन
          </label>
          <label>
            <input
              type="radio"
              name="landRevenueApplicable"
              value="full"
              checked={formData.landRevenueApplicable === "full"}
              onChange={handleRadioChange}
            />{" "}
            सम्पूर्ण जग्गामा
          </label>
          <label>
            <input
              type="radio"
              name="landRevenueApplicable"
              value="partial"
              checked={formData.landRevenueApplicable === "partial"}
              onChange={handleRadioChange}
            />{" "}
            आंशिक जग्गामा
          </label>
        </div>
        <div>
          <label>संरचनाको क्षेत्रफल (तम्बाई x जोडाई)</label>
          <div style={{ display: "flex", gap: "10px" }}>
            <input
              type="text"
              name="structureAreaLength"
              placeholder="तम्बाई"
              value={formData.structureAreaLength}
              onChange={handleChange}
            />
            <input
              type="text"
              name="structureAreaBreadth"
              placeholder="जोडाई"
              value={formData.structureAreaBreadth}
              onChange={handleChange}
            />
          </div>
          <p>कुल क्षेत्रफल (वर्ग फिट): {/* Calculate and display */}</p>
        </div>
      </fieldset>

      <fieldset>
        <legend>सम्पतिको मूल्याङ्कन विवरण</legend>
        {/* This section would typically involve a list of items that can be added/removed */}
        <p>
          <em>
            यो खण्डमा मूल्याङ्कन विवरणहरूको सूची थप्न र व्यवस्थापन गर्न सकिन्छ।
            (उदाहरणका लागि, लागू हुने आर्थिक वर्ष, मूल्याङ्कन समूह, दररेट, आदि)
          </em>
        </p>
        <button type="button">नयाँ मूल्याङ्कन थप्नुहोस्</button>
      </fieldset>

      <fieldset>
        <legend>मालपोत कर विवरण</legend>
        {/* This section would also typically involve a list of items */}
        <p>
          <em>
            यो खण्डमा मालपोत कर विवरणहरूको सूची थप्न र व्यवस्थापन गर्न सकिन्छ।
          </em>
        </p>
        <button type="button">नयाँ मालपोत विवरण थप्नुहोस्</button>
      </fieldset>
    </div>
  );
};

export default LandDetailForm;