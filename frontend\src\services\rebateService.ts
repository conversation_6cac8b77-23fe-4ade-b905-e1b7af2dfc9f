import api from "./api";
import type { Rebate } from "../types/rebate.ts";

export const createRebate = async (rebateData: Omit<Rebate, "id">): Promise<Rebate> => {
  const response = await api.post("/rebates", rebateData);
  return response.data;
};

export const getRebates = async (): Promise<Rebate[]> => {
  const response = await api.get("/rebates");
  return response.data;
};

export const getRebateById = async (id: number): Promise<Rebate> => {
  const response = await api.get(`/rebates/${id}`);
  return response.data;
};
