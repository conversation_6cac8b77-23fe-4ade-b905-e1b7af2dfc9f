import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import AdminLayout from "../admin/AdminLayout";
import AdminForm from "../admin/AdminForm";
import { createRebate } from "../../services/rebateService";
import type { Rebate, RebateItem } from "../../types/rebate";

const RebateForm: React.FC = () => {
  const navigate = useNavigate();
  const [isReversal, setIsReversal] = useState(false);
  const [rebateItems, setRebateItems] = useState<RebateItem[]>([]);

  const [formData, setFormData] = useState({
    officeCode: "",
    pan: "",
    accountType: "",
    name: "",
    exemptionDate: "",
    serialNo: "",
    scheme: "",
    maNo: "",
    reason: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;
    setIsReversal(checked);
    if (!checked) {
      setFormData(prev => ({
        ...prev,
        maNo: "",
      }));
    }
  };

  const handleAddRebateItem = () => {
    const newItem: RebateItem = {
      id: rebateItems.length + 1,
      fiscalYear: "",
      filingPeriod: "",
      taxPeriod: "",
      totalExemptedAmount: 0,
      discountAmount: 0,
    };
    setRebateItems([...rebateItems, newItem]);
  };

  const handleRebateItemChange = (id: number, field: string, value: string | number) => {
    setRebateItems(prevItems => prevItems.map(item => (item.id === id ? { ...item, [field]: value } : item)));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const rebateData: Omit<Rebate, "id"> = {
        ...formData,
        isReversal,
        rebateItems,
      };
      await createRebate(rebateData);
      toast.success("Rebate saved successfully");
      navigate("/rebates");
    } catch (error) {
      console.error("Error saving rebate:", error);
      toast.error("Failed to save rebate");
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6 text-center">मिनाहा फारम (Rebate Form)</h1>

        <AdminForm onSubmit={handleSubmit}>
          <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <h2 className="text-xl font-semibold mb-4">मुख्य विवरण (Main Details)</h2>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              {/* Office Code */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">कार्यालय कोड:</label>
                <input
                  type="text"
                  id="officeCode"
                  name="officeCode"
                  value={formData.officeCode}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  readOnly
                />
              </div>

              {/* PAN */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">स्था. ले. नं.:</label>
                <input
                  type="text"
                  id="pan"
                  name="pan"
                  value={formData.pan}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Account Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">खाताको किसिम:</label>
                <input
                  type="text"
                  id="accountType"
                  name="accountType"
                  value={formData.accountType}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  readOnly
                />
              </div>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">नाम:</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Exemption Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">मिनाहा मिति:</label>
                <input
                  type="text"
                  id="exemptionDate"
                  name="exemptionDate"
                  value={formData.exemptionDate}
                  onChange={handleChange}
                  placeholder="YYYY.MM.DD"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Serial No */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">क्र.स.:</label>
                <input
                  type="text"
                  id="serialNo"
                  name="serialNo"
                  value={formData.serialNo}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>

              {/* Scheme */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">स्किम:</label>
                <select
                  id="scheme"
                  name="scheme"
                  value={formData.scheme}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  required
                >
                  <option value="">Select...</option>
                  {/* TODO: Populate schemes from an API call */}
                </select>
              </div>

              {/* Reversal Checkbox */}
              <div className="flex items-end">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isReversal"
                    checked={isReversal}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isReversal" className="ml-2 block text-sm text-gray-900">
                    Reversal
                  </label>
                </div>
              </div>

              {/* MA No */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">MA No:</label>
                <input
                  type="text"
                  id="maNo"
                  name="maNo"
                  value={formData.maNo}
                  onChange={handleChange}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:bg-gray-100"
                  disabled={!isReversal}
                  required={isReversal}
                />
              </div>
            </div>

            {/* Reason for Exemption */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">मिनाहा दिने कारण:</label>
              <textarea
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleChange}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              />
            </div>

            {/* Exemption Details Table */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium text-gray-900">मिनाहा दिने विवरण (Exemption Details)</h3>
                <div className="space-x-2">
                  <button
                    type="button"
                    onClick={handleAddRebateItem}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    + Add
                  </button>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Upload From Excel
                  </button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        क्र.स.
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        कर बर्ष
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        मा/चौ/दै
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        कर अवधि
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        मिनाहा कुल मा
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        छुट रकम
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {rebateItems.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                          No items added. Click 'Add' to insert a new row.
                        </td>
                      </tr>
                    ) : (
                      rebateItems.map(item => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.id}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="text"
                              value={item.fiscalYear}
                              onChange={e => handleRebateItemChange(item.id, "fiscalYear", e.target.value)}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              placeholder="e.g. 2080/81"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <select
                              value={item.filingPeriod}
                              onChange={e => handleRebateItemChange(item.id, "filingPeriod", e.target.value)}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            >
                              <option value="">Select...</option>
                              <option value="Monthly">Monthly</option>
                              <option value="Quarterly">Quarterly</option>
                              <option value="Yearly">Yearly</option>
                            </select>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="text"
                              value={item.taxPeriod}
                              onChange={e => handleRebateItemChange(item.id, "taxPeriod", e.target.value)}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                              placeholder="e.g. Q1, Q2, etc."
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={item.totalExemptedAmount || ""}
                              onChange={e =>
                                handleRebateItemChange(item.id, "totalExemptedAmount", Number(e.target.value))
                              }
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm text-right"
                              placeholder="0.00"
                              min="0"
                              step="0.01"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={item.discountAmount || ""}
                              onChange={e => handleRebateItemChange(item.id, "discountAmount", Number(e.target.value))}
                              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm text-right"
                              placeholder="0.00"
                              min="0"
                              step="0.01"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              type="button"
                              onClick={() => {
                                setRebateItems(rebateItems.filter(i => i.id !== item.id));
                              }}
                              className="text-red-600 hover:text-red-900"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={handleCancel}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Submit
              </button>
            </div>
          </div>
        </AdminForm>
      </div>
    </AdminLayout>
  );
};

export default RebateForm;
