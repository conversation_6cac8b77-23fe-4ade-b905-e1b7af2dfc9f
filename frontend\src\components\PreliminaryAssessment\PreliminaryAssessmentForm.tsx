import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import type { PreliminaryAssessmentCreateDto, PreliminaryAssessmentDetailDto } from "../../types/preliminaryAssessment";
import type { FiscalYear } from "../../services/taxConfigService";
import type { User } from "../../types";
import type { Province, District, MunicipalityLocation } from "../../services/api";
import { preliminaryAssessmentService } from "../../services/preliminaryAssessmentService";
import { fiscalYearService } from "../../services/fiscalYearService";
import { userService, propertyService } from "../../services/api";
import AdminLayout from "../admin/AdminLayout";
import AdminForm from "../admin/AdminForm";
import AdminTable from "../admin/AdminTable";

// Interface for transformed return filing data
interface TransformedReturnFiling {
  returnFilingId: string;
  propertyId: string;
  propertyAddress: string;
  fiscalYearId: string;
  fiscalYearName: string;
  submissionDate: string;
  createdAt: string;
}

interface PreliminaryAssessmentFormProps {
  mode: "create" | "edit";
  assessmentId?: string;
}

export const PreliminaryAssessmentForm: React.FC<PreliminaryAssessmentFormProps> = ({ mode, assessmentId }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<PreliminaryAssessmentCreateDto>({
    taxpayerRegistration: "",
    taxpayerName: "",
    address: "",
    phone: "",
    accountNumber: "",
    assessmentPeriodFrom: new Date(),
    assessmentPeriodTo: new Date(),
    actSection: "",
    rule: "",
    bank: "",
    branch: "",
    reasonForAssessment: "",
    appealNumber: "",
    otherReasonDescription: "",
    interestCalculationDate: new Date(),
    preliminaryAssessmentDate: new Date(),
    reason: "",
    regulations: "",
    fiscalYearId: "",
    municipalityId: "",
    taxDetails: [],
  });

  // Additional state for dropdowns and taxpayer lookup
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [municipalities, setMunicipalities] = useState<MunicipalityLocation[]>([]);
  const [filedTaxes, setFiledTaxes] = useState<TransformedReturnFiling[]>([]);
  const [taxpayerLoading, setTaxpayerLoading] = useState(false);
  const [taxpayerFound, setTaxpayerFound] = useState(false);

  // Location hierarchy state
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [selectedProvince] = useState<string>("");
  const [selectedDistrict, setSelectedDistrict] = useState<string>("");
  const [selectedMunicipality, setSelectedMunicipality] = useState<string>("");
  const [wardNumber, setWardNumber] = useState<string>("");

  // Taxpayer selection state
  const [availableTaxpayers, setAvailableTaxpayers] = useState<User[]>([]);
  const [selectedTaxpayer, setSelectedTaxpayer] = useState<string>("");
  const [taxpayersLoading, setTaxpayersLoading] = useState(false);

  // Function to handle taxpayer lookup by registration number
  const handleTaxpayerLookup = async (registrationNumber: string) => {
    if (!registrationNumber.trim()) {
      setTaxpayerFound(false);
      return;
    }

    setTaxpayerLoading(true);
    try {
      // Look up the taxpayer by registration number
      const response = await userService.getUserByRegistration(registrationNumber);
      if (response) {
        setFormData(prev => ({
          ...prev,
          taxpayerName: response.fullName || "",
          phone: response.phoneNumber || "",
          municipalityId: response.municipalityId || "",
          // Add other fields as needed from the response
        }));
        setTaxpayerFound(true);
      } else {
        setTaxpayerFound(false);
      }
    } catch (error) {
      console.error("Error looking up taxpayer:", error);
      setTaxpayerFound(false);
    } finally {
      setTaxpayerLoading(false);
    }
  };

  const [currentDetail, setCurrentDetail] = useState<PreliminaryAssessmentDetailDto>({
    serialNumber: 1,
    filingPeriod: "",
    period: "",
    taxYear: "",
    assessedAmount: 0,
    penalty: 0,
    additionalAmount: 0,
    interest: 0,
    total: 0,
  });

  const [editingDetailIndex, setEditingDetailIndex] = useState<number | null>(null);

  const loadFiledTaxes = useCallback(async (taxpayerId: string) => {
    try {
      // Fetch actual filed tax returns with fiscal year information for the selected taxpayer
      const filedReturns = await propertyService.getTaxpayerReturnFilings(taxpayerId);

      // Transform the data to include fiscal year information
      const transformedData = (filedReturns as unknown[]).map(
         (filing: unknown) => ({
           returnFilingId: (filing as { returnFilingId: string }).returnFilingId,
           propertyId: (filing as { propertyId: string }).propertyId,
           propertyAddress: (filing as { propertyAddress: string }).propertyAddress,
           fiscalYearId: (filing as { fiscalYearId: string }).fiscalYearId,
           fiscalYearName: (filing as { fiscalYearName: string }).fiscalYearName,
           submissionDate: (filing as { submissionDate: string }).submissionDate,
           createdAt: (filing as { createdAt: string }).createdAt,
         })
      );

      setFiledTaxes(transformedData);
    } catch (error) {
      console.error("Error loading filed taxes:", error);
      setFiledTaxes([]);
      toast.error("Failed to load filed tax returns");
    }
  }, []);

  const loadAssessment = useCallback(async (id: string) => {
    try {
      setLoading(true);
      const assessment = await preliminaryAssessmentService.getById(id);
      if (assessment) {
        setFormData({
          taxpayerRegistration: assessment.taxpayerRegistration,
          taxpayerName: assessment.taxpayerName,
          address: assessment.address || "",
          phone: assessment.phone || "",
          accountNumber: assessment.accountNumber || "",
          assessmentPeriodFrom: new Date(assessment.assessmentPeriodFrom),
          assessmentPeriodTo: new Date(assessment.assessmentPeriodTo),
          actSection: assessment.actSection || "",
          rule: assessment.rule || "",
          bank: assessment.bank || "",
          branch: assessment.branch || "",
          reasonForAssessment: assessment.reasonForAssessment || "",
          appealNumber: assessment.appealNumber || "",
          otherReasonDescription: assessment.otherReasonDescription || "",
          interestCalculationDate: assessment.interestCalculationDate ? new Date(assessment.interestCalculationDate) : new Date(),
          preliminaryAssessmentDate: assessment.preliminaryAssessmentDate ? new Date(assessment.preliminaryAssessmentDate) : new Date(),
          reason: assessment.reason || "",
          regulations: assessment.regulations || "",
          municipalityId: assessment.municipalityId || "",
          fiscalYearId: assessment.fiscalYearId || "",
          taxDetails: assessment.taxDetails || [],
        });
        setTaxpayerFound(true);
      }
    } catch (error) {
      console.error("Error loading assessment:", error);
      toast.error("Failed to load assessment");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadInitialData();
    if (mode === "edit" && assessmentId) {
      loadAssessment(assessmentId);
    }
  }, [mode, assessmentId, loadAssessment]);

  const loadInitialData = async () => {
    try {
      // Load fiscal years and provinces
      const [fiscalYearsData, provincesData] = await Promise.all([
        fiscalYearService.getAll(),
        userService.getProvinces(),
      ]);
      setFiscalYears(fiscalYearsData);
      setProvinces(provincesData);
    } catch (error) {
      console.error("Error loading initial data:", error);
      toast.error("Failed to load form data");
    }
  };

  // Load districts when province is selected
  useEffect(() => {
    const loadDistricts = async () => {
      if (selectedProvince) {
        try {
          const districtsData = await userService.getDistrictsByProvince(selectedProvince);
          setDistricts(districtsData);
          setSelectedDistrict("");
          setMunicipalities([]);
          setSelectedMunicipality("");
          setWardNumber("");
          setAvailableTaxpayers([]);
          setSelectedTaxpayer("");
        } catch (error) {
          console.error("Error loading districts:", error);
        }
      } else {
        setDistricts([]);
        setSelectedDistrict("");
        setMunicipalities([]);
        setSelectedMunicipality("");
      }
    };

    loadDistricts();
  }, [selectedProvince]);

  // Load municipalities when district is selected
  useEffect(() => {
    const loadMunicipalities = async () => {
      if (selectedDistrict) {
        try {
          const municipalitiesData = await userService.getMunicipalitiesByDistrict(selectedDistrict);
          setMunicipalities(municipalitiesData);
          setSelectedMunicipality("");
          setWardNumber("");
          setAvailableTaxpayers([]);
          setSelectedTaxpayer("");
        } catch (error) {
          console.error("Error loading municipalities:", error);
        }
      } else {
        setMunicipalities([]);
        setSelectedMunicipality("");
      }
    };

    loadMunicipalities();
  }, [selectedDistrict]);

  // Load taxpayers when municipality and ward are selected
  useEffect(() => {
    const loadTaxpayers = async () => {
      if (selectedMunicipality && wardNumber) {
        setTaxpayersLoading(true);
        try {
          const taxpayersData = await userService.getTaxpayers(selectedMunicipality, wardNumber);
          setAvailableTaxpayers(taxpayersData);
          setSelectedTaxpayer("");
        } catch (error) {
          console.error("Error loading taxpayers:", error);
          setAvailableTaxpayers([]);
        } finally {
          setTaxpayersLoading(false);
        }
      } else {
        setAvailableTaxpayers([]);
        setSelectedTaxpayer("");
      }
    };

    loadTaxpayers();
  }, [selectedMunicipality, wardNumber]);

  const handleTaxpayerSelection = async (taxpayerId: string) => {
    if (!taxpayerId) {
      setSelectedTaxpayer("");
      setTaxpayerFound(false);
      setFiledTaxes([]);
      return;
    }

    const selectedUser = availableTaxpayers.find(user => user.id === taxpayerId);
    if (selectedUser) {
      setSelectedTaxpayer(taxpayerId);
      setFormData(prev => ({
        ...prev,
        taxpayerRegistration: selectedUser.citizenshipNumber || selectedUser.id || "",
        taxpayerName: selectedUser.fullName || "",
        address: "", // Address not available in current backend response
        phone: selectedUser.phoneNumber || "",
        accountNumber: selectedUser.pan || "",
        municipalityId: selectedUser.municipalityId || "",
      }));
      setTaxpayerFound(true);
      // Load filed taxes for this taxpayer
      await loadFiledTaxes(selectedUser.id);
    }
  };

  const lookupTaxpayer = useCallback(async (registration: string) => {
    if (!registration.trim()) {
      setTaxpayerFound(false);
      setFiledTaxes([]);
      return;
    }

    try {
      setTaxpayerLoading(true);
      const user = await userService.getUserByRegistration(registration);
      if (user) {
        setFormData(prev => ({
          ...prev,
          taxpayerName: user.fullName || "",
          address: "", // Address not available in current backend response
          phone: user.phoneNumber || "",
          accountNumber: user.pan || "",
          municipalityId: user.municipalityId || "",
        }));
        setTaxpayerFound(true);
        // Load filed taxes for this taxpayer
        await loadFiledTaxes(user.id);
      } else {
        setTaxpayerFound(false);
        setFiledTaxes([]);
        toast.error("Taxpayer not found");
      }
    } catch (error) {
      console.error("Error looking up taxpayer:", error);
      setTaxpayerFound(false);
      setFiledTaxes([]);
      toast.error("Failed to lookup taxpayer");
    } finally {
      setTaxpayerLoading(false);
    }
  }, [loadFiledTaxes]);



  const handleInputChange = (
    field: keyof PreliminaryAssessmentCreateDto,
    value: string | Date | PreliminaryAssessmentDetailDto[]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Handle taxpayer registration lookup for manual input
    if (field === "taxpayerRegistration" && typeof value === "string") {
      // Clear dropdown selection when manual input is used
      if (value !== selectedTaxpayer) {
        setSelectedTaxpayer("");
      }

      // Debounce the lookup to avoid too many API calls
      setTimeout(() => {
        if (value === formData.taxpayerRegistration) {
          lookupTaxpayer(value);
        }
      }, 500);
    }
  };

  const handleDetailChange = (field: keyof PreliminaryAssessmentDetailDto, value: string | number) => {
    setCurrentDetail(prev => {
      const updated = { ...prev, [field]: value };
      // Auto-calculate total when amount values change
      if (["assessedAmount", "penalty", "additionalAmount", "interest"].includes(field)) {
        updated.total = updated.assessedAmount + updated.penalty + updated.additionalAmount + updated.interest;
      }
      return updated;
    });
  };

  const addOrUpdateDetail = () => {
    if (!currentDetail.filingPeriod) {
      toast.error("Please select fiscal year");
      return;
    }

    if (editingDetailIndex !== null) {
      // Update existing detail
      const updatedDetails = [...formData.taxDetails];
      updatedDetails[editingDetailIndex] = currentDetail;
      setFormData(prev => ({ ...prev, taxDetails: updatedDetails }));
      setEditingDetailIndex(null);
    } else {
      // Add new detail
      setFormData(prev => ({
        ...prev,
        taxDetails: [...prev.taxDetails, currentDetail],
      }));
    }

    // Reset current detail
    setCurrentDetail({
      serialNumber: formData.taxDetails.length + 1,
      filingPeriod: "",
      period: "",
      taxYear: "",
      assessedAmount: 0,
      penalty: 0,
      additionalAmount: 0,
      interest: 0,
      total: 0,
    });
  };

  const editDetail = (index: number) => {
    setCurrentDetail(formData.taxDetails[index]);
    setEditingDetailIndex(index);
  };

  const removeDetail = (index: number) => {
    const updatedDetails = formData.taxDetails.filter((_, i) => i !== index);
    // Update serial numbers
    const reorderedDetails = updatedDetails.map((detail, i) => ({
      ...detail,
      serialNumber: i + 1,
    }));
    setFormData(prev => ({ ...prev, taxDetails: reorderedDetails }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.taxpayerRegistration || !formData.taxpayerName || !formData.municipalityId) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (!taxpayerFound) {
      toast.error("Please select a valid taxpayer");
      return;
    }

    // Check if there are unsaved tax details in the current form
    const hasPartialTaxDetail =
      currentDetail.filingPeriod.trim() ||
      currentDetail.taxYear.trim() ||
      currentDetail.assessedAmount > 0 ||
      currentDetail.penalty > 0 ||
      currentDetail.additionalAmount > 0 ||
      currentDetail.interest > 0;

    if (hasPartialTaxDetail) {
      toast.error("Please add the current tax detail before submitting or clear the form");
      return;
    }

    if (formData.taxDetails.length === 0) {
      toast.error("Please add at least one tax detail");
      return;
    }

    try {
      setLoading(true);

      if (mode === "create") {
        await preliminaryAssessmentService.create(formData);
        toast.success("Preliminary assessment created successfully");
      } else if (mode === "edit" && assessmentId) {
        await preliminaryAssessmentService.update(assessmentId, formData);
        toast.success("Preliminary assessment updated successfully");
      }

      navigate("/admin/preliminary-assessments");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error &&
        "response" in error &&
        typeof error.response === "object" &&
        error.response !== null &&
        "data" in error.response &&
        typeof error.response.data === "object" &&
        error.response.data !== null &&
        "message" in error.response.data &&
        typeof error.response.data.message === "string"
          ? error.response.data.message
          : "Failed to save assessment";
      toast.error(errorMessage);
      console.error("Error saving assessment:", error);
    } finally {
      setLoading(false);
    }
  };

  const grandTotal = formData.taxDetails.reduce((sum, detail) => sum + detail.total, 0);

  const taxDetailColumns = [
    {
      accessor: "serialNumber" as keyof PreliminaryAssessmentDetailDto,
      header: "S.N.",
    },
    {
      accessor: (item: PreliminaryAssessmentDetailDto) =>
        fiscalYears.find(fy => fy.fiscalYearId === item.filingPeriod)?.name || item.filingPeriod,
      header: "Filing Period",
    },
    {
      accessor: "period" as keyof PreliminaryAssessmentDetailDto,
      header: "Period",
    },
    {
      accessor: "taxYear" as keyof PreliminaryAssessmentDetailDto,
      header: "Tax Year",
    },
    {
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.assessedAmount.toLocaleString()}`,
      header: "Assessed Amount",
    },
    {
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.penalty.toLocaleString()}`,
      header: "Penalty Amount",
    },
    {
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.additionalAmount.toLocaleString()}`,
      header: "Additional Amount",
    },
    {
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.interest.toLocaleString()}`,
      header: "Interest Amount",
    },
    {
      accessor: (item: PreliminaryAssessmentDetailDto) => `Rs. ${item.total.toLocaleString()}`,
      header: "Total Amount",
    },
  ];

  return (
    <AdminLayout title={mode === "create" ? "Create Preliminary Tax Assessment" : "Edit Preliminary Tax Assessment"}>
      <div className="space-y-6">
        <AdminForm onSubmit={handleSubmit}>
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Taxpayer No *</label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.taxpayerRegistration}
                    onChange={e => {
                      // Reset taxpayer found state when changing the number
                      if (taxpayerFound) {
                        setTaxpayerFound(false);
                      }
                      handleInputChange("taxpayerRegistration", e.target.value);
                    }}
                    onBlur={() => {
                      // Trigger taxpayer lookup when field loses focus
                      if (formData.taxpayerRegistration && !taxpayerFound) {
                        handleTaxpayerLookup(formData.taxpayerRegistration);
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      taxpayerLoading
                        ? "border-yellow-300"
                        : taxpayerFound
                        ? "border-green-300"
                        : formData.taxpayerRegistration && !taxpayerFound
                        ? "border-red-300"
                        : "border-gray-300"
                    }`}
                    maxLength={50}
                    placeholder="Enter taxpayer number"
                    required
                  />
                  {taxpayerLoading && (
                    <div className="absolute right-3 top-2.5">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                  )}
                </div>
                {formData.taxpayerRegistration && !taxpayerFound && !taxpayerLoading && (
                  <p className="text-sm text-red-600 mt-1">Taxpayer not found</p>
                )}
                <p className="text-sm text-gray-500 mt-1">
                  Use this field if taxpayer is not found in the dropdown above
                </p>
              </div>

              {taxpayerFound && filedTaxes.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Filed Taxes</label>
                  <select
                    value={formData.returnFilingId || ""}
                    onChange={e => handleInputChange("returnFilingId", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Filed Tax</option>
                    {filedTaxes.map(filing => (
                      <option key={filing.returnFilingId} value={filing.returnFilingId}>
                        {filing.fiscalYearName} - {filing.propertyAddress}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Select Taxpayer *</label>
                <div className="relative">
                  <select
                    value={selectedTaxpayer}
                    onChange={e => handleTaxpayerSelection(e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      taxpayersLoading ? "border-yellow-300" : taxpayerFound ? "border-green-300" : "border-gray-300"
                    }`}
                    required
                    disabled={!selectedMunicipality || !wardNumber || taxpayersLoading}
                  >
                    <option value="">Select Taxpayer</option>
                    {availableTaxpayers.map(taxpayer => (
                      <option key={taxpayer.id} value={taxpayer.id}>
                        {taxpayer.fullName}
                      </option>
                    ))}
                  </select>
                  {taxpayersLoading && (
                    <div className="absolute right-3 top-2.5">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                  )}
                </div>
                {availableTaxpayers.length === 0 && selectedMunicipality && wardNumber && !taxpayersLoading && (
                  <p className="text-sm text-yellow-600 mt-1">No taxpayers found in this ward</p>
                )}
                {taxpayerFound && <p className="text-sm text-green-600 mt-1">Taxpayer selected</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Province</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {selectedProvince ? provinces.find(p => p.id === selectedProvince)?.name || "N/A" : "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">District</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {selectedDistrict ? districts.find(d => d.id === selectedDistrict)?.name || "N/A" : "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Municipality</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {selectedMunicipality
                    ? municipalities.find(m => m.id === selectedMunicipality)?.name || "N/A"
                    : "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ward Number</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {wardNumber || "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Street/Tole</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {formData.address ? formData.address.split(",")[0] || "N/A" : "N/A"}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">House Number</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {formData.address
                    ? formData.address.split(",").length > 1
                      ? formData.address.split(",")[1].trim()
                      : "N/A"
                    : "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {formData.email || "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mobile Number</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {formData.mobileNumber || "N/A"}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <div className="w-full px-3 py-2 bg-gray-100 rounded-md border border-gray-300">
                  {formData.phone || "N/A"}
                </div>
              </div>

              {/* Bank Details Section */}
              <div className="col-span-1 md:col-span-3">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Bank Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                      <input
                        type="text"
                        value={formData.accountNumber}
                        onChange={e => handleInputChange("accountNumber", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        maxLength={50}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Bank</label>
                      <input
                        type="text"
                        value={formData.bank || ""}
                        onChange={e => handleInputChange("bank", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        maxLength={100}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Branch</label>
                      <input
                        type="text"
                        value={formData.branch || ""}
                        onChange={e => handleInputChange("branch", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        maxLength={100}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Act Section</label>
                <input
                  type="text"
                  value={formData.actSection || ""}
                  onChange={e => handleInputChange("actSection", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  maxLength={100}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Rule</label>
                <input
                  type="text"
                  value={formData.rule || ""}
                  onChange={e => handleInputChange("rule", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  maxLength={100}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Assessment Period</label>
                <select
                  value={formData.assessmentPeriod || ""}
                  onChange={e => handleInputChange("assessmentPeriod", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Fiscal Year</option>
                  {fiscalYears.map(fy => (
                    <option key={fy.fiscalYearId} value={fy.name}>
                      {fy.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="col-span-1 md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Reason for Assessment</label>
                <textarea
                  value={formData.reasonForAssessment || ""}
                  onChange={e => handleInputChange("reasonForAssessment", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  maxLength={500}
                />
              </div>
            </div>
          </div>

          {/* Assessment Period */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Period & Dates</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">From Date *</label>
                <input
                  type="date"
                  value={formData.assessmentPeriodFrom.toISOString().split("T")[0]}
                  onChange={e => handleInputChange("assessmentPeriodFrom", new Date(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">To Date *</label>
                <input
                  type="date"
                  value={formData.assessmentPeriodTo.toISOString().split("T")[0]}
                  onChange={e => handleInputChange("assessmentPeriodTo", new Date(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Interest Calculation Date</label>
                <input
                  type="date"
                  value={
                    formData.interestCalculationDate ? formData.interestCalculationDate.toISOString().split("T")[0] : ""
                  }
                  onChange={e =>
                    handleInputChange("interestCalculationDate", e.target.value ? new Date(e.target.value) : new Date())
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Preliminary Assessment Date</label>
                <input
                  type="date"
                  value={
                    formData.preliminaryAssessmentDate
                      ? formData.preliminaryAssessmentDate.toISOString().split("T")[0]
                      : ""
                  }
                  onChange={e =>
                    handleInputChange(
                      "preliminaryAssessmentDate",
                      e.target.value ? new Date(e.target.value) : new Date()
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reason</label>
                <textarea
                  value={formData.reason}
                  onChange={e => handleInputChange("reason", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Regulations</label>
                <textarea
                  value={formData.regulations}
                  onChange={e => handleInputChange("regulations", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
            </div>
          </div>

          {/* Tax Details */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tax Details</h3>

            {/* Add/Edit Tax Detail Form */}
            <div className="border border-gray-200 rounded-lg p-4 mb-4">
              <h4 className="text-md font-medium text-gray-800 mb-3">
                {editingDetailIndex !== null ? "Edit Tax Detail" : "Add Tax Detail"}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Fiscal Year *</label>
                  <select
                    value={currentDetail.filingPeriod}
                    onChange={e => handleDetailChange("filingPeriod", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">
                      {fiscalYears.length === 0 ? "No fiscal years found" : "Select Fiscal Year"}
                    </option>
                    {fiscalYears.map(fy => (
                      <option key={fy.fiscalYearId} value={fy.fiscalYearId}>
                        {fy.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Assessed Amount</label>
                  <input
                    type="number"
                    value={currentDetail.assessedAmount}
                    onChange={e => handleDetailChange("assessedAmount", parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Penalty</label>
                  <input
                    type="number"
                    value={currentDetail.penalty}
                    onChange={e => handleDetailChange("penalty", parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Additional Amount</label>
                  <input
                    type="number"
                    value={currentDetail.additionalAmount}
                    onChange={e => handleDetailChange("additionalAmount", parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Interest</label>
                  <input
                    type="number"
                    value={currentDetail.interest}
                    onChange={e => handleDetailChange("interest", parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total</label>
                  <input
                    type="number"
                    value={currentDetail.total}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                    readOnly
                  />
                </div>

                <div className="flex items-end space-x-2">
                  <button
                    type="button"
                    onClick={addOrUpdateDetail}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {editingDetailIndex !== null ? "Update" : "Add"}
                  </button>
                  {(currentDetail.filingPeriod.trim() ||
                    currentDetail.taxYear.trim() ||
                    currentDetail.assessedAmount > 0 ||
                    currentDetail.penalty > 0 ||
                    currentDetail.additionalAmount > 0 ||
                    currentDetail.interest > 0) && (
                    <button
                      type="button"
                      onClick={() => {
                        setCurrentDetail({
                          serialNumber: formData.taxDetails.length + 1,
                          filingPeriod: "",
                          period: "",
                          taxYear: "",
                          assessedAmount: 0,
                          penalty: 0,
                          additionalAmount: 0,
                          interest: 0,
                          total: 0,
                        });
                        setEditingDetailIndex(null);
                      }}
                      className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                      Clear
                    </button>
                  )}
                </div>
              </div>

              {editingDetailIndex !== null && (
                <div className="mt-2">
                  <button
                    type="button"
                    onClick={() => {
                      setEditingDetailIndex(null);
                      setCurrentDetail({
                        serialNumber: formData.taxDetails.length + 1,
                        filingPeriod: "",
                        period: "",
                        taxYear: "",
                        assessedAmount: 0,
                        penalty: 0,
                        additionalAmount: 0,
                        interest: 0,
                        total: 0,
                      });
                    }}
                    className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    Cancel Edit
                  </button>
                </div>
              )}
            </div>

            {/* Tax Details Table */}
            {formData.taxDetails.length > 0 && (
              <div>
                <AdminTable
                  columns={taxDetailColumns}
                  data={formData.taxDetails}
                  keyField="serialNumber"
                  actions={(item: PreliminaryAssessmentDetailDto) => {
                    const index = formData.taxDetails.findIndex(detail => detail.serialNumber === item.serialNumber);
                    return (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => editDetail(index)}
                          className="px-3 py-1.5 bg-blue-600 text-white hover:bg-blue-700 text-xs font-medium rounded-md border border-blue-600 hover:border-blue-700 transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => removeDetail(index)}
                          className="px-3 py-1.5 bg-red-600 text-white hover:bg-red-700 text-xs font-medium rounded-md border border-red-600 hover:border-red-700 transition-colors"
                        >
                          Remove
                        </button>
                      </div>
                    );
                  }}
                />

                <div className="mt-4 text-right">
                  <div className="text-lg font-semibold text-gray-900">
                    Grand Total: Rs. {grandTotal.toLocaleString()}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Form Actions */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => navigate("/admin/preliminary-assessments")}
                className="px-6 py-2 border border-gray-300 text-white rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? "Saving..." : mode === "create" ? "Create Assessment" : "Update Assessment"}
              </button>
            </div>
          </div>
        </AdminForm>
      </div>
    </AdminLayout>
  );
};
