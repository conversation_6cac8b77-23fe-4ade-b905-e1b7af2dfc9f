import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import * as api from '../services/api';
import UnderpaidAppealForm from '../components/UnderpaidAppealForm';
import NegotiationView from '../components/NegotiationView';
import NegotiatedPaymentForm from '../components/NegotiatedPaymentForm';

interface Assessment {
  id: string;
  propertyId: string;
  propertyAddress: string;
  assessmentYear: number;
  finalAssessedValue: number;
  taxAmount: number;
  paymentStatus: string;
  origin?: string;
  superseded?: boolean;
  createdAt: string;
}

interface Appeal {
  id: string;
  assessmentId: string;
  reason: string;
  status: string;
  createdAt: string;
}

interface Negotiation {
  id: string;
  appealId: string;
  assessmentId: string;
  status: string;
  createdAt: string;
}

const CitizenUnderpaidTaxDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [negotiations, setNegotiations] = useState<Negotiation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // UI state
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null);
  const [showAppealForm, setShowAppealForm] = useState(false);
  const [selectedNegotiation, setSelectedNegotiation] = useState<string | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [activeTab, setActiveTab] = useState('assessments');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError('');
        
        // Fetch underpaid assessments
        const assessmentsResponse = await api.get('/assessments/citizen/underpaid') as unknown;
        setAssessments((assessmentsResponse as { data: Assessment[] }).data);
        
        // Fetch appeals
        const appealsResponse = await api.get('/appeals/citizen') as unknown;
        setAppeals((appealsResponse as { data: Appeal[] }).data);
        
        // Fetch negotiations
        const negotiationsResponse = await api.get('/negotiations/citizen') as unknown;
        setNegotiations((negotiationsResponse as { data: Negotiation[] }).data);
      } catch (err) {
        console.error('Failed to fetch data:', err);
        setError('Failed to load your tax information. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  const handleAppealSubmit = async () => {
    setShowAppealForm(false);
    setSelectedAssessment(null);
    
    // Refresh appeals data
    try {
      const appealsResponse = await api.get('/appeals/citizen') as unknown;
      setAppeals((appealsResponse as { data: Appeal[] }).data);
    } catch (err) {
      console.error('Failed to refresh appeals:', err);
    }
  };

  const handlePaymentSuccess = async () => {
    setShowPaymentForm(false);
    setSelectedNegotiation(null);
    
    // Refresh assessments and negotiations data
    try {
      const assessmentsResponse = await api.get('/assessments/citizen/underpaid') as unknown;
      setAssessments((assessmentsResponse as { data: Assessment[] }).data);
      
      const negotiationsResponse = await api.get('/negotiations/citizen') as unknown;
      setNegotiations((negotiationsResponse as { data: Negotiation[] }).data);
    } catch (err) {
      console.error('Failed to refresh data:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'badge-warning';
      case 'paid':
        return 'badge-success';
      case 'overdue':
        return 'badge-error';
      case 'underpaid':
        return 'badge-warning';
      case 'pendingnegotiated':
        return 'badge-info';
      default:
        return 'badge-ghost';
    }
  };

  const renderAssessments = () => {
    if (assessments.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-base-content/70">You have no underpaid assessments.</p>
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="table table-zebra">
          <thead>
            <tr>
              <th>Property</th>
              <th>Year</th>
              <th>Tax Amount</th>
              <th>Status</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {assessments.map(assessment => (
              <tr key={assessment.id}>
                <td>{assessment.propertyAddress}</td>
                <td>{assessment.assessmentYear}</td>
                <td>NPR {assessment.taxAmount.toFixed(2)}</td>
                <td>
                  <div className={`badge ${getStatusBadge(assessment.paymentStatus)}`}>
                    {assessment.paymentStatus}
                  </div>
                </td>
                <td>{formatDate(assessment.createdAt)}</td>
                <td>
                  <div className="flex gap-2">
                    {assessment.paymentStatus === 'Underpaid' && !assessment.superseded && (
                      <button
                        onClick={() => {
                          setSelectedAssessment(assessment);
                          setShowAppealForm(true);
                        }}
                        className="btn btn-primary btn-xs"
                      >
                        Appeal
                      </button>
                    )}
                    {assessment.paymentStatus === 'PendingNegotiated' && (
                      <button
                        onClick={() => navigate(`/payments/negotiated/${assessment.id}`)}
                        className="btn btn-success btn-xs"
                      >
                        Pay
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderAppeals = () => {
    if (appeals.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-base-content/70">You have no appeals.</p>
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="table table-zebra">
          <thead>
            <tr>
              <th>Appeal ID</th>
              <th>Reason</th>
              <th>Status</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {appeals.map(appeal => (
              <tr key={appeal.id}>
                <td>{appeal.id.substring(0, 8)}...</td>
                <td>{appeal.reason.substring(0, 50)}...</td>
                <td>
                  <div className={`badge ${getStatusBadge(appeal.status)}`}>
                    {appeal.status}
                  </div>
                </td>
                <td>{formatDate(appeal.createdAt)}</td>
                <td>
                  <button
                    onClick={() => navigate(`/appeals/${appeal.id}`)}
                    className="btn btn-primary btn-xs"
                  >
                    View
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderNegotiations = () => {
    if (negotiations.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-base-content/70">You have no negotiations.</p>
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="table table-zebra">
          <thead>
            <tr>
              <th>Negotiation ID</th>
              <th>Status</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {negotiations.map(negotiation => (
              <tr key={negotiation.id}>
                <td>{negotiation.id.substring(0, 8)}...</td>
                <td>
                  <div className={`badge ${getStatusBadge(negotiation.status)}`}>
                    {negotiation.status}
                  </div>
                </td>
                <td>{formatDate(negotiation.createdAt)}</td>
                <td>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setSelectedNegotiation(negotiation.id)}
                      className="btn btn-primary btn-xs"
                    >
                      View
                    </button>
                    {negotiation.status === 'Finalized' && (
                      <button
                        onClick={() => {
                          setSelectedNegotiation(negotiation.id);
                          setShowPaymentForm(true);
                        }}
                        className="btn btn-success btn-xs"
                      >
                        Pay
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Underpaid Tax Management</h1>
        <div className="text-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
          <p className="mt-2 text-base-content/70">Loading your tax information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Underpaid Tax Management</h1>
      
      {error && (
        <div className="alert alert-error mb-4">
          <span>{error}</span>
        </div>
      )}
      
      {showAppealForm && selectedAssessment ? (
        <UnderpaidAppealForm
          assessment={selectedAssessment}
          onSubmit={handleAppealSubmit}
          onCancel={() => {
            setShowAppealForm(false);
            setSelectedAssessment(null);
          }}
        />
      ) : selectedNegotiation && showPaymentForm ? (
        <NegotiatedPaymentForm
          negotiationId={selectedNegotiation}
          onSuccess={handlePaymentSuccess}
          onCancel={() => {
            setShowPaymentForm(false);
            setSelectedNegotiation(null);
          }}
        />
      ) : selectedNegotiation ? (
        <NegotiationView
          negotiationId={selectedNegotiation}
          onPaymentClick={() => setShowPaymentForm(true)}
          onClose={() => setSelectedNegotiation(null)}
        />
      ) : (
        <>
          <div className="mb-6">
            <div className="tabs tabs-boxed">
              <button
                onClick={() => setActiveTab('assessments')}
                className={`tab ${activeTab === 'assessments' ? 'tab-active' : ''}`}
              >
                Underpaid Assessments
              </button>
              <button
                onClick={() => setActiveTab('appeals')}
                className={`tab ${activeTab === 'appeals' ? 'tab-active' : ''}`}
              >
                Appeals
              </button>
              <button
                onClick={() => setActiveTab('negotiations')}
                className={`tab ${activeTab === 'negotiations' ? 'tab-active' : ''}`}
              >
                Negotiations
              </button>
            </div>
          </div>
          
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              {activeTab === 'assessments' && renderAssessments()}
              {activeTab === 'appeals' && renderAppeals()}
              {activeTab === 'negotiations' && renderNegotiations()}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CitizenUnderpaidTaxDashboard;
