import React, { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { userService } from "../services/api";
import api from "../services/api";
import type { User, District } from "../types";
import { AdminLayout } from "../components/admin";

// Helper component for consistent info display
const InfoRow: React.FC<{
  label: string;
  value?: string | number | boolean | null;
  isCode?: boolean;
  children?: React.ReactNode;
}> = ({ label, value, isCode = false, children }) => {
  // Skip rendering if no value or children
  if ((value === undefined || value === null || value === "") && !children) {
    return null;
  }

  return (
    <div className="flex py-1">
      <span className="font-semibold w-1/3">{label}:</span>
      <div className="flex-1">
        {children ||
          (isCode ? (
            <code className="font-mono bg-base-300 px-2 py-1 rounded w-full block overflow-x-auto">{value}</code>
          ) : (
            <span>{String(value)}</span>
          ))}
      </div>
    </div>
  );
};

const PendingUsers: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [pendingUsers, setPendingUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");
  const [districts, setDistricts] = useState<District[]>([]);

  // Fetch districts
  const loadDistricts = useCallback(async () => {
    try {
      const response = await api.get('/districts');
      setDistricts(response.data);
    } catch (err: unknown) {
      console.error('Failed to load districts:', err);
    }
  }, []);

  // Helper function to get district name by ID
  const getDistrictName = (districtId: string | null | undefined): string => {
    if (!districtId) return "N/A";
    const district = districts.find(d => d.districtId === districtId);
    return district ? district.name : districtId; // Fallback to ID if name not found
  };

  // Fetch pending users
  const loadPendingUsers = useCallback(async () => {
    try {
      setLoading(true);
      const users = await userService.getPendingUsers();
      setPendingUsers(users);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(`Failed to load pending users: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // Redirect if not an officer
    if (user && user.role !== "Officer") {
      navigate("/dashboard");
      return;
    }

    loadDistricts();
    loadPendingUsers();
  }, [user, navigate, loadPendingUsers, loadDistricts]);

  // Handle user approval
  const handleApproveUser = async (user: User) => {
    try {
      const result = await userService.updateUserStatus(user.id, "Active");

      // Update the user in the local state with the new details
      const updatedUsers = pendingUsers.map(u =>
        u.id === user.id
          ? {
              ...u,
              status: "Active" as const,
              pan: result.pan || u.pan,
              temporaryPassword: result.temporaryPassword,
            }
          : u
      ) as User[];
      setPendingUsers(updatedUsers);

      setSuccess(
        `User approved successfully. ` +
          (result.temporaryPassword ? `Temporary password: ${result.temporaryPassword}` : "")
      );
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(`Failed to approve user: ${errorMessage}`);
    }
  };

  // Handle user rejection
  const handleRejectUser = async () => {
    if (!selectedUser) return;

    try {
      await userService.updateUserStatus(selectedUser.id, "Rejected", rejectionReason);
      setSuccess("User rejected successfully");
      setShowRejectModal(false);
      setRejectionReason("");
      setSelectedUser(null);
      // Refresh the list
      loadPendingUsers();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(`Failed to reject user: ${errorMessage}`);
    }
  };

  // Open rejection modal
  const openRejectModal = (user: User, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedUser(user);
    setShowRejectModal(true);
  };

  // Type guard to check if user needs approval
  const needsApproval = (user: User): boolean => {
    return user.status === "Pending" || user.status === "Inactive" || user.status === undefined;
  };

  // Open detail modal
  const openDetailModal = (user: User) => {
    setSelectedUser(user);
    setShowDetailModal(true);
  };

  // Close modals and reset state
  const closeModals = () => {
    setShowRejectModal(false);
    setShowDetailModal(false);
    setSelectedUser(null);
    setRejectionReason("");
  };

  // Clear messages
  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <AdminLayout
      title="Pending User Approvals"
      subtitle="Review and manage user registration requests for your municipality"
    >
      <>
        {/* Error/Success Messages */}
        {error && (
          <div className="alert alert-error mb-4">
            <span>{error}</span>
            <button onClick={clearMessages} className="btn btn-ghost btn-sm">
              ×
            </button>
          </div>
        )}

        {success && (
          <div className="alert alert-success mb-4">
            <span>{success}</span>
            <button onClick={clearMessages} className="btn btn-ghost btn-sm">
              ×
            </button>
          </div>
        )}

        {/* Users Table */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            {loading ? (
              <div className="text-center py-8">
                <span className="loading loading-spinner loading-lg"></span>
                <p className="mt-2 text-base-content/70">Loading pending users...</p>
              </div>
            ) : pendingUsers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-base-content/70">No pending users to approve.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table table-zebra">
                  <thead>
                    <tr>
                      <th>Submission #</th>
                      <th>User</th>
                      <th>Municipality</th>
                      <th>Registered</th>
                      <th className="text-right">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pendingUsers.map(user => (
                      <tr
                        key={user.id}
                        className="hover:bg-base-200 cursor-pointer"
                        onClick={() => openDetailModal(user)}
                      >
                        <td className="font-mono text-sm">
                          <div className="font-mono font-medium">{user.submissionNumber || "N/A"}</div>
                        </td>
                        <td>
                          <div>
                            <div className="font-medium">{user.fullName || `${user.firstName} ${user.lastName}`}</div>
                            <div className="text-base-content/70 text-sm">{user.email}</div>
                            {user.phoneNumber && <div className="text-base-content/70 text-sm">{user.phoneNumber}</div>}
                            {user.status === "Active" && user.pan && (
                              <div className="mt-1">
                                <span className="badge badge-sm badge-success mr-1">PAN</span>
                                <span className="text-xs font-mono">{user.pan}</span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="text-base-content/70">
                          {user.municipalityName || (user.municipalityId ? `ID: ${user.municipalityId}` : "N/A")}
                        </td>
                        <td className="text-base-content/70">
                          {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : "N/A"}
                        </td>
                        <td>
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={e => {
                                e.stopPropagation();
                                handleApproveUser(user);
                              }}
                              className="btn btn-success btn-sm"
                              disabled={user.status === "Active"}
                            >
                              {user.status === "Active" ? "Approved" : "Approve"}
                            </button>
                            <button onClick={e => openRejectModal(user, e)} className="btn btn-error btn-sm">
                              Reject
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Rejection Modal */}
        {showRejectModal && (
          <div className="modal modal-open">
            <div className="modal-box">
              <h3 className="font-bold text-lg mb-4">Reject User Registration</h3>
              <p className="text-base-content/70 mb-4">
                Please provide a reason for rejecting this user registration. This information may be shared with the
                user.
              </p>
              <div className="form-control mb-4">
                <label htmlFor="rejectionReason" className="label">
                  <span className="label-text">Rejection Reason</span>
                </label>
                <textarea
                  id="rejectionReason"
                  value={rejectionReason}
                  onChange={e => setRejectionReason(e.target.value)}
                  className="textarea textarea-bordered"
                  rows={4}
                  placeholder="Enter reason for rejection..."
                ></textarea>
              </div>
              <div className="modal-action">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectionReason("");
                    setSelectedUser(null);
                  }}
                  className="btn btn-ghost"
                >
                  Cancel
                </button>
                <button onClick={handleRejectUser} className="btn btn-error" disabled={!rejectionReason.trim()}>
                  Reject User
                </button>
              </div>
            </div>
            <div
              className="modal-backdrop"
              onClick={() => {
                setShowRejectModal(false);
                setRejectionReason("");
                setSelectedUser(null);
              }}
            ></div>
          </div>
        )}

        {/* User Detail Modal */}
        {showDetailModal && selectedUser && (
          <div className="modal modal-open">
            <div className="modal-box max-w-5xl max-h-[90vh] overflow-y-auto">
              <div className="mb-6">
                <div className="flex justify-between items-start">
                  <h3 className="font-bold text-2xl">
                    {selectedUser.fullName || `${selectedUser.firstName} ${selectedUser.lastName}`}
                  </h3>
                  <div>
                    {selectedUser.status === "Active" ? (
                      <span className="badge badge-success">Active</span>
                    ) : selectedUser.status === "Pending" || selectedUser.status === "PendingApproval" ? (
                      <span className="badge badge-warning">Pending Approval</span>
                    ) : selectedUser.status === "Rejected" ? (
                      <span className="badge badge-error">Rejected</span>
                    ) : (
                      <span className="badge badge-ghost">Inactive</span>
                    )}
                  </div>
                </div>
                <div className="w-full flex justify-between items-center mt-2 text-sm bg-base-200/50 px-4 py-2 rounded">
                  <div className="flex-1">
                    <span className="text-base-content/70 mr-2">Submission #:</span>
                    <span className="font-mono">{selectedUser.submissionNumber || "N/A"}</span>
                  </div>
                  <div className="flex-1 text-center">
                    <span className="text-base-content/70 mr-2">Registered:</span>
                    <span>
                      {selectedUser.createdAt ? new Date(selectedUser.createdAt).toLocaleDateString() : "N/A"}
                    </span>
                  </div>
                  <div className="flex-1 text-right">
                    <span className="text-base-content/70 mr-2">Status:</span>
                    <span className="font-medium">
                      {selectedUser.status === "Active"
                        ? "Active"
                        : selectedUser.status === "Pending" || selectedUser.status === "PendingApproval"
                        ? "Pending Approval"
                        : selectedUser.status === "Rejected"
                        ? "Rejected"
                        : "Inactive"}
                    </span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
                {/* Personal Information Column */}
                <div className="space-y-6">
                  {/* Personal Information Section */}
                  <div className="bg-base-200 p-6 rounded-lg shadow-sm">
                    <h4 className="font-bold text-lg mb-4 border-b pb-2 flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                      Personal Information
                    </h4>
                    <div className="space-y-3">
                      {/* First Row: Full Name, Gender, DOB, Profession */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <InfoRow
                          label="Full Name"
                          value={selectedUser.fullName || `${selectedUser.firstName} ${selectedUser.lastName}`}
                        />
                        <InfoRow label="Gender" value={selectedUser.gender || "Not provided"} />
                        <InfoRow label="Date of Birth">
                          {selectedUser.dateOfBirth ? (
                            <span>
                              {new Date(selectedUser.dateOfBirth).toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                              })}
                            </span>
                          ) : (
                            "Not provided"
                          )}
                        </InfoRow>
                        <InfoRow label="Profession" value={selectedUser.profession || "Not provided"} />
                      </div>

                      {/* Second Row: Father's Name, Mother's Name, Grandfather's Name, Grandmother's Name */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <InfoRow label="Father's Name" value={selectedUser.fatherName || "Not provided"} />
                        <InfoRow label="Mother's Name" value={selectedUser.motherName || "Not provided"} />
                        <InfoRow label="Grandfather's Name" value={selectedUser.grandfatherName || "Not provided"} />
                        <InfoRow label="Grandmother's Name" value={selectedUser.grandmotherName || "Not provided"} />
                      </div>

                      {/* Third Row: Marital Status, Spouse Name */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <InfoRow label="Marital Status" value={selectedUser.maritalStatus || "Not provided"} />
                        <InfoRow
                          label="Spouse Name"
                          value={
                            selectedUser.spouseName ||
                            (selectedUser.maritalStatus === "Married" ? "Not provided" : "N/A")
                          }
                        />
                      </div>
                    </div>

                    {/* Contact Information Section */}
                    <div className="bg-base-200 p-6 rounded-lg shadow-sm">
                      <h4 className="font-bold text-lg mb-4 border-b pb-2 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                        Contact Information
                      </h4>
                      <div className="space-y-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <InfoRow label="Email Address" value={selectedUser.email || "Not provided"} />
                          <InfoRow label="Mobile Number" value={selectedUser.phoneNumber || "Not provided"} />
                          <InfoRow label="Telephone" value={selectedUser.telephone || "Not provided"} />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Address & System Information Column */}
                  <div className="space-y-6">
                    <div className="bg-base-200 p-6 rounded-lg shadow-sm">
                      <h4 className="font-bold text-lg mb-4 border-b pb-2 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                        Address Information
                      </h4>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                          {/* Permanent Address */}

                          <div className="border border-base-300 rounded-lg p-4">
                            <h5 className="font-medium text-base-content/70 mb-3">Permanent Address</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <InfoRow label="Province" value={selectedUser.province || "N/A"} />
                              <InfoRow label="District" value={selectedUser.district || "N/A"} />
                              <InfoRow label="Municipality/VDC" value={selectedUser.municipalityName || "N/A"} />
                              <InfoRow label="Ward Number" value={selectedUser.wardNumber || "N/A"} />
                              <InfoRow label="Tole/Street" value={selectedUser.toleStreet || "N/A"} />
                              <InfoRow label="House Number" value={selectedUser.houseNumber || "N/A"} />
                            </div>
                          </div>

                          {/* Temporary Address */}
                          <div className="border border-base-300 rounded-lg p-4">
                            <h5 className="font-medium text-base-content/70 mb-3">Temporary Address</h5>
                            {selectedUser.temporaryAddress ? (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <InfoRow label="Province" value={selectedUser.tempProvince || "N/A"} />
                                <InfoRow label="District" value={selectedUser.tempDistrict || "N/A"} />
                                <InfoRow label="Municipality/VDC" value={selectedUser.tempMunicipality || "N/A"} />
                                <InfoRow label="Ward Number" value={selectedUser.tempWardNumber || "N/A"} />
                                <InfoRow label="Tole/Street" value={selectedUser.tempToleStreet || "N/A"} />
                                <InfoRow label="House Number" value={selectedUser.tempHouseNumber || "N/A"} />
                              </div>
                            ) : (
                              <div className="text-base-content/70 italic">N/A - Same as permanent address</div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-base-200 p-6 rounded-lg shadow-sm">
                      <h4 className="font-bold text-lg mb-4 border-b pb-2 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          />
                        </svg>
                        Documents
                      </h4>
                      <div className="space-y-6">
                        {/* Citizenship Document Section */}
                        <div className="border border-base-300 rounded-lg p-4">
                          <h5 className="font-medium text-base-content/70 mb-3">Citizenship Document</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <InfoRow label="Citizenship Number" value={selectedUser.citizenshipNumber || "N/A"} />
                            <InfoRow label="Issue District" value={getDistrictName(selectedUser.citizenshipIssueDistrict)} />
                            <InfoRow
                              label="Issue Date"
                              value={
                                selectedUser.citizenshipIssueDate
                                  ? new Date(selectedUser.citizenshipIssueDate).toLocaleDateString()
                                  : "N/A"
                              }
                            />
                            <InfoRow label="Issue Office" value={selectedUser.nationalIdIssueOffice || "N/A"} />

                            {/* Document Preview Button */}
                            <div className="md:col-span-2 flex justify-end">
                              {selectedUser.documentPath && (
                                <a
                                  href={`/api/documents/${selectedUser.documentPath}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="btn btn-primary btn-sm mt-2"
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                    />
                                  </svg>
                                  View Citizenship Document
                                </a>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* National ID Section */}
                        <div className="border border-base-300 rounded-lg p-4">
                          <h5 className="font-medium text-base-content/70 mb-3">National ID</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <InfoRow label="National ID Number" value={selectedUser.nationalIdNumber || "N/A"} />
                            <InfoRow label="Issue District" value={getDistrictName(selectedUser.nationalIdIssueDistrict)} />
                            <InfoRow
                              label="Issue Date"
                              value={
                                selectedUser.nationalIdIssueDate
                                  ? new Date(selectedUser.nationalIdIssueDate).toLocaleDateString()
                                  : "N/A"
                              }
                            />
                            <InfoRow label="Issue Office" value={selectedUser.nationalIdIssueOffice || "N/A"} />

                            {/* Document Preview Button */}
                            <div className="md:col-span-2 flex justify-end">
                              {selectedUser.nationalIdDocumentPath && (
                                <a
                                  href={`/api/documents/${selectedUser.nationalIdDocumentPath}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="btn btn-primary btn-sm mt-2"
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                    />
                                  </svg>
                                  View National ID Document
                                </a>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="col-span-2 border-t border-base-300 pt-3 mt-1">
                          <h5 className="font-medium text-base-content/70 mb-2">PAN Details</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <InfoRow label="PAN Number" value={selectedUser.pan || "N/A"} />
                            <InfoRow label="Issue District" value={getDistrictName(selectedUser.panIssueDistrict)} />
                            <InfoRow
                              label="Issue Date"
                              value={
                                selectedUser.panIssueDate
                                  ? new Date(selectedUser.panIssueDate).toLocaleDateString()
                                  : "N/A"
                              }
                            />
                            <InfoRow label="Issue Office" value={selectedUser.nationalIdIssueOffice || "N/A"} />
                          </div>
                        </div>

                        {selectedUser.documentPath && (
                          <div className="col-span-2">
                            <h5 className="font-medium text-base-content/70 mb-2">Documents</h5>
                            <div className="flex flex-wrap gap-2">
                              {selectedUser.documentPath && (
                                <a
                                  href={`/api/documents/${selectedUser.documentPath}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="btn btn-outline btn-sm"
                                  onClick={e => e.stopPropagation()}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  View Document
                                </a>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                      {/* Document Preview Buttons */}
                      <div className="col-span-2 flex flex-wrap justify-end gap-2 mt-4">
                        {/* Citizenship Document Preview Button */}
                        <button
                          className="btn btn-outline btn-sm"
                          onClick={() => {
                            if (selectedUser.documentPath) {
                              window.open(`/api/documents/${selectedUser.documentPath}`, "_blank");
                            }
                          }}
                          disabled={!selectedUser.documentPath}
                          title={selectedUser.documentPath ? "Preview Citizenship Document" : "No document available"}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            />
                          </svg>
                          View Citizenship
                        </button>

                        {/* National ID Document Preview Button */}
                        <button
                          className="btn btn-outline btn-sm"
                          onClick={() => {
                            if (selectedUser.nationalIdDocumentPath) {
                              window.open(`/api/documents/${selectedUser.nationalIdDocumentPath}`, "_blank");
                            }
                          }}
                          disabled={!selectedUser.nationalIdDocumentPath}
                          title={
                            selectedUser.nationalIdDocumentPath
                              ? "Preview National ID Document"
                              : "No document available"
                          }
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            />
                          </svg>
                          View National ID
                        </button>

                        {/* PAN Document Preview Button */}
                        <button
                          className="btn btn-outline btn-sm"
                          onClick={() => {
                            if (selectedUser.panDocumentPath) {
                              window.open(`/api/documents/${selectedUser.panDocumentPath}`, "_blank");
                            }
                          }}
                          disabled={!selectedUser.panDocumentPath}
                          title={selectedUser.panDocumentPath ? "Preview PAN Document" : "No document available"}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            />
                          </svg>
                          View PAN
                        </button>

                        {/* Additional Document Preview Buttons */}
                        {selectedUser.otherDocuments?.map((doc, index) => (
                          <button
                            key={index}
                            className="btn btn-outline btn-sm"
                            onClick={() => window.open(`/api/documents/${doc.path}`, "_blank")}
                            title={`View ${doc.documentType || "Document"}`}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4 mr-1"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                            {doc.documentType || `Document ${index + 1}`}
                          </button>
                        ))}
                      </div>



                      <div className="pt-2 mt-2 border-t border-base-300 space-y-2">
                        <InfoRow label="Registered On" value={new Date(selectedUser.createdAt).toLocaleString()} />
                        {selectedUser.updatedAt && (
                          <InfoRow label="Last Updated" value={new Date(selectedUser.updatedAt).toLocaleString()} />
                        )}
                        {selectedUser.lastLoginAt ? (
                          <InfoRow label="Last Login" value={new Date(selectedUser.lastLoginAt).toLocaleString()} />
                        ) : (
                          <InfoRow label="Last Login" value="Never logged in" />
                        )}
                      </div>
                    </div>
                  </div>

                  {needsApproval(selectedUser) && (
                    <div className="bg-base-200 p-6 rounded-lg shadow-sm">
                      <h4 className="font-bold text-lg mb-4 border-b pb-2 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        Actions
                      </h4>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <button
                          onClick={async e => {
                            e.stopPropagation();
                            await handleApproveUser(selectedUser);
                            closeModals();
                          }}
                          className={`btn ${
                            selectedUser.status === "Active" || selectedUser.status === "Rejected"
                              ? "btn-disabled"
                              : "btn-success"
                          } flex-1`}
                          disabled={selectedUser.status === "Active" || selectedUser.status === "Rejected"}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 mr-1"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                          {selectedUser.status === "Active" || selectedUser.status === "Rejected"
                            ? "Already Processed"
                            : "Approve User"}
                        </button>
                        <button
                          onClick={e => {
                            e.stopPropagation();
                            setShowDetailModal(false);
                            setShowRejectModal(true);
                          }}
                          className="btn btn-error flex-1"
                          disabled={selectedUser.status === "Active" || selectedUser.status === "Rejected"}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 mr-1"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                          Reject User
                        </button>
                      </div>
                    </div>
                  )}

                  {selectedUser.status === "Active" && selectedUser.temporaryPassword && (
                    <div className="bg-warning/10 rounded-lg p-4 border border-warning/20">
                      <div className="flex items-start">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-warning mt-0.5 mr-2 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h2a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <div>
                          <h4 className="font-bold text-warning">Temporary Password</h4>
                          <p className="text-sm text-warning/80 mt-1 mb-3">
                            This password was generated for the user. Make sure to copy it now as it won't be shown
                            again.
                          </p>
                          <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
                            <div className="relative flex-grow">
                              <input
                                type="text"
                                readOnly
                                value={selectedUser.temporaryPassword}
                                className="input input-bordered w-full font-mono pr-10"
                              />
                              <button
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-xs"
                                onClick={e => {
                                  e.stopPropagation();
                                  navigator.clipboard.writeText(selectedUser.temporaryPassword || "");
                                  const temp = selectedUser.temporaryPassword;
                                  setSelectedUser({ ...selectedUser, temporaryPassword: "Copied!" });
                                  setTimeout(() => {
                                    setSelectedUser({ ...selectedUser, temporaryPassword: temp });
                                  }, 2000);
                                }}
                                title="Copy to clipboard"
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="modal-action">
                <button onClick={closeModals} className="btn">
                  Close
                </button>
              </div>
            </div>
            <div className="modal-backdrop" onClick={closeModals}></div>
          </div>
        )}
      </>
    </AdminLayout>
  );
};

export default PendingUsers;
