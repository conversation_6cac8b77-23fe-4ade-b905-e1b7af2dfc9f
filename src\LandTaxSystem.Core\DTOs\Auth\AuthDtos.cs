using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Auth
{
    public class RegisterDto
    {
        // Personal Information
        [Required, MaxLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? MiddleName { get; set; }

        [Required, MaxLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        public DateTime DateOfBirth { get; set; }
        
        [Required, MaxLength(10)]
        public string Gender { get; set; } = string.Empty;
        
        [Required, MaxLength(20)]
        public string Nationality { get; set; } = "Nepali"; // Default value

        [MaxLength(100)]
        public string? Profession { get; set; }

        public bool IsMinor { get; set; } = false;

        [MaxLength(255)]
        public string? GuardianName { get; set; }

        [MaxLength(50)]
        public string? GuardianRelation { get; set; }
        
        [Required, MaxLength(50)]
        public string CitizenshipNumber { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? PAN { get; set; }

        // Address Information - Permanent
        [Required]
        public Guid PermanentProvinceId { get; set; }

        [Required]
        public Guid PermanentDistrictId { get; set; }

        [Required]
        public Guid PermanentMunicipalityId { get; set; }
        
        [Required, Range(1, 50)]
        public int PermanentWardNumber { get; set; }
        
        [Required, MaxLength(100)]
        public string PermanentTole { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? PermanentHouseNumber { get; set; }

        // Address Information - Temporary (Optional)
        public Guid? TemporaryProvinceId { get; set; }

        public Guid? TemporaryDistrictId { get; set; }

        public Guid? TemporaryMunicipalityId { get; set; }
        
        public int? TemporaryWardNumber { get; set; }
        
        [MaxLength(100)]
        public string? TemporaryTole { get; set; }

        [MaxLength(50)]
        public string? TemporaryHouseNumber { get; set; }

        // Contact Information
        [Phone, MaxLength(15)]
        public string? Telephone { get; set; }

        [Phone, MaxLength(15)]
        public string? Mobile { get; set; }

        [Required, EmailAddress, MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        // Relative Information
        [Required, MaxLength(100)]
        public string FatherName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string? MotherName { get; set; }
        
        [Required, MaxLength(100)]
        public string GrandfatherName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string? GrandmotherName { get; set; }
        
        [MaxLength(20)]
        public string? MaritalStatus { get; set; }
        
        [MaxLength(100)]
        public string? SpouseName { get; set; }

        // Preferred Alert Service
        public bool PreferSMS { get; set; } = true;
        public bool PreferEmail { get; set; } = true;

        // Authentication
        [Required, MinLength(8), MaxLength(128)]
        [RegularExpression(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$",
            ErrorMessage = "Password must include at least one uppercase letter, one lowercase letter, one digit, and one special character.")]
        public string Password { get; set; } = string.Empty;

        [Required, Compare("Password")]
        public string ConfirmPassword { get; set; } = string.Empty;
        
        // Document Metadata
        // Citizenship Document
        [MaxLength(100)]
        public string? CitizenshipIssueDistrict { get; set; }
        
        public DateTime? CitizenshipIssueDate { get; set; }
        
        [MaxLength(100)]
        public string? CitizenshipIssueOffice { get; set; }
        
        // National ID Document
        [MaxLength(50)]
        public string? NationalIdNumber { get; set; }
        
        [MaxLength(100)]
        public string? NationalIdIssueDistrict { get; set; }
        
        public DateTime? NationalIdIssueDate { get; set; }
        
        [MaxLength(100)]
        public string? NationalIdIssueOffice { get; set; }
        
        // PAN Document
        public DateTime? PanIssueDate { get; set; }
        
        [MaxLength(100)]
        public string? PanIssueDistrict { get; set; }
        
        [MaxLength(100)]
        public string? PanIssueOffice { get; set; }
        
        // The documents will be handled separately as file uploads
        // in the API controller using IFormFile
        // - Citizenship document
        // - National ID document
        // - PAN document
    }

    public class LoginDto
    {
        // Either Email or PAN must be provided, but not necessarily both
        [EmailAddress]
        public string? Email { get; set; }

        // PAN serves as TaxPayerID for login
        [MaxLength(20)]
        public string? PAN { get; set; }

        [Required]
        public string Password { get; set; } = string.Empty;
    }

    public class AuthResponseDto
    {
        public string Token { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
    }
}
