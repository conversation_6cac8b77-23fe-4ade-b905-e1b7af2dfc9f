using System;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs;
using LandTaxSystem.Core.Entities;

namespace LandTaxSystem.Core.Interfaces
{
    /// <summary>
    /// Service interface for calculating tax on properties
    /// </summary>
    public interface ITaxCalculationService
    {
        /// <summary>
        /// Calculates tax for a property based on the specified fiscal year's rules
        /// </summary>
        /// <param name="property">The property to calculate tax for</param>
        /// <param name="fiscalYearId">The fiscal year to use for tax calculation</param>
        /// <returns>Detailed tax calculation result</returns>
        Task<TaxCalculationResult> CalculateTaxAsync(Property property, Guid fiscalYearId);

        /// <summary>
        /// Calculates tax for a property using the current active fiscal year
        /// </summary>
        /// <param name="property">The property to calculate tax for</param>
        /// <returns>Detailed tax calculation result</returns>
        Task<TaxCalculationResult> CalculateTaxAsync(Property property);

        /// <summary>
        /// Calculates tax for a property by property ID and fiscal year ID
        /// </summary>
        /// <param name="propertyId">The ID of the property to calculate tax for</param>
        /// <param name="fiscalYearId">The fiscal year to use for tax calculation</param>
        /// <returns>Detailed tax calculation result</returns>
        Task<TaxCalculationResult> CalculateTaxAsync(Guid propertyId, Guid fiscalYearId);
    }
}
