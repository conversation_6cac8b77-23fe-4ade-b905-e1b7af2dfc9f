import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import AdminSidebar from "./AdminSidebar";
import AdminHeader from "./AdminHeader";
import AdminBreadcrumb from "./AdminBreadcrumb";
import type { BreadcrumbItem } from "./AdminBreadcrumb";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  className?: string;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({
  children,
  title,
  subtitle,
  breadcrumbs,
  className = "",
}) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const location = useLocation();

  // Auto-generate title from route if not provided
  const getPageTitle = () => {
    if (title) return title;

    const path = location.pathname;
    const segments = path.split("/").filter(Boolean);

    if (segments.length === 0) return "Dashboard";

    // Convert route segments to readable titles
    const titleMap: { [key: string]: string } = {
      dashboard: "Dashboard",
      properties: "Properties",
      users: "User Management",
      municipalities: "Municipality Management",
      "fiscal-years": "Fiscal Year Management",
      assessments: "Assessment Management",
      "tax-config": "Tax Configuration",
      "preliminary-assessments": "Preliminary Assessments",
      appeals: "Appeals",
      negotiations: "Negotiations",
      payments: "Payments",
      register: "Register Property",
      review: "Property Review",
      pending: "Pending Users",
    };

    const lastSegment = segments[segments.length - 1];
    return (
      titleMap[lastSegment] ||
      lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1)
    );
  };

  // Auto-generate breadcrumbs if not provided
  const getBreadcrumbs = (): BreadcrumbItem[] => {
    if (breadcrumbs) return breadcrumbs;

    const path = location.pathname;
    const segments = path.split("/").filter(Boolean);

    const items: BreadcrumbItem[] = [
      {
        label: "Dashboard",
        href: "/dashboard",
        icon: (
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
            />
          </svg>
        ),
      },
    ];

    if (
      segments.length > 1 ||
      (segments.length === 1 && segments[0] !== "dashboard")
    ) {
      const titleMap: { [key: string]: string } = {
        properties: "Properties",
        users: "Users",
        municipalities: "Municipalities",
        "fiscal-years": "Fiscal Years",
        assessments: "Assessments",
        "tax-config": "Tax Configuration",
        "preliminary-assessments": "Preliminary Assessments",
        appeals: "Appeals",
        negotiations: "Negotiations",
        payments: "Payments",
      };

      let currentPath = "";
      segments.forEach((segment, index) => {
        currentPath += `/${segment}`;
        const isLast = index === segments.length - 1;

        items.push({
          label:
            titleMap[segment] ||
            segment.charAt(0).toUpperCase() + segment.slice(1),
          href: isLast ? undefined : currentPath,
        });
      });
    }

    return items;
  };

  // Close sidebar when route changes (mobile)
  useEffect(() => {
    setIsSidebarOpen(false);
  }, [location.pathname]);

  // Close sidebar when clicking outside (mobile)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (
        isSidebarOpen &&
        !target.closest(".drawer-side") &&
        !target.closest(".drawer-toggle")
      ) {
        setIsSidebarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isSidebarOpen]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const pageTitle = getPageTitle();
  const pageBreadcrumbs = getBreadcrumbs();

  return (
    <div className="drawer lg:drawer-open min-h-screen bg-base-200">
      {/* Drawer Toggle Input */}
      <input
        id="admin-drawer"
        type="checkbox"
        className="drawer-toggle"
        checked={isSidebarOpen}
        onChange={toggleSidebar}
      />

      {/* Main Content */}
      <div className="drawer-content flex flex-col">
        {/* Header */}
        <AdminHeader
          title={pageTitle}
          subtitle={subtitle}
          onMenuToggle={toggleSidebar}
        />

        {/* Breadcrumbs */}
        {pageBreadcrumbs.length > 1 && (
          <div className="bg-base-100 border-b border-base-300 px-4 py-2">
            <AdminBreadcrumb items={pageBreadcrumbs} />
          </div>
        )}

        {/* Page Content */}
        <main className={`flex-1 p-6 ${className}`}>{children}</main>
      </div>

      {/* Sidebar */}
      <div className="drawer-side">
        <label
          htmlFor="admin-drawer"
          className="drawer-overlay lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        ></label>
        <AdminSidebar />
      </div>
    </div>
  );
};

export default AdminLayout;
