import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import api from "../services/api";
import { fiscalYearService } from "../services/fiscalYearService";
import type { FiscalYear } from "../services/taxConfigService";
import { MapContainer, TileLayer, Polygon, useMapEvents } from "react-leaflet";
import AreaInput, { type AreaMeasurement } from "../components/AreaInput";
import type { RopaniSystem, BighaSystem } from "../utils/areaConverter";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import { type LandDetailFormState } from "./LandDetailForm";
import AdminLayout from "../components/admin/AdminLayout";

// Fix for default markers in react-leaflet
// @ts-expect-error - Known issue with Leaflet types
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
  iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
});

interface Province {
  provinceId: string;
  name: string;
  code: string;
}
interface District {
  districtId: string;
  name: string;
  code: string;
  provinceId: string;
}
interface Municipality {
  municipalityId: string;
  name: string;
  districtId: string;
  wardCount: number;
}

interface PropertyFormData {
  province: string;
  district: string;
  municipalityId: string;
  wardNumber: number;
  street: string;
  parcelNumber: string;
  address: string;
  landArea: number;
  usageType: "Residential" | "Commercial" | "Industrial" | "Agricultural" | "";
  molNo?: string;
  lalpurjaNo?: string;
  sheetNo?: string;
  builtUpArea?: number;
  constructionType?: string;
  constructionYear?: number;
  parcelGeometry: L.LatLng[][];
  // Valuation Purpose fields
  landType?: string;
  natureOfLand?: string;
  landUse?: string;
  landOwnership?: string;
  streetType?: string;
  relationWithStreet?: string;
  physicalArea?: string;
  isExempted?: boolean;
  exemptionReason?: string;

  // Building on Land fields
  buildingNumber?: string;
  floorNumber?: string;
  buildingConstructionYear?: number;
  buildingType?: string;

  // Facilities in Land (multiple selection)
  facilities?: string[];

  // Directions (4 killa)
  eastBoundary?: string;
  westBoundary?: string;
  southBoundary?: string;
  northBoundary?: string;

  // Land Ownership
  ownershipType?: string;
  ownershipDetails?: string;
  ownershipValue?: string;

  // Others Section
  isTaxApplicable?: boolean;
  applicableFiscalYear?: string;
  otherDetails?: string;
  isDeregistered?: boolean;
  deregistrationReason?: string;
}

interface MapDrawerProps {
  onPolygonComplete: (coordinates: L.LatLng[][]) => void;
  polygon: L.LatLng[][];
}

const MapDrawer: React.FC<MapDrawerProps> = ({ onPolygonComplete, polygon }) => {
  const [drawing, setDrawing] = useState(false);
  const [currentPolygon, setCurrentPolygon] = useState<L.LatLng[]>([]);

  const MapEvents = () => {
    useMapEvents({
      click: e => {
        if (drawing) {
          setCurrentPolygon(prev => [...prev, e.latlng]);
        }
      },
      dblclick: () => {
        if (drawing && currentPolygon.length >= 3) {
          onPolygonComplete([currentPolygon]);
          setDrawing(false);
          setCurrentPolygon([]);
        }
      },
    });
    return null;
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <button
          type="button"
          onClick={() => {
            setDrawing(true);
            setCurrentPolygon([]);
          }}
          disabled={drawing}
          className="btn btn-primary disabled:opacity-50"
        >
          {drawing ? "Drawing..." : "Start Drawing Parcel"}
        </button>
        {drawing && (
          <button
            type="button"
            onClick={() => {
              setDrawing(false);
              setCurrentPolygon([]);
            }}
            className="btn btn-secondary"
          >
            Cancel
          </button>
        )}
      </div>
      <div className="text-sm text-base-content/70">
        {drawing ? (
          <p>Click to add points. Double-click to complete.</p>
        ) : (
          <p>Click "Start Drawing Parcel" to begin.</p>
        )}
      </div>
      <div className="h-96 border rounded-lg overflow-hidden">
        <MapContainer center={[27.7172, 85.324]} zoom={13} style={{ height: "100%", width: "100%" }}>
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />
          <MapEvents />
          {drawing && currentPolygon.length > 0 && (
            <Polygon positions={currentPolygon} pathOptions={{ color: "red", fillOpacity: 0.3 }} />
          )}
          {polygon.length > 0 && !drawing && (
            <Polygon positions={polygon} pathOptions={{ color: "blue", fillOpacity: 0.4 }} />
          )}
        </MapContainer>
      </div>
    </div>
  );
};

const PropertyRegistration: React.FC = () => {
  const navigate = useNavigate();
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [municipalities, setMunicipalities] = useState<Municipality[]>([]);
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState<PropertyFormData>({
    province: "",
    district: "",
    municipalityId: "",
    wardNumber: 1,
    street: "",
    parcelNumber: "",
    address: "",
    landArea: 0.01,
    usageType: "",
    builtUpArea: undefined,
    constructionType: "",
    constructionYear: undefined,
    parcelGeometry: [],
    isExempted: false,
    exemptionReason: "",
  });
  const [area, setArea] = useState<AreaMeasurement>();
  const [landDetailFormData, setLandDetailFormData] = useState<LandDetailFormState>({
    oldVdc: "",
    oldWardNo: "",
    currentWardNo: "",
    kittaNo: "",
    molNo: "",
    mapNo: "",
    fiscalYear: "2080/81",
    otherDetails: "",
    isMultiRateValuation: false,
    isTemporaryHouse: false,
    isCultivable: false,
    measurementUnit: "ropani-aana-paisa-daam",
    areaBigha: "0",
    areaKattha: "0",
    areaDhur: "0",
    areaRopani: "0",
    areaAana: "0",
    areaPaisa: "0",
    areaDaam: "0",
    taxpayerPrice: "0.00",
    taxpayerLandRevenuePrice: "0.00",
    deactivatePlot: false,
    lastFyForInclusion: "",
    landRevenueApplicable: "no",
    structureAreaLength: "",
    structureAreaBreadth: "",
  });
  const [files, setFiles] = useState<{
    ownershipCertificate: File | null;
    buildingPermit: File | null;
  }>({ ownershipCertificate: null, buildingPermit: null });

  useEffect(() => {
    if (!area) return;
    setFormData(prev => ({ ...prev, landArea: area.squareMeters }));
    const values = area.values;
    if (area.unitSystem === "ropani" && "ropani" in values) {
      const { ropani, aana, paisa, daam } = values as RopaniSystem;
      setLandDetailFormData(prev => ({
        ...prev,
        areaRopani: String(ropani),
        areaAana: String(aana),
        areaPaisa: String(paisa),
        areaDaam: String(daam),
        areaBigha: "0",
        areaKattha: "0",
        areaDhur: "0",
      }));
    } else if (area.unitSystem === "bigha" && "bigha" in values) {
      const { bigha, kattha, dhur } = values as BighaSystem;
      setLandDetailFormData(prev => ({
        ...prev,
        areaBigha: String(bigha),
        areaKattha: String(kattha),
        areaDhur: String(dhur),
        areaRopani: "0",
        areaAana: "0",
        areaPaisa: "0",
        areaDaam: "0",
      }));
    }
  }, [area]);

  useEffect(() => {
    api
      .get("/provinces")
      .then(res => setProvinces(res.data))
      .catch((err: unknown) => {
        console.error("Error fetching provinces:", err);
        setProvinces([]);
      });
  }, []);

  useEffect(() => {
    fiscalYearService
      .getAll()
      .then((res: FiscalYear[]) => {
        setFiscalYears(res);
        // Set the first fiscal year as default if available
        if (res.length > 0) {
          setLandDetailFormData(prev => ({
            ...prev,
            fiscalYear: res[0].name
          }));
        }
      })
      .catch((err: unknown) => {
        console.error("Error fetching fiscal years:", err);
        setFiscalYears([]);
      });
  }, []);

  useEffect(() => {
    if (formData.province) {
      api
        .get(`/provinces/${formData.province}/districts`)
        .then(res => {
          setDistricts(res.data);
          setFormData(prev => ({
            ...prev,
            district: "",
            municipalityId: "",
          }));
        })
        .catch((err: unknown) => {
          console.error("Error fetching districts:", err);
          setDistricts([]);
        });
    } else {
      setDistricts([]);
      setFormData(prev => ({ ...prev, district: "", municipalityId: "" }));
    }
  }, [formData.province]);

  useEffect(() => {
    if (formData.district) {
      api
        .get(`/districts/${formData.district}/municipalities`)
        .then(res => {
          setMunicipalities(res.data);
          setFormData(prev => ({ ...prev, municipalityId: "" }));
        })
        .catch((err: unknown) => {
          console.error("Error fetching municipalities:", err);
          setMunicipalities([]);
        });
    } else {
      setMunicipalities([]);
    }
  }, [formData.district]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: (e.target as HTMLInputElement).type === "number" ? parseFloat(value) || 0 : value,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files: selectedFiles } = e.target;
    if (selectedFiles?.[0]) {
      setFiles(prev => ({ ...prev, [name]: selectedFiles[0] }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submission - landArea:", formData.landArea);
    console.log("Form submission - parcelGeometry:", formData.parcelGeometry);
    console.log("Form submission - area state:", area);

    if (!files.ownershipCertificate) {
      toast.error("Ownership Certificate is required");
      return;
    }
    if (formData.landArea < 0.01) {
      toast.error("Land area must be at least 0.01 sq meters");
      return;
    }
    if (formData.parcelGeometry.length === 0) {
      toast.error("Please draw the property boundary");
      return;
    }

    setLoading(true);
    const submissionData = new FormData();

    // Map frontend field names to backend expected names
    submissionData.append("MunicipalityId", formData.municipalityId);
    submissionData.append("WardNumber", String(formData.wardNumber));
    submissionData.append("Street", formData.street);
    submissionData.append("ParcelNumber", formData.parcelNumber);
    submissionData.append("Address", formData.address);
    submissionData.append("LandAreaSqM", String(formData.landArea));
    submissionData.append("UsageType", formData.usageType);
    if (formData.molNo) submissionData.append("MolNo", formData.molNo);
    if (formData.sheetNo) submissionData.append("SheetNo", formData.sheetNo);

    // Land details are now included in the LandDetails object below

    // Add building details if provided
    if (formData.builtUpArea) submissionData.append("BuildingBuiltUpAreaSqM", String(formData.builtUpArea));
    if (formData.constructionType) submissionData.append("BuildingConstructionType", formData.constructionType);
    if (formData.constructionYear) submissionData.append("BuildingConstructionYear", String(formData.constructionYear));

    // Convert polygon coordinates to proper GeoJSON format
    if (formData.parcelGeometry.length > 0) {
      const geoJson = {
        type: "Polygon",
        coordinates: [formData.parcelGeometry[0].map(point => [point.lng, point.lat])],
      };
      submissionData.append("ParcelGeoJson", JSON.stringify(geoJson));
    }

    // Add applicable fiscal year
    if (formData.applicableFiscalYear) {
      const selectedFiscalYear = fiscalYears.find(fy => fy.name === formData.applicableFiscalYear);
      if (selectedFiscalYear) {
        submissionData.append("ApplicableFiscalYearId", selectedFiscalYear.fiscalYearId);
      }
    }

    // Add files
    if (files.ownershipCertificate) submissionData.append("OwnershipCertificate", files.ownershipCertificate);
    if (files.buildingPermit) submissionData.append("BuildingPermit", files.buildingPermit);

    // Add each land detail field separately with LandDetails. prefix
    submissionData.append("LandDetails.PropertyId", "00000000-0000-0000-0000-000000000000");
    submissionData.append("LandDetails.OldVdc", landDetailFormData.oldVdc || "");
    submissionData.append("LandDetails.OldWardNo", landDetailFormData.oldWardNo || "");
    submissionData.append("LandDetails.CurrentWardNo", landDetailFormData.currentWardNo || "");
    submissionData.append("LandDetails.KittaNo", landDetailFormData.kittaNo || "");
    submissionData.append("LandDetails.MapNo", landDetailFormData.mapNo || "");
    submissionData.append("LandDetails.FiscalYear", landDetailFormData.fiscalYear || "2080/81");
    submissionData.append("LandDetails.OtherDetails", landDetailFormData.otherDetails || "");
    submissionData.append("LandDetails.IsMultiRateValuation", String(landDetailFormData.isMultiRateValuation || false));
    submissionData.append("LandDetails.IsTemporaryHouse", String(landDetailFormData.isTemporaryHouse || false));
    submissionData.append("LandDetails.IsCultivable", String(landDetailFormData.isCultivable || false));
    submissionData.append(
      "LandDetails.MeasurementUnit",
      landDetailFormData.measurementUnit || "ropani-aana-paisa-daam"
    );
    submissionData.append("LandDetails.AreaBigha", String(parseFloat(landDetailFormData.areaBigha) || 0));
    submissionData.append("LandDetails.AreaKattha", String(parseFloat(landDetailFormData.areaKattha) || 0));
    submissionData.append("LandDetails.AreaDhur", String(parseFloat(landDetailFormData.areaDhur) || 0));
    submissionData.append("LandDetails.AreaRopani", String(parseFloat(landDetailFormData.areaRopani) || 0));
    submissionData.append("LandDetails.AreaAana", String(parseFloat(landDetailFormData.areaAana) || 0));
    submissionData.append("LandDetails.AreaPaisa", String(parseFloat(landDetailFormData.areaPaisa) || 0));
    submissionData.append("LandDetails.AreaDaam", String(parseFloat(landDetailFormData.areaDaam) || 0));
    submissionData.append("LandDetails.TaxpayerPrice", String(parseFloat(landDetailFormData.taxpayerPrice) || 0));
    submissionData.append(
      "LandDetails.TaxpayerLandRevenuePrice",
      String(parseFloat(landDetailFormData.taxpayerLandRevenuePrice) || 0)
    );
    submissionData.append("LandDetails.DeactivatePlot", String(landDetailFormData.deactivatePlot || false));
    submissionData.append("LandDetails.LastFyForInclusion", landDetailFormData.lastFyForInclusion || "");
    // Convert the string value to boolean for the backend
    const landRevenueApplicable = landDetailFormData.landRevenueApplicable === "full" ? "true" : "false";
    submissionData.append("LandDetails.LandRevenueApplicable", landRevenueApplicable);
    submissionData.append(
      "LandDetails.StructureAreaLength",
      String(parseFloat(landDetailFormData.structureAreaLength) || 0)
    );
    submissionData.append(
      "LandDetails.StructureAreaBreadth",
      String(parseFloat(landDetailFormData.structureAreaBreadth) || 0)
    );

    try {
      await api.post("/properties", submissionData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      toast.success("Property registered successfully! Redirecting to dashboard...");
      setTimeout(() => navigate("/dashboard"), 2000);
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Registration failed: ${error.message}`);
      } else {
        toast.error("Registration failed: An unknown error occurred.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout title="Property Registration">
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div>
            <h1 className="text-3xl font-medium text-gray-900 mb-6">Property Registration</h1>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Location Information */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <h2 className="text-xl font-medium text-gray-900 mb-4">Location Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                    <div className="form-control">
                      <label htmlFor="province" className="label">
                        <span className="label-text">Province *</span>
                      </label>
                      <select
                        id="province"
                        name="province"
                        value={formData.province}
                        onChange={handleInputChange}
                        required
                        className="select select-bordered w-full"
                      >
                        <option value="">Select Province</option>
                        {provinces.map(p => (
                          <option key={p.provinceId} value={p.provinceId}>
                            {p.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="form-control">
                      <label htmlFor="district" className="label">
                        <span className="label-text">District *</span>
                      </label>
                      <select
                        id="district"
                        name="district"
                        value={formData.district}
                        onChange={handleInputChange}
                        required
                        disabled={!formData.province}
                        className="select select-bordered w-full disabled:opacity-50"
                      >
                        <option value="">Select District</option>
                        {districts.map(d => (
                          <option key={d.districtId} value={d.districtId}>
                            {d.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="form-control">
                      <label htmlFor="municipalityId" className="label">
                        <span className="label-text">Municipality *</span>
                      </label>
                      <select
                        id="municipalityId"
                        name="municipalityId"
                        value={formData.municipalityId}
                        onChange={handleInputChange}
                        required
                        disabled={!formData.district}
                        className="select select-bordered w-full disabled:opacity-50"
                      >
                        <option value="">Select Municipality</option>
                        {municipalities.map(m => (
                          <option key={m.municipalityId} value={m.municipalityId}>
                            {m.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="form-control">
                      <label htmlFor="wardNumber" className="label">
                        <span className="label-text">Ward Number *</span>
                      </label>
                      <input
                        type="number"
                        id="wardNumber"
                        name="wardNumber"
                        value={formData.wardNumber}
                        onChange={handleInputChange}
                        required
                        min="1"
                        className="input input-bordered w-full"
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="street" className="label">
                        <span className="label-text">Street/Tole</span>
                      </label>
                      <input
                        type="text"
                        id="street"
                        name="street"
                        value={formData.street}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                      />
                    </div>
                  </div>
                  
                  {/* Additional Required Fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div className="form-control">
                      <label htmlFor="address" className="label">
                        <span className="label-text">Full Address *</span>
                      </label>
                      <textarea
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        required
                        className="textarea textarea-bordered w-full"
                        placeholder="Enter complete property address"
                        rows={3}
                      />
                    </div>
                    
                    <div className="form-control">
                      <label htmlFor="usageType" className="label">
                        <span className="label-text">Property Usage Type *</span>
                      </label>
                      <select
                        id="usageType"
                        name="usageType"
                        value={formData.usageType}
                        onChange={handleInputChange}
                        required
                        className="select select-bordered w-full"
                      >
                        <option value="">Select Usage Type</option>
                        <option value="Residential">Residential</option>
                        <option value="Commercial">Commercial</option>
                        <option value="Industrial">Industrial</option>
                        <option value="Agricultural">Agricultural</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              {/* Property Details */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <h2 className="text-xl font-medium text-gray-900 mb-4">Property Details</h2>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-x-8 gap-y-6">
                    <div className="form-control">
                      <label htmlFor="sheetNo" className="label">
                        <span className="label-text">Sheet Number</span>
                      </label>
                      <input
                        type="text"
                        id="sheetNo"
                        name="sheetNo"
                        value={formData.sheetNo || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="Land record sheet number"
                        maxLength={50}
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="parcelNumber" className="label">
                        <span className="label-text">Parcel Number (Kitta No) *</span>
                      </label>
                      <input
                        type="text"
                        id="parcelNumber"
                        name="parcelNumber"
                        value={formData.parcelNumber}
                        onChange={handleInputChange}
                        required
                        className="input input-bordered w-full"
                      />
                    </div>

                    <div className="form-control">
                      <label htmlFor="molNo" className="label">
                        <span className="label-text">MOL Number</span>
                      </label>
                      <input
                        type="text"
                        id="molNo"
                        name="molNo"
                        value={formData.molNo || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="Ministry of Land number"
                        maxLength={100}
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="lalpurjaNo" className="label">
                        <span className="label-text">Lalpurja No.</span>
                      </label>
                      <input
                        type="text"
                        id="lalpurjaNo"
                        name="lalpurjaNo"
                        value={formData.lalpurjaNo || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="Lalpurja document number"
                        maxLength={100}
                      />
                    </div>
                    <div className="form-control md:col-span-4">
                      <div>
                        <label className="label">
                          <span className="label-text">Land Area</span>
                        </label>
                        <AreaInput onChange={setArea} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Property Geolocation */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <h2 className="text-xl font-medium text-gray-900 mb-4">Property Geolocation</h2>
                  <MapDrawer
                    onPolygonComplete={p => setFormData(prev => ({ ...prev, parcelGeometry: p }))}
                    polygon={formData.parcelGeometry}
                  />
                </div>
              </div>

              {/* For Valuation Purpose Section */}
              <div className="bg-white rounded-lg shadow p-6 mt-6">
                <h2 className="text-xl font-semibold mb-6">For Valuation Purpose</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Type of Land */}
                  <div className="form-control">
                    <label htmlFor="landType" className="label">
                      <span className="label-text">प्रयोगको प्रकार (Type of Land) *</span>
                    </label>
                    <select
                      id="landType"
                      name="landType"
                      value={formData.landType || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Select Type</option>
                      <option value="Agricultural">Agricultural</option>
                      <option value="Residential">Residential</option>
                      <option value="Commercial">Commercial</option>
                      <option value="Industrial">Industrial</option>
                      <option value="Forest">Forest</option>
                      <option value="Grazing">Grazing</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  {/* Nature of Land */}
                  <div className="form-control">
                    <label htmlFor="natureOfLand" className="label">
                      <span className="label-text">Nature of Land *</span>
                    </label>
                    <select
                      id="natureOfLand"
                      name="natureOfLand"
                      value={formData.natureOfLand || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Select Nature</option>
                      <option value="Flat">Flat</option>
                      <option value="Sloping">Sloping</option>
                      <option value="Valley">Valley</option>
                      <option value="Hilly">Hilly</option>
                      <option value="Mountainous">Mountainous</option>
                    </select>
                  </div>

                  {/* Land Use */}
                  <div className="form-control">
                    <label htmlFor="landUse" className="label">
                      <span className="label-text">Land Use *</span>
                    </label>
                    <select
                      id="landUse"
                      name="landUse"
                      value={formData.landUse || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Select Land Use</option>
                      <option value="Agriculture">Agriculture</option>
                      <option value="Residential">Residential</option>
                      <option value="Commercial">Commercial</option>
                      <option value="Mixed Use">Mixed Use</option>
                      <option value="Public">Public</option>
                      <option value="Institutional">Institutional</option>
                    </select>
                  </div>

                  {/* Land Ownership */}
                  <div className="form-control">
                    <label htmlFor="landOwnership" className="label">
                      <span className="label-text">Land Ownership *</span>
                    </label>
                    <select
                      id="landOwnership"
                      name="landOwnership"
                      value={formData.landOwnership || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Select Ownership</option>
                      <option value="Private">Private</option>
                      <option value="Guthi">Guthi</option>
                      <option value="Government">Government</option>
                      <option value="Trust">Trust</option>
                      <option value="Institutional">Institutional</option>
                    </select>
                  </div>

                  {/* Street Type */}
                  <div className="form-control">
                    <label htmlFor="streetType" className="label">
                      <span className="label-text">Street Type *</span>
                    </label>
                    <select
                      id="streetType"
                      name="streetType"
                      value={formData.streetType || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Select Street Type</option>
                      <option value="Main Road">Main Road</option>
                      <option value="Feeder Road">Feeder Road</option>
                      <option value="Alley">Alley</option>
                      <option value="Highway">Highway</option>
                      <option value="No Direct Access">No Direct Access</option>
                    </select>
                  </div>

                  {/* Relation with Street */}
                  <div className="form-control">
                    <label htmlFor="relationWithStreet" className="label">
                      <span className="label-text">Relation with Street *</span>
                    </label>
                    <select
                      id="relationWithStreet"
                      name="relationWithStreet"
                      value={formData.relationWithStreet || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Select Relation</option>
                      <option value="Corner Plot">Corner Plot</option>
                      <option value="Middle Plot">Middle Plot</option>
                      <option value="End Plot">End Plot</option>
                      <option value="Interior Plot">Interior Plot</option>
                    </select>
                  </div>

                  {/* Tax Exemption */}
                  <div className="form-control col-span-1">
                    <label className="label cursor-pointer justify-start gap-3">
                      <input
                        type="checkbox"
                        id="isExempted"
                        name="isExempted"
                        checked={!!formData.isExempted}
                        onChange={e => {
                          // Update the isExempted state
                          setFormData(prev => ({
                            ...prev,
                            isExempted: e.target.checked,
                            // Clear exemption reason when unchecking
                            ...(e.target.checked ? {} : { exemptionReason: "" }),
                          }));
                        }}
                        className="checkbox checkbox-primary"
                      />
                      <span className="label-text font-medium">Is this property tax exempted?</span>
                    </label>
                  </div>

                  {/* Exemption Reason (Conditional) */}
                  {formData.isExempted && (
                    <div className="form-control col-span-2">
                      <label htmlFor="exemptionReason" className="label">
                        <span className="label-text">Exemption Reason *</span>
                      </label>
                      <textarea
                        id="exemptionReason"
                        name="exemptionReason"
                        value={formData.exemptionReason || ""}
                        onChange={handleInputChange}
                        className="textarea textarea-bordered w-full h-32"
                        placeholder="Please provide the reason for tax exemption"
                        required
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Building on Land Section */}
              <div className="card bg-base-100 shadow-md mb-6">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold mb-4">
                    जग्गामा भवन भएमा (If there is a building on the land)
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Building Number */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">संख्याः (Number)</span>
                      </label>
                      <input
                        type="text"
                        name="buildingNumber"
                        value={formData.buildingNumber || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="Building number"
                      />
                    </div>

                    {/* Floor */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">तलाः (Floor)</span>
                      </label>
                      <input
                        type="text"
                        name="floorNumber"
                        value={formData.floorNumber || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="Floor number"
                      />
                    </div>

                    {/* Construction Year */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">निर्माण वर्ष (Construction Year)</span>
                      </label>
                      <input
                        type="number"
                        name="buildingConstructionYear"
                        value={formData.buildingConstructionYear || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="e.g., 2020"
                        min="1900"
                        max={new Date().getFullYear()}
                      />
                    </div>

                    {/* Building Type */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">प्रकार (Type)</span>
                      </label>
                      <select
                        name="buildingType"
                        value={formData.buildingType || ""}
                        onChange={handleInputChange}
                        className="select select-bordered w-full"
                      >
                        <option value="">Select Type</option>
                        <option value="Residential">Residential</option>
                        <option value="Commercial">Commercial</option>
                        <option value="Industrial">Industrial</option>
                        <option value="Mixed Use">Mixed Use</option>
                        <option value="Institutional">Institutional</option>
                        <option value="Religious">Religious</option>
                        <option value="Agricultural">Agricultural</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              {/* Facilities in Land Section */}
              <div className="card bg-base-100 shadow-md mb-6">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold mb-4">Facilities in Land</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[
                      "Electricity",
                      "Water Supply",
                      "Sewer",
                      "Road Access",
                      "Drainage",
                      "Telephone",
                      "Internet",
                      "Gas Supply",
                      "Street Lighting",
                      "Waste Management",
                      "Parking",
                      "Security",
                      "Playground",
                      "Garden",
                      "Park",
                      "Swimming Pool",
                    ].map(facility => (
                      <label key={facility} className="label cursor-pointer justify-start gap-2">
                        <input
                          type="checkbox"
                          name="facilities"
                          value={facility}
                          checked={formData.facilities?.includes(facility) || false}
                          onChange={e => {
                            const isChecked = e.target.checked;
                            setFormData(prev => ({
                              ...prev,
                              facilities: isChecked
                                ? [...(prev.facilities || []), facility]
                                : (prev.facilities || []).filter(f => f !== facility),
                            }));
                          }}
                          className="checkbox checkbox-primary checkbox-sm"
                        />
                        <span className="label-text">{facility}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* Directions (4 killa) Section */}
              <div className="card bg-base-100 shadow-md mb-6">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold mb-4">चार किल्ला (4 Directions)</h2>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {/* East Boundary */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">पूर्व (East)</span>
                      </label>
                      <input
                        type="text"
                        name="eastBoundary"
                        value={formData.eastBoundary || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="East boundary details"
                      />
                    </div>

                    {/* West Boundary */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">पश्चिम (West)</span>
                      </label>
                      <input
                        type="text"
                        name="westBoundary"
                        value={formData.westBoundary || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="West boundary details"
                      />
                    </div>

                    {/* South Boundary */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">दक्षिण (South)</span>
                      </label>
                      <input
                        type="text"
                        name="southBoundary"
                        value={formData.southBoundary || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="South boundary details"
                      />
                    </div>

                    {/* North Boundary */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">उत्तर (North)</span>
                      </label>
                      <input
                        type="text"
                        name="northBoundary"
                        value={formData.northBoundary || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="North boundary details"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Land Ownership Section */}
              <div className="card bg-base-100 shadow-md mb-6">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold mb-4">जग्गाको स्वामित्व प्रकार (Land Ownership Type)</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Ownership Type */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">प्रकार (Type)</span>
                      </label>
                      <select
                        name="ownershipType"
                        value={formData.ownershipType || ""}
                        onChange={handleInputChange}
                        className="select select-bordered w-full"
                      >
                        <option value="">Select Ownership Type</option>
                        <option value="Freehold">Freehold</option>
                        <option value="Leasehold">Leasehold</option>
                        <option value="Guthi">Guthi</option>
                        <option value="Trust">Trust</option>
                        <option value="Government">Government</option>
                        <option value="Institutional">Institutional</option>
                        <option value="Other">Other</option>
                      </select>
                    </div>

                    {/* Ownership Details */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">विवरण (Details)</span>
                      </label>
                      <input
                        type="text"
                        name="ownershipDetails"
                        value={formData.ownershipDetails || ""}
                        onChange={handleInputChange}
                        className="input input-bordered w-full"
                        placeholder="Ownership details"
                      />
                    </div>

                    {/* Ownership Value */}
                    <div className="form-control">
                      <label className="label">
                        <span className="label-text">मूल्य (Value at that time)</span>
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          name="ownershipValue"
                          value={formData.ownershipValue || ""}
                          onChange={handleInputChange}
                          className="input input-bordered w-full pl-10"
                          placeholder="Value in NPR"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Others Section */}
              <div className="card bg-base-100 shadow-md mb-6">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold mb-4">अन्य (Others)</h2>

                  {/* Tax Applicable */}
                  <div className="form-control mb-4">
                    <label className="label">
                      <span className="label-text font-medium">कर लागू हुन्छ? (Is Tax Applicable?)</span>
                    </label>
                    <div className="flex gap-4">
                      <label className="label cursor-pointer flex-1 border rounded-lg p-4 hover:bg-base-200">
                        <input
                          type="radio"
                          name="isTaxApplicable"
                          className="radio radio-primary"
                          checked={formData.isTaxApplicable === true}
                          onChange={() => setFormData(prev => ({ ...prev, isTaxApplicable: true }))}
                        />
                        <span className="label-text ml-2">हो (Yes)</span>
                      </label>
                      <label className="label cursor-pointer flex-1 border rounded-lg p-4 hover:bg-base-200">
                        <input
                          type="radio"
                          name="isTaxApplicable"
                          className="radio radio-primary"
                          checked={formData.isTaxApplicable === false}
                          onChange={() => setFormData(prev => ({ ...prev, isTaxApplicable: false }))}
                        />
                        <span className="label-text ml-2">होइन (No)</span>
                      </label>
                    </div>
                  </div>

                  {/* Applicable Fiscal Year */}
                  <div className="form-control mb-4">
                    <label className="label">
                      <span className="label-text">लागू आर्थिक वर्ष (Applicable Fiscal Year)</span>
                    </label>
                    <select
                      name="applicableFiscalYear"
                      value={formData.applicableFiscalYear || ""}
                      onChange={handleInputChange}
                      className="select select-bordered w-full"
                    >
                      <option value="">Select Fiscal Year</option>
                      {fiscalYears.map((fiscalYear) => (
                        <option key={fiscalYear.fiscalYearId} value={fiscalYear.name}>
                          {fiscalYear.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Other Details */}
                  <div className="form-control mb-4">
                    <label className="label">
                      <span className="label-text">अन्य विवरण (Other Details)</span>
                    </label>
                    <textarea
                      name="otherDetails"
                      value={formData.otherDetails || ""}
                      onChange={handleInputChange}
                      className="textarea textarea-bordered h-24"
                      placeholder="Enter any other relevant details"
                    ></textarea>
                  </div>

                  {/* Deregister */}
                  <div className="form-control">
                    <label className="label cursor-pointer justify-start gap-3">
                      <input
                        type="checkbox"
                        name="isDeregistered"
                        checked={formData.isDeregistered || false}
                        onChange={e => {
                          const isChecked = e.target.checked;
                          setFormData(prev => ({
                            ...prev,
                            isDeregistered: isChecked,
                            ...(isChecked ? {} : { deregistrationReason: "" }),
                          }));
                        }}
                        className="checkbox checkbox-primary"
                      />
                      <span className="label-text font-medium">डिर्जिस्टर गर्नुहुन्छ? (Deregister?)</span>
                    </label>

                    {/* Deregistration Reason (Conditional) */}
                    {formData.isDeregistered && (
                      <div className="mt-2 ml-8">
                        <label className="label">
                          <span className="label-text">कारण (Reason for Deregistration)</span>
                        </label>
                        <textarea
                          name="deregistrationReason"
                          value={formData.deregistrationReason || ""}
                          onChange={handleInputChange}
                          className="textarea textarea-bordered w-full h-24"
                          placeholder="Please provide the reason for deregistration"
                          required
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Required Documents */}
              <div className="bg-white rounded-lg shadow p-6">
                <div>
                  <h2 className="text-xl font-medium text-gray-900 mb-4">Required Documents</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="form-control">
                      <label htmlFor="ownershipCertificate" className="label">
                        <span className="label-text">Ownership Certificate (Lal Purja) *</span>
                      </label>
                      <input
                        type="file"
                        id="ownershipCertificate"
                        name="ownershipCertificate"
                        onChange={handleFileChange}
                        required
                        className="file-input file-input-bordered w-full"
                      />
                    </div>
                    <div className="form-control">
                      <label htmlFor="buildingPermit" className="label">
                        <span className="label-text">Building Permit (Naqsa Pass, if applicable)</span>
                      </label>
                      <input
                        type="file"
                        id="buildingPermit"
                        name="buildingPermit"
                        onChange={handleFileChange}
                        className="file-input file-input-bordered w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end mt-8">
                <button type="submit" disabled={loading} className="btn btn-primary btn-lg w-full">
                  {loading && <span className="loading loading-spinner loading-sm"></span>}
                  {loading ? "Submitting..." : "Register Property"}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default PropertyRegistration;
