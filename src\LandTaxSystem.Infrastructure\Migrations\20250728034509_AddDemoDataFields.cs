﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddDemoDataFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PreliminaryAssessmentStatusHistories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PreliminaryAssessmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    FromStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ToStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ChangedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: false),
                    ChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Reason = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PreliminaryAssessmentStatusHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PreliminaryAssessmentStatusHistories_AspNetUsers_ChangedBy",
                        column: x => x.ChangedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PreliminaryAssessmentStatusHistories_PreliminaryAssessments~",
                        column: x => x.PreliminaryAssessmentId,
                        principalTable: "PreliminaryAssessments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessmentStatusHistories_ChangedBy",
                table: "PreliminaryAssessmentStatusHistories",
                column: "ChangedBy");

            migrationBuilder.CreateIndex(
                name: "IX_PreliminaryAssessmentStatusHistories_PreliminaryAssessmentId",
                table: "PreliminaryAssessmentStatusHistories",
                column: "PreliminaryAssessmentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PreliminaryAssessmentStatusHistories");
        }
    }
}
