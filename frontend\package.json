{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.9", "@types/leaflet": "^1.9.18", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "daisyui": "^4.7.3", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "postcss": "^8.4.31", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.1", "tailwindcss": "^3.4.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}