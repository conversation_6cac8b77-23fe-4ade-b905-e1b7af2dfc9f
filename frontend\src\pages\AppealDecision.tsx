import React from 'react';
import AdminLayout from '../components/admin/AdminLayout';
import AppealDecisionForm from '../components/AppealDecisionForm';
import type { BreadcrumbItem } from '../components/admin/AdminBreadcrumb';

const AppealDecision: React.FC = () => {
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Appeal Decision', href: '/appeal-decision' }
  ];

  return (
    <AdminLayout 
      title="पुनरावेदन निर्णय"
      subtitle="पुनरावेदन समाधानको सूचना फारम"
      breadcrumbs={breadcrumbItems}
    >
      <AppealDecisionForm />
    </AdminLayout>
  );
};

export default AppealDecision;