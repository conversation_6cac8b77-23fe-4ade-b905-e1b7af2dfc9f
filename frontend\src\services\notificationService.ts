import api from './api';

export interface Notification {
  id: string;
  userId: string;
  message: string;
  type: 'payment' | 'appeal' | 'negotiation' | 'assessment' | 'system';
  isRead: boolean;
  createdAt: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
}

export const notificationService = {
  // Get all notifications for the current user
  getAll: async (): Promise<Notification[]> => {
    const response = await api.get('/notifications');
    return response.data;
  },

  // Get unread notifications count
  getUnreadCount: async (): Promise<number> => {
    const response = await api.get('/notifications/unread/count');
    return response.data.count;
  },

  // Mark a notification as read
  markAsRead: async (id: string): Promise<void> => {
    await api.put(`/notifications/${id}/read`);
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<void> => {
    await api.put('/notifications/read-all');
  },

  // Delete a notification
  delete: async (id: string): Promise<void> => {
    await api.delete(`/notifications/${id}`);
  }
};

export default notificationService;
