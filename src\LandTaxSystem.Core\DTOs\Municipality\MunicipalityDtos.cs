using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using LandTaxSystem.Core.Models;

namespace LandTaxSystem.Core.DTOs.Municipality
{
    public class MunicipalityCreateDto
    {
        [Required, MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public Guid DistrictId { get; set; }

        [Range(1, 50)]
        public int WardCount { get; set; } = 0;

        public ValuationRulesConfig? ValuationRulesConfig { get; set; }

        public List<TaxSlab>? TaxSlabsConfig { get; set; }

        public ExemptionRulesConfig? ExemptionRulesConfig { get; set; }
    }

    public class MunicipalityResponseDto
    {
        public Guid MunicipalityId { get; set; }

        public string Name { get; set; } = string.Empty;

        public Guid DistrictId { get; set; }
        
        public string DistrictName { get; set; } = string.Empty;
        
        public Guid ProvinceId { get; set; }
        
        public string ProvinceName { get; set; } = string.Empty;

        public int WardCount { get; set; }

        public ValuationRulesConfig ValuationRulesConfig { get; set; } = new ValuationRulesConfig();

        public List<TaxSlab> TaxSlabsConfig { get; set; } = new List<TaxSlab>();

        public ExemptionRulesConfig ExemptionRulesConfig { get; set; } = new ExemptionRulesConfig();

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        public decimal DefaultPenaltyPercent { get; set; }
        public decimal DefaultDiscountPercent { get; set; }
    }

    public class MunicipalityUpdateDto
    {
        [MaxLength(100)]
        public string? Name { get; set; }

        [MaxLength(50)]
        public string? Province { get; set; }

        [MaxLength(50)]
        public string? District { get; set; }

        [Range(1, 50)]
        public int? WardCount { get; set; }

        public ValuationRulesConfig? ValuationRulesConfig { get; set; }

        public List<TaxSlab>? TaxSlabsConfig { get; set; }

        public ExemptionRulesConfig? ExemptionRulesConfig { get; set; }
    }
    
    public class MunicipalityListItemDto
    {
        public Guid MunicipalityId { get; set; }
        public string Name { get; set; } = string.Empty;
    }
}
