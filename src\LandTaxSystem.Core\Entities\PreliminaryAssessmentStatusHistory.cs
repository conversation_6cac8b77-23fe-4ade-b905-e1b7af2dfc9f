using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class PreliminaryAssessmentStatusHistory
    {
        [Key]
        public Guid Id { get; set; }
        
        [Required]
        public Guid PreliminaryAssessmentId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string FromStatus { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string ToStatus { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(450)]
        public string ChangedBy { get; set; } = string.Empty;
        
        [Required]
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
        
        [MaxLength(1000)]
        public string? Reason { get; set; }
        
        // Navigation properties
        [ForeignKey("PreliminaryAssessmentId")]
        public virtual PreliminaryAssessment? PreliminaryAssessment { get; set; }
        
        [ForeignKey("ChangedBy")]
        public virtual ApplicationUser? ChangedByUser { get; set; }
    }
}