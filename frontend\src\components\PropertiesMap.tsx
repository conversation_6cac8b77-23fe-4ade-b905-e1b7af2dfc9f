import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>y<PERSON>, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import type { Property } from '../types';

// Fix for default marker icons
const DefaultIcon = L.icon({
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});

// Set default icon for all markers
if (typeof window !== 'undefined') {
  L.Marker.prototype.options.icon = DefaultIcon;
}

// Component to update map center when prop changes
const MapCenterUpdater: React.FC<{ center: [number, number] }> = ({ center }) => {
  const map = useMap();
  
  useEffect(() => {
    console.log('Updating map center to:', center);
    map.setView(center, map.getZoom());
  }, [center, map]);
  
  return null;
};

interface PropertiesMapProps {
  properties: Property[];
  center: [number, number];
  zoom: number;
  height?: string;
  width?: string;
  className?: string;
  showTaxPaymentStatus?: boolean;
}

const PropertiesMap: React.FC<PropertiesMapProps> = ({
  properties,
  center,
  zoom,
  height = '400px',
  width = '100%',
  className = '',
  showTaxPaymentStatus = false,
}) => {
  const mapContainerStyle = {
    height,
    width,
    minHeight: '300px',
  };

  // Function to get marker color based on property status or tax payment status
  const getMarkerColor = (property: Property) => {
    // Check if property has government ownership type - this takes priority
    if (property.ownershipType === 'Government') {
      return '#6c757d'; // Gray for government/exempted properties
    }
    
    if (showTaxPaymentStatus) {
      // Color-code based on tax payment status
      if (property.assessedValue && property.assessedValue > 0) {
        // Property has been assessed
        if (property.taxDue && property.taxDue > 0) {
          return '#dc3545'; // Red for unpaid taxes
        } else {
          return '#28a745'; // Green for paid/no tax due
        }
      } else {
        return '#ffc107'; // Yellow for pending assessment
      }
    } else {
      // Original color-coding based on property status
      switch (property.status?.toLowerCase()) {
        case 'approved':
        case 'assessed':
          return '#28a745'; // Green
        case 'pendingreview':
          return '#ffc107'; // Yellow
        case 'rejected':
          return '#dc3545'; // Red
        default:
          return '#007bff'; // Blue
      }
    }
  };

  // Create custom icon with dynamic color
  const createCustomIcon = (property: Property, index: number) => {
    return L.divIcon({
      className: 'custom-marker',
      html: `
        <div style="
          background-color: ${getMarkerColor(property)};
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid white;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 12px;
          transform: translate(-50%, -50%);
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        ">
          ${property.parcelNumber || (index + 1)}
        </div>
      `,
      iconSize: [24, 24],
      iconAnchor: [12, 12],
      popupAnchor: [0, -12],
    });
  };

  // Function to get coordinates from property
  const getPropertyCoordinates = (property: Property): [number, number] | null => {
    try {
      const geoJson = typeof property.parcelGeoJson === 'string'
        ? JSON.parse(property.parcelGeoJson)
        : property.parcelGeoJson;
      
      const coordinates = geoJson?.coordinates?.[0]?.[0];
      if (Array.isArray(coordinates) && coordinates.length >= 2) {
        const [longitude, latitude] = coordinates;
        return [Number(latitude), Number(longitude)];
      }
      return null;
    } catch (error) {
      console.error('Error parsing property coordinates:', error);
      return null;
    }
  };

  // Function to get polygon coordinates from property
  const getPolygonCoordinates = (property: Property): [number, number][] => {
    if (!property.parcelGeoJson) return [];

    try {
      const geoJson = typeof property.parcelGeoJson === 'string'
        ? JSON.parse(property.parcelGeoJson)
        : property.parcelGeoJson;

      if (geoJson.coordinates && geoJson.coordinates.length > 0) {
        // Convert from [longitude, latitude] to [latitude, longitude] for Leaflet
        return geoJson.coordinates[0].map((coord: number[]) => [
          Number(coord[1]), // latitude
          Number(coord[0])  // longitude
        ]);
      }
      return [];
    } catch (error) {
      console.error('Error parsing polygon coordinates:', error);
      return [];
    }
  };

  // Function to get polygon color based on property status or tax payment status
  const getPolygonColor = (property: Property) => {
    const color = getMarkerColor(property);
    return { color: color, fillColor: color, fillOpacity: 0.3 };
  };

  return (
    <div className={`relative z-0 ${className}`} style={mapContainerStyle}>
      <MapContainer
        center={center}
        zoom={zoom}
        style={{ height: '100%', width: '100%', minHeight: '300px' }}
        zoomControl={true}
        scrollWheelZoom={true}
        className="rounded-lg border border-gray-200 shadow-sm"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        <MapCenterUpdater center={center} />
        
        {properties.map((property, index) => {
          const coordinates = getPropertyCoordinates(property);
          const polygonCoords = getPolygonCoordinates(property);
          const polygonStyle = getPolygonColor(property);
          
          return (
            <React.Fragment key={`${property.id}-${index}`}>
              {/* Render polygon if coordinates are available */}
              {polygonCoords.length > 0 && (
                <Polygon
                  positions={polygonCoords}
                  pathOptions={{
                    ...polygonStyle,
                    weight: 2,
                    opacity: 0.8,
                  }}
                >
                  <Popup className="min-w-[200px]">
                    <div className="text-sm">
                      <h4 className="font-bold">Property #{property.id}</h4>
                      <p className="text-gray-600">{property.address || 'No address available'}</p>
                      {property.parcelNumber && (
                        <p className="mt-1">
                          Parcel No: <span className="font-medium">{property.parcelNumber}</span>
                        </p>
                      )}
                      {property.landArea && (
                        <p className="mt-1">
                          Land Area: <span className="font-medium">{property.landArea} sq.m</span>
                        </p>
                      )}
                      {property.status && (
                        <p className="mt-1">
                          Status: <span className="font-medium capitalize">
                            {property.status.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                        </p>
                      )}
                      {property.ownershipType && (
                        <p className="mt-1">
                          Ownership: <span className={`font-medium ${
                            property.ownershipType === 'Government' ? 'text-gray-600' : ''
                          }`}>
                            {property.ownershipType}
                            {property.ownershipType === 'Government' ? ' (Exempted)' : ''}
                          </span>
                        </p>
                      )}
                      {showTaxPaymentStatus && (
                        <p className="mt-1">
                          Tax Payment Status: <span className={`font-medium ${
                            property.assessedValue && property.assessedValue > 0
                              ? property.taxDue && property.taxDue > 0
                                ? 'text-red-600'
                                : 'text-green-600'
                              : 'text-yellow-600'
                          }`}>
                            {property.assessedValue && property.assessedValue > 0
                              ? property.taxDue && property.taxDue > 0
                                ? `Unpaid (NPR ${property.taxDue.toLocaleString()})`
                                : 'Paid/No Tax Due'
                              : 'Pending Assessment'}
                          </span>
                        </p>
                      )}
                      {property.taxDue !== undefined && property.taxDue > 0 && (
                        <p className="mt-1">
                          Tax Due: <span className="font-medium">
                            NPR {property.taxDue.toLocaleString()}
                          </span>
                        </p>
                      )}
                      {property.assessedValue !== undefined && (
                        <p className="mt-1">
                          Assessed Value: <span className="font-medium">
                            NPR {Number(property.assessedValue).toLocaleString()}
                          </span>
                        </p>
                      )}
                    </div>
                  </Popup>
                </Polygon>
              )}
              
              {/* Render marker as fallback if no polygon or as center point */}
              {coordinates && (
                <Marker
                  position={coordinates}
                  icon={createCustomIcon(property, index)}
                >
                  <Popup className="min-w-[200px]">
                    <div className="text-sm">
                      <h4 className="font-bold">Property #{property.id}</h4>
                      <p className="text-gray-600">{property.address || 'No address available'}</p>
                      {property.parcelNumber && (
                        <p className="mt-1">
                          Parcel No: <span className="font-medium">{property.parcelNumber}</span>
                        </p>
                      )}
                      {property.landArea && (
                        <p className="mt-1">
                          Land Area: <span className="font-medium">{property.landArea} sq.m</span>
                        </p>
                      )}
                      {property.status && (
                        <p className="mt-1">
                          Status: <span className="font-medium capitalize">
                            {property.status.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                        </p>
                      )}
                      {property.ownershipType && (
                        <p className="mt-1">
                          Ownership: <span className={`font-medium ${
                            property.ownershipType === 'Government' ? 'text-gray-600' : ''
                          }`}>
                            {property.ownershipType}
                            {property.ownershipType === 'Government' ? ' (Exempted)' : ''}
                          </span>
                        </p>
                      )}
                      {showTaxPaymentStatus && (
                        <p className="mt-1">
                          Tax Payment Status: <span className={`font-medium ${
                            property.assessedValue && property.assessedValue > 0
                              ? property.taxDue && property.taxDue > 0
                                ? 'text-red-600'
                                : 'text-green-600'
                              : 'text-yellow-600'
                          }`}>
                            {property.assessedValue && property.assessedValue > 0
                              ? property.taxDue && property.taxDue > 0
                                ? `Unpaid (NPR ${property.taxDue.toLocaleString()})`
                                : 'Paid/No Tax Due'
                              : 'Pending Assessment'}
                          </span>
                        </p>
                      )}
                      {property.taxDue !== undefined && property.taxDue > 0 && (
                        <p className="mt-1">
                          Tax Due: <span className="font-medium">
                            NPR {property.taxDue.toLocaleString()}
                          </span>
                        </p>
                      )}
                      {property.assessedValue !== undefined && (
                        <p className="mt-1">
                          Assessed Value: <span className="font-medium">
                            NPR {Number(property.assessedValue).toLocaleString()}
                          </span>
                        </p>
                      )}
                    </div>
                  </Popup>
                </Marker>
              )}
            </React.Fragment>
          );
        })}
      </MapContainer>
    </div>
  );
};

export default PropertiesMap;
