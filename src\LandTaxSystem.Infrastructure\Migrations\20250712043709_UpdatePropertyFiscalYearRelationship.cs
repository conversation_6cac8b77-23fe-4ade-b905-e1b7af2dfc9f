﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePropertyFiscalYearRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApplicableFiscalYear",
                table: "Properties");

            migrationBuilder.AddColumn<Guid>(
                name: "ApplicableFiscalYearId",
                table: "Properties",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Properties_ApplicableFiscalYearId",
                table: "Properties",
                column: "ApplicableFiscalYearId");

            migrationBuilder.AddForeignKey(
                name: "FK_Properties_FiscalYears_ApplicableFiscalYearId",
                table: "Properties",
                column: "ApplicableFiscalYearId",
                principalTable: "FiscalYears",
                principalColumn: "FiscalYearId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Properties_FiscalYears_ApplicableFiscalYearId",
                table: "Properties");

            migrationBuilder.DropIndex(
                name: "IX_Properties_ApplicableFiscalYearId",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "ApplicableFiscalYearId",
                table: "Properties");

            migrationBuilder.AddColumn<string>(
                name: "ApplicableFiscalYear",
                table: "Properties",
                type: "text",
                nullable: true);
        }
    }
}
