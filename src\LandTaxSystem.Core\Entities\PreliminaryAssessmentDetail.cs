using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class PreliminaryAssessmentDetail
    {
        [Key]
        public Guid Id { get; set; }
        
        [Required]
        public Guid PreliminaryAssessmentId { get; set; }
        
        [Required]
        public int SerialNumber { get; set; }
        
        [MaxLength(50)]
        public string FilingPeriod { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? Period { get; set; }

        [MaxLength(20)]
        public string TaxYear { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AssessedAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal AdditionalAmount { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal Penalty { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal Interest { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal Total { get; set; }
        
        // Navigation property
        [ForeignKey("PreliminaryAssessmentId")]
        public virtual PreliminaryAssessment PreliminaryAssessment { get; set; } = null!;
    }
}
