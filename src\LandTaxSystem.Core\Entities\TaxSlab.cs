using System;

namespace LandTaxSystem.Core.Models
{
    public class TaxSlab
    {
        public decimal MinValue { get; set; }
        public decimal MaxValue { get; set; }
        public decimal Rate { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class PenaltyRule
    {
        public int DaysAfterDue { get; set; }
        public decimal Rate { get; set; }
        public decimal MaxPenalty { get; set; }
        public string Description { get; set; } = string.Empty;
    }
}
