@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

@layer components {
  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600 px-4 py-2 rounded-md;
  }

  .btn-secondary {
    @apply bg-gray-500 text-white hover:bg-gray-600 px-4 py-2 rounded-md;
  }

  .input-field {
    @apply border border-base-300 rounded-md w-full px-3 py-2;
  }

  .form-label {
    @apply font-medium;
  }

  /* Admin dashboard layout classes */
  .admin-layout {
    @apply flex min-h-screen bg-base-200;
  }

  .admin-sidebar {
    @apply w-64 bg-base-300 text-base-content hidden lg:block;
  }

  .admin-content {
    @apply flex-1 p-6;
  }

  .admin-header {
    @apply bg-base-100 shadow-md p-4 mb-6 rounded-lg;
  }

  .admin-card {
    @apply bg-base-100 shadow-md rounded-lg;
  }

  .admin-card-title {
    @apply text-lg font-semibold;
  }

  .admin-table {
    @apply w-full;
  }

  .admin-stats {
    @apply bg-base-100 shadow-md;
  }

  /* DaisyUI component classes */
  .btn {
    @apply px-4 py-2 rounded-md font-medium;
  }

  .card {
    @apply rounded-lg overflow-hidden;
  }

  .card-body {
    @apply p-6;
  }

  .card-title {
    @apply text-xl font-bold mb-2;
  }

  .badge {
    @apply px-2 py-1 text-xs rounded-full;
  }

  .input {
    @apply border border-base-300 rounded-md px-3 py-2;
  }

  .input-bordered {
    @apply border border-base-300;
  }

  .checkbox {
    @apply h-5 w-5 rounded border-base-300;
  }

  .loading {
    @apply animate-spin;
  }

  .alert {
    @apply p-4 rounded-lg;
  }

  .alert-error {
    @apply bg-error/20 text-error;
  }

  .alert-warning {
    @apply bg-warning/20 text-warning;
  }

  /* Additional DaisyUI classes */
  .btn-info {
    @apply bg-info text-info-content hover:bg-info/80;
  }

  .btn-success {
    @apply bg-success text-success-content hover:bg-success/80;
  }

  .btn-error {
    @apply bg-error text-error-content hover:bg-error/80;
  }

  .btn-warning {
    @apply bg-warning text-warning-content hover:bg-warning/80;
  }

  .btn-ghost {
    @apply bg-transparent;
  }

  .btn-sm {
    @apply px-3 py-1 text-sm;
  }

  .badge-success {
    @apply bg-success text-success-content;
  }

  .badge-warning {
    @apply bg-warning text-warning-content;
  }

  .badge-error {
    @apply bg-error text-error-content;
  }

  .badge-ghost {
    @apply bg-base-300 text-base-content;
  }

  .checkbox-primary {
    @apply accent-primary;
  }

  .loading-spinner {
    @apply border-2 border-current border-t-transparent rounded-full;
  }

  .loading-lg {
    @apply w-8 h-8;
  }

  .link {
    @apply underline;
  }

  .link-primary {
    @apply text-blue-500;
  }

  .tabs {
    @apply flex;
  }

  .tabs-boxed {
    @apply bg-base-200 rounded-lg p-1;
  }

  .tab {
    @apply px-5 py-2 rounded-md font-medium transition-colors duration-150 border border-base-300 bg-base-200 text-base-content;
    min-height: 2.5rem;
    line-height: 1.25;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
  }

  .tab:last-child {
    margin-right: 0;
  }

  .tab:hover:not(.tab-active) {
    @apply bg-base-300 border-primary text-primary;
  }

  .tab-active {
    @apply bg-primary text-primary-content border-primary shadow font-bold;
    z-index: 1;
  }

  .form-control {
    @apply flex flex-col;
  }

  .label {
    @apply flex items-center;
  }

  .label-text {
    @apply text-sm;
  }
}
