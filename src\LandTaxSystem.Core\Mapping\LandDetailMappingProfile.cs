using AutoMapper;
using LandTaxSystem.Core.DTOs.LandDetail;
using LandTaxSystem.Core.Entities;

namespace LandTaxSystem.Core.Mapping
{
    public class LandDetailMappingProfile : Profile
    {
        public LandDetailMappingProfile()
        {
            // Map from Entity to DTO
            CreateMap<LandDetail, LandDetailDto>();
            
            // Map from Create DTO to Entity
            CreateMap<CreateLandDetailDto, LandDetail>()
                .ForMember(dest => dest.LandDetailId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow));
            
            // Map from Update DTO to Entity
            CreateMap<UpdateLandDetailDto, LandDetail>()
                .ForMember(dest => dest.PropertyId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow));
        }
    }
}
