﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateAssessmentFiscalYear : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "FiscalYearId",
                table: "Assessments",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<decimal>(
                name: "OriginalAmount",
                table: "Assessments",
                type: "numeric",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Assessments_FiscalYearId",
                table: "Assessments",
                column: "FiscalYearId");

            migrationBuilder.AddForeignKey(
                name: "FK_Assessments_FiscalYears_FiscalYearId",
                table: "Assessments",
                column: "FiscalYearId",
                principalTable: "FiscalYears",
                principalColumn: "FiscalYearId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Assessments_FiscalYears_FiscalYearId",
                table: "Assessments");

            migrationBuilder.DropIndex(
                name: "IX_Assessments_FiscalYearId",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "FiscalYearId",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "OriginalAmount",
                table: "Assessments");
        }
    }
}
