import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { authService } from "../services/api";
import api from "../services/api";

const Register: React.FC = () => {
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [submissionNumber, setSubmissionNumber] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showSubmissionSuccess, setShowSubmissionSuccess] = useState(false);

  // File states
  const [citizenshipDocument, setCitizenshipDocument] = useState<File | null>(null);
  const [nationalIdDocument, setNationalIdDocument] = useState<File | null>(null);
  const [panDocument, setPanDocument] = useState<File | null>(null);

  // Location data states
  const [provinces, setProvinces] = useState<{ provinceId: string; name: string; code: string }[]>([]);
  const [districts, setDistricts] = useState<{ districtId: string; name: string; code: string; provinceId: string }[]>(
    []
  );
  const [citizenshipDistricts, setCitizenshipDistricts] = useState<
    { districtId: string; name: string; code: string; provinceId: string }[]
  >([]);
  const [panDistricts, setPanDistricts] = useState<
    { districtId: string; name: string; code: string; provinceId: string }[]
  >([]);
  const [municipalities, setMunicipalities] = useState<
    { municipalityId: string; name: string; districtId: string; wardCount: number }[]
  >([]);

  // Temporary address location data states
  const [tempDistricts, setTempDistricts] = useState<
    { districtId: string; name: string; code: string; provinceId: string }[]
  >([]);
  const [tempMunicipalities, setTempMunicipalities] = useState<
    { municipalityId: string; name: string; districtId: string; wardCount: number }[]
  >([]);

  // Form data state
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: "",
    middleName: "",
    lastName: "",
    fatherName: "", // Required field
    motherName: "", // New field
    grandfatherName: "", // Required field
    grandmotherName: "", // New field
    maritalStatus: "", // New field
    spouseName: "", // New field
    citizenshipNumber: "", // Required field
    email: "",
    mobile: "",
    telephone: "",
    dateOfBirth: "",
    gender: "",
    profession: "", // Added missing profession field
    pan: "",
    nationality: "Nepali",
    isMinor: false,
    guardianName: "",
    guardianRelation: "",

    // Security
    password: "",
    confirmPassword: "",

    // Address Information - Permanent
    permanentProvinceId: "", // Should be GUID
    permanentDistrictId: "", // Should be GUID
    permanentMunicipalityId: "", // Should be GUID
    permanentWardNumber: "",
    permanentTole: "",
    permanentHouseNumber: "",

    // Same as permanent address checkbox (UI helper)
    sameAsPermanent: false,

    // Address Information - Temporary
    temporaryProvinceId: "", // Should be GUID
    temporaryDistrictId: "", // Should be GUID
    temporaryMunicipalityId: "", // Should be GUID
    temporaryWardNumber: "",
    temporaryTole: "",
    temporaryHouseNumber: "",

    // Document Metadata
    // Citizenship Document
    citizenshipIssueDistrict: "",
    citizenshipIssueDate: "",
    citizenshipIssueOffice: "",

    // National ID Document
    nationalIdNumber: "",
    nationalIdIssueDistrict: "",
    nationalIdIssueDate: "",
    nationalIdIssueOffice: "",

    // PAN Document
    panIssueDate: "",
    panIssueDistrict: "",
    panIssueOffice: "",

    // Contact Preferences
    preferSMS: true,
    preferEmail: true,
  });

  // Fetch provinces on component mount
  useEffect(() => {
    const fetchProvinces = async () => {
      try {
        const response = await api.get("/provinces");
        setProvinces(response.data);

        // Fetch all districts for citizenship and PAN dropdowns on component mount
        const fetchAllDistricts = async () => {
          try {
            const response = await api.get("/districts");
            setCitizenshipDistricts(response.data);
            setPanDistricts(response.data);
          } catch (err) {
            console.error("Error fetching all districts:", err);
            setError("Failed to load districts. Some features may be limited.");
          }
        };

        fetchAllDistricts();
      } catch (err) {
        console.error("Error fetching provinces:", err);
        setError("Failed to load provinces. Please try again.");
      }
    };

    fetchProvinces();
  }, []);

  // Update districts when permanent province changes
  useEffect(() => {
    const fetchDistricts = async () => {
      if (formData.permanentProvinceId) {
        try {
          const response = await api.get(`/provinces/${formData.permanentProvinceId}/districts`);
          setDistricts(response.data);

          // Reset district and municipality if province changes
          setFormData(prev => ({
            ...prev,
            permanentDistrictId: "",
            permanentMunicipalityId: "",
          }));
        } catch (err) {
          console.error("Error fetching districts:", err);
          setError("Failed to load districts. Please try again.");
          setDistricts([]);
        }
      } else {
        setDistricts([]);
      }
    };

    fetchDistricts();
  }, [formData.permanentProvinceId]);

  // Update municipalities when permanent district changes
  useEffect(() => {
    const fetchMunicipalities = async () => {
      if (formData.permanentDistrictId) {
        try {
          const response = await api.get(`/districts/${formData.permanentDistrictId}/municipalities`);
          setMunicipalities(response.data);

          // Reset municipality if district changes
          setFormData(prev => ({
            ...prev,
            permanentMunicipalityId: "",
          }));
        } catch (err) {
          console.error("Error fetching municipalities:", err);
          setError("Failed to load municipalities. Please try again.");
          setMunicipalities([]);
        }
      } else {
        setMunicipalities([]);
      }
    };

    fetchMunicipalities();
  }, [formData.permanentDistrictId]);

  // Update temporary districts when temporary province changes
  useEffect(() => {
    // Skip if same as permanent address is checked
    if (formData.sameAsPermanent) return;

    const fetchTempDistricts = async () => {
      if (formData.temporaryProvinceId) {
        try {
          const response = await api.get(`/provinces/${formData.temporaryProvinceId}/districts`);
          setTempDistricts(response.data);

          // Reset district and municipality if province changes
          setFormData(prev => ({
            ...prev,
            temporaryDistrictId: "",
            temporaryMunicipalityId: "",
          }));
        } catch (err) {
          console.error("Error fetching temporary districts:", err);
          setError("Failed to load temporary districts. Please try again.");
          setTempDistricts([]);
        }
      } else {
        setTempDistricts([]);
      }
    };

    fetchTempDistricts();
  }, [formData.temporaryProvinceId, formData.sameAsPermanent]);

  // Update temporary municipalities when temporary district changes
  useEffect(() => {
    // Skip if same as permanent address is checked
    if (formData.sameAsPermanent) return;

    const fetchTempMunicipalities = async () => {
      if (formData.temporaryDistrictId) {
        try {
          const response = await api.get(`/districts/${formData.temporaryDistrictId}/municipalities`);
          setTempMunicipalities(response.data);

          // Reset municipality if district changes
          setFormData(prev => ({
            ...prev,
            temporaryMunicipalityId: "",
          }));
        } catch (err) {
          console.error("Error fetching temporary municipalities:", err);
          setError("Failed to load temporary municipalities. Please try again.");
          setTempMunicipalities([]);
        }
      } else {
        setTempMunicipalities([]);
      }
    };

    fetchTempMunicipalities();
  }, [formData.temporaryDistrictId, formData.sameAsPermanent]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    // Handle checkbox separately since 'checked' only exists on HTMLInputElement
    const isCheckbox = type === "checkbox";
    const checked = isCheckbox ? (e.target as HTMLInputElement).checked : false;

    if (isCheckbox && name === "sameAsPermanent") {
      setFormData(prev => ({
        ...prev,
        sameAsPermanent: checked,
        // If checked, copy permanent address to temporary address
        ...(checked && {
          temporaryProvinceId: prev.permanentProvinceId,
          temporaryDistrictId: prev.permanentDistrictId,
          temporaryMunicipalityId: prev.permanentMunicipalityId,
          temporaryWardNumber: prev.permanentWardNumber,
          temporaryTole: prev.permanentTole,
          temporaryHouseNumber: prev.permanentHouseNumber,
        }),
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: isCheckbox ? checked : value,
      }));
    }
  };

  // Handle file uploads
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files } = e.target;

    if (files && files.length > 0) {
      const file = files[0];
      const maxSize = 5 * 1024 * 1024; // 5MB

      // Validate file size
      if (file.size > maxSize) {
        setError(`File ${file.name} is too large. Maximum size is 5MB.`);
        return;
      }

      // Validate file type
      const allowedTypes = ["image/jpeg", "image/png", "application/pdf"];
      if (!allowedTypes.includes(file.type)) {
        setError(`File ${file.name} is not supported. Please upload JPG, PNG, or PDF.`);
        return;
      }

      // Set the appropriate file state
      if (name === "citizenshipDocument") {
        setCitizenshipDocument(file);
      } else if (name === "nationalIdDocument") {
        setNationalIdDocument(file);
      } else if (name === "panDocument") {
        setPanDocument(file);
      }

      setError(""); // Clear any previous errors
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    // Validate password match
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    // Validate password complexity
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(formData.password)) {
      setError(
        "Password must be at least 8 characters long and include uppercase, lowercase, digit, and special character"
      );
      return;
    }

    // Validate citizenship document is uploaded (required)
    if (!citizenshipDocument) {
      setError("Citizenship document is required");
      return;
    }

    // Create form data for submission
    const registerFormData = new FormData();

    // Add all form fields to FormData
    Object.entries(formData).forEach(([key, value]) => {
      if (key !== "sameAsPermanent") {
        // Include all fields including document metadata
        if (value !== null && value !== undefined) {
          registerFormData.append(key, value.toString());
        }
      }
    });

    // Add files
    registerFormData.append("citizenshipDocument", citizenshipDocument);
    if (nationalIdDocument) {
      registerFormData.append("nationalIdDocument", nationalIdDocument);
    }
    if (panDocument) {
      registerFormData.append("panDocument", panDocument);
    }

    setIsLoading(true);

    try {
      const response = await authService.registerWithFormData(registerFormData);

      // Handle successful registration
      setSubmissionNumber(response.submissionNumber);
      setSuccess(`Registration successful! Your submission number is ${response.submissionNumber}`);
      setShowSubmissionSuccess(true);

      // Clear form after successful submission
      setFormData({
        // Personal Information
        firstName: "",
        middleName: "",
        lastName: "",
        fatherName: "",
        motherName: "",
        grandfatherName: "",
        grandmotherName: "",
        maritalStatus: "",
        spouseName: "",
        citizenshipNumber: "",
        email: "",
        mobile: "",
        telephone: "",
        dateOfBirth: "",
        gender: "",
        profession: "",
        pan: "",
        nationality: "Nepali",
        isMinor: false,
        guardianName: "",
        guardianRelation: "",

        // Security
        password: "",
        confirmPassword: "",

        // Address Information - Permanent
        permanentProvinceId: "",
        permanentDistrictId: "",
        permanentMunicipalityId: "",
        permanentWardNumber: "",
        permanentTole: "",
        permanentHouseNumber: "",
        sameAsPermanent: false,

        // Address Information - Temporary
        temporaryProvinceId: "",
        temporaryDistrictId: "",
        temporaryMunicipalityId: "",
        temporaryWardNumber: "",
        temporaryTole: "",
        temporaryHouseNumber: "",

        // Document Metadata
        citizenshipIssueDistrict: "",
        citizenshipIssueDate: "",
        citizenshipIssueOffice: "",
        nationalIdNumber: "",
        nationalIdIssueDistrict: "",
        nationalIdIssueDate: "",
        nationalIdIssueOffice: "",
        panIssueDate: "",
        panIssueDistrict: "",
        panIssueOffice: "",

        // Contact Preferences
        preferSMS: true,
        preferEmail: true,
      });

      setCitizenshipDocument(null);
      setNationalIdDocument(null);
      setPanDocument(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Registration failed");
    } finally {
      setIsLoading(false);
    }
  };

  // If showing the submission success screen
  if (showSubmissionSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-200 py-12 px-4 sm:px-6 lg:px-8">
        <div className="card w-full max-w-md bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title text-3xl font-bold justify-center text-success mb-4">Registration Successful!</h2>
            <div className="alert alert-success">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="stroke-current shrink-0 h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <div>
                <p className="text-lg font-medium">Your submission has been received</p>
                <p className="mt-2 text-base-content/70">Your submission number is:</p>
                <p className="mt-1 text-2xl font-bold">{submissionNumber}</p>
                <p className="mt-4 text-sm text-base-content/70">
                  Please save this number for tracking your registration status.
                </p>
                <p className="mt-2 text-sm text-base-content/70">You will be redirected to the login page shortly.</p>
              </div>
            </div>
            <div className="card-actions justify-center mt-6 space-x-4">
              <Link to="/login" className="link link-primary font-medium">
                Go to Login
              </Link>
              <Link to="/track-submission" className="link link-primary font-medium">
                Track Submission
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200 py-8 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto max-w-6xl">
        <div className="card w-full bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-center mb-6">
              <h2 className="card-title text-3xl font-bold justify-center mb-2">Taxpayer Registration</h2>
              <p className="text-base-content/70">Join the Land Tax Management System</p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              {error && (
                <div className="alert alert-error">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="stroke-current shrink-0 h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>{error}</span>
                </div>
              )}

              {success && (
                <div className="alert alert-success">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="stroke-current shrink-0 h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span>{success}</span>
                </div>
              )}

              <div className="space-y-6">
                {/* Personal Information Section */}
                <div className="card bg-base-200 shadow-sm">
                  <div className="card-body">
                    <h3 className="card-title text-lg mb-4">Personal Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="form-control">
                        <label className="label" htmlFor="firstName">
                          <span className="label-text font-medium">First Name *</span>
                        </label>
                        <input
                          id="firstName"
                          name="firstName"
                          type="text"
                          required
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="First Name"
                          value={formData.firstName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="middleName">
                          <span className="label-text font-medium">Middle Name</span>
                        </label>
                        <input
                          id="middleName"
                          name="middleName"
                          type="text"
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="Middle Name (Optional)"
                          value={formData.middleName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="lastName">
                          <span className="label-text font-medium">Last Name *</span>
                        </label>
                        <input
                          id="lastName"
                          name="lastName"
                          type="text"
                          required
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="Last Name"
                          value={formData.lastName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="gender">
                          <span className="label-text font-medium">Gender *</span>
                        </label>
                        <select
                          id="gender"
                          name="gender"
                          required
                          className="select select-bordered w-full"
                          value={formData.gender}
                          onChange={handleChange}
                        >
                          <option value="">Select Gender</option>
                          <option value="Male">Male</option>
                          <option value="Female">Female</option>
                          <option value="Other">Other</option>
                        </select>
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="dateOfBirth">
                          <span className="label-text font-medium">Date of Birth *</span>
                        </label>
                        <input
                          id="dateOfBirth"
                          name="dateOfBirth"
                          type="date"
                          required
                          className="input input-bordered w-full"
                          value={formData.dateOfBirth}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="profession">
                          <span className="label-text font-medium">Profession *</span>
                        </label>
                        <select
                          id="profession"
                          name="profession"
                          required
                          className="select select-bordered w-full"
                          value={formData.profession}
                          onChange={handleChange}
                        >
                          <option value="">Select Profession</option>
                          <option value="Agriculture">Agriculture</option>
                          <option value="Business">Business</option>
                          <option value="Doctor">Doctor</option>
                          <option value="Engineer">Engineer</option>
                          <option value="Government Service">Government Service</option>
                          <option value="Private Service">Private Service</option>
                          <option value="Student">Student</option>
                          <option value="Teacher">Teacher</option>
                          <option value="Other">Other</option>
                        </select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"></div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="form-control">
                        <label className="label" htmlFor="fatherName">
                          <span className="label-text font-medium">Father's Name *</span>
                        </label>
                        <input
                          id="fatherName"
                          name="fatherName"
                          type="text"
                          required
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="Father's Name"
                          value={formData.fatherName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="motherName">
                          <span className="label-text font-medium">Mother's Name *</span>
                        </label>
                        <input
                          id="motherName"
                          name="motherName"
                          type="text"
                          required
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="Mother's Name"
                          value={formData.motherName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="grandfatherName">
                          <span className="label-text font-medium">Grandfather's Name *</span>
                        </label>
                        <input
                          id="grandfatherName"
                          name="grandfatherName"
                          type="text"
                          required
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="Grandfather's Name"
                          value={formData.grandfatherName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="grandmotherName">
                          <span className="label-text font-medium">Grandmother's Name *</span>
                        </label>
                        <input
                          id="grandmotherName"
                          name="grandmotherName"
                          type="text"
                          required
                          maxLength={50}
                          className="input input-bordered w-full"
                          placeholder="Grandmother's Name"
                          value={formData.grandmotherName}
                          onChange={handleChange}
                        />
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="maritalStatus">
                          <span className="label-text font-medium">Marital Status *</span>
                        </label>
                        <select
                          id="maritalStatus"
                          name="maritalStatus"
                          required
                          className="select select-bordered w-full"
                          value={formData.maritalStatus}
                          onChange={handleChange}
                        >
                          <option value="">Select Marital Status</option>
                          <option value="Single">Single</option>
                          <option value="Married">Married</option>
                          <option value="Divorced">Divorced</option>
                          <option value="Widowed">Widowed</option>
                        </select>
                      </div>

                      {formData.maritalStatus === "Married" && (
                        <div className="form-control">
                          <label className="label" htmlFor="spouseName">
                            <span className="label-text font-medium">Spouse's Name *</span>
                          </label>
                          <input
                            id="spouseName"
                            name="spouseName"
                            type="text"
                            required={formData.maritalStatus === "Married"}
                            maxLength={100}
                            className="input input-bordered w-full"
                            placeholder="Spouse's Full Name"
                            value={formData.spouseName}
                            onChange={handleChange}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/*Contact Information Section */}
                <div className="card bg-base-200 shadow-sm">
                  <div className="card-body">
                    <h3 className="card-title text-lg mb-4">Contact Information</h3>
                    <div className="space-y-4">
                      {/* Email, Mobile, Telephone */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="form-control">
                          <label className="label" htmlFor="email">
                            <span className="label-text font-medium">Email Address *</span>
                          </label>
                          <input
                            id="email"
                            name="email"
                            type="email"
                            required
                            maxLength={255}
                            className="input input-bordered w-full"
                            placeholder="Enter your email"
                            value={formData.email}
                            onChange={handleChange}
                          />
                        </div>
                        <div className="form-control">
                          <label className="label" htmlFor="mobile">
                            <span className="label-text font-medium">Mobile Number *</span>
                          </label>
                          <input
                            id="mobile"
                            name="mobile"
                            type="tel"
                            required
                            className="input input-bordered w-full"
                            placeholder="Enter your mobile number"
                            value={formData.mobile}
                            onChange={handleChange}
                          />
                        </div>
                        <div className="form-control">
                          <label className="label" htmlFor="telephone">
                            <span className="label-text font-medium">Telephone (Optional)</span>
                          </label>
                          <input
                            id="telephone"
                            name="telephone"
                            type="tel"
                            className="input input-bordered w-full"
                            placeholder="Enter telephone number (if any)"
                            value={formData.telephone}
                            onChange={handleChange}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Permanent Address */}
                <div className="card bg-base-200 shadow-sm">
                  <div className="card-body">
                    <h3 className="card-title text-lg mb-4">Permanent Address</h3>
                    <div className="space-y-4">
                      {/* Permanent Province, District, Municipality */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="form-control">
                          <label className="label" htmlFor="permanentProvinceId">
                            <span className="label-text font-medium">Province *</span>
                          </label>
                          <select
                            id="permanentProvinceId"
                            name="permanentProvinceId"
                            value={formData.permanentProvinceId}
                            onChange={handleChange}
                            className="select select-bordered w-full"
                            required
                          >
                            <option value="">Select Province</option>
                            {provinces.map(province => (
                              <option key={province.provinceId} value={province.provinceId}>
                                {province.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        <div className="form-control">
                          <label className="label" htmlFor="permanentDistrictId">
                            <span className="label-text font-medium">District *</span>
                          </label>
                          <select
                            id="permanentDistrictId"
                            name="permanentDistrictId"
                            value={formData.permanentDistrictId}
                            onChange={handleChange}
                            className="select select-bordered w-full"
                            required
                          >
                            <option value="">Select District</option>
                            {districts.map(district => (
                              <option key={district.districtId} value={district.districtId}>
                                {district.name}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="form-control">
                          <label className="label" htmlFor="permanentMunicipalityId">
                            <span className="label-text font-medium">Municipality/VDC *</span>
                          </label>
                          <select
                            id="permanentMunicipalityId"
                            name="permanentMunicipalityId"
                            value={formData.permanentMunicipalityId}
                            onChange={handleChange}
                            className="select select-bordered w-full"
                            required
                          >
                            <option value="">Select Municipality</option>
                            {municipalities.map(municipality => (
                              <option key={municipality.municipalityId} value={municipality.municipalityId}>
                                {municipality.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="form-control">
                          <label className="label" htmlFor="permanentWardNumber">
                            <span className="label-text font-medium">Ward No. *</span>
                          </label>
                          <input
                            type="number"
                            id="permanentWardNumber"
                            name="permanentWardNumber"
                            value={formData.permanentWardNumber}
                            onChange={handleChange}
                            min="1"
                            max="50"
                            className="input input-bordered w-full"
                            required
                          />
                        </div>
                        <div className="form-control">
                          <label className="label" htmlFor="permanentTole">
                            <span className="label-text font-medium">Tole/Street *</span>
                          </label>
                          <input
                            type="text"
                            id="permanentTole"
                            name="permanentTole"
                            value={formData.permanentTole}
                            onChange={handleChange}
                            className="input input-bordered w-full"
                            required
                          />
                        </div>

                        <div className="form-control">
                          <label className="label" htmlFor="permanentHouseNumber">
                            <span className="label-text font-medium">House No. *</span>
                          </label>
                          <input
                            id="permanentHouseNumber"
                            name="permanentHouseNumber"
                            type="text"
                            className="input input-bordered w-full"
                            placeholder="Enter house number"
                            value={formData.permanentHouseNumber}
                            onChange={handleChange}
                            required
                            disabled={!formData.permanentWardNumber} // Disable until ward number is selected
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Temporary Address Section */}
                <div className="card bg-base-200 shadow-sm">
                  <div className="card-body">
                    <h3 className="card-title text-lg mb-4">Temporary Address</h3>

                    {/* Same as permanent address checkbox */}
                    <div className="form-control mb-4">
                      <label className="label cursor-pointer justify-start gap-2">
                        <input
                          type="checkbox"
                          name="sameAsPermanent"
                          checked={formData.sameAsPermanent}
                          onChange={handleChange}
                          className="checkbox checkbox-primary"
                        />
                        <span className="label-text font-medium">Same as permanent address</span>
                      </label>
                    </div>

                    {/* Temporary Address Fields - Conditionally Rendered */}
                    {!formData.sameAsPermanent && (
                      <div className="space-y-4">
                        {/* Temporary Province, District, Municipality */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="form-control">
                            <label className="label" htmlFor="temporaryProvinceId">
                              <span className="label-text font-medium">Province</span>
                            </label>
                            <select
                              id="temporaryProvinceId"
                              name="temporaryProvinceId"
                              className="select select-bordered w-full"
                              value={formData.temporaryProvinceId}
                              onChange={handleChange}
                            >
                              <option value="">Select Province</option>
                              {provinces.map(province => (
                                <option key={province.provinceId} value={province.provinceId}>
                                  {province.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="temporaryDistrictId">
                              <span className="label-text font-medium">District</span>
                            </label>
                            <select
                              id="temporaryDistrictId"
                              name="temporaryDistrictId"
                              className="select select-bordered w-full"
                              value={formData.temporaryDistrictId}
                              onChange={handleChange}
                            >
                              <option value="">Select District</option>
                              {tempDistricts.map(district => (
                                <option key={district.districtId} value={district.districtId}>
                                  {district.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="temporaryMunicipalityId">
                              <span className="label-text font-medium">Municipality/VDC</span>
                            </label>
                            <select
                              id="temporaryMunicipalityId"
                              name="temporaryMunicipalityId"
                              className="select select-bordered w-full"
                              value={formData.temporaryMunicipalityId}
                              onChange={handleChange}
                            >
                              <option value="">Select Municipality</option>
                              {tempMunicipalities.map(municipality => (
                                <option key={municipality.municipalityId} value={municipality.municipalityId}>
                                  {municipality.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        {/* Temporary Ward, Tole, House No. */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="form-control">
                            <label className="label" htmlFor="temporaryWardNumber">
                              <span className="label-text font-medium">Ward No.</span>
                            </label>
                            <input
                              id="temporaryWardNumber"
                              name="temporaryWardNumber"
                              type="number"
                              min="1"
                              max="50"
                              className="input input-bordered w-full"
                              placeholder="Enter ward number"
                              value={formData.temporaryWardNumber}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="temporaryTole">
                              <span className="label-text font-medium">Tole/Street</span>
                            </label>
                            <input
                              id="temporaryTole"
                              name="temporaryTole"
                              type="text"
                              className="input input-bordered w-full"
                              placeholder="Enter tole or street"
                              value={formData.temporaryTole}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="temporaryHouseNumber">
                              <span className="label-text font-medium">House No.</span>
                            </label>
                            <input
                              id="temporaryHouseNumber"
                              name="temporaryHouseNumber"
                              type="text"
                              className="input input-bordered w-full"
                              placeholder="Enter house number"
                              value={formData.temporaryHouseNumber}
                              onChange={handleChange}
                              disabled={!formData.temporaryWardNumber} // Disable until ward number is entered
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Security Section */}
                <div className="card bg-base-200 shadow-sm">
                  <div className="card-body">
                    <h3 className="card-title text-lg mb-4">Security</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="form-control">
                        <label className="label" htmlFor="password">
                          <span className="label-text font-medium">Password *</span>
                        </label>
                        <input
                          id="password"
                          name="password"
                          type="password"
                          required
                          className="input input-bordered w-full"
                          placeholder="Enter your password"
                          value={formData.password}
                          onChange={handleChange}
                        />
                        <label className="label">
                          <span className="label-text-alt text-base-content/70">
                            Password must be at least 8 characters with uppercase, lowercase, digit, and special
                            character.
                          </span>
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="label" htmlFor="confirmPassword">
                          <span className="label-text font-medium">Confirm Password *</span>
                        </label>
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type="password"
                          required
                          className="input input-bordered w-full"
                          placeholder="Confirm your password"
                          value={formData.confirmPassword}
                          onChange={handleChange}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Document Upload Section */}

                <div className="card bg-base-200 shadow-sm">
                  <div className="card-body">
                    <h3 className="card-title text-lg mb-4">Document Upload</h3>

                    <div className="space-y-6">
                      {/* Citizenship Document Section */}
                      <div className="bg-base-100 p-4 rounded-lg border border-base-300">
                        <h4 className="text-md font-semibold mb-3">Citizenship Document *</h4>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                          <div className="form-control">
                            <label className="label" htmlFor="citizenshipNumber">
                              <span className="label-text font-medium">Citizenship No. *</span>
                            </label>
                            <input
                              id="citizenshipNumber"
                              name="citizenshipNumber"
                              type="text"
                              required
                              maxLength={50}
                              className="input input-bordered w-full"
                              placeholder="Citizenship No."
                              value={formData.citizenshipNumber}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="citizenshipIssueDistrict">
                              <span className="label-text font-medium">Issue District *</span>
                            </label>
                            <select
                              id="citizenshipIssueDistrict"
                              name="citizenshipIssueDistrict"
                              required
                              className="select select-bordered w-full"
                              value={formData.citizenshipIssueDistrict}
                              onChange={handleChange}
                            >
                              <option value="">Select District</option>
                              {citizenshipDistricts.map(district => (
                                <option key={district.districtId} value={district.name}>
                                  {district.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="citizenshipIssueDate">
                              <span className="label-text font-medium">Issue Date *</span>
                            </label>
                            <input
                              id="citizenshipIssueDate"
                              name="citizenshipIssueDate"
                              type="date"
                              required
                              className="input input-bordered w-full"
                              value={formData.citizenshipIssueDate}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="citizenshipIssueOffice">
                              <span className="label-text font-medium">Issue Office *</span>
                            </label>
                            <input
                              id="citizenshipIssueOffice"
                              name="citizenshipIssueOffice"
                              type="text"
                              required
                              className="input input-bordered w-full"
                              placeholder="e.g., DAO"
                              value={formData.citizenshipIssueOffice}
                              onChange={handleChange}
                            />
                          </div>
                        </div>

                        <div className="form-control">
                          <label className="label" htmlFor="citizenshipDocument">
                            <span className="label-text font-medium">Upload Document * (JPG, PNG, PDF, max 5MB)</span>
                          </label>
                          <input
                            id="citizenshipDocument"
                            name="citizenshipDocument"
                            type="file"
                            required
                            accept="image/jpeg,image/png,application/pdf"
                            className="file-input file-input-bordered w-full"
                            onChange={handleFileChange}
                          />
                          {citizenshipDocument ? (
                            <label className="label">
                              <span className="label-text-alt text-success">
                                File selected: {citizenshipDocument.name}
                              </span>
                            </label>
                          ) : (
                            <label className="label">
                              <span className="label-text-alt text-warning">
                                Please upload a clear scan of your citizenship document
                              </span>
                            </label>
                          )}
                        </div>
                      </div>

                      {/* National ID Document Section */}
                      <div className="bg-base-100 p-4 rounded-lg border border-base-300">
                        <h4 className="text-md font-semibold mb-3">National ID Document (Optional)</h4>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                          <div className="form-control">
                            <label className="label" htmlFor="nationalIdNumber">
                              <span className="label-text font-medium">National ID Number (Optional)</span>
                            </label>
                            <input
                              id="nationalIdNumber"
                              name="nationalIdNumber"
                              type="text"
                              className="input input-bordered w-full"
                              placeholder="Enter your National ID number (if available)"
                              value={formData.nationalIdNumber}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="nationalIdIssueDistrict">
                              <span className="label-text font-medium">Issue District</span>
                            </label>
                            <select
                              id="nationalIdIssueDistrict"
                              name="nationalIdIssueDistrict"
                              className="select select-bordered w-full"
                              value={formData.nationalIdIssueDistrict}
                              onChange={handleChange}
                            >
                              <option value="">Select District</option>
                              {citizenshipDistricts.map(district => (
                                <option key={district.districtId} value={district.name}>
                                  {district.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="nationalIdIssueOffice">
                              <span className="label-text font-medium">Issue Office</span>
                            </label>
                            <input
                              id="nationalIdIssueOffice"
                              name="nationalIdIssueOffice"
                              type="text"
                              className="input input-bordered w-full"
                              placeholder="e.g., Kathmandu District Administration Office"
                              value={formData.nationalIdIssueOffice}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="nationalIdIssueDate">
                              <span className="label-text font-medium">Issue Date</span>
                            </label>
                            <input
                              id="nationalIdIssueDate"
                              name="nationalIdIssueDate"
                              type="date"
                              className="input input-bordered w-full"
                              value={formData.nationalIdIssueDate}
                              onChange={handleChange}
                            />
                          </div>
                        </div>

                        <div className="form-control">
                          <label className="label" htmlFor="nationalIdDocument">
                            <span className="label-text font-medium">Upload Document (JPG, PNG, PDF, max 5MB)</span>
                          </label>
                          <input
                            id="nationalIdDocument"
                            name="nationalIdDocument"
                            type="file"
                            accept="image/jpeg,image/png,application/pdf"
                            className="file-input file-input-bordered w-full"
                            onChange={handleFileChange}
                          />
                          {nationalIdDocument ? (
                            <label className="label">
                              <span className="label-text-alt text-success">
                                File selected: {nationalIdDocument.name}
                              </span>
                            </label>
                          ) : (
                            <label className="label">
                              <span className="label-text-alt text-base-content/70">
                                Upload your National ID if available
                              </span>
                            </label>
                          )}
                        </div>
                      </div>

                      {/* PAN Document Section */}
                      <div className="bg-base-100 p-4 rounded-lg border border-base-300">
                        <h4 className="text-md font-semibold mb-3">PAN Document (Optional)</h4>

                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                          <div className="form-control">
                            <label className="label" htmlFor="pan">
                              <span className="label-text font-medium">PAN Number</span>
                            </label>
                            <input
                              id="pan"
                              name="pan"
                              type="text"
                              className="input input-bordered w-full"
                              placeholder="Enter your PAN number (if available)"
                              value={formData.pan}
                              onChange={handleChange}
                            />
                          </div>
                          <div className="form-control">
                            <label className="label" htmlFor="panIssueDistrict">
                              <span className="label-text font-medium">Issue District</span>
                            </label>
                            <select
                              id="panIssueDistrict"
                              name="panIssueDistrict"
                              className="select select-bordered w-full"
                              value={formData.panIssueDistrict}
                              onChange={handleChange}
                            >
                              <option value="">Select District</option>
                              {panDistricts.map(district => (
                                <option key={district.districtId} value={district.districtId}>
                                  {district.name}
                                </option>
                              ))}
                            </select>
                          </div>
                          <div className="form-control">
                            <label className="label" htmlFor="panIssueDate">
                              <span className="label-text font-medium">Issue Date</span>
                            </label>
                            <input
                              id="panIssueDate"
                              name="panIssueDate"
                              type="date"
                              className="input input-bordered w-full"
                              value={formData.panIssueDate}
                              onChange={handleChange}
                            />
                          </div>

                          <div className="form-control">
                            <label className="label" htmlFor="panIssueOffice">
                              <span className="label-text font-medium">Issue Office</span>
                            </label>
                            <input
                              id="panIssueOffice"
                              name="panIssueOffice"
                              type="text"
                              className="input input-bordered w-full"
                              placeholder="e.g., Inland Revenue Office"
                              value={formData.panIssueOffice}
                              onChange={handleChange}
                            />
                          </div>
                        </div>

                        <div className="form-control">
                          <label className="label" htmlFor="panDocument">
                            <span className="label-text font-medium">Upload Document (JPG, PNG, PDF, max 5MB)</span>
                          </label>
                          <input
                            id="panDocument"
                            name="panDocument"
                            type="file"
                            accept="image/jpeg,image/png,application/pdf"
                            className="file-input file-input-bordered w-full"
                            onChange={handleFileChange}
                          />
                          {panDocument ? (
                            <label className="label">
                              <span className="label-text-alt text-success">File selected: {panDocument.name}</span>
                            </label>
                          ) : (
                            <label className="label">
                              <span className="label-text-alt text-base-content/70">
                                Upload your PAN document if available
                              </span>
                            </label>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm">
                    <Link to="/login" className="link link-primary font-medium">
                      Already have an account? Sign in
                    </Link>
                  </div>
                  <div className="text-sm">
                    <Link to="/track-submission" className="link link-primary font-medium">
                      Track your submission
                    </Link>
                  </div>
                </div>

                <div className="form-control mt-6">
                  <button type="submit" disabled={isLoading} className="btn btn-primary w-full">
                    {isLoading ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        Registering...
                      </>
                    ) : (
                      "Register"
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
