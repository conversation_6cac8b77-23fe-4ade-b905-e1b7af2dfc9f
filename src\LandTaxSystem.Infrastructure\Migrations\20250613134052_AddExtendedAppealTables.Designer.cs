﻿// <auto-generated />
using System;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250613134052_AddExtendedAppealTables")]
    partial class AddExtendedAppealTables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "postgis");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Appeal", b =>
                {
                    b.Property<Guid>("AppealId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TaxPayerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("AppealId");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("TaxPayerId");

                    b.ToTable("Appeals", t =>
                        {
                            t.HasCheckConstraint("CK_Appeal_Status", "\"Status\" IN ('Pending', 'Resolved')");
                        });
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("CitizenshipNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocumentPath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("PAN")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PermanentAddress")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("Role")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SubmissionNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ToleStreet")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("TwoGenerations")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("WardNumber")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.HasKey("Id");

                    b.HasIndex("MunicipalityId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("PAN")
                        .IsUnique();

                    b.HasIndex("SubmissionNumber")
                        .IsUnique();

                    b.ToTable("AspNetUsers", null, t =>
                        {
                            t.HasCheckConstraint("CK_User_Status", "\"Status\" IN ('PendingApproval', 'Active', 'Rejected')");
                        });
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Assessment", b =>
                {
                    b.Property<Guid>("AssessmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AssessedByOfficerId")
                        .HasColumnType("text");

                    b.Property<DateTime>("AssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AssessmentYear")
                        .HasColumnType("integer");

                    b.Property<decimal>("CalculatedValue")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExemptionAppliedDetailsJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<decimal>("FinalAssessedValue")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<int>("Origin")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OriginalAmount")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("OverriddenValue")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<string>("OverrideReason")
                        .HasColumnType("text");

                    b.Property<string>("PaymentStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("PreviousAssessmentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PropertyId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Superseded")
                        .HasColumnType("boolean");

                    b.Property<decimal>("TaxAmount")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("AssessmentId");

                    b.HasIndex("AssessedByOfficerId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("PropertyId", "AssessmentYear")
                        .IsUnique();

                    b.ToTable("Assessments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AssessmentLineItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AssessedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FiscalYear")
                        .HasColumnType("integer");

                    b.Property<decimal>("InterestAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PenaltyAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SerialNumber")
                        .HasColumnType("integer");

                    b.Property<string>("TaxDescription")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentId");

                    b.ToTable("AssessmentLineItem");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AuditLog", b =>
                {
                    b.Property<long>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("LogId"));

                    b.Property<string>("ActionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("NewValueJson")
                        .HasColumnType("jsonb");

                    b.Property<string>("OldValueJson")
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("LogId");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.District", b =>
                {
                    b.Property<Guid>("DistrictId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("ProvinceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("DistrictId");

                    b.HasIndex("ProvinceId");

                    b.ToTable("Districts");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ExtendedAppeal", b =>
                {
                    b.Property<Guid>("AppealId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AppealAuthority")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("AppealDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AppealDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("AppealSubject")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LocationCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OfficeCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("OrderDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OrderNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RegistrationNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TaxDeterminationOrderNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("TaxpayerAddress")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("TaxpayerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("AppealId");

                    b.HasIndex("UserId");

                    b.ToTable("ExtendedAppeals", t =>
                        {
                            t.HasCheckConstraint("CK_ExtendedAppeal_Status", "\"Status\" IN ('Pending', 'Resolved')");
                        });
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FiscalYear", b =>
                {
                    b.Property<Guid>("FiscalYearId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("FiscalYearId");

                    b.ToTable("FiscalYears");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Municipality", b =>
                {
                    b.Property<Guid>("MunicipalityId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DistrictId")
                        .HasColumnType("uuid");

                    b.Property<string>("ExemptionRulesConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TaxSlabsConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("[]");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ValuationRulesConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<int>("WardCount")
                        .HasColumnType("integer");

                    b.HasKey("MunicipalityId");

                    b.HasIndex("DistrictId");

                    b.ToTable("Municipalities");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.MunicipalityTaxConfig", b =>
                {
                    b.Property<Guid>("MunicipalityTaxConfigId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("FinalizedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FinalizedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsFinalized")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("PenaltyRulesJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("{}");

                    b.Property<string>("TaxSlabsConfigJson")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("[]");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("MunicipalityTaxConfigId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("MunicipalityId");

                    b.ToTable("MunicipalityTaxConfigs");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Negotiation", b =>
                {
                    b.Property<Guid>("NegotiationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AppealId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("NegotiatedAmount")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<DateTime>("NegotiationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OfficerId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("NegotiationId");

                    b.HasIndex("AppealId")
                        .IsUnique();

                    b.HasIndex("OfficerId");

                    b.ToTable("Negotiations");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Notification", b =>
                {
                    b.Property<Guid>("NotificationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("RelatedEntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("RelatedEntityType")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("NotificationId");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Payment", b =>
                {
                    b.Property<Guid>("PaymentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AmountPaid")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<Guid?>("AssessmentId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("BatchPaymentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FiscalYearId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsReconciled")
                        .HasColumnType("boolean");

                    b.Property<bool>("Partial")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentGateway")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("PropertyId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Provisional")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("PaymentId");

                    b.HasIndex("AssessmentId");

                    b.HasIndex("FiscalYearId");

                    b.HasIndex("PropertyId");

                    b.ToTable("Payments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Property", b =>
                {
                    b.Property<Guid>("PropertyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<decimal?>("BuildingBuiltUpAreaSqM")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)");

                    b.Property<string>("BuildingConstructionType")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("BuildingConstructionYear")
                        .HasColumnType("integer");

                    b.Property<string>("BuildingPermitPath")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDefaulter")
                        .HasColumnType("boolean");

                    b.Property<decimal>("LandAreaSqM")
                        .HasPrecision(10, 2)
                        .HasColumnType("numeric(10,2)");

                    b.Property<Guid>("MunicipalityId")
                        .HasColumnType("uuid");

                    b.Property<string>("OwnerUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OwnershipCertificatePath")
                        .HasColumnType("text");

                    b.Property<Polygon>("ParcelGeometry")
                        .IsRequired()
                        .HasColumnType("geometry(Polygon, 4326)");

                    b.Property<string>("ParcelNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Street")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UsageType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("WardNumber")
                        .HasColumnType("integer");

                    b.HasKey("PropertyId");

                    b.HasIndex("MunicipalityId");

                    b.HasIndex("OwnerUserId");

                    b.ToTable("Properties");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Province", b =>
                {
                    b.Property<Guid>("ProvinceId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("ProvinceId");

                    b.ToTable("Provinces");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.TaxPeriod", b =>
                {
                    b.Property<Guid>("TaxPeriodId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AppealAmount")
                        .HasPrecision(15, 2)
                        .HasColumnType("numeric(15,2)");

                    b.Property<string>("AppealSubject")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ExtendedAppealId")
                        .HasColumnType("uuid");

                    b.Property<string>("Period")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TaxPeriodValue")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Year")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.HasKey("TaxPeriodId");

                    b.HasIndex("ExtendedAppealId");

                    b.ToTable("TaxPeriods");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Appeal", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Assessment", "Assessment")
                        .WithMany()
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "TaxPayer")
                        .WithMany()
                        .HasForeignKey("TaxPayerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("TaxPayer");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ApplicationUser", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany("Officers")
                        .HasForeignKey("MunicipalityId");

                    b.Navigation("Municipality");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Assessment", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "AssessedByOfficer")
                        .WithMany()
                        .HasForeignKey("AssessedByOfficerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Property", "Property")
                        .WithMany("Assessments")
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AssessedByOfficer");

                    b.Navigation("FiscalYear");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AssessmentLineItem", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Assessment", "Assessment")
                        .WithMany("LineItems")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.AuditLog", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("User");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.District", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Province", "Province")
                        .WithMany("Districts")
                        .HasForeignKey("ProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Province");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ExtendedAppeal", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Municipality", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.District", "District")
                        .WithMany("Municipalities")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.MunicipalityTaxConfig", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany("MunicipalityTaxConfigs")
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany("MunicipalityTaxConfigs")
                        .HasForeignKey("MunicipalityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FiscalYear");

                    b.Navigation("Municipality");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Negotiation", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Appeal", "Appeal")
                        .WithOne("Negotiation")
                        .HasForeignKey("LandTaxSystem.Core.Entities.Negotiation", "AppealId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "Officer")
                        .WithMany()
                        .HasForeignKey("OfficerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Appeal");

                    b.Navigation("Officer");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Payment", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Assessment", "Assessment")
                        .WithMany("Payments")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("LandTaxSystem.Core.Entities.FiscalYear", "FiscalYear")
                        .WithMany()
                        .HasForeignKey("FiscalYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.Property", "Property")
                        .WithMany()
                        .HasForeignKey("PropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Assessment");

                    b.Navigation("FiscalYear");

                    b.Navigation("Property");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Property", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.Municipality", "Municipality")
                        .WithMany("Properties")
                        .HasForeignKey("MunicipalityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Municipality");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.TaxPeriod", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ExtendedAppeal", "ExtendedAppeal")
                        .WithMany("TaxPeriods")
                        .HasForeignKey("ExtendedAppealId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExtendedAppeal");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("LandTaxSystem.Core.Entities.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Appeal", b =>
                {
                    b.Navigation("Negotiation");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Assessment", b =>
                {
                    b.Navigation("LineItems");

                    b.Navigation("Payments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.District", b =>
                {
                    b.Navigation("Municipalities");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.ExtendedAppeal", b =>
                {
                    b.Navigation("TaxPeriods");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.FiscalYear", b =>
                {
                    b.Navigation("MunicipalityTaxConfigs");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Municipality", b =>
                {
                    b.Navigation("MunicipalityTaxConfigs");

                    b.Navigation("Officers");

                    b.Navigation("Properties");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Property", b =>
                {
                    b.Navigation("Assessments");
                });

            modelBuilder.Entity("LandTaxSystem.Core.Entities.Province", b =>
                {
                    b.Navigation("Districts");
                });
#pragma warning restore 612, 618
        }
    }
}
