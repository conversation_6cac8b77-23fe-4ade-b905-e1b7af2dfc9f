import type { Property, Assessment } from './index';

// Mock interfaces that make certain fields optional for testing/demo purposes
export interface MockProperty extends Omit<Property, 'parcelGeoJson' | 'wardNumber' | 'street' | 'parcelNumber'> {
  parcelGeoJson?: Partial<Property['parcelGeoJson']>;
  wardNumber?: number;
  street?: string;
  parcelNumber?: string;
}

export interface MockAssessment extends Omit<Assessment, 
  'province' | 'district' | 'municipality' | 'wardNumber' | 
  'street' | 'parcelNumber' | 'propertyAddress' | 'createdAt' | 'updatedAt'> {
  province?: string;
  district?: string;
  municipality?: string;
  wardNumber?: number;
  street?: string;
  parcelNumber?: string;
  propertyAddress?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Helper function to convert mock property to full property
export const toFullProperty = (mockProperty: MockProperty): Property => {
  return {
    ...mockProperty,
    parcelGeoJson: mockProperty.parcelGeoJson && typeof mockProperty.parcelGeoJson === 'object' ? {
      type: (mockProperty.parcelGeoJson as { type?: string }).type || "Polygon",
      coordinates: (mockProperty.parcelGeoJson as { coordinates?: number[][][] }).coordinates || [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]
    } : {
      type: "Polygon",
      coordinates: [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]
    },
    wardNumber: mockProperty.wardNumber || 1,
    street: mockProperty.street || "Default Street",
    parcelNumber: mockProperty.parcelNumber || "DEFAULT-123"
  };
};

// Helper function to convert mock assessment to full assessment
export const toFullAssessment = (mockAssessment: MockAssessment): Assessment => {
  return {
    ...mockAssessment,
    province: mockAssessment.province || "Default Province",
    district: mockAssessment.district || "Default District",
    municipality: mockAssessment.municipality || "Default Municipality",
    wardNumber: mockAssessment.wardNumber || 1,
    street: mockAssessment.street || "Default Street",
    parcelNumber: mockAssessment.parcelNumber || "DEFAULT-123",
    propertyAddress: mockAssessment.propertyAddress || "Default Address",
    createdAt: mockAssessment.createdAt || new Date().toISOString(),
    updatedAt: mockAssessment.updatedAt || new Date().toISOString()
  };
};
