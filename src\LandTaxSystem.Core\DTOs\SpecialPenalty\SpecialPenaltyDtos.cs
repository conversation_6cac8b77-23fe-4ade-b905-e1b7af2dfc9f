using System;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.SpecialPenalty
{
    public class SpecialPenaltyResponseDto
    {
        public Guid SpecialPenaltyId { get; set; }
        public string SpecialPenaltyNo { get; set; } = string.Empty;
        public string TaxpayerId { get; set; } = string.Empty;
        public string TaxpayerName { get; set; } = string.Empty;
        public string TaxpayerPAN { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public int TaxYear { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string? OtherReason { get; set; }
        public string? ReasonDetails { get; set; }
        public DateTime EffectiveDate { get; set; }
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? CreatedByName { get; set; }
        
        // Taxpayer Information
        public TaxpayerInfoDto? TaxpayerInfo { get; set; }
    }
    
    public class TaxpayerInfoDto
    {
        public string Name { get; set; } = string.Empty;
        public string Province { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public string Municipality { get; set; } = string.Empty;
        public string WardNo { get; set; } = string.Empty;
        public string StreetTole { get; set; } = string.Empty;
        public string HouseNo { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Mobile { get; set; } = string.Empty;
        public string ContactPerson { get; set; } = string.Empty;
    }
    
    public class CreateSpecialPenaltyDto
    {
        [Required]
        [StringLength(50)]
        public string TaxpayerNo { get; set; } = string.Empty; // This will be PAN
        
        [Required]
        [StringLength(50)]
        public string AccountType { get; set; } = string.Empty;
        
        [Required]
        [Range(2000, 3000, ErrorMessage = "Tax year must be between 2000 and 3000")]
        public int TaxYear { get; set; }
        
        [Required]
        [StringLength(10)]
        public string Reason { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? OtherReason { get; set; }
        
        [StringLength(1000)]
        public string? ReasonDetails { get; set; }
        
        [Required]
        public DateTime EffectiveDate { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
    }
    
    public class UpdateSpecialPenaltyDto
    {
        [Required]
        [StringLength(50)]
        public string AccountType { get; set; } = string.Empty;
        
        [Required]
        [Range(2000, 3000, ErrorMessage = "Tax year must be between 2000 and 3000")]
        public int TaxYear { get; set; }
        
        [Required]
        [StringLength(10)]
        public string Reason { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? OtherReason { get; set; }
        
        [StringLength(1000)]
        public string? ReasonDetails { get; set; }
        
        [Required]
        public DateTime EffectiveDate { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
    }
    
    public class SpecialPenaltyListQueryDto
    {
        public string? TaxpayerNo { get; set; }
        public string? Status { get; set; }
        public int? TaxYear { get; set; }
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
    
    public class SpecialPenaltyStatusUpdateDto
    {
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = string.Empty; // Active, Cancelled, Paid
        
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
}