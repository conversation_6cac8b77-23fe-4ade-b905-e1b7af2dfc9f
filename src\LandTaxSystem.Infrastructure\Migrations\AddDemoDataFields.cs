using Microsoft.EntityFrameworkCore.Migrations;

namespace LandTaxSystem.Infrastructure.Migrations
{
    public partial class AddDemoDataFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add missing fields to Properties table
            migrationBuilder.AddColumn<decimal>(
                name: "BuildingTotalAreaSqM",
                table: "Properties",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ValuationMicrozone",
                table: "Properties",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LandType",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OwnershipType",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsTaxApplicable",
                table: "Properties",
                type: "boolean",
                nullable: false,
                defaultValue: true);

            // Add missing fiscal years
            migrationBuilder.Sql(@"
                INSERT INTO ""FiscalYears"" (""FiscalYearId"", ""Name"", ""StartDate"", ""EndDate"", ""IsActive"", ""CreatedAt"", ""UpdatedAt"")
                VALUES 
                ('f47ac10b-58cc-4372-a567-0e02b2c3d476', '2076/77', '2019-07-16', '2020-07-15', false, NOW(), NOW()),
                ('f47ac10b-58cc-4372-a567-0e02b2c3d477', '2077/78', '2020-07-16', '2021-07-15', false, NOW(), NOW()),
                ('f47ac10b-58cc-4372-a567-0e02b2c3d478', '2078/79', '2021-07-16', '2022-07-15', false, NOW(), NOW()),
                ('f47ac10b-58cc-4372-a567-0e02b2c3d480', '2079/80', '2022-07-16', '2023-07-15', false, NOW(), NOW()),
                ('f47ac10b-58cc-4372-a567-0e02b2c3d481', '2081/82', '2024-07-16', '2025-07-15', false, NOW(), NOW())
                ON CONFLICT (""FiscalYearId"") DO NOTHING;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(name: "BuildingTotalAreaSqM", table: "Properties");
            migrationBuilder.DropColumn(name: "ValuationMicrozone", table: "Properties");
            migrationBuilder.DropColumn(name: "LandType", table: "Properties");
            migrationBuilder.DropColumn(name: "OwnershipType", table: "Properties");
            migrationBuilder.DropColumn(name: "IsTaxApplicable", table: "Properties");
        }
    }
}