import React from "react";
import { useAuth } from "../context/AuthContext";
import { useLocation } from "react-router-dom";
import PublicNavbar from "./PublicNavbar";

interface LayoutWrapperProps {
  children: React.ReactNode;
}

const LayoutWrapper: React.FC<LayoutWrapperProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // Define public routes that should use the public navbar
  const publicRoutes = ["/", "/login", "/register", "/track-submission"];
  const isPublicRoute = publicRoutes.includes(location.pathname);

  // If it's a public route or user is not authenticated, show public navbar
  if (isPublicRoute || !isAuthenticated) {
    return (
      <>
        <PublicNavbar />
        <div className="container mx-auto py-6">{children}</div>
      </>
    );
  }

  // For authenticated users on protected routes, children will handle their own layout (AdminLayout)
  return <>{children}</>;
};

export default LayoutWrapper;
