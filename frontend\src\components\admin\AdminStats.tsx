import React from 'react';

interface StatItem {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
  trend?: {
    value: number;
    isUpward: boolean;
  };
}

interface AdminStatsProps {
  items: StatItem[];
  className?: string;
  columns?: 1 | 2 | 3 | 4;
  vertical?: boolean;
}

const AdminStats: React.FC<AdminStatsProps> = ({ 
  items, 
  className = '', 
  columns = 4,
  vertical = false
}) => {
  // Determine the grid columns based on the columns prop
  const gridClass = vertical ? '' : `grid-cols-1 ${
    columns === 1 ? 'md:grid-cols-1' :
    columns === 2 ? 'md:grid-cols-2' :
    columns === 3 ? 'md:grid-cols-3' :
    'md:grid-cols-2 lg:grid-cols-4'
  }`;

  // Function to get color classes
  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'primary': return 'text-primary';
      case 'secondary': return 'text-secondary';
      case 'accent': return 'text-accent';
      case 'info': return 'text-info';
      case 'success': return 'text-success';
      case 'warning': return 'text-warning';
      case 'error': return 'text-error';
      default: return 'text-primary';
    }
  };

  // Function to format numbers with commas
  const formatNumber = (num: number | string): string => {
    if (typeof num === 'string') return num;
    return num.toLocaleString();
  };

  return (
    <div className={`grid ${gridClass} gap-4 ${className}`}>
      {items.map((item, index) => (
        <div key={index} className={`stats shadow ${vertical ? 'stats-vertical' : ''} bg-base-100`}>
          <div className="stat">
            {item.icon && (
              <div className={`stat-figure ${getColorClasses(item.color)}`}>
                {item.icon}
              </div>
            )}
            <div className="stat-title">{item.title}</div>
            <div className={`stat-value ${getColorClasses(item.color)}`}>
              {formatNumber(item.value)}
            </div>
            {item.description && (
              <div className="stat-desc">{item.description}</div>
            )}
            {item.trend && (
              <div className={`stat-desc ${item.trend.isUpward ? 'text-success' : 'text-error'}`}>
                {item.trend.isUpward ? '↗︎' : '↘︎'} {item.trend.value}%
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default AdminStats;