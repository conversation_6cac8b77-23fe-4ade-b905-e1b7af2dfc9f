-- Check if the Properties table exists
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE  table_schema = 'public'
   AND    table_name   = 'Properties'
) AS table_exists;

-- Get column information for Properties table
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default,
    character_maximum_length
FROM 
    information_schema.columns
WHERE 
    table_name = 'Properties'
ORDER BY 
    ordinal_position;
