-- Fix FiscalYear table structure
ALTER TABLE "FiscalYears" 
ADD COLUMN IF NOT EXISTS "Name" VARCHAR(255) NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS "StartDate" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS "EndDate" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS "IsActive" BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS "UpdatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Clear existing fiscal years if needed
-- DELETE FROM "FiscalYears";

-- Insert sample fiscal years if they don't exist
INSERT INTO "FiscalYears" ("FiscalYearId", "Name", "StartDate", "EndDate", "IsActive")
SELECT 
    '2d3c00b9-c018-4ba9-a34d-05c4b39d0c39', -- Use a fixed GUID for reproducibility
    'FY 2025-2026',
    '2025-04-14',
    '2026-04-13',
    TRUE
WHERE NOT EXISTS (SELECT 1 FROM "FiscalYears" WHERE "Name" = 'FY 2025-2026');

INSERT INTO "FiscalYears" ("FiscalYearId", "Name", "StartDate", "EndDate", "IsActive")
SELECT 
    'f8c5d54a-8a3e-4b5c-9e6f-7d8c9b0a1b2c',
    'FY 2024-2025',
    '2024-04-14',
    '2025-04-13',
    FALSE
WHERE NOT EXISTS (SELECT 1 FROM "FiscalYears" WHERE "Name" = 'FY 2024-2025');

INSERT INTO "FiscalYears" ("FiscalYearId", "Name", "StartDate", "EndDate", "IsActive")
SELECT 
    'a1b2c3d4-e5f6-4a5b-9c8d-7e6f5a4b3c2d',
    'FY 2023-2024',
    '2023-04-14',
    '2024-04-13',
    FALSE
WHERE NOT EXISTS (SELECT 1 FROM "FiscalYears" WHERE "Name" = 'FY 2023-2024');

-- Ensure only one fiscal year is active
UPDATE "FiscalYears" SET "IsActive" = FALSE
WHERE "Name" != 'FY 2025-2026' AND "IsActive" = TRUE;

-- Verify fiscal years were inserted
SELECT * FROM "FiscalYears";
