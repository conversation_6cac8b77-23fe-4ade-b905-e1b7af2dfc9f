import daisyui from "daisyui";

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // DaisyUI semantic colors
        primary: '#3b82f6',
        'primary-content': '#ffffff',
        'primary-focus': '#2563eb',
        secondary: '#6b7280',
        'secondary-content': '#ffffff',
        'secondary-focus': '#4b5563',
        accent: '#f59e0b',
        'accent-content': '#ffffff',
        neutral: '#374151',
        'neutral-content': '#ffffff',
        'base-100': '#ffffff',
        'base-200': '#f3f4f6',
        'base-300': '#e5e7eb',
        'base-content': '#1f2937',
        info: '#3b82f6',
        'info-content': '#ffffff',
        success: '#10b981',
        'success-content': '#ffffff',
        warning: '#f59e0b',
        'warning-content': '#ffffff',
        error: '#ef4444',
        'error-content': '#ffffff',
      },
    },
  },
  plugins: [daisyui],
  daisyui: {
    themes: [
      {
        landtaxlight: {
          "primary": "#3b82f6",
          "primary-content": "#ffffff",
          "secondary": "#6b7280",
          "secondary-content": "#ffffff",
          "accent": "#f59e0b",
          "accent-content": "#ffffff",
          "neutral": "#374151",
          "neutral-content": "#ffffff",
          "base-100": "#ffffff",
          "base-200": "#f3f4f6",
          "base-300": "#e5e7eb",
          "base-content": "#1f2937",
          "info": "#3b82f6",
          "info-content": "#ffffff",
          "success": "#10b981",
          "success-content": "#ffffff",
          "warning": "#f59e0b",
          "warning-content": "#ffffff",
          "error": "#ef4444",
          "error-content": "#ffffff",
        },
      },
    ],
    base: true,
    styled: true,
    utils: true,
    logs: true,
    prefix: "",
  },
}