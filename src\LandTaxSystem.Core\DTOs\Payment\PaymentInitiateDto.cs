using System;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Payment
{
    public class PaymentInitiateDto
    {
        /// <summary>
        /// Property ID for property-based payments (required)
        /// </summary>
        [Required]
        public Guid PropertyId { get; set; }

        /// <summary>
        /// Assessment ID for assessment-based payments (optional)
        /// If provided, payment will be linked to this assessment
        /// </summary>
        public Guid? AssessmentId { get; set; }

        /// <summary>
        /// Fiscal Year ID for property-based payments (optional)
        /// If not provided, active fiscal year will be used
        /// </summary>
        public Guid? FiscalYearId { get; set; }

        /// <summary>
        /// Payment amount
        /// </summary>
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than zero.")]
        public decimal Amount { get; set; }
    }
}
