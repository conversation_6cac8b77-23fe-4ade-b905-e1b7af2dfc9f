import React from "react";
import type { Property } from "../../../../types";
import { DetailField } from "../common/DetailField";
import { SectionHeader } from "../common/SectionHeader";

interface PropertyDetailsSectionProps {
  property: Property;
}

export const PropertyDetailsSection: React.FC<PropertyDetailsSectionProps> = ({
  property,
}) => {
  return (
    <div className="card bg-base-200 shadow-sm">
      <div className="card-body p-4">
        <SectionHeader
          title="Basic Property Information"
          icon="🏠"
          subtitle="Primary property registration details"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <DetailField label="Property Address" value={property.address} />
          <DetailField
            label="Ward Number"
            value={property.wardNumber}
            type="number"
          />
          <DetailField label="Street/Tole" value={property.street} />
          <DetailField label="Parcel Number" value={property.parcelNumber} />
          <DetailField label="Usage Type" value={property.usageType} />
          <DetailField
            label="Land Area"
            value={property.landAreaSqM}
            type="number"
            unit="sq.m"
          />

          {/* Building Details */}
          {property.buildingBuiltUpAreaSqM && (
            <DetailField
              label="Built-up Area"
              value={property.buildingBuiltUpAreaSqM}
              type="number"
              unit="sq.m"
            />
          )}
          {property.buildingConstructionType && (
            <DetailField
              label="Construction Type"
              value={property.buildingConstructionType}
            />
          )}
          {property.buildingConstructionYear && (
            <DetailField
              label="Construction Year"
              value={property.buildingConstructionYear}
              type="number"
            />
          )}

          {/* Additional Land Area Units */}
          {property.landDetails?.areaRopani && (
            <DetailField
              label="Area (Ropani)"
              value={property.landDetails.areaRopani}
              type="number"
              unit="ropani"
            />
          )}
          {property.landDetails?.areaAana && (
            <DetailField
              label="Area (Aana)"
              value={property.landDetails.areaAana}
              type="number"
              unit="aana"
            />
          )}
          {property.landDetails?.areaPaisa && (
            <DetailField
              label="Area (Paisa)"
              value={property.landDetails.areaPaisa}
              type="number"
              unit="paisa"
            />
          )}
          {property.landDetails?.areaDaam && (
            <DetailField
              label="Area (Daam)"
              value={property.landDetails.areaDaam}
              type="number"
              unit="daam"
            />
          )}

          <DetailField
            label="Registration Date"
            value={property.registrationDate}
            type="date"
          />
          <DetailField label="Status" value={property.status} />
        </div>
      </div>
    </div>
  );
};
