using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class AssessmentLineItem
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public Guid AssessmentId { get; set; }

        [ForeignKey(nameof(AssessmentId))]
        public Assessment Assessment { get; set; }

        [Required]
        public int SerialNumber { get; set; }

        [Required]
        [StringLength(100)]
        public string TaxDescription { get; set; } = string.Empty;

        [Required]
        public int FiscalYear { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AssessedAmount { get; set; }

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PenaltyAmount { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InterestAmount { get; set; } = 0;

        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
    }
}
