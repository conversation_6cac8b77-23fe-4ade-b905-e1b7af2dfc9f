using Microsoft.EntityFrameworkCore;
using Npgsql;

var connectionString = "Host=localhost;Database=land_tax_system;Username=********;Password=********";

await using var connection = new NpgsqlConnection(connectionString);
await connection.OpenAsync();

// Check if the migration history table exists
var command = connection.CreateCommand();
command.CommandText = @"
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = '__EFMigrationsHistory'
    );";

var tableExists = (bool)(await command.ExecuteScalarAsync() ?? false);
Console.WriteLine($"Migration history table exists: {tableExists}");

if (tableExists)
{
    // List all applied migrations
    command.CommandText = "SELECT * FROM \"__EFMigrationsHistory\" ORDER BY \"MigrationId\" DESC";
    
    await using var reader = await command.ExecuteReaderAsync();
    Console.WriteLine("\nApplied migrations:");
    Console.WriteLine("Migration ID\t\t\t\tProduct Version");
    Console.WriteLine("------------------------------------------------");
    
    while (await reader.ReadAsync())
    {
        Console.WriteLine($"{reader["MigrationId"],-40} {reader["ProductVersion"]}");
    }
}
else
{
    Console.WriteLine("No migration history table found. Database may not be initialized.");
}
