import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { propertyService, assessmentService } from "../services/api";
import type { Property, Assessment } from "../types";
import { AdminLayout } from "../components/admin";

interface PropertyWithPendingTax extends Property {
  pendingAssessment?: Assessment;
  hasPendingTax: boolean;
}

const PaymentsPage: React.FC = () => {
  const [properties, setProperties] = useState<PropertyWithPendingTax[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    console.log("PaymentsPage component mounted");
    loadPropertiesWithTax();
  }, []);

  const loadPropertiesWithTax = async () => {
    console.log("Loading properties with tax data...");
    try {
      setLoading(true);
      setError("");

      // Get user's properties
      console.log("Fetching user properties...");
      const userProperties = await propertyService.getMyProperties();
      console.log("Received properties:", userProperties.length);

      // Filter only approved properties
      const approvedProperties = userProperties.filter((property: Property) => property.status === "Approved");

      // For each approved property, check for pending assessments
      const propertiesWithTaxData = await Promise.all(
        approvedProperties.map(async (property: Property) => {
          try {
            // Get assessments for this property
            const assessments = await assessmentService.getByProperty(property.id);

            // Find the latest pending assessment
            const pendingAssessments = assessments
              .filter((assessment: Assessment) => assessment.paymentStatus === "Pending")
              .sort(
                (a: Assessment, b: Assessment) =>
                  new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime()
              );

            const pendingAssessment = pendingAssessments.length > 0 ? pendingAssessments[0] : undefined;

            return {
              ...property,
              pendingAssessment,
              hasPendingTax: !!pendingAssessment,
            } as PropertyWithPendingTax;
          } catch (err) {
            console.error(`Failed to load assessments for property ${property.id}:`, err);
            return {
              ...property,
              hasPendingTax: false,
            } as PropertyWithPendingTax;
          }
        })
      );

      setProperties(propertiesWithTaxData);
    } catch (err) {
      console.error("Failed to load properties:", err);

      // Check if it's an authentication error
      if (err && typeof err === "object" && "response" in err) {
        const axiosError = err as { response?: { status?: number } };
        if (axiosError.response?.status === 401) {
          setError("Your session has expired. Please log in again.");
          return;
        }
      }

      setError("Failed to load your properties. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `NPR ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const propertiesWithPendingTax = properties.filter(p => p.hasPendingTax);
  const totalPendingAmount = propertiesWithPendingTax.reduce(
    (total, property) => total + (property.pendingAssessment?.taxAmount || 0),
    0
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  return (
    <AdminLayout title="Tax Payments" subtitle="Manage and pay your property tax assessments">
      <div className="max-w-6xl mx-auto">
        {error && (
          <div className="alert alert-error mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="stroke-current shrink-0 h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>{error}</span>
          </div>
        )}

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="card bg-base-100 shadow-md">
            <div className="card-body">
              <h2 className="card-title text-primary">Total Properties</h2>
              <div className="text-3xl font-bold">{properties.length}</div>
              <p className="text-base-content/70">Registered properties</p>
            </div>
          </div>

          <div className="card bg-base-100 shadow-md">
            <div className="card-body">
              <h2 className="card-title text-warning">Pending Payments</h2>
              <div className="text-3xl font-bold">{propertiesWithPendingTax.length}</div>
              <p className="text-base-content/70">Properties with due tax</p>
            </div>
          </div>

          <div className="card bg-base-100 shadow-md">
            <div className="card-body">
              <h2 className="card-title text-error">Total Amount Due</h2>
              <div className="text-2xl font-bold">{formatCurrency(totalPendingAmount)}</div>
              <p className="text-base-content/70">Outstanding tax amount</p>
            </div>
          </div>
        </div>

        {/* Payment Options */}
        {propertiesWithPendingTax.length > 0 && (
          <div className="card bg-base-100 shadow-xl mb-8">
            <div className="card-body">
              <h2 className="card-title text-xl mb-4">Payment Options</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Bulk Payment */}
                <div className="card bg-primary/10 border border-primary/20">
                  <div className="card-body text-center">
                    <div className="text-primary text-4xl mb-4">🏘️</div>
                    <h3 className="card-title justify-center mb-2">Bulk Payment</h3>
                    <p className="text-base-content/70 mb-4">Pay for multiple properties at once and save time</p>
                    <Link to="/bulk-tax-payment" className="btn btn-primary">
                      Pay Multiple Properties
                    </Link>
                  </div>
                </div>

                {/* Individual Payment */}
                <div className="card bg-secondary/10 border border-secondary/20">
                  <div className="card-body text-center">
                    <div className="text-secondary text-4xl mb-4">🏠</div>
                    <h3 className="card-title justify-center mb-2">Individual Payment</h3>
                    <p className="text-base-content/70 mb-4">Pay for one property at a time with detailed review</p>
                    <Link to="/properties" className="btn btn-secondary">
                      View My Properties
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Properties with Pending Tax */}
        {propertiesWithPendingTax.length > 0 ? (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title text-xl mb-4">Properties with Pending Tax</h2>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Property Address</th>
                      <th>Municipality</th>
                      <th>Assessment Year</th>
                      <th>Tax Amount</th>
                      <th>Due Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {propertiesWithPendingTax.map(property => (
                      <tr key={property.id}>
                        <td>
                          <div className="font-medium">{property.address}</div>
                          <div className="text-sm text-base-content/70">
                            {property.usageType} • {property.landArea} sq.m
                          </div>
                        </td>
                        <td>{property.municipalityName}</td>
                        <td>{property.pendingAssessment?.assessmentYear}</td>
                        <td>
                          <span className="font-bold text-error">
                            {formatCurrency(property.pendingAssessment?.taxAmount || 0)}
                          </span>
                        </td>
                        <td>
                          {property.pendingAssessment?.assessmentDate && (
                            <span className="text-warning">
                              {formatDate(property.pendingAssessment.assessmentDate)}
                            </span>
                          )}
                        </td>
                        <td>
                          <div className="flex gap-2">
                            <Link to={`/property/${property.id}`} className="btn btn-ghost btn-sm">
                              View Details
                            </Link>
                            <Link to={`/tax-payment-options/${property.id}`} className="btn btn-primary btn-sm">
                              Pay Now
                            </Link>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body text-center">
              <div className="text-success text-6xl mb-4">✅</div>
              <h2 className="card-title justify-center text-2xl mb-4">All Caught Up!</h2>
              <p className="text-base-content/70 mb-6">You don't have any pending tax payments at the moment.</p>

              {properties.length === 0 ? (
                <div>
                  <p className="text-base-content/70 mb-4">You don't have any registered properties yet.</p>
                  <Link to="/properties/register" className="btn btn-primary">
                    Register Your First Property
                  </Link>
                </div>
              ) : (
                <div>
                  <p className="text-base-content/70 mb-4">
                    You have {properties.length} registered {properties.length === 1 ? "property" : "properties"}.
                  </p>
                  <Link to="/properties" className="btn btn-primary">
                    View My Properties
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default PaymentsPage;
