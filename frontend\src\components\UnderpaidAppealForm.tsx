import React, { useState } from 'react';
import api from '../services/api';

interface UnderpaidAssessment {
  id: string;
  propertyId: string;
  propertyAddress: string;
  assessmentYear: number;
  finalAssessedValue: number;
  taxAmount: number;
  paymentStatus: string;
  origin?: string;
  superseded?: boolean;
  createdAt: string;
}

interface UnderpaidAppealFormProps {
  assessment: UnderpaidAssessment;
  onSubmit: () => void;
  onCancel: () => void;
}

const UnderpaidAppealForm: React.FC<UnderpaidAppealFormProps> = ({
  assessment,
  onSubmit,
  onCancel
}) => {
  const [reason, setReason] = useState('');
  const [evidence, setEvidence] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reason.trim()) {
      setError('Please provide a reason for your appeal');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const formData = new FormData();
      formData.append('assessmentId', assessment.id);
      formData.append('reason', reason);
      if (evidence) {
        formData.append('evidenceFile', evidence);
      }

      await api.post('/appeals', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      onSubmit();
    } catch (err) {
      console.error('Failed to submit appeal:', err);
      setError('Failed to submit appeal. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setEvidence(e.target.files[0]);
    }
  };

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">Appeal Underpaid Assessment</h2>
        
        {error && (
          <div className="alert alert-error">
            <span>{error}</span>
          </div>
        )}
      
        <div className="mb-4">
          <h3 className="font-semibold mb-2">Assessment Details</h3>
          <div className="bg-base-200 p-3 rounded">
            <p><span className="font-medium">Property Address:</span> {assessment.propertyAddress}</p>
            <p><span className="font-medium">Assessment Year:</span> {assessment.assessmentYear}</p>
            <p><span className="font-medium">Tax Amount:</span> ${assessment.taxAmount.toFixed(2)}</p>
            <p><span className="font-medium">Status:</span> {assessment.paymentStatus}</p>
          </div>
        </div>
      
        <form onSubmit={handleSubmit}>
          <div className="form-control mb-4">
            <label className="label">
              <span className="label-text font-bold">Reason for Appeal</span>
            </label>
            <textarea
              className="textarea textarea-bordered w-full"
              rows={4}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Explain why you are appealing this assessment..."
              required
            />
          </div>
        
          <div className="form-control mb-4">
            <label className="label">
              <span className="label-text font-bold">Supporting Evidence (Optional)</span>
            </label>
            <input
              type="file"
              className="file-input file-input-bordered w-full"
              onChange={handleFileChange}
            />
            <label className="label">
              <span className="label-text-alt">Upload any documents that support your appeal (PDF, JPG, PNG)</span>
            </label>
          </div>
        
          <div className="card-actions justify-between mt-6">
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-ghost"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !reason.trim()}
              className="btn btn-primary"
            >
              {loading ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Submitting...
                </>
              ) : (
                'Submit Appeal'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UnderpaidAppealForm;
