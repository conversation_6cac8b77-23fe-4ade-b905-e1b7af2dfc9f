import React from "react";

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  icon?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  icon,
}) => {
  return (
    <div className="flex items-center space-x-3 mb-4">
      {icon && <div className="text-2xl">{icon}</div>}
      <div>
        <h3 className="text-lg font-semibold text-base-content">{title}</h3>
        {subtitle && <p className="text-sm text-base-content/70">{subtitle}</p>}
      </div>
    </div>
  );
};
