using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.FinalAssessment
{
    public class FinalAssessmentCreateDto
    {
        [Required]
        [MaxLength(50)]
        public string TaxpayerRegistration { get; set; } = string.Empty;
        
        [Required]
        public Guid FiscalYearId { get; set; }
        
        [Required]
        public Guid MunicipalityId { get; set; }
        
        public Guid? ReturnFilingId { get; set; }
        
        public Guid? PreliminaryAssessmentId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string TaxpayerName { get; set; } = string.Empty;
        
        [MaxLength(255)]
        public string? Address { get; set; }
        
        [MaxLength(20)]
        public string? Phone { get; set; }
        
        [Required]
        public DateTime AssessmentPeriodFrom { get; set; }
        
        [Required]
        public DateTime AssessmentPeriodTo { get; set; }
        
        public string? SectionsRules { get; set; }
        
        [MaxLength(100)]
        public string? BankName { get; set; }
        
        [MaxLength(100)]
        public string? BranchName { get; set; }
        
        [MaxLength(50)]
        public string? ReasonCode { get; set; }
        
        [MaxLength(50)]
        public string? AppealNumber { get; set; }
        
        public string? ReasonDescription { get; set; }
        
        public DateTime? InterestPenaltyCalculationDate { get; set; }
        
        public DateTime? FinalAssessmentDate { get; set; }
        
        public List<FinalAssessmentDetailDto> TaxDetails { get; set; } = new List<FinalAssessmentDetailDto>();
    }
    
    public class FinalAssessmentUpdateDto
    {
        [Required]
        [MaxLength(50)]
        public string TaxpayerRegistration { get; set; } = string.Empty;
        
        [Required]
        public Guid FiscalYearId { get; set; }
        
        [Required]
        public Guid MunicipalityId { get; set; }
        
        public Guid? ReturnFilingId { get; set; }
        
        public Guid? PreliminaryAssessmentId { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string TaxpayerName { get; set; } = string.Empty;
        
        [MaxLength(255)]
        public string? Address { get; set; }
        
        [MaxLength(20)]
        public string? Phone { get; set; }
        
        [Required]
        public DateTime AssessmentPeriodFrom { get; set; }
        
        [Required]
        public DateTime AssessmentPeriodTo { get; set; }
        
        public string? SectionsRules { get; set; }
        
        [MaxLength(100)]
        public string? BankName { get; set; }
        
        [MaxLength(100)]
        public string? BranchName { get; set; }
        
        [MaxLength(50)]
        public string? ReasonCode { get; set; }
        
        [MaxLength(50)]
        public string? AppealNumber { get; set; }
        
        public string? ReasonDescription { get; set; }
        
        public DateTime? InterestPenaltyCalculationDate { get; set; }
        
        public DateTime? FinalAssessmentDate { get; set; }
        
        public List<FinalAssessmentDetailDto> TaxDetails { get; set; } = new List<FinalAssessmentDetailDto>();
    }
    
    public class FinalAssessmentDetailDto
    {
        public int SerialNumber { get; set; }
        
        [MaxLength(100)]
        public string? FilingPeriod { get; set; }
        
        [MaxLength(100)]
        public string? TaxPeriod { get; set; }
        
        [MaxLength(50)]
        public string? TaxYear { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Assessed amount must be a positive value")]
        public decimal AssessedAmount { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Penalty must be a positive value")]
        public decimal Penalty { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Additional amount must be a positive value")]
        public decimal AdditionalAmount { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Interest must be a positive value")]
        public decimal Interest { get; set; }
        
        public decimal Total { get; set; }
    }
    
    public class FinalAssessmentResponseDto
    {
        public Guid Id { get; set; }
        public string TaxpayerRegistration { get; set; } = string.Empty;
        public Guid FiscalYearId { get; set; }
        public Guid MunicipalityId { get; set; }
        public Guid? ReturnFilingId { get; set; }
        public Guid? PreliminaryAssessmentId { get; set; }
        public string TaxpayerName { get; set; } = string.Empty;
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public DateTime AssessmentPeriodFrom { get; set; }
        public DateTime AssessmentPeriodTo { get; set; }
        public string? SectionsRules { get; set; }
        public string? BankName { get; set; }
        public string? BranchName { get; set; }
        public string? ReasonCode { get; set; }
        public string? AppealNumber { get; set; }
        public string? ReasonDescription { get; set; }
        public DateTime? InterestPenaltyCalculationDate { get; set; }
        public DateTime? FinalAssessmentDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        public List<FinalAssessmentDetailDto> TaxDetails { get; set; } = new List<FinalAssessmentDetailDto>();
        public decimal GrandTotal { get; set; }
    }
    
    public class FinalAssessmentListDto
    {
        public Guid Id { get; set; }
        public string TaxpayerRegistration { get; set; } = string.Empty;
        public Guid FiscalYearId { get; set; }
        public Guid MunicipalityId { get; set; }
        public Guid? ReturnFilingId { get; set; }
        public Guid? PreliminaryAssessmentId { get; set; }
        public string TaxpayerName { get; set; } = string.Empty;
        public DateTime AssessmentPeriodFrom { get; set; }
        public DateTime AssessmentPeriodTo { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public decimal GrandTotal { get; set; }
    }
    
    public class FinalAssessmentSearchParams
    {
        public string? TaxpayerRegistration { get; set; }
        public string? TaxpayerName { get; set; }
        public Guid? FiscalYearId { get; set; }
        public Guid? MunicipalityId { get; set; }
        public string? Status { get; set; }
        public DateTime? CreatedFrom { get; set; }
        public DateTime? CreatedTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
    
    public class FinalAssessmentListResponse
    {
        public List<FinalAssessmentListDto> Items { get; set; } = new List<FinalAssessmentListDto>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }
}