using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.API.DTOs.SpecialPenalty
{
    public class SpecialPenaltyResponseDto
    {
        public Guid SpecialPenaltyId { get; set; }
        public string SpecialPenaltyNo { get; set; } = null!;
        public TaxpayerInfoDto TaxpayerInfo { get; set; } = null!;
        public string AccountType { get; set; } = null!;
        public int TaxYear { get; set; }
        public string Reason { get; set; } = null!;
        public string? OtherReason { get; set; }
        public string? ReasonDetails { get; set; }
        public DateTime EffectiveDate { get; set; }
        public decimal Amount { get; set; }
        public string Status { get; set; } = null!;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
    }

    public class TaxpayerInfoDto
    {
        public string TaxpayerId { get; set; } = null!;
        public string FullName { get; set; } = null!;
        public string PAN { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
    }

    public class CreateSpecialPenaltyDto
    {
        [Required]
        public string TaxpayerId { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string AccountType { get; set; } = null!;
        
        [Required]
        public int TaxYear { get; set; }
        
        [Required]
        [StringLength(10)]
        public string Reason { get; set; } = null!;
        
        [StringLength(500)]
        public string? OtherReason { get; set; }
        
        [StringLength(1000)]
        public string? ReasonDetails { get; set; }
        
        [Required]
        public DateTime EffectiveDate { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
    }

    public class UpdateSpecialPenaltyDto
    {
        [StringLength(50)]
        public string? AccountType { get; set; }
        
        public int? TaxYear { get; set; }
        
        [StringLength(10)]
        public string? Reason { get; set; }
        
        [StringLength(500)]
        public string? OtherReason { get; set; }
        
        [StringLength(1000)]
        public string? ReasonDetails { get; set; }
        
        public DateTime? EffectiveDate { get; set; }
        
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal? Amount { get; set; }
    }

    public class SpecialPenaltyListQueryDto
    {
        public string? Status { get; set; }
        public int? TaxYear { get; set; }
        public string? AccountType { get; set; }
        public string? SearchTerm { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SortBy { get; set; } = "CreatedAt";
        public string? SortOrder { get; set; } = "desc";
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class SpecialPenaltyStatusUpdateDto
    {
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = null!;
    }
}