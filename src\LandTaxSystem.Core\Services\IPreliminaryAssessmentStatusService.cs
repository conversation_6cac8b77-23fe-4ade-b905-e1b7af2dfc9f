using System;
using System.Threading.Tasks;
using LandTaxSystem.Core.Enums;

namespace LandTaxSystem.Core.Services
{
    public interface IPreliminaryAssessmentStatusService
    {
        /// <summary>
        /// Updates the status of a preliminary assessment with validation
        /// </summary>
        Task<StatusUpdateResult> UpdateStatusAsync(Guid assessmentId, PreliminaryAssessmentStatus newStatus, string userId, string? reason = null);
        
        /// <summary>
        /// Gets the current status of an assessment
        /// </summary>
        Task<PreliminaryAssessmentStatus?> GetStatusAsync(Guid assessmentId);
        
        /// <summary>
        /// Gets all possible next statuses for an assessment
        /// </summary>
        Task<PreliminaryAssessmentStatus[]> GetPossibleNextStatusesAsync(Guid assessmentId);
        
        /// <summary>
        /// Submits assessment for review (Draft -> PendingReview)
        /// </summary>
        Task<StatusUpdateResult> SubmitForReviewAsync(Guid assessmentId, string userId);
        
        /// <summary>
        /// Starts review process (PendingReview -> UnderReview)
        /// </summary>
        Task<StatusUpdateResult> StartReviewAsync(Guid assessmentId, string userId);
        
        /// <summary>
        /// Approves assessment (UnderReview -> Approved)
        /// </summary>
        Task<StatusUpdateResult> ApproveAsync(Guid assessmentId, string userId, string? approvalNotes = null);
        
        /// <summary>
        /// Rejects assessment (UnderReview -> Rejected)
        /// </summary>
        Task<StatusUpdateResult> RejectAsync(Guid assessmentId, string userId, string rejectionReason);
        
        /// <summary>
        /// Finalizes assessment (Approved -> Finalized)
        /// </summary>
        Task<StatusUpdateResult> FinalizeAsync(Guid assessmentId, string userId);
        
        /// <summary>
        /// Cancels assessment (any status -> Cancelled)
        /// </summary>
        Task<StatusUpdateResult> CancelAsync(Guid assessmentId, string userId, string cancellationReason);
    }
    
    public class StatusUpdateResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public PreliminaryAssessmentStatus? OldStatus { get; set; }
        public PreliminaryAssessmentStatus? NewStatus { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        
        public static StatusUpdateResult SuccessResult(PreliminaryAssessmentStatus oldStatus, PreliminaryAssessmentStatus newStatus, string updatedBy)
        {
            return new StatusUpdateResult
            {
                Success = true,
                OldStatus = oldStatus,
                NewStatus = newStatus,
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = updatedBy
            };
        }
        
        public static StatusUpdateResult FailureResult(string errorMessage)
        {
            return new StatusUpdateResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }
}