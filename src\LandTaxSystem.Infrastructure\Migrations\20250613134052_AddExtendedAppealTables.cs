﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddExtendedAppealTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ExtendedAppeals",
                columns: table => new
                {
                    AppealId = table.Column<Guid>(type: "uuid", nullable: false),
                    OfficeCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    LocationCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TaxpayerName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    TaxpayerAddress = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AppealDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AppealSubject = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    TaxDeterminationOrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AppealAuthority = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RegistrationNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OrderDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AppealDescription = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ResolvedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExtendedAppeals", x => x.AppealId);
                    table.CheckConstraint("CK_ExtendedAppeal_Status", "\"Status\" IN ('Pending', 'Resolved')");
                    table.ForeignKey(
                        name: "FK_ExtendedAppeals_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TaxPeriods",
                columns: table => new
                {
                    TaxPeriodId = table.Column<Guid>(type: "uuid", nullable: false),
                    ExtendedAppealId = table.Column<Guid>(type: "uuid", nullable: false),
                    Year = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Period = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    TaxPeriodValue = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AppealSubject = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AppealAmount = table.Column<decimal>(type: "numeric(15,2)", precision: 15, scale: 2, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxPeriods", x => x.TaxPeriodId);
                    table.ForeignKey(
                        name: "FK_TaxPeriods_ExtendedAppeals_ExtendedAppealId",
                        column: x => x.ExtendedAppealId,
                        principalTable: "ExtendedAppeals",
                        principalColumn: "AppealId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExtendedAppeals_UserId",
                table: "ExtendedAppeals",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxPeriods_ExtendedAppealId",
                table: "TaxPeriods",
                column: "ExtendedAppealId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TaxPeriods");

            migrationBuilder.DropTable(
                name: "ExtendedAppeals");
        }
    }
}
