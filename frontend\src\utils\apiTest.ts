// API Connectivity Test Utility
import { authService } from "../services/api";

export const testApiConnectivity = async () => {
  try {
    // Test a simple endpoint without authentication
    const response = await fetch("/api/municipalities", {
      headers: {
        Accept: "application/json",
      },
    });

    console.log("API Status:", response.status);
    console.log("API Response:", response.statusText);

    if (response.status === 401) {
      console.log(
        "✅ API is accessible but requires authentication (expected)"
      );
      return true;
    } else if (response.ok) {
      const data = await response.json();
      console.log("✅ API is accessible and responding:", data);
      return true;
    } else {
      console.log("❌ API returned unexpected status:", response.status);
      return false;
    }
  } catch (error) {
    console.error("❌ API connectivity test failed:", error);
    return false;
  }
};

export const testLogin = async () => {
  try {
    console.log("Testing login with demo credentials...");
    const result = await authService.login("<EMAIL>", "Password123!");
    console.log("✅ Login successful:", result);
    return result;
  } catch (error) {
    console.error("❌ Login test failed:", error);
    throw error;
  }
};

// Development helper function
export const runAPITests = async () => {
  console.group("🧪 API Connectivity Tests");

  const isConnected = await testApiConnectivity();

  if (isConnected) {
    try {
      await testLogin();
    } catch {
      console.warn("Login test failed, but API is accessible");
    }
  }

  console.groupEnd();
};
