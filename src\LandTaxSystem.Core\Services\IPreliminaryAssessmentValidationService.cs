using System;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.PreliminaryAssessment;
using LandTaxSystem.Core.Enums;

namespace LandTaxSystem.Core.Services
{
    public interface IPreliminaryAssessmentValidationService
    {
        /// <summary>
        /// Validates if a status transition is allowed
        /// </summary>
        Task<ValidationResult> ValidateStatusTransitionAsync(Guid assessmentId, PreliminaryAssessmentStatus newStatus, string userId);
        
        /// <summary>
        /// Validates assessment period overlaps for the same taxpayer
        /// </summary>
        Task<ValidationResult> ValidateAssessmentPeriodAsync(string taxpayerRegistration, DateTime periodFrom, DateTime periodTo, Guid? excludeAssessmentId = null);
        
        /// <summary>
        /// Validates tax calculation logic
        /// </summary>
        ValidationResult ValidateTaxCalculation(PreliminaryAssessmentCreateDto dto);
        
        /// <summary>
        /// Validates business rules before creating assessment
        /// </summary>
        Task<ValidationResult> ValidateCreateAsync(PreliminaryAssessmentCreateDto dto);
        
        /// <summary>
        /// Validates business rules before updating assessment
        /// </summary>
        Task<ValidationResult> ValidateUpdateAsync(Guid assessmentId, PreliminaryAssessmentUpdateDto dto);
        
        /// <summary>
        /// Validates if assessment can be deleted
        /// </summary>
        Task<ValidationResult> ValidateDeleteAsync(Guid assessmentId);
    }
    
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        
        public static ValidationResult Success() => new ValidationResult { IsValid = true };
        
        public static ValidationResult Failure(params string[] errors) => new ValidationResult 
        { 
            IsValid = false, 
            Errors = errors.ToList() 
        };
        
        public static ValidationResult Warning(string warning) => new ValidationResult 
        { 
            IsValid = true, 
            Warnings = new List<string> { warning } 
        };
    }
}