/**
 * Conversion constants for Nepali land measurement units.
 */
export const CONVERSIONS = {
  ROPANI_TO_SQM: 508.72,
  AANA_PER_ROPANI: 16,
  PAISA_PER_AANA: 4,
  DAAM_PER_PAISA: 4,
  BIGHA_TO_SQM: 6772.63,
  KAT<PERSON><PERSON>_PER_BIGHA: 20,
  DHUR_PER_KATTHA: 20,
};

export type RopaniSystem = {
  ropani: number;
  aana: number;
  paisa: number;
  daam: number;
};

export type BighaSystem = {
  bigha: number;
  kattha: number;
  dhur: number;
};

/**
 * Converts area from the Ropani system to square meters.
 * @param {RopaniSystem} units - The area in Ropani, Aana, Paisa, and Daam.
 * @returns {number} The total area in square meters.
 */
export const ropaniToSqM = ({ ropani, aana, paisa, daam }: RopaniSystem): number => {
  const totalDaam = ropani * CONVERSIONS.AANA_PER_ROPANI * CONVERSIONS.PAISA_PER_AANA * CONVERSIONS.DAAM_PER_PAISA +
                    aana * CONVERSIONS.PAISA_PER_AANA * CONVERSIONS.DAAM_PER_PAISA +
                    paisa * CONVERSIONS.DAAM_PER_PAISA +
                    daam;
  const totalRopani = totalDaam / (CONVERSIONS.AANA_PER_ROPANI * CONVERSIONS.PAISA_PER_AANA * CONVERSIONS.DAAM_PER_PAISA);
  return totalRopani * CONVERSIONS.ROPANI_TO_SQM;
};

/**
 * Converts area from the Bigha system to square meters.
 * @param {BighaSystem} units - The area in Bigha, Kattha, and Dhur.
 * @returns {number} The total area in square meters.
 */
export const bighaToSqM = ({ bigha, kattha, dhur }: BighaSystem): number => {
  const totalDhur = bigha * CONVERSIONS.KATTHA_PER_BIGHA * CONVERSIONS.DHUR_PER_KATTHA +
                    kattha * CONVERSIONS.DHUR_PER_KATTHA +
                    dhur;
  const totalBigha = totalDhur / (CONVERSIONS.KATTHA_PER_BIGHA * CONVERSIONS.DHUR_PER_KATTHA);
  return totalBigha * CONVERSIONS.BIGHA_TO_SQM;
};
