using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.PreliminaryAssessment;

namespace LandTaxSystem.Core.Interfaces
{
    public interface IPreliminaryAssessmentService
    {
        Task<PreliminaryAssessmentResponseDto> CreateAsync(PreliminaryAssessmentCreateDto dto, string userId);
        Task<PreliminaryAssessmentResponseDto> UpdateAsync(Guid id, PreliminaryAssessmentUpdateDto dto, string userId);
        Task<PreliminaryAssessmentResponseDto?> GetByIdAsync(Guid id);
        Task<IEnumerable<PreliminaryAssessmentListDto>> GetAllAsync(int page = 1, int pageSize = 10);
        Task<IEnumerable<PreliminaryAssessmentListDto>> GetByTaxpayerRegistrationAsync(string taxpayerRegistration);
        Task<IEnumerable<PreliminaryAssessmentListDto>> GetByMunicipalityAsync(Guid municipalityId);
        Task<IEnumerable<PreliminaryAssessmentListDto>> GetByFiscalYearAsync(Guid fiscalYearId);
        Task<bool> DeleteAsync(Guid id);
        Task<bool> ExistsAsync(string taxpayerRegistration);
        Task<int> GetTotalCountAsync();
    }
}