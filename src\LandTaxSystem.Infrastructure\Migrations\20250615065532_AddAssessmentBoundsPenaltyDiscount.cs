﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAssessmentBoundsPenaltyDiscount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "ActualAssessment",
                table: "Assessments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountPercent",
                table: "Assessments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "LowerAssessment",
                table: "Assessments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "PenaltyPercent",
                table: "Assessments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "UpperAssessment",
                table: "Assessments",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ActualAssessment",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "DiscountPercent",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "LowerAssessment",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "PenaltyPercent",
                table: "Assessments");

            migrationBuilder.DropColumn(
                name: "UpperAssessment",
                table: "Assessments");
        }
    }
}
