using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.Entities
{
    public class FiscalYear
    {
        public Guid FiscalYearId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<MunicipalityTaxConfig> MunicipalityTaxConfigs { get; set; } = new List<MunicipalityTaxConfig>();
    }
}
