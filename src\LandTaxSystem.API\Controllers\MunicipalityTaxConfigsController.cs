using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.TaxConfig;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Models;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MunicipalityTaxConfigsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public MunicipalityTaxConfigsController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        [Authorize(Policy = "RequireCentralAdminRole")]
        public async Task<ActionResult<IEnumerable<MunicipalityTaxConfigResponseDto>>> GetAllMunicipalityTaxConfigs()
        {
            var taxConfigs = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .OrderBy(mtc => mtc.Municipality.Name)
                .ThenBy(mtc => mtc.FiscalYear.StartDate)
                .ToListAsync();

            return Ok(taxConfigs.Select(MapToResponseDto).ToList());
        }

        [HttpGet("municipality/{municipalityId}")]
        public async Task<ActionResult<IEnumerable<MunicipalityTaxConfigResponseDto>>> GetMunicipalityTaxConfigs(Guid municipalityId)
        {
            // Check if user is authorized to access this municipality's tax configs
            if (!await UserCanAccessMunicipality(municipalityId))
            {
                return Forbid();
            }

            var taxConfigs = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .Where(mtc => mtc.MunicipalityId == municipalityId)
                .OrderByDescending(mtc => mtc.FiscalYear.StartDate)
                .ToListAsync();

            return Ok(taxConfigs.Select(MapToResponseDto).ToList());
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<MunicipalityTaxConfigResponseDto>> GetMunicipalityTaxConfig(Guid id)
        {
            var taxConfig = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .FirstOrDefaultAsync(mtc => mtc.MunicipalityTaxConfigId == id);

            if (taxConfig == null)
            {
                return NotFound();
            }

            // Check if user is authorized to access this municipality's tax config
            if (!await UserCanAccessMunicipality(taxConfig.MunicipalityId))
            {
                return Forbid();
            }

            return MapToResponseDto(taxConfig);
        }
        [HttpPost]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<ActionResult<MunicipalityTaxConfigResponseDto>> CreateMunicipalityTaxConfig(MunicipalityTaxConfigCreateDto createDto)
        {
            // Check if user is authorized to create tax config for this municipality
            if (!await UserCanAccessMunicipality(createDto.MunicipalityId))
            {
                return Forbid();
            }

            // Validate ValuationRulesConfig if provided
            if (!ValidateValuationRulesConfig(createDto.ValuationRulesConfig, out string validationError))
            {
                return BadRequest($"Validation failed: {validationError}");
            }

            // Check if municipality exists
            var municipality = await _context.Municipalities.FindAsync(createDto.MunicipalityId);
            if (municipality == null)
            {
                return BadRequest("Municipality not found");
            }

            // Check if fiscal year exists
            var fiscalYear = await _context.FiscalYears.FindAsync(createDto.FiscalYearId);
            if (fiscalYear == null)
            {
                return BadRequest("Fiscal year not found");
            }

            // Check if tax config already exists for this municipality and fiscal year
            if (await _context.MunicipalityTaxConfigs.AnyAsync(
                mtc => mtc.MunicipalityId == createDto.MunicipalityId &&
                       mtc.FiscalYearId == createDto.FiscalYearId))
            {
                return BadRequest("Tax configuration already exists for this municipality and fiscal year");
            }

            var taxConfig = new MunicipalityTaxConfig
            {
                MunicipalityTaxConfigId = Guid.NewGuid(),
                MunicipalityId = createDto.MunicipalityId,
                FiscalYearId = createDto.FiscalYearId,
                TaxSlabsConfigJson = JsonSerializer.Serialize(createDto.TaxSlabsConfig ?? new List<TaxSlab>()),
                PenaltyRulesJson = JsonSerializer.Serialize(createDto.PenaltyRules ?? new List<PenaltyRule>()),
                IsFinalized = false,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Serialize ValuationRulesConfig if provided
            if (createDto.ValuationRulesConfig != null)
            {
                taxConfig.ValuationRulesConfigJson = JsonSerializer.Serialize(createDto.ValuationRulesConfig);
            }

            _context.MunicipalityTaxConfigs.Add(taxConfig);
            await _context.SaveChangesAsync();

            // Reload with navigation properties for response
            taxConfig = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .FirstAsync(mtc => mtc.MunicipalityTaxConfigId == taxConfig.MunicipalityTaxConfigId);

            return CreatedAtAction(
                nameof(GetMunicipalityTaxConfig),
                new { id = taxConfig.MunicipalityTaxConfigId },
                MapToResponseDto(taxConfig));
        }
        [HttpPut("{id}")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<IActionResult> UpdateMunicipalityTaxConfig(Guid id, MunicipalityTaxConfigUpdateDto updateDto)
        {
            var taxConfig = await _context.MunicipalityTaxConfigs.FindAsync(id);

            if (taxConfig == null)
            {
                return NotFound();
            }

            // Check if user is authorized to update this municipality's tax config
            if (!await UserCanAccessMunicipality(taxConfig.MunicipalityId))
            {
                return Forbid();
            }

            // Check if tax config is already finalized
            if (taxConfig.IsFinalized)
            {
                return BadRequest("Cannot update a finalized tax configuration");
            }

            // Validate ValuationRulesConfig if provided
            if (!ValidateValuationRulesConfig(updateDto.ValuationRulesConfig, out string validationError))
            {
                return BadRequest($"Validation failed: {validationError}");
            }

            // Update properties if provided
            if (updateDto.TaxSlabsConfig != null)
            {
                taxConfig.TaxSlabsConfigJson = JsonSerializer.Serialize(updateDto.TaxSlabsConfig);
            }

            if (updateDto.PenaltyRules != null)
            {
                taxConfig.PenaltyRulesJson = JsonSerializer.Serialize(updateDto.PenaltyRules);
            }

            // Update ValuationRulesConfig if provided
            if (updateDto.ValuationRulesConfig != null)
            {
                taxConfig.ValuationRulesConfigJson = JsonSerializer.Serialize(updateDto.ValuationRulesConfig);
            }

            taxConfig.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await MunicipalityTaxConfigExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            // Reload with navigation properties for response
            taxConfig = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .FirstAsync(mtc => mtc.MunicipalityTaxConfigId == id);

            return Ok(MapToResponseDto(taxConfig));
        }

        [HttpPut("{id}/finalize")]
        [Authorize(Policy = "RequireOfficerRole")]
        public async Task<IActionResult> FinalizeMunicipalityTaxConfig(Guid id, MunicipalityTaxConfigFinalizeDto finalizeDto)
        {
            var taxConfig = await _context.MunicipalityTaxConfigs.FindAsync(id);

            if (taxConfig == null)
            {
                return NotFound();
            }

            // Check if user is authorized to finalize this municipality's tax config
            if (!await UserCanAccessMunicipality(taxConfig.MunicipalityId))
            {
                return Forbid();
            }

            // Check if already finalized
            if (taxConfig.IsFinalized && finalizeDto.Finalize)
            {
                return BadRequest("Tax configuration is already finalized");
            }

            // Check if trying to unfinalize
            if (!finalizeDto.Finalize)
            {
                return BadRequest("Cannot unfinalize a tax configuration once it's finalized");
            }

            // Get the current user's username
            var username = User.FindFirstValue(ClaimTypes.Name) ?? "Unknown";

            taxConfig.IsFinalized = true;
            taxConfig.FinalizedAt = DateTime.UtcNow;
            taxConfig.FinalizedBy = username;
            taxConfig.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Reload with navigation properties for response
            taxConfig = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .FirstAsync(mtc => mtc.MunicipalityTaxConfigId == id);

            return Ok(MapToResponseDto(taxConfig));
        }

        [HttpGet("fiscal-year/{fiscalYearId}")]
        [Authorize(Policy = "RequireCentralAdminRole")]
        public async Task<ActionResult<IEnumerable<MunicipalityTaxConfigResponseDto>>> GetTaxConfigsByFiscalYear(Guid fiscalYearId)
        {
            var taxConfigs = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .Where(mtc => mtc.FiscalYearId == fiscalYearId)
                .OrderBy(mtc => mtc.Municipality.Name)
                .ToListAsync();

            return Ok(taxConfigs.Select(MapToResponseDto).ToList());
        }

        [HttpGet("municipality/{municipalityId}/fiscal-year/{fiscalYearId}")]
        public async Task<ActionResult<MunicipalityTaxConfigResponseDto>> GetTaxConfigByMunicipalityAndFiscalYear(Guid municipalityId, Guid fiscalYearId)
        {
            // Check if user is authorized to access this municipality's tax configs
            if (!await UserCanAccessMunicipality(municipalityId))
            {
                return Forbid();
            }

            var taxConfig = await _context.MunicipalityTaxConfigs
                .Include(mtc => mtc.Municipality)
                .Include(mtc => mtc.FiscalYear)
                .FirstOrDefaultAsync(mtc => mtc.MunicipalityId == municipalityId && mtc.FiscalYearId == fiscalYearId);

            if (taxConfig == null)
            {
                return NotFound();
            }

            return MapToResponseDto(taxConfig);
        }

        private async Task<bool> MunicipalityTaxConfigExists(Guid id)
        {
            return await _context.MunicipalityTaxConfigs.AnyAsync(e => e.MunicipalityTaxConfigId == id);
        }
        private MunicipalityTaxConfigResponseDto MapToResponseDto(MunicipalityTaxConfig taxConfig)
        {
            var responseDto = new MunicipalityTaxConfigResponseDto
            {
                MunicipalityTaxConfigId = taxConfig.MunicipalityTaxConfigId,
                MunicipalityId = taxConfig.MunicipalityId,
                MunicipalityName = taxConfig.Municipality?.Name ?? "Unknown",
                FiscalYearId = taxConfig.FiscalYearId,
                FiscalYearName = taxConfig.FiscalYear?.Name ?? "Unknown",
                TaxSlabsConfig = JsonSerializer.Deserialize<List<TaxSlab>>(taxConfig.TaxSlabsConfigJson) ?? new List<TaxSlab>(),
                PenaltyRules = DeserializePenaltyRules(taxConfig.PenaltyRulesJson),
                IsFinalized = taxConfig.IsFinalized,
                FinalizedAt = taxConfig.FinalizedAt,
                FinalizedBy = taxConfig.FinalizedBy,
                CreatedAt = taxConfig.CreatedAt,
                UpdatedAt = taxConfig.UpdatedAt
            };

            // Deserialize ValuationRulesConfig if it exists
            if (!string.IsNullOrEmpty(taxConfig.ValuationRulesConfigJson) && taxConfig.ValuationRulesConfigJson != "{}")
            {
                try
                {
                    responseDto.ValuationRulesConfig = JsonSerializer.Deserialize<ValuationRulesConfigDto>(taxConfig.ValuationRulesConfigJson);
                }
                catch (JsonException)
                {
                    // If deserialization fails, leave as null
                    responseDto.ValuationRulesConfig = null;
                }
            }

            return responseDto;
        }

        private bool ValidateValuationRulesConfig(ValuationRulesConfigDto? config, out string errorMessage)
        {
            errorMessage = string.Empty;

            if (config == null)
            {
                return true; // Null is valid as it's optional
            }

            // Validate AnnualDepreciationRate (must be between 0 and 1)
            if (config.AnnualDepreciationRate < 0 || config.AnnualDepreciationRate > 1)
            {
                errorMessage = "AnnualDepreciationRate must be between 0 and 1 (0-100%)";
                return false;
            }

            // Validate LandMVR dictionary values (must be non-negative)
            if (config.LandMVR?.Any(kvp => kvp.Value < 0) == true)
            {
                errorMessage = "All Land MVR values must be non-negative";
                return false;
            }

            // Validate BuildingBaseRatePerSqm dictionary values (must be non-negative)
            if (config.BuildingBaseRatePerSqm?.Any(kvp => kvp.Value < 0) == true)
            {
                errorMessage = "All Building Base Rate per Sqm values must be non-negative";
                return false;
            }

            return true;
        }

        private async Task<bool> UserCanAccessMunicipality(Guid municipalityId)
        {
            // Central admins can access any municipality
            if (User.IsInRole("CentralAdmin"))
            {
                return true;
            }

            // Officers can only access their assigned municipality
            if (User.IsInRole("Officer"))
            {
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                if (string.IsNullOrEmpty(userId))
                {
                    return false;
                }

                var user = await _context.Users.FindAsync(userId);
                return user != null && user.MunicipalityId == municipalityId;
            }

            // Other roles cannot access municipality tax configs
            return false;
        }

        private List<PenaltyRule> DeserializePenaltyRules(string penaltyRulesJson)
        {
            if (string.IsNullOrEmpty(penaltyRulesJson) || penaltyRulesJson == "{}")
            {
                return new List<PenaltyRule>();
            }

            try
            {
                // First, try to deserialize as a list
                return JsonSerializer.Deserialize<List<PenaltyRule>>(penaltyRulesJson) ?? new List<PenaltyRule>();
            }
            catch (JsonException)
            {
                // If that fails, try to deserialize as a single object and wrap it in a list
                try
                {
                    var singleRule = JsonSerializer.Deserialize<PenaltyRule>(penaltyRulesJson);
                    return singleRule != null ? new List<PenaltyRule> { singleRule } : new List<PenaltyRule>();
                }
                catch (JsonException)
                {
                    // If both fail, return an empty list
                    return new List<PenaltyRule>();
                }
            }
        }
    }
}
