{"semi": true, "trailingComma": "es5", "singleQuote": false, "printWidth": 120, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.json", "options": {"printWidth": 120}}]}