import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { taxCalculationService } from "../services/taxCalculationService";
import { paymentService } from "../services/api";
import api from "../services/api";
import type { TaxCalculationResult, PaymentResponseDto } from "../types";

interface DirectTaxPaymentProps {
  propertyId: string;
  fiscalYearId?: string;
  calculatedTaxAmount?: number;
  onPaymentSuccess?: (payment: PaymentResponseDto) => void;
  onPaymentError?: (error: string) => void;
  onCancel?: () => void;
  className?: string;
}

const DirectTaxPayment: React.FC<DirectTaxPaymentProps> = ({
  propertyId,
  fiscalYearId,
  calculatedTaxAmount,
  onPaymentSuccess,
  onPaymentError,
  onCancel,
  className = "",
}) => {
  const navigate = useNavigate();
  const [taxCalculation, setTaxCalculation] =
    useState<TaxCalculationResult | null>(null);
  const [paymentAmount, setPaymentAmount] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState<string>("card");
  const [loading, setLoading] = useState(false);
  const [calculatingTax, setCalculatingTax] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCustomAmount, setShowCustomAmount] = useState(false);
  // Fetch tax calculation if not provided
  useEffect(() => {
    if (!calculatedTaxAmount && propertyId) {
      fetchTaxCalculation();
    } else if (calculatedTaxAmount) {
      setPaymentAmount(calculatedTaxAmount.toString());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propertyId, fiscalYearId, calculatedTaxAmount]);

  const fetchTaxCalculation = async () => {
    if (!propertyId) return;

    setCalculatingTax(true);
    setError(null);

    try {
      let result: TaxCalculationResult;
      if (fiscalYearId) {
        // Use specific fiscal year if provided
        result = await taxCalculationService.getTaxCalculation(propertyId, fiscalYearId);
      } else {
        // Use current active fiscal year if no specific fiscal year provided
        result = await taxCalculationService.getCurrentTaxCalculation(propertyId);
      }
      setTaxCalculation(result);
      setPaymentAmount(result.taxAmount.toString());
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to calculate tax");
    } finally {
      setCalculatingTax(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NP", {
      style: "currency",
      currency: "NPR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleAmountChange = (value: string) => {
    // Only allow positive numbers with up to 2 decimal places
    const regex = /^\d*\.?\d{0,2}$/;
    if (regex.test(value) || value === "") {
      setPaymentAmount(value);
    }
  };
  const handlePayment = async () => {
    if (!propertyId) {
      setError("Missing property information");
      return;
    }

    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
      setError("Please enter a valid payment amount");
      return;
    }

    setLoading(true);
    setError(null);    try {
      let targetFiscalYearId = fiscalYearId;
        // If no fiscal year provided, get the current active fiscal year
      if (!targetFiscalYearId) {
        const fiscalYearResponse = await api.get('/FiscalYears/active');
        const activeFiscalYear = fiscalYearResponse.data;
        targetFiscalYearId = activeFiscalYear.fiscalYearId;
      }

      // Ensure we have a fiscal year ID before proceeding
      if (!targetFiscalYearId) {
        throw new Error('Could not determine fiscal year for payment');
      }

      const payment = await paymentService.payTaxDirectly(
        propertyId,
        targetFiscalYearId,
        amount,
        paymentMethod
      );

      if (onPaymentSuccess) {
        onPaymentSuccess(payment);
      } else {
        // Default behavior: navigate to payment receipt
        navigate(`/payments/receipt/${payment.paymentId}`);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Payment failed. Please try again.";
      setError(errorMessage);
      if (onPaymentError) {
        onPaymentError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const expectedTax = calculatedTaxAmount || taxCalculation?.taxAmount || 0;
  const enteredAmount = parseFloat(paymentAmount) || 0;
  const isUnderpayment = enteredAmount > 0 && enteredAmount < expectedTax;
  const isOverpayment = enteredAmount > expectedTax;

  if (calculatingTax) {
    return (
      <div className={`card bg-base-100 shadow-xl p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="text-center">
            <div className="h-8 bg-base-300 rounded w-48 mx-auto mb-4"></div>
            <div className="h-4 bg-base-300 rounded w-32 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`card bg-base-100 shadow-xl p-6 ${className}`}>
      <h3 className="text-lg font-semibold mb-6">
        Direct Tax Payment
      </h3>

      {/* Tax Amount Display */}
      {expectedTax > 0 && (
        <div className="bg-info/10 p-4 rounded-lg mb-6">
          <h4 className="text-sm font-medium text-info mb-1">
            Calculated Tax Amount
          </h4>
          <p className="text-2xl font-bold text-info">
            {formatCurrency(expectedTax)}
          </p>
        </div>
      )}

      {/* Payment Method Selection */}
      <div className="form-control mb-6">
        <label className="label">
          <span className="label-text">Payment Method</span>
        </label>
        <select
          value={paymentMethod}
          onChange={(e) => setPaymentMethod(e.target.value)}
          className="select select-bordered"
          disabled={loading}
        >
          <option value="card">Credit/Debit Card</option>
          <option value="bank">Bank Transfer</option>
          <option value="digital">Digital Wallet</option>
        </select>
      </div>

      {/* Payment Amount */}
      <div className="form-control mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="label">
            <span className="label-text">Payment Amount</span>
          </label>
          {expectedTax > 0 && (
            <button
              type="button"
              onClick={() => setShowCustomAmount(!showCustomAmount)}
              className="link link-primary text-sm"
            >
              {showCustomAmount
                ? "Use calculated amount"
                : "Enter custom amount"}
            </button>
          )}
        </div>

        <div className="relative">
          <input
            type="text"
            value={paymentAmount}
            onChange={(e) => handleAmountChange(e.target.value)}
            placeholder="0.00"
            className="input input-bordered pl-12"
            disabled={loading || (!showCustomAmount && expectedTax > 0)}
          />
        </div>

        {/* Payment warnings */}
        {isUnderpayment && (
          <div className="alert alert-warning mt-2">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm">
                  <strong>Underpayment:</strong> You're paying{" "}
                  {formatCurrency(enteredAmount)} but the full tax is{" "}
                  {formatCurrency(expectedTax)}. An assessment will be created
                  for the remaining amount.
                </p>
              </div>
            </div>
          </div>
        )}

        {isOverpayment && (
          <div className="alert alert-info mt-2">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm">
                  <strong>Overpayment:</strong> You're paying{" "}
                  {formatCurrency(enteredAmount)} but the tax is only{" "}
                  {formatCurrency(expectedTax)}. The excess amount will be
                  recorded.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="alert alert-error mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 btn btn-ghost"
            disabled={loading}
          >
            Cancel
          </button>
        )}
        <button
          type="button"
          onClick={handlePayment}
          disabled={loading || !paymentAmount || parseFloat(paymentAmount) <= 0}
          className="flex-1 btn btn-primary"
        >
          {loading
            ? "Processing..."
            : `Pay ${
                paymentAmount
                  ? formatCurrency(parseFloat(paymentAmount))
                  : "Tax"
              }`}
        </button>
      </div>
    </div>
  );
};

export default DirectTaxPayment;
