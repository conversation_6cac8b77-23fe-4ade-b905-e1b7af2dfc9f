import axios from "axios";
import type {
  AuthResponse,
  Municipality,
  Property,
  Assessment,
  User,
  PaymentResponseDto,
} from "../types";

// Additional interfaces for location hierarchy
export interface Province {
  id: string;
  name: string;
  code?: string;
}

export interface District {
  id: string;
  name: string;
  provinceId: string;
  code?: string;
}

export interface MunicipalityLocation {
  id: string;
  name: string;
  districtId: string;
  code?: string;
}

// Interface for return filing data
export interface ReturnFiling {
  id: string;
  propertyId: string;
  fiscalYearId: string;
  submissionDate: string;
  createdAt: string;
  status?: string;
  [key: string]: unknown;
}

const API_URL = "/api";

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to add auth token to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem("token");
      window.location.href = "/login";
    }

    // Extract error message from response
    let errorMessage = "An error occurred";
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.response?.data?.errors) {
      // Handle validation errors
      const errors = error.response.data.errors;
      errorMessage = Object.values(errors).flat().join(", ");
    } else if (error.response?.data) {
      errorMessage = error.response.data;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return Promise.reject(new Error(errorMessage));
  }
);

// Authentication Services
export const authService = {
  login: async (emailOrPAN: string, password: string): Promise<AuthResponse> => {
    // Determine if input is email or PAN based on format
    const isEmail = emailOrPAN.includes('@');
    
    const response = await api.post<AuthResponse>("/auth/login", {
      [isEmail ? 'email' : 'pan']: emailOrPAN,
      password,
    });
    localStorage.setItem("token", response.data.token);
    return response.data;
  },

  register: async (
    fullName: string,
    email: string,
    password: string,
    confirmPassword: string,
    phoneNumber?: string
  ): Promise<{ userId: string; message: string }> => {
    const response = await api.post<{ userId: string; message: string }>(
      "/auth/register",
      {
        fullName,
        email,
        password,
        confirmPassword,
        phoneNumber,
      }
    );
    return response.data;
  },
  
  registerWithFormData: async (
    formData: FormData
  ): Promise<{ userId: string; message: string; submissionNumber: string }> => {
    const response = await api.post<{ userId: string; message: string; submissionNumber: string }>(
      "/auth/register",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data;
  },

  logout: (): void => {
    localStorage.removeItem("token");
  },

  isAuthenticated: (): boolean => {
    return localStorage.getItem("token") !== null;
  },
};

// Municipality Services
export const municipalityService = {
  getAll: async (): Promise<Municipality[]> => {
    const response = await api.get<Municipality[]>("/municipalities");
    return response.data;
  },
  
  getMunicipalities: async (): Promise<Municipality[]> => {
    const response = await api.get<Municipality[]>("/auth/registration-municipalities");
    return response.data;
  },
  getById: async (municipalityId: string): Promise<Municipality> => {
    const response = await api.get<Municipality>(
      `/municipalities/${municipalityId}`
    );
    return response.data;
  },
  create: async (
    municipality: Omit<
      Municipality,
      "municipalityId" | "createdAt" | "updatedAt"
    >
  ): Promise<Municipality> => {
    const response = await api.post<Municipality>(
      "/municipalities",
      municipality
    );
    return response.data;
  },

  update: async (
    municipalityId: string,
    municipality: Partial<Municipality>
  ): Promise<Municipality> => {
    const response = await api.put<Municipality>(
      `/municipalities/${municipalityId}`,
      municipality
    );
    return response.data;
  },

  updateTaxDefaults: async (
    municipalityId: string,
    defaultPenaltyPercent: number,
    defaultDiscountPercent: number
  ): Promise<Municipality> => {
    const response = await api.put<Municipality>(
      `/municipalities/${municipalityId}/tax-defaults`,
      { defaultPenaltyPercent, defaultDiscountPercent }
    );
    return response.data;
  },
};

// Property Services
export const propertyService = {
  getMyProperties: async (): Promise<Property[]> => {
    const response = await api.get<Property[]>("/properties/mine");
    return response.data;
  },

  getByMunicipality: async (
    municipalityId: string, // This should be a GUID string format
    status?: string
  ): Promise<Property[]> => {
    const response = await api.get<Property[]>(
      `/properties/municipality/${municipalityId}`,
      {
        params: { status },
      }
    );
    return response.data;
  },

  getById: async (id: string): Promise<Property> => {
    const response = await api.get<Property>(`/properties/${id}`);
    return response.data;
  },

  create: async (propertyData: FormData): Promise<Property> => {
    const response = await api.post<Property>("/properties", propertyData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  updateStatus: async (
    id: string,
    status: "Approved" | "Rejected",
    reason?: string
  ): Promise<Property> => {
    const response = await api.put<Property>(`/properties/${id}/status`, {
      status,
      reason,
    });
    return response.data;
  },

  getMyReturnFilings: async (): Promise<Array<{
    returnFilingId: string;
    propertyId: string;
    propertyAddress: string;
    fiscalYearId: string;
    fiscalYearName: string;
    submissionDate: string;
    createdAt: string;
  }>> => {
    const response = await api.get("/properties/return-filings/mine");
    return response.data;
  },
  getTaxpayerReturnFilings: async (taxpayerId: string): Promise<ReturnFiling[]> => {
    const response = await api.get(`/properties/return-filings/taxpayer/${taxpayerId}`);
    return response.data;
  },
};

// Assessment Services
export const assessmentService = {
  getByProperty: async (propertyId: string): Promise<Assessment[]> => {
    const response = await api.get<Assessment[]>(
      `/assessments/property/${propertyId}`
    );
    return response.data;
  },

  getById: async (id: string): Promise<Assessment> => {
    const response = await api.get<Assessment>(`/assessments/${id}`);
    return response.data;
  },

  create: async (data: {
    propertyId: string;
    assessmentYear: number;
    overriddenValue?: number;
    overrideReason?: string;
  }): Promise<Assessment> => {
    const response = await api.post<Assessment>("/assessments", data);
    return response.data;
  },

  updatePaymentStatus: async (
    id: string,
    paymentStatus: "Pending" | "Paid" | "Overdue"
  ): Promise<Assessment> => {
    const response = await api.put<Assessment>(
      `/assessments/${id}/payment-status`,
      { paymentStatus }
    );
    return response.data;
  },

  getOverdueAssessments: async (): Promise<Assessment[]> => {
    const response = await api.get<Assessment[]>("/assessments/overdue");
    return response.data;
  },

  filterAssessments: async (
    municipalityId?: string,
    assessmentYear?: number,
    paymentStatus?: string,
    minTaxAmount?: number,
    maxTaxAmount?: number
  ): Promise<Assessment[]> => {
    const response = await api.get<Assessment[]>("/assessments/filter", {
      params: {
        municipalityId,
        assessmentYear,
        paymentStatus,
        minTaxAmount,
        maxTaxAmount,
      },
    });
    return response.data;
  },
};

// Payment Services
export const paymentService = {
  initiatePayment: async (
    assessmentId: string
  ): Promise<{ paymentGatewayUrl: string; internalPaymentId: string }> => {
    const response = await api.post<{
      paymentGatewayUrl: string;
      internalPaymentId: string;
    }>("/payments/initiate", { assessmentId });
    return response.data;
  },

  // New function to support provisional payments directly for properties
  initiateProvisionalPayment: async (
    propertyId: string,
    amount: number,
    paymentMethod: string = "card"
  ): Promise<{ paymentGatewayUrl: string; internalPaymentId: string }> => {
    const response = await api.post("/payments/provisional", { 
      propertyId, 
      amount,
      paymentMethod 
    });
    return response.data;
  },

  processDirectPayment: async (
    assessmentId: string,
    paymentMethod: string
  ): Promise<{ success: boolean; paymentId: string }> => {
    const response = await api.post("/payments", { assessmentId, paymentMethod });
    return response.data;
  },

  processBulkPayment: async (
    assessmentIds: string[],
    paymentMethod: string
  ): Promise<{ success: boolean; paymentIds: string[] }> => {
    const response = await api.post("/payments/bulk", { assessmentIds, paymentMethod });
    return response.data;
  },

  getPaymentHistory: async (propertyId: string): Promise<{ payments: Array<{ id: string; amount: number; date: string; status: string }> }> => {
    const response = await api.get(`/payments/property/${propertyId}`);
    return response.data;
  },

  getReceipt: async (paymentId: string): Promise<Blob> => {
    return api.get(`/payments/receipt/${paymentId}`, {
      responseType: 'blob'
    });
  },
  // Direct tax payment using the new pay-tax endpoint
  payTaxDirectly: async (
    propertyId: string,
    fiscalYearId: string,
    amountPaid: number,
    paymentMethod: string = "card",
    transactionId?: string
  ): Promise<PaymentResponseDto> => {
    const response = await api.post("/payments/pay-tax", {
      propertyId,
      fiscalYearId,
      amountPaid,
      paymentMethod,
      transactionId
    });
    return response.data;
  },
};

// User Management Services
export const userService = {
  getUsers(
    page: number = 1,
    size: number = 10,
    search?: string,
    role?: string
  ): Promise<{ users: User[]; totalCount: number; totalPages: number }> {
    let url = `/users?page=${page}&size=${size}`;
    if (search) url += `&search=${encodeURIComponent(search)}`;
    if (role) url += `&role=${encodeURIComponent(role)}`;
    return api.get(url).then((response) => response.data);
  },

  getUserById(id: string): Promise<User> {
    return api.get(`/users/${id}`).then((response) => response.data);
  },

  getPendingUsers(): Promise<User[]> {
    return api.get('/users/pending').then((response) => response.data);
  },

  getTaxpayers(municipalityId: string, wardNumber?: string): Promise<User[]> {
    let url = `/users/taxpayers?municipalityId=${municipalityId}`;
    if (wardNumber) url += `&wardNumber=${encodeURIComponent(wardNumber)}`;
    return api.get(url).then((response) => response.data);
  },

  // Get all citizens (taxpayers) - for forms that need to select any taxpayer
  getAllCitizens(): Promise<User[]> {
    return api.get('/users?role=Citizen&status=Active&size=1000').then((response) => response.data.users);
  },

  // Location hierarchy services
  getProvinces(): Promise<Province[]> {
    return api.get('/provinces').then((response) => response.data);
  },

  getDistrictsByProvince(provinceId: string): Promise<District[]> {
    return api.get(`/provinces/${provinceId}/districts`).then((response) => response.data);
  },

  getMunicipalitiesByDistrict(districtId: string): Promise<MunicipalityLocation[]> {
    return api.get(`/districts/${districtId}/municipalities`).then((response) => response.data);
  },

  async updateUserStatus(userId: string, status: 'Active' | 'Rejected', rejectionReason?: string): Promise<{ pan?: string; temporaryPassword?: string; message: string }> {
    const response = await api.put<{ pan?: string; temporaryPassword?: string; message: string }>(`/users/${userId}/status`, { status, rejectionReason });
    return response.data;
  },

  createUser(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phoneNumber?: string;
    role: string;
    municipalityId?: string;
  }): Promise<User> {
    return api.post("/users", userData).then((response) => response.data);
  },

  updateUser(
    id: string,
    userData: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phoneNumber?: string;
      role?: string;
      municipalityId?: string;
    }
  ): Promise<User> {
    return api.put(`/users/${id}`, userData).then((response) => response.data);
  },

  deleteUser(id: string): Promise<void> {
    return api.delete(`/users/${id}`).then(() => {});
  },

  toggleUserStatus(id: string): Promise<User> {
    return api.put(`/users/${id}/toggle-status`).then((response) => response.data);
  },

  resetPassword(id: string, newPassword: string): Promise<void> {
    return api.put(`/users/${id}/reset-password`, { newPassword }).then(() => {});
  },

  getUserByRegistration(registration: string): Promise<User | null> {
    return this.getUsers(1, 1, registration).then((response) => {
      const user = response.users.find(u => u.pan === registration);
      return user || null;
    });
  },
};

// Generic HTTP Methods
export const get = <T = unknown>(url: string): Promise<T> => {
  return api.get(url).then((response) => response.data);
};

export const post = <T = unknown>(url: string, data?: object): Promise<T> => {
  return api.post(url, data).then((response) => response.data);
};

export const put = <T = unknown>(url: string, data?: object): Promise<T> => {
  return api.put(url, data).then((response) => response.data);
};

export const del = <T = unknown>(url: string): Promise<T> => {
  return api.delete(url).then((response) => response.data);
};

export default api;
