using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class FinalAssessmentDetail
    {
        [Key]
        public Guid Id { get; set; }
        
        [Required]
        public Guid FinalAssessmentId { get; set; }
        
        [Required]
        public int SerialNumber { get; set; }
        
        [MaxLength(100)]
        public string? FilingPeriod { get; set; }
        
        [MaxLength(100)]
        public string? TaxPeriod { get; set; }
        
        [MaxLength(50)]
        public string? TaxYear { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal AssessedAmount { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal Penalty { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal AdditionalAmount { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal Interest { get; set; }
        
        [Column(TypeName = "decimal(15,2)")]
        public decimal Total { get; set; }
        
        // Navigation property
        [ForeignKey("FinalAssessmentId")]
        public virtual FinalAssessment FinalAssessment { get; set; } = null!;
    }
}