import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../services/api";

const SubmissionStatus: React.FC = () => {
  const [submissionNumber, setSubmissionNumber] = useState("");
  const [status, setStatus] = useState<{
    submissionNumber: string;
    status: string;
    type: string;
    submissionDate: string;
    comments?: string;
    rejectionReason?: string;
  } | null>(null);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!submissionNumber.trim()) {
      setError("Please enter a submission number");
      return;
    }

    setIsLoading(true);
    setError("");
    setStatus(null);

    try {
      const response = await api.get(`/users/submission/${submissionNumber}`);
      setStatus(response.data);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to retrieve submission status. Please verify your submission number."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
      case "approved":
        return "badge-success";
      case "rejected":
        return "badge-error";
      case "pendingapproval":
      default:
        return "badge-warning";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-base-200 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="text-center mb-6">
              <h2 className="card-title text-3xl justify-center">Track Your Submission</h2>
              <p className="text-base-content/70 mt-2">Enter your submission number to check the status</p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              {error && (
                <div className="alert alert-error">
                  <span>{error}</span>
                </div>
              )}

              <div className="form-control">
                <label htmlFor="submissionNumber" className="label">
                  <span className="label-text">Submission Number</span>
                </label>
                <input
                  id="submissionNumber"
                  name="submissionNumber"
                  type="text"
                  required
                  className="input input-bordered"
                  placeholder="Enter your submission number"
                  value={submissionNumber}
                  onChange={e => setSubmissionNumber(e.target.value)}
                />
              </div>

              <div>
                <button type="submit" disabled={isLoading} className="btn btn-primary w-full">
                  {isLoading ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Checking...
                    </>
                  ) : (
                    "Check Status"
                  )}
                </button>
              </div>
            </form>

            {status && (
              <div className="mt-6">
                <div className="divider"></div>
                <div className="card bg-base-200">
                  <div className="card-body">
                    <h3 className="card-title">Submission Details</h3>
                    <div className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                      <div>
                        <dt className="text-sm font-medium text-base-content/70">Submission Number</dt>
                        <dd className="mt-1 text-sm font-medium">{status.submissionNumber}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-base-content/70">Submission Type</dt>
                        <dd className="mt-1 text-sm font-medium">{status.type}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-base-content/70">Submission Date</dt>
                        <dd className="mt-1 text-sm font-medium">{formatDate(status.submissionDate)}</dd>
                      </div>
                      <div>
                        <dt className="text-sm font-medium text-base-content/70">Status</dt>
                        <dd className="mt-1">
                          <div className={`badge ${getStatusBadge(status.status)}`}>{status.status}</div>
                        </dd>
                      </div>
                      {status.comments && (
                        <div className="sm:col-span-2">
                          <dt className="text-sm font-medium text-base-content/70">Comments</dt>
                          <dd className="mt-1 text-sm">{status.comments}</dd>
                        </div>
                      )}
                      {status.rejectionReason && (
                        <div className="sm:col-span-2">
                          <dt className="text-sm font-medium text-base-content/70">Rejection Reason</dt>
                          <dd className="mt-1 text-sm text-error">{status.rejectionReason}</dd>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="text-center mt-6">
              <button type="button" onClick={() => navigate("/login")} className="btn btn-neutral">
                Back to Login
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmissionStatus;
