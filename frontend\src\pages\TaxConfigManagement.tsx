import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import {
  getMunicipalityTaxConfigs,
  finalizeMunicipalityTaxConfig,
} from "../services/taxConfigService";
import { fiscalYearService } from "../services/fiscalYearService";
import CreateTaxConfigForm from "../components/CreateTaxConfigForm";
import type {
  MunicipalityTaxConfig,
  FiscalYear,
} from "../services/taxConfigService";
import { AdminLayout, AdminTabs } from "../components/admin";

const TaxConfigManagement: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [taxConfigs, setTaxConfigs] = useState<MunicipalityTaxConfig[]>([]);
  const [activeFiscalYear, setActiveFiscalYear] = useState<FiscalYear | null>(
    null
  );
  const [fiscalYears, setFiscalYears] = useState<FiscalYear[]>([]);
  const [selectedFiscalYearId, setSelectedFiscalYearId] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [activeTab, setActiveTab] = useState<"overview" | "configurations" | "history">("overview");

  useEffect(() => {
    const fetchData = async () => {
      if (!user?.municipalityId) {
        setError("No municipality assigned to this user");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching data for municipality:", user.municipalityId);

        // Fetch ALL fiscal years first
        try {
          console.log("Fetching ALL fiscal years...");
          const allFiscalYears = await fiscalYearService.getAll();
          console.log("All fiscal years fetched:", allFiscalYears);

          if (allFiscalYears && allFiscalYears.length > 0) {
            // Store all fiscal years in state
            setFiscalYears(allFiscalYears);
            console.log(
              `Loaded ${allFiscalYears.length} fiscal years for dropdown`
            );

            // Find active fiscal year from the list
            const activeFY = allFiscalYears.find((fy) => fy.isActive);
            if (activeFY) {
              console.log("Active fiscal year found in list:", activeFY.name);
              setActiveFiscalYear(activeFY);
              setSelectedFiscalYearId(activeFY.fiscalYearId);
            } else {
              // If no active year, default to first in the list
              console.log(
                "No active fiscal year found, defaulting to first in list"
              );
              setSelectedFiscalYearId(allFiscalYears[0].fiscalYearId);
            }
          } else {
            console.warn("No fiscal years returned from API");
          }
        } catch (err) {
          console.error("Error fetching fiscal years:", err);
          setError("Failed to load fiscal years. Please try again later.");
        }

        // Fetch tax configs
        try {
          console.log(
            "Fetching tax configurations for municipality:",
            user.municipalityId
          );
          const configsData = await getMunicipalityTaxConfigs(
            user.municipalityId.toString()
          );
          console.log("Tax configurations fetched:", configsData);
          setTaxConfigs(configsData || []);
          setError(null);
        } catch (err) {
          console.error("Error fetching tax configurations:", err);
          setError("Failed to load tax configuration data");
        }
      } catch (err) {
        console.error("Error in data fetching:", err);
        setError("Failed to load data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.municipalityId]);

  const handleFiscalYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedFiscalYearId(e.target.value);
    // Reset any error messages when changing fiscal year
    setError(null);
  };

  const handleCreateNewConfig = () => {
    if (!user?.municipalityId || !selectedFiscalYearId) {
      setError("Please select a fiscal year");
      return;
    }

    // Check if config already exists for this fiscal year
    const existingConfig = taxConfigs.find(
      (config) => config.fiscalYearId === selectedFiscalYearId
    );

    if (existingConfig) {
      const fiscalYear = fiscalYears.find(
        (fy) => fy.fiscalYearId === selectedFiscalYearId
      );
      setError(
        `Tax configuration already exists for fiscal year ${
          fiscalYear?.name || selectedFiscalYearId
        }`
      );
      return;
    }

    // Show the create form
    setShowCreateForm(true);
    setError(null);
  };

  const handleConfigCreated = (newConfig: MunicipalityTaxConfig) => {
    // Update the list with the new config
    setTaxConfigs([...taxConfigs, newConfig]);
    setShowCreateForm(false);
    setError(null);
  };

  const handleCancelCreate = () => {
    setShowCreateForm(false);
    setError(null);
  };

  const handleFinalizeConfig = async (configId: string) => {
    if (
      window.confirm(
        "Are you sure you want to finalize this tax configuration? This action cannot be undone."
      )
    ) {
      try {
        const finalizedConfig = await finalizeMunicipalityTaxConfig(configId);

        // Update the list with the finalized config
        setTaxConfigs(
          taxConfigs.map((config) =>
            config.municipalityTaxConfigId === configId
              ? finalizedConfig
              : config
          )
        );

        setError(null);
      } catch (err) {
        console.error("Error finalizing tax configuration:", err);
        setError("Failed to finalize tax configuration");
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center">
        <div className="text-center">
          <span className="loading loading-spinner loading-lg"></span>
          <p className="mt-4 text-base-content/70">Loading tax configuration data...</p>
        </div>
      </div>
    );
  }

  if (!user?.municipalityId) {
    return (
      <AdminLayout title="Tax Configuration Management">
        <div className="alert alert-warning">
          <span>
            You don't have access to manage tax configurations. Only municipal
            officers can access this page.
          </span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Tax Configuration Management">
      {/* Tabs */}
      <AdminTabs
        tabs={[
          { key: "overview", label: "Overview" },
          { key: "configurations", label: "Configurations", count: taxConfigs.length },
          { key: "history", label: "History" }
        ]}
        activeTab={activeTab}
        onTabChange={(tabKey) => setActiveTab(tabKey as "overview" | "configurations" | "history")}
        className="mb-6"
      />

      {error && (
        <div className="alert alert-error mb-6">
          <span>{error}</span>
        </div>
      )}

      {showCreateForm ? (
        <CreateTaxConfigForm
          selectedFiscalYearId={selectedFiscalYearId}
          fiscalYears={fiscalYears}
          onConfigCreated={handleConfigCreated}
          onCancel={handleCancelCreate}
        />
      ) : (
        <div className="space-y-6">
          {/* Tab Content */}
          {activeTab === "overview" && (
            <>
              {/* Fiscal Year Selection */}
              <div className="card bg-base-100 shadow-xl">
                <div className="card-body">
                  <h2 className="card-title">
                    Fiscal Year Selection
                  </h2>

                  {loading ? (
                    <p className="text-base-content/70">Loading fiscal years...</p>
                  ) : fiscalYears.length > 0 ? (
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-grow">
                        <div className="form-control">
                          <label className="label">
                            <span className="label-text">Select Fiscal Year</span>
                          </label>
                          <select
                            id="fiscal-year-select"
                            className="select select-bordered w-full"
                            value={selectedFiscalYearId}
                            onChange={handleFiscalYearChange}
                            data-testid="fiscal-year-dropdown"
                          >
                            <option value="">-- Select a fiscal year --</option>
                            {fiscalYears.length > 0 ? (
                              fiscalYears.map((year) => (
                                <option
                                  key={year.fiscalYearId}
                                  value={year.fiscalYearId}
                                >
                                  {year.name} {year.isActive ? "(Active)" : ""}
                                </option>
                              ))
                            ) : (
                              <option value="" disabled>
                                No fiscal years available
                              </option>
                            )}
                          </select>
                          {fiscalYears.length > 0 && (
                            <label className="label">
                              <span className="label-text-alt">
                                {`${fiscalYears.length} fiscal year${
                                  fiscalYears.length !== 1 ? "s" : ""
                                } available`}
                              </span>
                            </label>
                          )}
                          <label className="label">
                            <span className="label-text-alt">
                              {activeFiscalYear
                                ? `Active fiscal year: ${activeFiscalYear.name}`
                                : "No active fiscal year set"}
                            </span>
                          </label>
                        </div>
                      </div>

                      <div className="self-end">
                        <button
                          className="btn btn-primary"
                          onClick={handleCreateNewConfig}
                          disabled={!selectedFiscalYearId}
                          data-testid="create-config-button"
                        >
                          Create New Configuration
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-warning">No fiscal years available</p>
                  )}

                  {activeFiscalYear && (
                    <div className="alert alert-info mt-4">
                      <div>
                        <h3 className="font-medium mb-2">
                          Current Active Fiscal Year
                        </h3>
                        <p>
                          <strong>Name:</strong> {activeFiscalYear.name}
                        </p>
                        <p>
                          <strong>Period:</strong>{" "}
                          {new Date(activeFiscalYear.startDate).toLocaleDateString()} to{" "}
                          {new Date(activeFiscalYear.endDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Summary Stats */}
              <div className="card bg-base-100 shadow-xl">
                <div className="card-body">
                  <h2 className="card-title">Summary</h2>
                  <div className="stats stats-vertical lg:stats-horizontal shadow">
                    <div className="stat">
                      <div className="stat-title">Total Configurations</div>
                      <div className="stat-value">{taxConfigs.length}</div>
                      <div className="stat-desc">All fiscal years</div>
                    </div>
                    <div className="stat">
                      <div className="stat-title">Finalized</div>
                      <div className="stat-value">
                        {taxConfigs.filter(config => config.isFinalized).length}
                      </div>
                      <div className="stat-desc">Ready for use</div>
                    </div>
                    <div className="stat">
                      <div className="stat-title">Draft</div>
                      <div className="stat-value">
                        {taxConfigs.filter(config => !config.isFinalized).length}
                      </div>
                      <div className="stat-desc">In progress</div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {activeTab === "configurations" && (
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="card-title">Tax Configurations</h2>
                  {selectedFiscalYearId &&
                    !taxConfigs.some(
                      (config) => config.fiscalYearId === selectedFiscalYearId
                    ) && (
                      <button
                        onClick={handleCreateNewConfig}
                        className="btn btn-primary"
                      >
                        Create New Configuration
                      </button>
                    )}
                </div>

                {/* Filter tax configs to show only those for the selected fiscal year if one is selected */}
                {taxConfigs.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="table table-zebra">
                      <thead>
                        <tr>
                          <th>Fiscal Year</th>
                          <th>Tax Slabs</th>
                          <th>Penalties</th>
                          <th>Status</th>
                          <th>Valuation Rules</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {taxConfigs
                          .filter(
                            (config) =>
                              !selectedFiscalYearId ||
                              config.fiscalYearId === selectedFiscalYearId
                          )
                          .map((config) => (
                            <tr key={config.municipalityTaxConfigId}>
                              <td>{config.fiscalYearName}</td>
                              <td>{config.taxSlabsConfig.length} slabs</td>
                              <td>{config.penaltyRules.length} rules</td>
                              <td>
                                <div className={`badge ${
                                  config.isFinalized
                                    ? "badge-success"
                                    : "badge-warning"
                                }`}>
                                  {config.isFinalized ? "Finalized" : "Draft"}
                                </div>
                              </td>
                              <td>
                                {Object.keys(config.valuationRulesConfig || {}).length}{" "}
                                rules
                              </td>
                              <td>
                                <div className="flex space-x-2">
                                  <button
                                    onClick={() =>
                                      navigate(
                                        `/tax-config/${config.municipalityTaxConfigId}`
                                      )
                                    }
                                    className="btn btn-ghost btn-sm"
                                  >
                                    View/Edit
                                  </button>
                                  {!config.isFinalized && (
                                    <button
                                      onClick={() =>
                                        handleFinalizeConfig(
                                          config.municipalityTaxConfigId
                                        )
                                      }
                                      className="btn btn-success btn-sm"
                                    >
                                      Finalize
                                    </button>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-base-content/70">
                      No tax configurations found for this municipality
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === "history" && (
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Configuration History</h2>
                <div className="overflow-x-auto">
                  <table className="table table-zebra">
                    <thead>
                      <tr>
                        <th>Fiscal Year</th>
                        <th>Created Date</th>
                        <th>Last Modified</th>
                        <th>Status</th>
                        <th>Created By</th>
                      </tr>
                    </thead>
                    <tbody>
                      {taxConfigs.map((config) => (
                        <tr key={config.municipalityTaxConfigId}>
                          <td>{config.fiscalYearName}</td>
                          <td>{new Date(config.createdAt).toLocaleDateString()}</td>
                          <td>{new Date(config.updatedAt || config.createdAt).toLocaleDateString()}</td>
                          <td>
                            <div className={`badge ${
                              config.isFinalized
                                ? "badge-success"
                                : "badge-warning"
                            }`}>
                              {config.isFinalized ? "Finalized" : "Draft"}
                            </div>
                          </td>
                          <td>{config.finalizedBy || "System"}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {taxConfigs.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-base-content/70">
                      No configuration history available
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </AdminLayout>
  );
};

export default TaxConfigManagement;
