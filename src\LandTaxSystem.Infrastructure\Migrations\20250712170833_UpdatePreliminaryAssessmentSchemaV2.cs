﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePreliminaryAssessmentSchemaV2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Category",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "ConstructionCost",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "HouseProperty",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "LandValue",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.AddColumn<string>(
                name: "ActSection",
                table: "PreliminaryAssessments",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AppealNumber",
                table: "PreliminaryAssessments",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AssessmentPeriod",
                table: "PreliminaryAssessments",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Bank",
                table: "PreliminaryAssessments",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Branch",
                table: "PreliminaryAssessments",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Email",
                table: "PreliminaryAssessments",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "InterestCalculationDate",
                table: "PreliminaryAssessments",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MobileNumber",
                table: "PreliminaryAssessments",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OtherReasonDescription",
                table: "PreliminaryAssessments",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "PreliminaryAssessmentDate",
                table: "PreliminaryAssessments",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReasonForAssessment",
                table: "PreliminaryAssessments",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Rule",
                table: "PreliminaryAssessments",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Total",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(15,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Penalty",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(15,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Interest",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(15,2)");

            migrationBuilder.AddColumn<decimal>(
                name: "AdditionalAmount",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "AssessedAmount",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "FilingPeriod",
                table: "PreliminaryAssessmentDetails",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Period",
                table: "PreliminaryAssessmentDetails",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxYear",
                table: "PreliminaryAssessmentDetails",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ActSection",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "AppealNumber",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "AssessmentPeriod",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "Bank",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "Branch",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "Email",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "InterestCalculationDate",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "MobileNumber",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "OtherReasonDescription",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "PreliminaryAssessmentDate",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "ReasonForAssessment",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "Rule",
                table: "PreliminaryAssessments");

            migrationBuilder.DropColumn(
                name: "AdditionalAmount",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "AssessedAmount",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "FilingPeriod",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "Period",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.DropColumn(
                name: "TaxYear",
                table: "PreliminaryAssessmentDetails");

            migrationBuilder.AlterColumn<decimal>(
                name: "Total",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(15,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Penalty",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(15,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Interest",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(15,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)");

            migrationBuilder.AddColumn<string>(
                name: "Category",
                table: "PreliminaryAssessmentDetails",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ConstructionCost",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "HouseProperty",
                table: "PreliminaryAssessmentDetails",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LandValue",
                table: "PreliminaryAssessmentDetails",
                type: "numeric(15,2)",
                nullable: false,
                defaultValue: 0m);
        }
    }
}
