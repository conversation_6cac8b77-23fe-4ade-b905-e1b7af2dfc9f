export interface FinalAssessmentDetailDto {
  serialNumber: number;
  filingPeriod?: string;
  taxPeriod?: string;
  taxYear?: string;
  assessedAmount: number;
  penalty: number;
  additionalAmount: number;
  interest: number;
  total: number;
}

export interface FinalAssessmentDto {
  id: string;
  taxpayerRegistration: string;
  taxpayerName: string;
  address?: string;
  phone?: string;
  accountNumber?: string;
  fiscalYearId: string;
  municipalityId: string;
  preliminaryAssessmentId?: string;
  assessmentPeriodFrom: Date;
  assessmentPeriodTo: Date;
  actSection?: string;
  rule?: string;
  bank?: string;
  branch?: string;
  reasonForAssessment?: string;
  appealNumber?: string;
  otherReasonDescription?: string;
  interestCalculationDate?: Date;
  finalAssessmentDate?: Date;
  reason?: string;
  regulations?: string;
  taxDetails: FinalAssessmentDetailDto[];
  grandTotal: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface FinalAssessmentCreateDto {
    taxpayerRegistration: string;
  fiscalYearId: string;
  municipalityId: string;
  returnFilingId?: string;
  preliminaryAssessmentId?: string;
  taxpayerName: string;
  address?: string;
  phone?: string;
  assessmentPeriodFrom: Date;
  assessmentPeriodTo: Date;
  sectionsRules?: string;
  bankName?: string;
  branchName?: string;
  reasonCode?: string;
  appealNumber?: string;
  reasonDescription?: string;
  interestPenaltyCalculationDate?: Date;
  finalAssessmentDate?: Date;
  taxDetails: FinalAssessmentDetailDto[];
}

export interface FinalAssessmentUpdateDto {
  taxpayerRegistration?: string;
  taxpayerName?: string;
  address?: string;
  phone?: string;
  accountNumber?: string;
  fiscalYearId?: string;
  municipalityId?: string;
  preliminaryAssessmentId?: string;
  returnFilingId?: string;
  assessmentPeriodFrom?: Date;
  assessmentPeriodTo?: Date;
  actSection?: string;
  rule?: string;
  bank?: string;
  branch?: string;
  reasonForAssessment?: string;
  appealNumber?: string;
  otherReasonDescription?: string;
  interestCalculationDate?: Date;
  finalAssessmentDate?: Date;
  reason?: string;
  regulations?: string;
  taxDetails?: FinalAssessmentDetailDto[];
}

export interface FinalAssessmentListResponse {
  data: FinalAssessmentDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface FinalAssessmentSearchParams {
  pageNumber?: number;
  pageSize?: number;
  taxpayerRegistration?: string;
  taxpayerName?: string;
  fiscalYearId?: string;
  municipalityId?: string;
  preliminaryAssessmentId?: string;
  fromDate?: Date;
  toDate?: Date;
}