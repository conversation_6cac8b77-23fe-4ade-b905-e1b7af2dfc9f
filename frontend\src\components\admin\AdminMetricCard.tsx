import React from 'react';
import { Link } from 'react-router-dom';

interface AdminMetricCardProps {
  title: string;
  value?: string | number;
  description?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
  to?: string;
  className?: string;
  onClick?: () => void;
  badge?: {
    text: string;
    color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
  };
}

const AdminMetricCard: React.FC<AdminMetricCardProps> = ({
  title,
  value,
  description,
  icon,
  color = 'primary',
  to,
  className = '',
  onClick,
  badge
}) => {
  // Function to get color classes
  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'primary': return 'bg-primary/10 text-primary';
      case 'secondary': return 'bg-secondary/10 text-secondary';
      case 'accent': return 'bg-accent/10 text-accent';
      case 'info': return 'bg-info/10 text-info';
      case 'success': return 'bg-success/10 text-success';
      case 'warning': return 'bg-warning/10 text-warning';
      case 'error': return 'bg-error/10 text-error';
      default: return 'bg-primary/10 text-primary';
    }
  };

  // Function to get badge color classes
  const getBadgeColorClasses = (color?: string) => {
    switch (color) {
      case 'primary': return 'badge-primary';
      case 'secondary': return 'badge-secondary';
      case 'accent': return 'badge-accent';
      case 'info': return 'badge-info';
      case 'success': return 'badge-success';
      case 'warning': return 'badge-warning';
      case 'error': return 'badge-error';
      default: return 'badge-primary';
    }
  };

  // Wrapper component based on whether it's a link or clickable
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    if (to) {
      return (
        <Link to={to} className={`card bg-base-100 shadow-xl hover:bg-base-200 transition-colors cursor-pointer ${className}`}>
          {children}
        </Link>
      );
    }
    
    if (onClick) {
      return (
        <div 
          onClick={onClick} 
          className={`card bg-base-100 shadow-xl hover:bg-base-200 transition-colors cursor-pointer ${className}`}
        >
          {children}
        </div>
      );
    }
    
    return (
      <div className={`card bg-base-100 shadow-xl ${className}`}>
        {children}
      </div>
    );
  };

  return (
    <Wrapper>
      <div className="card-body">
        <div className="flex items-center">
          {icon && (
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center mr-4 ${getColorClasses(color)}`}>
              {icon}
            </div>
          )}
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-base-content">{title}</h3>
              {badge && (
                <span className={`badge ${getBadgeColorClasses(badge.color)} badge-sm`}>
                  {badge.text}
                </span>
              )}
            </div>
            {value && (
              <p className={`text-xl font-bold mt-1 ${getColorClasses(color).split(' ')[1]}`}>
                {typeof value === 'number' ? value.toLocaleString() : value}
              </p>
            )}
            {description && (
              <p className="text-sm text-base-content/70 mt-1">
                {description}
              </p>
            )}
          </div>
        </div>
      </div>
    </Wrapper>
  );
};

export default AdminMetricCard;