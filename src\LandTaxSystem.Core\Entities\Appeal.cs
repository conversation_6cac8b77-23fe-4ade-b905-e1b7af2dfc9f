using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.Entities
{
    public class Appeal
    {
        public Guid AppealId { get; set; } = Guid.NewGuid();
        public Guid AssessmentId { get; set; }
        public string TaxPayerId { get; set; } = null!;
        public string Reason { get; set; } = null!;
        public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;
        public string Status { get; set; } = "Pending"; // Pending, Resolved
        public DateTime? ResolvedAt { get; set; }
        
        // Fields from ExtendedAppeal
        public string OfficeCode { get; set; } = null!;
        public string? LocationCode { get; set; }
        public string TaxpayerName { get; set; } = null!;
        public string? TaxpayerAddress { get; set; }
        public DateTime AppealDate { get; set; } = DateTime.UtcNow;
        public string AppealSubject { get; set; } = null!;
        public string? TaxDeterminationOrderNumber { get; set; }
        public string? AppealAuthority { get; set; }
        public string? RegistrationNumber { get; set; }
        public string? OrderNumber { get; set; }
        public DateTime? OrderDate { get; set; }
        public string? AppealDescription { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual Assessment Assessment { get; set; } = null!;
        public virtual ApplicationUser TaxPayer { get; set; } = null!;
        public virtual Negotiation? Negotiation { get; set; } // Will be populated by EF Core
        public virtual ICollection<TaxPeriod> TaxPeriods { get; set; } = new List<TaxPeriod>();
    }

    // Move TaxPeriod class here to keep it with Appeal
    public class TaxPeriod
    {
        public Guid TaxPeriodId { get; set; } = Guid.NewGuid();
        
        public Guid AppealId { get; set; }
        
        // New fields for updated form
        public string FiscalYear { get; set; } = null!;
        
        public string StartDate { get; set; } = null!;
        
        public string EndDate { get; set; } = null!;
        
        // Legacy fields kept for backward compatibility
        public string? Year { get; set; }
        
        public string? Period { get; set; }
        
        public string? TaxPeriodValue { get; set; }
        
        public string? AppealSubject { get; set; }
        
        public decimal AppealAmount { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public virtual Appeal Appeal { get; set; } = null!;
    }
}
