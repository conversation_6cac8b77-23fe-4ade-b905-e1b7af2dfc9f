using AutoMapper;
using LandTaxSystem.Core.DTOs.Rebate;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LandTaxSystem.Infrastructure.Services
{
    public class RebateService : IRebateService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;

        public RebateService(ApplicationDbContext context, IMapper mapper)
        {
            _context = context;
            _mapper = mapper;
        }

        public async Task<RebateDto> CreateRebateAsync(CreateRebateDto createRebateDto)
        {
            var rebate = _mapper.Map<Rebate>(createRebateDto);
            _context.Rebates.Add(rebate);
            await _context.SaveChangesAsync();
            return _mapper.Map<RebateDto>(rebate);
        }

        public async Task<RebateDto> GetRebateAsync(int id)
        {
            var rebate = await _context.Rebates
                .Include(r => r.RebateItems)
                .FirstOrDefaultAsync(r => r.Id == id);
            return _mapper.Map<RebateDto>(rebate);
        }

        public async Task<IEnumerable<RebateDto>> GetRebatesAsync()
        {
            var rebates = await _context.Rebates
                .Include(r => r.RebateItems)
                .ToListAsync();
            return _mapper.Map<IEnumerable<RebateDto>>(rebates);
        }
    }
}