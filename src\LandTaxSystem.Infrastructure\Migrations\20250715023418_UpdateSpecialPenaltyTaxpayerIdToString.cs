﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSpecialPenaltyTaxpayerIdToString : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SpecialPenalties",
                columns: table => new
                {
                    SpecialPenaltyId = table.Column<Guid>(type: "uuid", nullable: false),
                    SpecialPenaltyNo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TaxpayerId = table.Column<string>(type: "text", nullable: false),
                    AccountType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TaxYear = table.Column<int>(type: "integer", nullable: false),
                    Reason = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    OtherReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ReasonDetails = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Active"),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    CreatedById = table.Column<string>(type: "text", nullable: true),
                    UpdatedById = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SpecialPenalties", x => x.SpecialPenaltyId);
                    table.ForeignKey(
                        name: "FK_SpecialPenalties_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SpecialPenalties_AspNetUsers_TaxpayerId",
                        column: x => x.TaxpayerId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SpecialPenalties_AspNetUsers_UpdatedById",
                        column: x => x.UpdatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_CreatedAt",
                table: "SpecialPenalties",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_CreatedById",
                table: "SpecialPenalties",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_SpecialPenaltyNo",
                table: "SpecialPenalties",
                column: "SpecialPenaltyNo",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_Status",
                table: "SpecialPenalties",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_TaxpayerId",
                table: "SpecialPenalties",
                column: "TaxpayerId");

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_TaxYear",
                table: "SpecialPenalties",
                column: "TaxYear");

            migrationBuilder.CreateIndex(
                name: "IX_SpecialPenalties_UpdatedById",
                table: "SpecialPenalties",
                column: "UpdatedById");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SpecialPenalties");
        }
    }
}
