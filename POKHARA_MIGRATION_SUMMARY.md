# Pokhara Migration Summary

This document summarizes the changes made to update the default municipality and user locations from Kathmandu to Pokhara Metropolitan City.

## Changes Made

### 1. Updated DbSeeder.cs
**File:** `src/LandTaxSystem.Infrastructure/Data/DbSeeder.cs`

- **AssignMunicipalityToOfficer method**: Changed default municipality assignment from Kathmandu Metropolitan City to Pokhara Metropolitan City
- **Officer user**: Now assigned to Pokhara with address "Ward 1, Pokhara Metropolitan City, Lakeside"
- **Citizen user**: Now assigned to Pokhara with address "Ward 2, Pokhara Metropolitan City, Bagar"

### 2. Updated SeedSampleData.cs
**File:** `src/LandTaxSystem.API/SeedSampleData.cs`

Updated all three sample properties to use Pokhara-specific locations and coordinates:

#### Property 1 (Residential)
- **Address**: "123 Lakeside Road, Ward 6, Pokhara"
- **Coordinates**: 83.9856°E, 28.2096°N (Lakeside area)
- **Ward**: 6

#### Property 2 (Commercial)
- **Address**: "456 Mahendra Pool, Ward 17, Pokhara"
- **Coordinates**: 83.9870°E, 28.2110°N (Mahendra Pool area)
- **Ward**: 17

#### Property 3 (Industrial)
- **Address**: "789 Prithvi Highway, Ward 25, Pokhara"
- **Coordinates**: 83.9880°E, 28.2120°N (Highway area)
- **Ward**: 25

### 3. SeedCadastralData.cs (Already Pokhara-focused)
**File:** `src/LandTaxSystem.API/SeedCadastralData.cs`

- Already configured to use Pokhara Metropolitan City as default municipality
- Creates sample property owners with Pokhara addresses
- Uses authentic Nepali names and Pokhara-specific ward numbers (1-33)

## Geographic Context

### Pokhara Metropolitan City Details
- **Province**: Gandaki Province
- **District**: Kaski
- **Ward Count**: 33 wards
- **Coordinates**: Approximately 28.2096°N, 83.9856°E
- **Notable Areas**: Lakeside, Mahendra Pool, Bagar, Prithvi Highway

### Coordinate System
- **SRID**: 4326 (WGS84) for sample data
- **SRID**: 32644 (UTM Zone 44N) for cadastral data

## Benefits of This Migration

1. **Consistency**: All seeding data now uses Pokhara as the default municipality
2. **Realistic Testing**: Pokhara is a major tourist and commercial hub, making it ideal for testing
3. **Geographic Accuracy**: Uses actual Pokhara coordinates and landmark names
4. **Cultural Authenticity**: Sample users have authentic Nepali names and local addresses

## Database Impact

- Default officer and citizen users will be associated with Pokhara Metropolitan City
- Sample properties will have Pokhara-specific addresses and coordinates
- Cadastral data from GeoJSON will continue to use Pokhara as the default municipality
- All new property registrations will default to Pokhara context

## Testing Recommendations

1. **Fresh Database**: For best results, start with a fresh database to see all changes
2. **Coordinate Verification**: Verify that the new coordinates display correctly on maps
3. **Ward Validation**: Ensure ward numbers (1-33) are within Pokhara's valid range
4. **Address Formatting**: Check that Pokhara addresses display properly in the UI

## Files Modified

1. `/src/LandTaxSystem.Infrastructure/Data/DbSeeder.cs`
2. `/src/LandTaxSystem.API/SeedSampleData.cs`

## Files Already Pokhara-Ready

1. `/src/LandTaxSystem.API/SeedCadastralData.cs`
2. `/src/LandTaxSystem.API/SeedCadastralDataConsole.cs`

All changes maintain backward compatibility while providing a more realistic and consistent testing environment focused on Pokhara Metropolitan City.