FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj files and restore as distinct layers
COPY ["src/LandTaxSystem.API/LandTaxSystem.API.csproj", "LandTaxSystem.API/"]
COPY ["src/LandTaxSystem.Core/LandTaxSystem.Core.csproj", "LandTaxSystem.Core/"]
COPY ["src/LandTaxSystem.Infrastructure/LandTaxSystem.Infrastructure.csproj", "LandTaxSystem.Infrastructure/"]

RUN dotnet restore "LandTaxSystem.API/LandTaxSystem.API.csproj"

# Copy everything else and build
COPY ./src/ .

WORKDIR "/src/LandTaxSystem.API"
RUN dotnet build "LandTaxSystem.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "LandTaxSystem.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "LandTaxSystem.API.dll"]
