import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { createMunicipalityTaxConfig } from "../services/taxConfigService";
import type {
  TaxSlab,
  PenaltyRule,
  FiscalYear,
  MunicipalityTaxConfig,
  ValuationRulesConfig,
} from "../services/taxConfigService";
import KeyValueTable, { type KeyValueEntry } from "./KeyValueTable";

interface CreateTaxConfigFormProps {
  selectedFiscalYearId: string;
  fiscalYears: FiscalYear[];
  onConfigCreated: (newConfig: MunicipalityTaxConfig) => void;
  onCancel: () => void;
}

const CreateTaxConfigForm: React.FC<CreateTaxConfigFormProps> = ({
  selectedFiscalYearId,
  fiscalYears,
  onConfigCreated,
  onCancel,
}) => {
  const { user } = useAuth();
  const [taxSlabs, setTaxSlabs] = useState<TaxSlab[]>([
    { minValue: 0, maxValue: 100000, rate: 0.01, description: "Basic rate" },
  ]);
  const [penaltyRules, setPenaltyRules] = useState<PenaltyRule[]>([
    {
      daysAfterDue: 30,
      rate: 0.05,
      maxPenalty: 0.2,
      description: "Late payment penalty",
    },
  ]);

  // Valuation Rules state
  const [landMVRRates, setLandMVRRates] = useState<KeyValueEntry[]>([
    { id: "land-1", key: "Residential", value: 500 },
    { id: "land-2", key: "Commercial", value: 800 },
    { id: "land-3", key: "Industrial", value: 600 },
  ]);
  const [buildingBaseRates, setBuildingBaseRates] = useState<KeyValueEntry[]>([
    { id: "building-1", key: "Residential", value: 300 },
    { id: "building-2", key: "Commercial", value: 500 },
    { id: "building-3", key: "Industrial", value: 400 },
  ]);
  const [annualDepreciationRate, setAnnualDepreciationRate] =
    useState<number>(0.01);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiscalYear, setSelectedFiscalYear] =
    useState<FiscalYear | null>(null);

  useEffect(() => {
    // Find the selected fiscal year object
    const fiscalYear = fiscalYears.find(
      (fy) => fy.fiscalYearId === selectedFiscalYearId
    );
    setSelectedFiscalYear(fiscalYear || null);
  }, [selectedFiscalYearId, fiscalYears]);

  // Handle adding a new tax slab
  const handleAddTaxSlab = () => {
    const lastSlab = taxSlabs[taxSlabs.length - 1];
    const newMaxValue = lastSlab ? lastSlab.maxValue + 100000 : 100000;

    const newSlab: TaxSlab = {
      minValue: lastSlab ? lastSlab.maxValue + 1 : 0,
      maxValue: newMaxValue,
      rate: 0.01,
      description: `Tax slab ${taxSlabs.length + 1}`,
    };
    setTaxSlabs([...taxSlabs, newSlab]);
  };

  // Handle adding a new penalty rule
  const handleAddPenaltyRule = () => {
    const newRule: PenaltyRule = {
      daysAfterDue: 30,
      rate: 0.05,
      maxPenalty: 0.2,
      description: `Penalty rule ${penaltyRules.length + 1}`,
    };
    setPenaltyRules([...penaltyRules, newRule]);
  };
  // Handle updating a tax slab
  const handleTaxSlabChange = (
    index: number,
    field: keyof TaxSlab,
    value: string
  ) => {
    const updatedSlabs = [...taxSlabs];
    updatedSlabs[index] = {
      ...updatedSlabs[index],
      [field]: field === "description" ? value : parseFloat(value),
    };
    setTaxSlabs(updatedSlabs);
  };

  // Handle updating a penalty rule
  const handlePenaltyRuleChange = (
    index: number,
    field: keyof PenaltyRule,
    value: string
  ) => {
    const updatedRules = [...penaltyRules];
    updatedRules[index] = {
      ...updatedRules[index],
      [field]: field === "description" ? value : parseFloat(value),
    };
    setPenaltyRules(updatedRules);
  };

  // Handle removing a tax slab
  const handleRemoveTaxSlab = (index: number) => {
    const updatedSlabs = [...taxSlabs];
    updatedSlabs.splice(index, 1);
    setTaxSlabs(updatedSlabs);
  };

  // Handle removing a penalty rule
  const handleRemovePenaltyRule = (index: number) => {
    const updatedRules = [...penaltyRules];
    updatedRules.splice(index, 1);
    setPenaltyRules(updatedRules);
  };
  // Create the tax configuration
  const handleCreateConfig = async () => {
    if (!user?.municipalityId || !selectedFiscalYearId) {
      setError("Missing required information");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Validate tax slabs
      if (taxSlabs.length === 0) {
        setError("At least one tax slab is required");
        setIsSubmitting(false);
        return;
      }

      // Validate penalty rules
      if (penaltyRules.length === 0) {
        setError("At least one penalty rule is required");
        setIsSubmitting(false);
        return;
      }
      // Validate valuation rules
      if (
        landMVRRates.some(
          (entry) => !entry.key || entry.value === "" || Number(entry.value) < 0
        )
      ) {
        setError(
          "All Land MVR rates must have valid property types and positive values"
        );
        setIsSubmitting(false);
        return;
      }

      if (
        buildingBaseRates.some(
          (entry) => !entry.key || entry.value === "" || Number(entry.value) < 0
        )
      ) {
        setError(
          "All Building Base rates must have valid property types and positive values"
        );
        setIsSubmitting(false);
        return;
      }

      if (annualDepreciationRate < 0 || annualDepreciationRate > 1) {
        setError("Annual Depreciation Rate must be between 0 and 1");
        setIsSubmitting(false);
        return;
      }

      // Check for overlapping tax slabs
      for (let i = 0; i < taxSlabs.length; i++) {
        for (let j = i + 1; j < taxSlabs.length; j++) {
          const slabA = taxSlabs[i];
          const slabB = taxSlabs[j];

          if (
            (slabA.minValue <= slabB.minValue &&
              slabA.maxValue >= slabB.minValue) ||
            (slabA.minValue <= slabB.maxValue &&
              slabA.maxValue >= slabB.maxValue) ||
            (slabA.minValue >= slabB.minValue &&
              slabA.maxValue <= slabB.maxValue)
          ) {
            setError(`Tax slabs ${i + 1} and ${j + 1} have overlapping ranges`);
            setIsSubmitting(false);
            return;
          }
        }
      }

      // Prepare valuation rules config
      const landMVRRatesDict: Record<string, number> = {};
      landMVRRates.forEach((entry) => {
        if (entry.key && entry.value !== "") {
          landMVRRatesDict[entry.key] = Number(entry.value);
        }
      });

      const buildingBaseRatesDict: Record<string, number> = {};
      buildingBaseRates.forEach((entry) => {
        if (entry.key && entry.value !== "") {
          buildingBaseRatesDict[entry.key] = Number(entry.value);
        }
      });
      const valuationRulesConfig: ValuationRulesConfig = {
        landMVR: landMVRRatesDict,
        buildingBaseRatePerSqm: buildingBaseRatesDict,
        annualDepreciationRate: annualDepreciationRate,
      };

      // Create the tax configuration
      const newConfig = await createMunicipalityTaxConfig({
        municipalityId: user.municipalityId,
        fiscalYearId: selectedFiscalYearId,
        taxSlabsConfig: taxSlabs,
        penaltyRules: penaltyRules,
        valuationRules: valuationRulesConfig as unknown as Record<string, string>,
      });

      onConfigCreated(newConfig);
      setIsSubmitting(false);
    } catch (err) {
      console.error("Error creating tax configuration:", err);
      setError("Failed to create tax configuration");
      setIsSubmitting(false);
    }
  };

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-xl">
          Create Tax Configuration for {user?.municipalityName || "N/A"} -{" "}
          {selectedFiscalYear?.name || "Fiscal Year"}
        </h2>

        {error && (
          <div className="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>{error}</span>
          </div>
        )}

      {/* Tax Slabs Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Tax Slabs</h3>
          <button
            type="button"
            onClick={handleAddTaxSlab}
            className="btn btn-primary btn-sm"
            disabled={isSubmitting}
          >
            Add Tax Slab
          </button>
        </div>

        {taxSlabs.length === 0 ? (
          <div className="alert alert-info">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span>No tax slabs defined</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="table table-zebra">
              <thead>
                <tr>
                  <th>Min Value</th>
                  <th>Max Value</th>
                  <th>Rate (%)</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {taxSlabs.map((slab, index) => (
                  <tr key={index}>
                    <td>
                      <input
                        type="number"
                        min="0"
                        value={slab.minValue}
                        onChange={(e) =>
                          handleTaxSlabChange(index, "minValue", e.target.value)
                        }
                        className="input input-bordered input-sm w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        min="0"
                        value={slab.maxValue}
                        onChange={(e) =>
                          handleTaxSlabChange(index, "maxValue", e.target.value)
                        }
                        className="input input-bordered input-sm w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={slab.rate}
                        onChange={(e) =>
                          handleTaxSlabChange(index, "rate", e.target.value)
                        }
                        className="input input-bordered input-sm w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td>
                      <input
                        type="text"
                        value={slab.description}
                        onChange={(e) =>
                          handleTaxSlabChange(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        className="input input-bordered input-sm w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td>
                      <button
                        type="button"
                        onClick={() => handleRemoveTaxSlab(index)}
                        className="btn btn-error btn-sm btn-circle"
                        disabled={isSubmitting || taxSlabs.length <= 1}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Penalty Rules Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium">Penalty Rules</h3>
          <button
            type="button"
            onClick={handleAddPenaltyRule}
            className="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm"
            disabled={isSubmitting}
          >
            Add Penalty Rule
          </button>
        </div>
        {penaltyRules.length === 0 ? (
          <p className="text-gray-500 italic">No penalty rules defined</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Days After Due
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Rate (%)
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Max Penalty (%)
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Description
                  </th>
                  <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {penaltyRules.map((rule, index) => (
                  <tr key={index}>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        value={rule.daysAfterDue}
                        onChange={(e) =>
                          handlePenaltyRuleChange(
                            index,
                            "daysAfterDue",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={rule.rate}
                        onChange={(e) =>
                          handlePenaltyRuleChange(index, "rate", e.target.value)
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={rule.maxPenalty}
                        onChange={(e) =>
                          handlePenaltyRuleChange(
                            index,
                            "maxPenalty",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <input
                        type="text"
                        value={rule.description}
                        onChange={(e) =>
                          handlePenaltyRuleChange(
                            index,
                            "description",
                            e.target.value
                          )
                        }
                        className="border rounded px-2 py-1 w-full"
                        disabled={isSubmitting}
                      />
                    </td>
                    <td className="py-2 px-4 border-b border-gray-200">
                      <button
                        type="button"
                        onClick={() => handleRemovePenaltyRule(index)}
                        className="text-red-600 hover:text-red-800"
                        disabled={isSubmitting || penaltyRules.length <= 1}
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}{" "}
      </div>

      {/* Valuation Rules Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-4">Valuation Rules</h3>

        {/* Land MVR Rates */}
        <div className="mb-4">
          <h4 className="text-md font-medium mb-2">
            Land MVR Rates (per sq ft)
          </h4>
          <KeyValueTable
            items={landMVRRates}
            onChange={setLandMVRRates}
            keyLabel="Property Type"
            valueLabel="Rate (per sq ft)"
            valueType="number"
            readOnly={isSubmitting}
            addButtonLabel="Add Property Type"
            removeButtonLabel="Remove"
          />
        </div>

        {/* Building Base Rates */}
        <div className="mb-4">
          <h4 className="text-md font-medium mb-2">
            Building Base Rates (per sq ft)
          </h4>
          <KeyValueTable
            items={buildingBaseRates}
            onChange={setBuildingBaseRates}
            keyLabel="Property Type"
            valueLabel="Rate (per sq ft)"
            valueType="number"
            readOnly={isSubmitting}
            addButtonLabel="Add Property Type"
            removeButtonLabel="Remove"
          />
        </div>

        {/* Annual Depreciation Rate */}
        <div className="mb-4">
          <label className="form-control w-full">
            <div className="label">
              <span className="label-text">Annual Depreciation Rate (decimal, e.g., 0.01 for 1%)</span>
            </div>
            <input
              type="number"
              min="0"
              max="1"
              step="0.001"
              value={annualDepreciationRate}
              onChange={(e) =>
                setAnnualDepreciationRate(parseFloat(e.target.value) || 0)
              }
              className="input input-bordered w-full"
              disabled={isSubmitting}
              placeholder="Enter depreciation rate (e.g., 0.01)"
            />
            <div className="label">
              <span className="label-text-alt">Enter as decimal (0.01 = 1%, 0.05 = 5%)</span>
            </div>
          </label>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="card-actions justify-end mt-6">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-ghost"
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleCreateConfig}
          className="btn btn-primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? 
            <span className="loading loading-spinner loading-sm"></span> : 
            "Create Configuration"}
        </button>
      </div>
      </div>
    </div>
  );
};

export default CreateTaxConfigForm;
