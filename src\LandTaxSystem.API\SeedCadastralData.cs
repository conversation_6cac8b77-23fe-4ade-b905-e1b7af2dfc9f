using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Collections.Generic;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetTopologySuite.Geometries;
using NetTopologySuite;
using NetTopologySuite.IO;
using Microsoft.AspNetCore.Identity;

namespace LandTaxSystem.API
{
    public class SeedCadastralData
    {
        private static readonly Random _random = new Random(42); // Fixed seed for reproducible results
        private static readonly string[] _usageTypes = { "Residential", "Commercial", "Agricultural", "Industrial" };
        private static readonly string[] _constructionTypes = { "RCC", "BrickMud", "Other" };
        private static readonly string[] _ownershipTypes = { "Individual", "Joint", "Government", "Institutional" };
        private static readonly string[] _landTypes = { "Arable", "Non-Arable", "Residential", "Commercial" };
        private static readonly string[] _streetTypes = { "Paved", "Gravel", "Dirt" };

        public static async Task SeedFromGeoJsonAsync(IServiceProvider serviceProvider, string geoJsonFilePath)
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();

            // Check if cadastral properties already exist
            var existingCadastralProperties = await dbContext.Properties
                .Where(p => p.SheetNo != null && p.SheetNo == "1000")
                .CountAsync();

            if (existingCadastralProperties > 0)
            {
                Console.WriteLine($"Cadastral properties already exist ({existingCadastralProperties} found). Skipping seeding.");
                return;
            }

            // Get required entities
            var municipality = await dbContext.Municipalities
                .FirstOrDefaultAsync(m => m.Name.Contains("Pokhara"));

            if (municipality == null)
            {
                Console.WriteLine("Pokhara Metropolitan City not found. Creating it...");
                municipality = await CreatePokharaMunicipalityAsync(dbContext);
            }

            var fiscalYear = await dbContext.FiscalYears
                .FirstOrDefaultAsync(f => f.IsActive);

            if (fiscalYear == null)
            {
                Console.WriteLine("No active fiscal year found. Cannot seed cadastral data.");
                return;
            }

            // Create sample property owners
            var propertyOwners = await CreateSampleOwnersAsync(userManager, municipality.MunicipalityId);

            Console.WriteLine($"Reading GeoJSON file: {geoJsonFilePath}");

            if (!File.Exists(geoJsonFilePath))
            {
                Console.WriteLine($"GeoJSON file not found: {geoJsonFilePath}");
                return;
            }

            var geoJsonContent = await File.ReadAllTextAsync(geoJsonFilePath);
            var geoJsonDoc = JsonDocument.Parse(geoJsonContent);

            var geometryFactory = NtsGeometryServices.Instance.CreateGeometryFactory(srid: 32644); // UTM Zone 44N
            var geoJsonReader = new GeoJsonReader();

            var properties = new List<Property>();
            var assessments = new List<Assessment>();
            var payments = new List<Payment>();

            Console.WriteLine("Processing GeoJSON features...");

            var features = geoJsonDoc.RootElement.GetProperty("features").EnumerateArray();
            var featureCount = 0;

            foreach (var feature in features)
            {
                featureCount++;
                var props = feature.GetProperty("properties");
                var geometry = feature.GetProperty("geometry");

                // Extract basic property information
                var fid = props.GetProperty("fid").GetInt32();
                var municipality_name = props.GetProperty("municipality").GetString() ?? "Pokhara Metropolitan City";
                var district = props.GetProperty("district").GetString() ?? "Kaski";
                var wardno = props.GetProperty("wardno").GetInt32();
                var parcelno = props.GetProperty("parcelno").GetInt32();
                var sheetid = props.GetProperty("sheetid").GetString() ?? "1000";
                var area = props.GetProperty("area").GetDouble();
                var valuation_microzone = props.GetProperty("valuation_microzone").GetInt32();

                // Handle nullable fields
                var landuse_category = props.TryGetProperty("landuse_category", out var landuseEl) && landuseEl.ValueKind != JsonValueKind.Null
                    ? landuseEl.GetString() : null;
                var slope_category = props.TryGetProperty("slope_category", out var slopeEl) && slopeEl.ValueKind != JsonValueKind.Null
                    ? slopeEl.GetString() : null;
                var shape_category = props.TryGetProperty("shape_category", out var shapeEl) && shapeEl.ValueKind != JsonValueKind.Null
                    ? shapeEl.GetString() : null;
                var frontage_category = props.TryGetProperty("frontage_category", out var frontageEl) && frontageEl.ValueKind != JsonValueKind.Null
                    ? frontageEl.GetString() : null;

                // Parse geometry
                var geometryJson = geometry.GetRawText();
                var parcelGeometry = geoJsonReader.Read<Polygon>(geometryJson);

                // Select random owner
                var owner = propertyOwners[_random.Next(propertyOwners.Count)];

                // Create property with realistic assumptions
                var property = new Property
                {
                    PropertyId = Guid.NewGuid(),
                    MunicipalityId = municipality.MunicipalityId,
                    OwnerUserId = owner.Id,
                    WardNumber = wardno,
                    Street = GenerateStreetName(wardno),
                    ParcelNumber = parcelno.ToString(),
                    SheetNo = sheetid,
                    Address = $"Ward {wardno}, Parcel {parcelno}, {municipality_name}",
                    LandAreaSqM = (decimal)area,
                    UsageType = MapLandUseToUsageType(landuse_category),
                    ParcelGeometry = parcelGeometry,

                    // Generate realistic building details for some properties
                    BuildingBuiltUpAreaSqM = ShouldHaveBuilding() ? (decimal?)(_random.NextDouble() * area * 0.7) : null,
                    BuildingConstructionType = ShouldHaveBuilding() ? _constructionTypes[_random.Next(_constructionTypes.Length)] : null,
                    BuildingConstructionYear = ShouldHaveBuilding() ? _random.Next(1990, 2024) : null,

                    // Nepali land units (approximate conversions)
                    Ropani = ConvertSqMToRopani((decimal)area),
                    Aana = ConvertSqMToAana((decimal)area),

                    // Additional property details
                    LandType = landuse_category ?? _landTypes[_random.Next(_landTypes.Length)],
                    NatureOfLand = slope_category ?? "PL", // Plain if not specified
                    StreetType = _streetTypes[_random.Next(_streetTypes.Length)],
                    RelationWithStreet = frontage_category ?? "AF", // All Frontage if not specified
                    OwnershipType = _ownershipTypes[_random.Next(_ownershipTypes.Length)],

                    // Boundaries (simulated)
                    EastBoundary = GenerateBoundary(),
                    WestBoundary = GenerateBoundary(),
                    NorthBoundary = GenerateBoundary(),
                    SouthBoundary = GenerateBoundary(),

                    // Status and dates
                    Status = "Approved", // Set as approved for seeded data
                    RegistrationDate = DateTime.UtcNow.AddDays(-_random.Next(30, 1095)), // Random date within last 3 years
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,

                    // Tax applicability
                    IsTaxApplicable = _random.NextDouble() > 0.05, // 95% are tax applicable
                    ApplicableFiscalYearId = fiscalYear.FiscalYearId
                };

                properties.Add(property);

                // Create assessment for tax-applicable properties
                if (property.IsTaxApplicable)
                {
                    var baseValue = CalculateBasePropertyValue(property, valuation_microzone);
                    var assessment = new Assessment
                    {
                        AssessmentId = Guid.NewGuid(),
                        PropertyId = property.PropertyId,
                        FiscalYearId = fiscalYear.FiscalYearId,
                        AssessmentYear = fiscalYear.StartDate?.Year ?? DateTime.UtcNow.Year,
                        CalculatedValue = baseValue,
                        FinalAssessedValue = baseValue,
                        TaxAmount = baseValue * 0.015m, // 1.5% tax rate
                        UpperAssessment = baseValue * 1.2m,
                        LowerAssessment = baseValue * 0.8m,
                        ActualAssessment = baseValue,
                        PaymentStatus = "Pending",
                        AssessmentDate = DateTime.UtcNow.AddDays(-_random.Next(1, 180)),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    assessments.Add(assessment);

                    // Create payments for some properties (60% paid, 40% unpaid)
                    if (_random.NextDouble() < 0.6)
                    {
                        var paymentAmount = _random.NextDouble() < 0.8
                            ? assessment.TaxAmount // Full payment
                            : assessment.TaxAmount * (decimal)(_random.NextDouble() * 0.8 + 0.2); // Partial payment

                        var payment = new Payment
                        {
                            PaymentId = Guid.NewGuid(),
                            AssessmentId = assessment.AssessmentId,
                            PropertyId = property.PropertyId,
                            FiscalYearId = fiscalYear.FiscalYearId,
                            AmountPaid = paymentAmount,
                            PaymentDate = DateTime.UtcNow.AddDays(-_random.Next(1, 150)),
                            PaymentGateway = _random.NextDouble() < 0.7 ? "Online" : "Bank",
                            TransactionId = $"TXN{DateTime.UtcNow.Ticks}{_random.Next(1000, 9999)}",
                            Status = "Success",
                            Partial = paymentAmount < assessment.TaxAmount,
                            IsReconciled = true,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        payments.Add(payment);

                        // Update assessment payment status
                        assessment.PaymentStatus = paymentAmount >= assessment.TaxAmount ? "Paid" : "Partial";
                    }
                }

                if (featureCount % 10 == 0)
                {
                    Console.WriteLine($"Processed {featureCount} features...");
                }
            }

            Console.WriteLine($"Saving {properties.Count} properties, {assessments.Count} assessments, and {payments.Count} payments to database...");

            // Save to database in batches
            await dbContext.Properties.AddRangeAsync(properties);
            await dbContext.SaveChangesAsync();

            await dbContext.Assessments.AddRangeAsync(assessments);
            await dbContext.SaveChangesAsync();

            await dbContext.Payments.AddRangeAsync(payments);
            await dbContext.SaveChangesAsync();

            Console.WriteLine($"Successfully seeded {properties.Count} cadastral properties from GeoJSON data!");
            Console.WriteLine($"- {assessments.Count} assessments created");
            Console.WriteLine($"- {payments.Count} payments recorded");
            Console.WriteLine($"- {payments.Count(p => !p.Partial)} properties fully paid");
            Console.WriteLine($"- {payments.Count(p => p.Partial)} properties partially paid");
            Console.WriteLine($"- {assessments.Count - payments.Count} properties unpaid");
        }

        private static async Task<Municipality> CreatePokharaMunicipalityAsync(ApplicationDbContext dbContext)
        {
            // Get or create Kaski district
            var district = await dbContext.Districts.FirstOrDefaultAsync(d => d.Name == "Kaski");
            if (district == null)
            {
                // Create a basic district if it doesn't exist
                district = new District
                {
                    DistrictId = Guid.NewGuid(),
                    Name = "Kaski",
                    Code = "KAS",
                    ProvinceId = Guid.NewGuid(), // This should be properly linked to Gandaki province
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                dbContext.Districts.Add(district);
                await dbContext.SaveChangesAsync();
            }

            var municipality = new Municipality
            {
                MunicipalityId = Guid.NewGuid(),
                Name = "Pokhara Metropolitan City",
                DistrictId = district.DistrictId,
                WardCount = 33,
                ValuationRulesConfigJson = "{}",
                TaxSlabsConfigJson = "[]",
                ExemptionRulesConfigJson = "[]",
                DefaultPenaltyPercent = 10,
                DefaultDiscountPercent = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Municipalities.Add(municipality);
            await dbContext.SaveChangesAsync();

            return municipality;
        }

        private static async Task<List<ApplicationUser>> CreateSampleOwnersAsync(UserManager<ApplicationUser> userManager, Guid municipalityId)
        {
            var owners = new List<ApplicationUser>();
            var nepaliNames = new[]
            {
                "Ram Bahadur Thapa", "Sita Devi Sharma", "Krishna Prasad Poudel", "Maya Kumari Gurung",
                "Bishnu Kumar Shrestha", "Kamala Devi Rai", "Surya Bahadur Magar", "Gita Kumari Tamang",
                "Hari Prasad Adhikari", "Radha Devi Karki", "Mohan Lal Joshi", "Saraswati Devi Bhandari",
                "Tek Bahadur Limbu", "Laxmi Devi Neupane", "Gopal Krishna Acharya", "Parvati Kumari Dahal"
            };

            for (int i = 0; i < nepaliNames.Length; i++)
            {
                var email = $"owner{i + 1}@cadastral.np";
                var existingUser = await userManager.FindByEmailAsync(email);

                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        UserName = email,
                        Email = email,
                        EmailConfirmed = true,
                        FullName = nepaliNames[i],
                        Role = "Citizen",
                        Status = "Active",
                        DateOfBirth = DateTime.UtcNow.AddYears(-_random.Next(25, 70)),
                        Gender = i % 2 == 0 ? "Male" : "Female",
                        Nationality = "Nepali",
                        CitizenshipNumber = $"15-02-77-{(12345 + i):D5}",
                        PermanentAddress = $"Ward {_random.Next(1, 34)}, Pokhara Metropolitan City",
                        WardNumber = _random.Next(1, 34).ToString(),
                        ToleStreet = $"Tole {_random.Next(1, 10)}",
                        MunicipalityId = municipalityId,
                        PAN = $"30{(1000000 + i):D7}",
                        PhoneNumber = $"98{_random.Next(10000000, 99999999)}",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    var result = await userManager.CreateAsync(user, "Password@123");
                    if (result.Succeeded)
                    {
                        owners.Add(user);
                    }
                }
                else
                {
                    owners.Add(existingUser);
                }
            }

            return owners;
        }

        private static string MapLandUseToUsageType(string? landUseCategory)
        {
            return landUseCategory switch
            {
                "RSO" => "Residential", // Residential Single Occupancy
                "RPO" => "Residential", // Residential Planned Occupancy  
                "RFL" => "Residential", // Residential Family Living
                "COM" => "Commercial",
                "IND" => "Industrial",
                "AGR" => "Agricultural",
                _ => "Residential" // Default to residential
            };
        }

        private static bool ShouldHaveBuilding()
        {
            return _random.NextDouble() < 0.75; // 75% of properties have buildings
        }

        private static decimal ConvertSqMToRopani(decimal sqM)
        {
            // 1 Ropani = 508.72 square meters (approximately)
            return Math.Round(sqM / 508.72m, 4);
        }

        private static decimal ConvertSqMToAana(decimal sqM)
        {
            // 1 Aana = 31.795 square meters (approximately)
            return Math.Round(sqM / 31.795m, 4);
        }

        private static string GenerateStreetName(int wardNo)
        {
            var streetNames = new[] { "Main Road", "Lakeside Road", "Prithvi Highway", "Mahendra Pool", "Bagar", "Chipledhunga", "Srijana Chowk" };
            return $"{streetNames[wardNo % streetNames.Length]} - Ward {wardNo}";
        }

        private static string GenerateBoundary()
        {
            var boundaries = new[] { "Road", "River", "Neighbor Property", "Government Land", "Forest", "Agricultural Land" };
            return boundaries[_random.Next(boundaries.Length)];
        }

        private static decimal CalculateBasePropertyValue(Property property, int valuationMicrozone)
        {
            // Base rate per square meter based on microzone (simulated)
            var baseRatePerSqM = valuationMicrozone switch
            {
                12 => 25000m, // High value zone
                11 => 20000m,
                10 => 15000m,
                _ => 10000m   // Default rate
            };

            // Adjust based on usage type
            var usageMultiplier = property.UsageType switch
            {
                "Commercial" => 1.5m,
                "Industrial" => 1.2m,
                "Residential" => 1.0m,
                "Agricultural" => 0.6m,
                _ => 1.0m
            };

            // Adjust based on building
            var buildingValue = 0m;
            if (property.BuildingBuiltUpAreaSqM.HasValue)
            {
                var buildingRatePerSqM = property.BuildingConstructionType switch
                {
                    "RCC" => 35000m,
                    "BrickMud" => 20000m,
                    "Other" => 15000m,
                    _ => 20000m
                };
                buildingValue = property.BuildingBuiltUpAreaSqM.Value * buildingRatePerSqM;
            }

            var landValue = property.LandAreaSqM * baseRatePerSqM * usageMultiplier;
            return Math.Round(landValue + buildingValue, 2);
        }
    }
}