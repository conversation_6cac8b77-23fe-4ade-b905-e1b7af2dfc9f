using AutoMapper;
using LandTaxSystem.Core.DTOs.Rebate;
using LandTaxSystem.Core.Entities;

namespace LandTaxSystem.Core.Mapping
{
    public class RebateMappingProfile : Profile
    {
        public RebateMappingProfile()
        {
            CreateMap<Rebate, RebateDto>().ReverseMap();
            CreateMap<RebateItem, RebateItemDto>().ReverseMap();
            CreateMap<Rebate, CreateRebateDto>().ReverseMap();
        }
    }
}