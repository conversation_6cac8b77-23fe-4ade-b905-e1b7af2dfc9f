using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Appeal;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AppealsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AppealsController> _logger;
        private readonly IEmailService _emailService;

        public AppealsController(ApplicationDbContext context, ILogger<AppealsController> logger, IEmailService emailService)
        {
            _context = context;
            _logger = logger;
            _emailService = emailService;
        }

        // GET: api/appeals
        [HttpGet]
        public async Task<ActionResult<IEnumerable<AppealListItemDto>>> GetAppeals([FromQuery] string? userId = null)
        {
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);

            // If user is a citizen, they can only see their own appeals
            if (userRole == "Citizen")
            {
                userId = currentUserId;
            }

            var query = _context.Appeals
                .Include(a => a.TaxPeriods)
                .AsQueryable();

            if (userId != null)
            {
                query = query.Where(a => a.TaxPayerId == userId);
            }

            var appeals = await query
                .Include(a => a.Assessment)
                    .ThenInclude(a => a.Property)
                .Include(a => a.Negotiation)
                .OrderByDescending(a => a.SubmittedAt)
                .Select(a => new AppealListItemDto
                {
                    AppealId = a.AppealId,
                    AssessmentId = a.AssessmentId,
                    PropertyAddress = a.Assessment.Property.Address,
                    AssessmentAmount = a.Assessment.FinalAssessedValue,
                    Status = a.Status,
                    SubmittedAt = a.SubmittedAt,
                    ResolvedAt = a.ResolvedAt,
                    NegotiatedAmount = a.Negotiation != null ? a.Negotiation.NegotiatedAmount : null
                })
                .ToListAsync();

            return appeals;
        }

        // GET: api/appeals/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<AppealDto>> GetAppeal(Guid id)
        {
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);

            var appeal = await _context.Appeals
                .Include(a => a.TaxPeriods)
                .Include(a => a.Assessment)
                    .ThenInclude(a => a.Property)
                .Include(a => a.Negotiation)
                .FirstOrDefaultAsync(a => a.AppealId == id);

            if (appeal == null)
            {
                return NotFound();
            }

            // Check if user is authorized to view this appeal
            if (userRole == "Citizen" && appeal.TaxPayerId != currentUserId)
            {
                return Forbid();
            }

            var taxPeriods = appeal.TaxPeriods.Select(tp => new TaxPeriodDto
            {
                FiscalYear = tp.FiscalYear,
                StartDate = tp.StartDate,
                EndDate = tp.EndDate,
                Year = tp.Year,
                Period = tp.Period,
                TaxPeriodValue = tp.TaxPeriodValue,
                AppealSubject = tp.AppealSubject,
                AppealAmount = tp.AppealAmount
            }).ToList();

            var appealDto = new AppealDto
            {
                AppealId = appeal.AppealId,
                AssessmentId = appeal.AssessmentId,
                TaxPayerId = appeal.TaxPayerId,
                TaxPayerName = appeal.TaxpayerName ?? "Unknown",
                Reason = appeal.Reason,
                Status = appeal.Status,
                SubmittedAt = appeal.SubmittedAt,
                ResolvedAt = appeal.ResolvedAt,
                AssessmentAmount = appeal.Assessment.FinalAssessedValue,
                PropertyAddress = appeal.Assessment.Property.Address,
                NegotiatedAmount = appeal.Negotiation?.NegotiatedAmount,
                NegotiationDate = appeal.Negotiation?.CreatedAt,
                OfficerName = appeal.Negotiation?.OfficerId != null ? "Officer" : null // Replace with actual officer name if available
            };

            return appealDto;
        }

        // POST: api/appeals
        [HttpPost]
        [Authorize(Roles = "Citizen,Officer")]
        public async Task<ActionResult<AppealDto>> CreateAppeal(CreateAppealDto createAppealDto)
        {
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Verify assessment exists
            var assessment = await _context.Assessments
                .Include(a => a.Property)
                .FirstOrDefaultAsync(a => a.AssessmentId == createAppealDto.AssessmentId);
                
            if (assessment == null)
            {
                return NotFound("Assessment not found");
            }

            var appeal = new Appeal
            {
                AppealId = Guid.NewGuid(),
                AssessmentId = createAppealDto.AssessmentId,
                TaxPayerId = currentUserId,
                Reason = createAppealDto.Reason,
                OfficeCode = createAppealDto.OfficeCode,
                LocationCode = createAppealDto.LocationCode,
                TaxpayerName = createAppealDto.TaxpayerName,
                TaxpayerAddress = createAppealDto.TaxpayerAddress,
                AppealDate = createAppealDto.AppealDate.ToUniversalTime(),
                AppealSubject = createAppealDto.AppealSubject,
                TaxDeterminationOrderNumber = createAppealDto.TaxDeterminationOrderNumber,
                AppealAuthority = createAppealDto.AppealAuthority,
                RegistrationNumber = createAppealDto.RegistrationNumber,
                OrderNumber = createAppealDto.OrderNumber,
                OrderDate = createAppealDto.OrderDate.HasValue ? createAppealDto.OrderDate.Value.ToUniversalTime() : null,
                AppealDescription = createAppealDto.AppealDescription,
                Status = "Pending",
                SubmittedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Add tax periods
            foreach (var periodDto in createAppealDto.TaxPeriods)
            {
                var taxPeriod = new TaxPeriod
                {
                    TaxPeriodId = Guid.NewGuid(),
                    AppealId = appeal.AppealId,
                    FiscalYear = periodDto.FiscalYear,
                    StartDate = periodDto.StartDate,
                    EndDate = periodDto.EndDate,
                    Year = periodDto.Year,
                    Period = periodDto.Period,
                    TaxPeriodValue = periodDto.TaxPeriodValue,
                    AppealSubject = periodDto.AppealSubject,
                    AppealAmount = periodDto.AppealAmount
                };
                
                appeal.TaxPeriods.Add(taxPeriod);
            }

            _context.Appeals.Add(appeal);

            // Create an audit log entry
            var auditLog = new AuditLog
            {
                ActionType = "Create",
                EntityType = "Appeal",
                EntityId = appeal.AppealId,
                UserId = currentUserId,
                Timestamp = DateTime.UtcNow,
                NewValueJson = System.Text.Json.JsonSerializer.Serialize(new
                {
                    appeal.AppealId,
                    appeal.AssessmentId,
                    appeal.OfficeCode,
                    appeal.TaxpayerName,
                    appeal.AppealDate,
                    appeal.AppealSubject,
                    appeal.Status,
                    appeal.SubmittedAt,
                    TaxPeriods = appeal.TaxPeriods.Select(tp => new
                    {
                        tp.Year,
                        tp.TaxPeriodValue,
                        tp.AppealAmount
                    }).ToList()
                })
            };

            _context.AuditLogs.Add(auditLog);

            await _context.SaveChangesAsync();

            // Create response DTO
            var taxPeriodDtos = appeal.TaxPeriods.Select(tp => new TaxPeriodDto
            {
                FiscalYear = tp.FiscalYear,
                StartDate = tp.StartDate,
                EndDate = tp.EndDate,
                Year = tp.Year,
                Period = tp.Period,
                TaxPeriodValue = tp.TaxPeriodValue,
                AppealSubject = tp.AppealSubject,
                AppealAmount = tp.AppealAmount
            }).ToList();

            var appealDto = new AppealDto
            {
                AppealId = appeal.AppealId,
                AssessmentId = appeal.AssessmentId,
                TaxPayerId = appeal.TaxPayerId,
                TaxPayerName = appeal.TaxpayerName ?? "Unknown",
                Reason = appeal.Reason,
                Status = appeal.Status,
                SubmittedAt = appeal.SubmittedAt,
                ResolvedAt = appeal.ResolvedAt,
                AssessmentAmount = assessment.FinalAssessedValue,
                PropertyAddress = assessment.Property.Address,
                NegotiatedAmount = null, // New appeal doesn't have negotiation yet
                NegotiationDate = null,
                OfficerName = null
            };

            return CreatedAtAction(nameof(GetAppeal), new { id = appeal.AppealId }, appealDto);
        }

        // PUT: api/appeals/{id}/resolve
        [HttpPut("{id}/resolve")]
        [Authorize(Roles = "Admin,Officer")]
        public async Task<IActionResult> ResolveAppeal(Guid id)
        {
            var appeal = await _context.Appeals.FindAsync(id);

            if (appeal == null)
            {
                return NotFound();
            }

            appeal.Status = "Resolved";
            appeal.ResolvedAt = DateTime.UtcNow;
            appeal.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return NoContent();
        }
    }
}
