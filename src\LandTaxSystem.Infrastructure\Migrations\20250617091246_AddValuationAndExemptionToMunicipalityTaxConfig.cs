﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddValuationAndExemptionToMunicipalityTaxConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ExemptionRulesConfigJson",
                table: "MunicipalityTaxConfigs",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ValuationRulesConfigJson",
                table: "MunicipalityTaxConfigs",
                type: "text",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExemptionRulesConfigJson",
                table: "MunicipalityTaxConfigs");

            migrationBuilder.DropColumn(
                name: "ValuationRulesConfigJson",
                table: "MunicipalityTaxConfigs");
        }
    }
}
