﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddPropertyFormFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApplicableFiscalYear",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "BuildingConstructionYearAlt",
                table: "Properties",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BuildingNumber",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BuildingType",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DeregistrationReason",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EastBoundary",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExemptionReason",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Facilities",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FloorNumber",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeregistered",
                table: "Properties",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsExempted",
                table: "Properties",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsTaxApplicable",
                table: "Properties",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "LalpurjaNo",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LandOwnership",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LandType",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LandUse",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NatureOfLand",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NorthBoundary",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OtherDetails",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OwnershipDetails",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OwnershipType",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OwnershipValue",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhysicalArea",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RelationWithStreet",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SouthBoundary",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StreetType",
                table: "Properties",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WestBoundary",
                table: "Properties",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApplicableFiscalYear",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "BuildingConstructionYearAlt",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "BuildingNumber",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "BuildingType",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "DeregistrationReason",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "EastBoundary",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "ExemptionReason",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "Facilities",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "FloorNumber",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "IsDeregistered",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "IsExempted",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "IsTaxApplicable",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "LalpurjaNo",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "LandOwnership",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "LandType",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "LandUse",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "NatureOfLand",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "NorthBoundary",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "OtherDetails",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "OwnershipDetails",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "OwnershipType",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "OwnershipValue",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "PhysicalArea",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "RelationWithStreet",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "SouthBoundary",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "StreetType",
                table: "Properties");

            migrationBuilder.DropColumn(
                name: "WestBoundary",
                table: "Properties");
        }
    }
}
