using System;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.TaxConfig
{
    public class FiscalYearCreateDto
    {
        [Required, MaxLength(50)]
        public string Name { get; set; } = string.Empty;
        
        public DateTime? StartDate { get; set; }
        
        public DateTime? EndDate { get; set; }
        
        public bool IsActive { get; set; } = true;
    }

    public class FiscalYearResponseDto
    {
        public Guid FiscalYearId { get; set; }
        public string? Name { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class FiscalYearUpdateDto
    {
        [MaxLength(50)]
        public string? Name { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsActive { get; set; }
    }
}
