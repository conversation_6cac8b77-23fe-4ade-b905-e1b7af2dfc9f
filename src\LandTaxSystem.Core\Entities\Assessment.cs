using System;
using System.Text.Json.Serialization;
using System.Collections.Generic;
using LandTaxSystem.Core.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class Assessment
    {
        public Guid AssessmentId { get; set; }
        public Guid PropertyId { get; set; }
        public int AssessmentYear { get; set; }
        public Guid FiscalYearId { get; set; }
        public decimal? OriginalAmount { get; set; }
        public decimal CalculatedValue { get; set; }
        public decimal? OverriddenValue { get; set; }
        public decimal FinalAssessedValue { get; set; }
        public decimal TaxAmount { get; set; }

        // New assessment bounds and adjustments
        public decimal UpperAssessment { get; set; }
        public decimal LowerAssessment { get; set; }
        public decimal ActualAssessment { get; set; }

        // Percentage-based adjustments (configured by officers)
        public decimal PenaltyPercent { get; set; } = 0; // e.g. 10 means +10%
        public decimal DiscountPercent { get; set; } = 0; // e.g. 5 means −5%

        // Computed field – not mapped to DB, calculated on the fly
        [NotMapped]
        public decimal TotalPayable
        {
            get
            {
                var clamped = Math.Max(LowerAssessment, Math.Min(ActualAssessment, UpperAssessment));
                var penaltyAmount = clamped * PenaltyPercent / 100m;
                var discountAmount = clamped * DiscountPercent / 100m;
                return clamped + penaltyAmount - discountAmount;
            }
        }

        public string? AssessedByOfficerId { get; set; }
        public DateTime AssessmentDate { get; set; } = DateTime.UtcNow;
        public string? OverrideReason { get; set; }

        [JsonIgnore]
        public string ExemptionAppliedDetailsJson { get; set; } = "{}";

        public string PaymentStatus { get; set; } = "Pending"; // Pending, Paid, InReview, Overdue

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public Guid? PreviousAssessmentId { get; set; } // For post-negotiation flows, references the original assessment
        public AssessmentOrigin Origin { get; set; } = AssessmentOrigin.Manual; // Tracks how this assessment was created
        public bool Superseded { get; set; } = false; // True if this assessment has been replaced by a negotiated one

        // Navigation properties
        public virtual Property Property { get; set; } = null!;
        public virtual ApplicationUser? AssessedByOfficer { get; set; }
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual FiscalYear FiscalYear { get; set; } = null!;
        public virtual ICollection<AssessmentLineItem> LineItems { get; set; } = new List<AssessmentLineItem>();
    }
}
