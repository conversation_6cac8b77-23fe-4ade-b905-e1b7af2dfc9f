import React, { useState, useEffect } from 'react';
import * as api from '../services/api';

interface NegotiatedPayment {
  negotiationId: string;
  assessmentId: string;
  propertyAddress: string;
  negotiatedAmount: number;
  dueDate?: string;
}

interface NegotiatedPaymentFormProps {
  negotiationId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const NegotiatedPaymentForm: React.FC<NegotiatedPaymentFormProps> = ({
  negotiationId,
  onSuccess,
  onCancel
}) => {
  const [paymentDetails, setPaymentDetails] = useState<NegotiatedPayment | null>(null);
  const [paymentMethod, setPaymentMethod] = useState('creditCard');
  const [cardNumber, setCardNumber] = useState('');
  const [cardName, setCardName] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchPaymentDetails = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/negotiations/${negotiationId}/payment-details`);
        setPaymentDetails((response as { data: unknown }).data as NegotiatedPayment);
      } catch (err) {
        console.error('Failed to fetch payment details:', err);
        setError('Failed to load payment details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchPaymentDetails();
  }, [negotiationId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!paymentDetails) return;
    
    // Basic validation
    if (paymentMethod === 'creditCard') {
      if (!cardNumber || !cardName || !expiryDate || !cvv) {
        setError('Please fill in all credit card details');
        return;
      }
      
      if (cardNumber.length < 16) {
        setError('Please enter a valid card number');
        return;
      }
      
      if (cvv.length < 3) {
        setError('Please enter a valid CVV');
        return;
      }
    }

    try {
      setSubmitting(true);
      setError('');
      
      await api.post('/payments/negotiated', {
        negotiationId: negotiationId,
        assessmentId: paymentDetails.assessmentId,
        amount: paymentDetails.negotiatedAmount,
        paymentMethod: paymentMethod,
        paymentDetails: paymentMethod === 'creditCard' ? {
          cardNumber: cardNumber.replace(/\s/g, ''),
          cardholderName: cardName,
          expiryDate,
          cvv
        } : {}
      });
      
      onSuccess();
    } catch (err) {
      console.error('Payment failed:', err);
      setError('Payment failed. Please check your details and try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Add space after every 4 digits
    const formatted = digits.replace(/(\d{4})(?=\d)/g, '$1 ');
    
    // Limit to 19 characters (16 digits + 3 spaces)
    return formatted.slice(0, 19);
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardNumber(formatCardNumber(e.target.value));
  };

  if (loading) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="flex justify-center">
            <span className="loading loading-spinner loading-lg"></span>
          </div>
          <p className="text-center">Loading payment details...</p>
        </div>
      </div>
    );
  }

  if (!paymentDetails) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">Payment Details Not Found</h2>
          <div className="alert alert-error">
            <span>{error || 'The requested payment details could not be found.'}</span>
          </div>
          <div className="card-actions justify-end">
            <button
              onClick={onCancel}
              className="btn btn-ghost"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">Pay Negotiated Amount</h2>
        
        {error && (
          <div className="alert alert-error">
            <span>{error}</span>
          </div>
        )}
      
        <div className="mb-6">
          <h3 className="font-semibold mb-2">Payment Summary</h3>
          <div className="bg-base-200 p-3 rounded">
            <p><span className="font-medium">Property Address:</span> {paymentDetails.propertyAddress}</p>
            <p><span className="font-medium">Negotiated Amount:</span> <span className="font-bold">${paymentDetails.negotiatedAmount.toFixed(2)}</span></p>
            {paymentDetails.dueDate && (
              <p><span className="font-medium">Due Date:</span> {new Date(paymentDetails.dueDate).toLocaleDateString()}</p>
            )}
          </div>
        </div>
      
        <form onSubmit={handleSubmit}>
          <div className="form-control mb-4">
            <label className="label">
              <span className="label-text font-bold">Payment Method</span>
            </label>
            <div className="flex space-x-4">
              <label className="label cursor-pointer">
                <input
                  type="radio"
                  className="radio radio-primary"
                  name="paymentMethod"
                  value="creditCard"
                  checked={paymentMethod === 'creditCard'}
                  onChange={() => setPaymentMethod('creditCard')}
                />
                <span className="label-text ml-2">Credit Card</span>
              </label>
              <label className="label cursor-pointer">
                <input
                  type="radio"
                  className="radio radio-primary"
                  name="paymentMethod"
                  value="bankTransfer"
                  checked={paymentMethod === 'bankTransfer'}
                  onChange={() => setPaymentMethod('bankTransfer')}
                />
                <span className="label-text ml-2">Bank Transfer</span>
              </label>
            </div>
          </div>
        
          {paymentMethod === 'creditCard' && (
            <>
              <div className="form-control mb-4">
                <label className="label">
                  <span className="label-text font-bold">Card Number</span>
                </label>
                <input
                  type="text"
                  className="input input-bordered w-full"
                  placeholder="1234 5678 9012 3456"
                  value={cardNumber}
                  onChange={handleCardNumberChange}
                  maxLength={19}
                />
              </div>
              
              <div className="form-control mb-4">
                <label className="label">
                  <span className="label-text font-bold">Cardholder Name</span>
                </label>
                <input
                  type="text"
                  className="input input-bordered w-full"
                  placeholder="John Doe"
                  value={cardName}
                  onChange={(e) => setCardName(e.target.value)}
                />
              </div>
            
              <div className="flex space-x-4">
                <div className="form-control mb-4 flex-1">
                  <label className="label">
                    <span className="label-text font-bold">Expiry Date</span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered w-full"
                    placeholder="MM/YY"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                    maxLength={5}
                  />
                </div>
                
                <div className="form-control mb-4 flex-1">
                  <label className="label">
                    <span className="label-text font-bold">CVV</span>
                  </label>
                  <input
                    type="password"
                    className="input input-bordered w-full"
                    placeholder="123"
                    value={cvv}
                    onChange={(e) => setCvv(e.target.value)}
                    maxLength={4}
                  />
                </div>
              </div>
          </>
        )}
        
          {paymentMethod === 'bankTransfer' && (
            <div className="alert alert-info mb-4">
              <div>
                <h4 className="font-semibold mb-2">Bank Transfer Instructions</h4>
                <p className="mb-2">Please transfer the exact amount to the following account:</p>
                <p><span className="font-medium">Bank Name:</span> National Bank</p>
                <p><span className="font-medium">Account Name:</span> Land Tax Authority</p>
                <p><span className="font-medium">Account Number:</span> **********</p>
                <p><span className="font-medium">Reference:</span> LAND-{paymentDetails.assessmentId.substring(0, 8)}</p>
                <p className="mt-2 text-sm opacity-70">
                  Note: After making the transfer, please upload the receipt below.
                </p>
                <div className="form-control mt-4">
                  <label className="label">
                    <span className="label-text font-bold">Upload Transfer Receipt</span>
                  </label>
                  <input
                    type="file"
                    className="file-input file-input-bordered w-full"
                  />
                </div>
              </div>
            </div>
          )}
        
          <div className="card-actions justify-between mt-6">
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-ghost"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="btn btn-primary"
            >
              {submitting ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Processing...
                </>
              ) : (
                `Pay $${paymentDetails.negotiatedAmount.toFixed(2)}`
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NegotiatedPaymentForm;
