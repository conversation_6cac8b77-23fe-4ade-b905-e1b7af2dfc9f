using System;
using System.Collections.Generic;
using NetTopologySuite.Geometries;

namespace LandTaxSystem.Core.Entities
{
    public class Property
    {
        public Guid PropertyId { get; set; }
        public string OwnerUserId { get; set; } = string.Empty;
        public Guid MunicipalityId { get; set; }

        // Location hierarchy fields
        public int WardNumber { get; set; }
        public string Street { get; set; } = string.Empty; // Street/Tole
        public string ParcelNumber { get; set; } = string.Empty; // Kitta No

        // GIS data
        public Polygon ParcelGeometry { get; set; } = null!;

        // Nepali land measurement units
        public decimal? Ropani { get; set; }
        public decimal? Aana { get; set; }
        public decimal? Paisa { get; set; }
        public decimal? Daam { get; set; }
        public decimal? Bigha { get; set; }
        public decimal? Kattha { get; set; }
        public decimal? Dhur { get; set; }

        public string Address { get; set; } = string.Empty;
        public decimal LandAreaSqM { get; set; }
        public string UsageType { get; set; } = string.Empty; // Residential, Commercial, Industrial, Agricultural

        // Building details
        public decimal? BuildingBuiltUpAreaSqM { get; set; }
        public string? BuildingConstructionType { get; set; } // RCC, BrickMud, Other
        public int? BuildingConstructionYear { get; set; }

        // Document paths
        public string? OwnershipCertificatePath { get; set; }
        public string? BuildingPermitPath { get; set; }

        public DateTime RegistrationDate { get; set; } = DateTime.UtcNow;
        public string Status { get; set; } = "PendingReview"; // PendingReview, Approved, Rejected, Assessed
        public bool IsDefaulter { get; set; } = false;

        // Document reference numbers
        public string? MolNo { get; set; } // MOL (Ministry of Land) number
        public string? LalpurjaNo { get; set; } // Lalpurja number
        public string? SheetNo { get; set; } // Sheet number from land records

        // Valuation Purpose fields
        public string? LandType { get; set; }
        public string? NatureOfLand { get; set; }
        public string? LandUse { get; set; }
        public string? LandOwnership { get; set; }
        public string? StreetType { get; set; }
        public string? RelationWithStreet { get; set; }
        public string? PhysicalArea { get; set; }
        public bool IsExempted { get; set; } = false;
        public string? ExemptionReason { get; set; }

        // Building on Land fields
        public string? BuildingNumber { get; set; }
        public string? FloorNumber { get; set; }
        public int? BuildingConstructionYearAlt { get; set; } // Alternative field for building construction year
        public string? BuildingType { get; set; }

        // Facilities in Land (stored as JSON string)
        public string? Facilities { get; set; }

        // Directions (4 killa)
        public string? EastBoundary { get; set; }
        public string? WestBoundary { get; set; }
        public string? SouthBoundary { get; set; }
        public string? NorthBoundary { get; set; }

        // Land Ownership
        public string? OwnershipType { get; set; }
        public string? OwnershipDetails { get; set; }
        public string? OwnershipValue { get; set; }

        // Others Section
        public bool IsTaxApplicable { get; set; } = true;
        public Guid? ApplicableFiscalYearId { get; set; }
        public string? OtherDetails { get; set; }
        public bool IsDeregistered { get; set; } = false;
        public string? DeregistrationReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public virtual ApplicationUser Owner { get; set; } = null!;
        public virtual Municipality Municipality { get; set; } = null!;
        public virtual FiscalYear? ApplicableFiscalYear { get; set; }
        public virtual ICollection<Assessment> Assessments { get; set; } = new List<Assessment>();
        public virtual ICollection<LandDetail> LandDetails { get; set; } = new List<LandDetail>();

        // Computed properties (not mapped to database)
        /// <summary>
        /// Gets the estimated tax amount for this property for the current fiscal year.
        /// This is a computed property that calculates tax dynamically using TaxCalculationService.
        /// Returns 0 if the property is not approved or if calculation fails.
        /// </summary>
        public decimal EstimatedTaxAmount 
        { 
            get 
            {
                // TODO: Implement actual tax calculation when TaxCalculationService is available (Task 11)
                // For now, return 0 as placeholder
                // This should call TaxCalculationService.CalculateTax(this, currentFiscalYearId)
                return 0m;
            } 
        }
    }
}
