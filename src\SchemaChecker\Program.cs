using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Infrastructure.Data;

var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
optionsBuilder.UseNpgsql("Host=localhost;Database=land_tax_system;Username=********;Password=********");

using var context = new ApplicationDbContext(optionsBuilder.Options);

// Check if Properties table exists
var tableExists = await context.Database.CanConnectAsync();
Console.WriteLine($"Database connection successful: {tableExists}");

// Get column information for Properties table
var connection = context.Database.GetDbConnection();
await connection.OpenAsync();

using var command = connection.CreateCommand();
command.CommandText = @"
    SELECT column_name, data_type, is_nullable, column_default 
    FROM information_schema.columns 
    WHERE table_name = 'Properties'";

using var reader = await command.ExecuteReaderAsync();
Console.WriteLine("\nProperties table columns:");
Console.WriteLine("Column Name\tData Type\tNullable\tDefault Value");
Console.WriteLine("------------------------------------------------");

while (await reader.ReadAsync())
{
    Console.WriteLine($"{reader["column_name"],-15} {reader["data_type"],-15} {reader["is_nullable"],-8} {reader["column_default"]}");
}

// Check if the migration was applied
var migrations = await context.Database.GetAppliedMigrationsAsync();
Console.WriteLine("\nApplied migrations:");
foreach (var migration in migrations)
{
    Console.WriteLine(migration);
}
