using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.FinalAssessment;

namespace LandTaxSystem.Core.Interfaces
{
    public interface IFinalAssessmentService
    {
        Task<FinalAssessmentResponseDto> CreateAsync(FinalAssessmentCreateDto dto, string userId);
        Task<FinalAssessmentResponseDto> UpdateAsync(Guid id, FinalAssessmentUpdateDto dto, string userId);
        Task<FinalAssessmentResponseDto?> GetByIdAsync(Guid id);
        Task<IEnumerable<FinalAssessmentListDto>> GetAllAsync(int page = 1, int pageSize = 10);
        Task<IEnumerable<FinalAssessmentListDto>> GetByTaxpayerRegistrationAsync(string taxpayerRegistration);
        Task<IEnumerable<FinalAssessmentListDto>> GetByMunicipalityAsync(Guid municipalityId);
        Task<IEnumerable<FinalAssessmentListDto>> GetByFiscalYearAsync(Guid fiscalYearId);
        Task<IEnumerable<FinalAssessmentListDto>> GetByPreliminaryAssessmentAsync(Guid preliminaryAssessmentId);
        Task<bool> DeleteAsync(Guid id);
        Task<bool> ExistsAsync(string taxpayerRegistration);
        Task<int> GetTotalCountAsync();
        Task<FinalAssessmentListResponse> SearchAsync(FinalAssessmentSearchParams searchParams);
    }
}