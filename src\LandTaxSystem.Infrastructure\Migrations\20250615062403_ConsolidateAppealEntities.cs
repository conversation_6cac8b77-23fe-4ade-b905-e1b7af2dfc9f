﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ConsolidateAppealEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaxPeriods_ExtendedAppeals_ExtendedAppealId",
                table: "TaxPeriods");

            migrationBuilder.DropTable(
                name: "ExtendedAppeals");

            migrationBuilder.RenameColumn(
                name: "ExtendedAppealId",
                table: "TaxPeriods",
                newName: "AppealId");

            migrationBuilder.RenameIndex(
                name: "IX_TaxPeriods_ExtendedAppealId",
                table: "TaxPeriods",
                newName: "IX_TaxPeriods_AppealId");

            migrationBuilder.AlterColumn<string>(
                name: "Year",
                table: "TaxPeriods",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(10)",
                oldMaxLength: 10,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "StartDate",
                table: "TaxPeriods",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "EndDate",
                table: "TaxPeriods",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<decimal>(
                name: "AppealAmount",
                table: "TaxPeriods",
                type: "numeric",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric(15,2)",
                oldPrecision: 15,
                oldScale: 2);

            migrationBuilder.AlterColumn<string>(
                name: "Reason",
                table: "Appeals",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AddColumn<string>(
                name: "AppealAuthority",
                table: "Appeals",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AppealDate",
                table: "Appeals",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "AppealDescription",
                table: "Appeals",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AppealSubject",
                table: "Appeals",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LocationCode",
                table: "Appeals",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OfficeCode",
                table: "Appeals",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "OrderDate",
                table: "Appeals",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OrderNumber",
                table: "Appeals",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegistrationNumber",
                table: "Appeals",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxDeterminationOrderNumber",
                table: "Appeals",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxpayerAddress",
                table: "Appeals",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxpayerName",
                table: "Appeals",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddForeignKey(
                name: "FK_TaxPeriods_Appeals_AppealId",
                table: "TaxPeriods",
                column: "AppealId",
                principalTable: "Appeals",
                principalColumn: "AppealId",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaxPeriods_Appeals_AppealId",
                table: "TaxPeriods");

            migrationBuilder.DropColumn(
                name: "AppealAuthority",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "AppealDate",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "AppealDescription",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "AppealSubject",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "LocationCode",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "OfficeCode",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "OrderDate",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "OrderNumber",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "RegistrationNumber",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "TaxDeterminationOrderNumber",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "TaxpayerAddress",
                table: "Appeals");

            migrationBuilder.DropColumn(
                name: "TaxpayerName",
                table: "Appeals");

            migrationBuilder.RenameColumn(
                name: "AppealId",
                table: "TaxPeriods",
                newName: "ExtendedAppealId");

            migrationBuilder.RenameIndex(
                name: "IX_TaxPeriods_AppealId",
                table: "TaxPeriods",
                newName: "IX_TaxPeriods_ExtendedAppealId");

            migrationBuilder.AlterColumn<string>(
                name: "Year",
                table: "TaxPeriods",
                type: "character varying(10)",
                maxLength: 10,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "StartDate",
                table: "TaxPeriods",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20);

            migrationBuilder.AlterColumn<string>(
                name: "EndDate",
                table: "TaxPeriods",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20);

            migrationBuilder.AlterColumn<decimal>(
                name: "AppealAmount",
                table: "TaxPeriods",
                type: "numeric(15,2)",
                precision: 15,
                scale: 2,
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "numeric",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Reason",
                table: "Appeals",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500);

            migrationBuilder.CreateTable(
                name: "ExtendedAppeals",
                columns: table => new
                {
                    AppealId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    AppealAuthority = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AppealDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AppealDescription = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AppealSubject = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LocationCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OfficeCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    OrderDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    OrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RegistrationNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ResolvedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TaxDeterminationOrderNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TaxpayerAddress = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    TaxpayerName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExtendedAppeals", x => x.AppealId);
                    table.CheckConstraint("CK_ExtendedAppeal_Status", "\"Status\" IN ('Pending', 'Resolved')");
                    table.ForeignKey(
                        name: "FK_ExtendedAppeals_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExtendedAppeals_UserId",
                table: "ExtendedAppeals",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaxPeriods_ExtendedAppeals_ExtendedAppealId",
                table: "TaxPeriods",
                column: "ExtendedAppealId",
                principalTable: "ExtendedAppeals",
                principalColumn: "AppealId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
