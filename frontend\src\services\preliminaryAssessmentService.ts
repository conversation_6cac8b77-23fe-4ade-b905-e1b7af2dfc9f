import api from './api';
import type {
  PreliminaryAssessmentDto,
  PreliminaryAssessmentCreateDto,
  PreliminaryAssessmentUpdateDto,
  PreliminaryAssessmentListResponse,
  PreliminaryAssessmentSearchParams,
  StatusUpdateRequest,
  StatusUpdateResult,
  ApprovalRequest,
  RejectionRequest,
} from '../types/preliminaryAssessment';

export const preliminaryAssessmentService = {
    // Get all preliminary assessments with pagination and filtering
    getAll: async (
        params?: PreliminaryAssessmentSearchParams
    ): Promise<PreliminaryAssessmentListResponse> => {
        const response = await api.get('/preliminaryassessments', { params });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const pageNumber = parseInt(response.headers['x-page'] || '1', 10);
        const pageSize = parseInt(response.headers['x-page-size'] || '10', 10);
        const totalPages = Math.ceil(totalCount / pageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber,
            pageSize,
            totalPages
        };
    },

    // Get preliminary assessment by ID
    getById: async (id: string): Promise<PreliminaryAssessmentDto> => {
        const response = await api.get(`/preliminaryassessments/${id}`);
        return response.data;
    },

    // Get preliminary assessment by order number
    getByOrderNumber: async (
        orderNumber: string
    ): Promise<PreliminaryAssessmentDto> => {
        const response = await api.get(`/preliminaryassessments/order/${orderNumber}`);
        return response.data;
    },

    // Get preliminary assessments by taxpayer registration
    getByTaxpayerRegistration: async (
        registration: string,
        pageNumber = 1,
        pageSize = 10
    ): Promise<PreliminaryAssessmentListResponse> => {
        const response = await api.get(`/preliminaryassessments/by-taxpayer/${registration}`, {
            params: { pageNumber, pageSize }
        });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const actualPageNumber = parseInt(response.headers['x-page'] || pageNumber.toString(), 10);
        const actualPageSize = parseInt(response.headers['x-page-size'] || pageSize.toString(), 10);
        const totalPages = Math.ceil(totalCount / actualPageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber: actualPageNumber,
            pageSize: actualPageSize,
            totalPages
        };
    },

    // Get preliminary assessments by office code
    getByOfficeCode: async (
        officeCode: string,
        pageNumber = 1,
        pageSize = 10
    ): Promise<PreliminaryAssessmentListResponse> => {
        const response = await api.get(`/preliminaryassessments/office/${officeCode}`, {
            params: { pageNumber, pageSize }
        });
        
        // Extract pagination data from headers
        const totalCount = parseInt(response.headers['x-total-count'] || '0', 10);
        const actualPageNumber = parseInt(response.headers['x-page'] || pageNumber.toString(), 10);
        const actualPageSize = parseInt(response.headers['x-page-size'] || pageSize.toString(), 10);
        const totalPages = Math.ceil(totalCount / actualPageSize);
        
        return {
            data: response.data,
            totalCount,
            pageNumber: actualPageNumber,
            pageSize: actualPageSize,
            totalPages
        };
    },

    // Create new preliminary assessment
    create: async (
        data: PreliminaryAssessmentCreateDto
    ): Promise<PreliminaryAssessmentDto> => {
        const response = await api.post('/preliminaryassessments', data);
        return response.data;
    },

    // Update preliminary assessment
    update: async (
        id: string,
        data: PreliminaryAssessmentUpdateDto
    ): Promise<PreliminaryAssessmentDto> => {
        const response = await api.put(`/preliminaryassessments/${id}`, data);
        return response.data;
    },

    // Delete preliminary assessment
    delete: async (id: string): Promise<void> => {
        await api.delete(`/preliminaryassessments/${id}`);
    },

    // Check if preliminary assessment exists
    exists: async (id: string): Promise<boolean> => {
        try {
            const response = await api.get(`/preliminaryassessments/${id}/exists`);
            return response.data.exists;
        } catch {
            return false;
        }
    },

    // Get total count of preliminary assessments
    getTotalCount: async (): Promise<number> => {
        const response = await api.get('/preliminaryassessments/count');
        return response.data.count;
    },

    // Export preliminary assessments to Excel
    exportToExcel: async (
        params?: PreliminaryAssessmentSearchParams
    ): Promise<Blob> => {
        const response = await api.get('/preliminaryassessments/export', {
            params,
            responseType: 'blob',
            headers: {
                Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            },
        });
        return response.data;
    },

    // Generate PDF report
    generatePdfReport: async (id: string): Promise<Blob> => {
        const response = await api.get(`/preliminaryassessments/${id}/pdf`, {
            responseType: 'blob',
            headers: {
                Accept: 'application/pdf',
            },
        });
        return response.data;
    },

    // Status management methods
    updateStatus: async (id: string, request: StatusUpdateRequest): Promise<StatusUpdateResult> => {
        const response = await api.put(`/preliminaryassessments/${id}/status`, request);
        return response.data;
    },

    submitForReview: async (id: string): Promise<StatusUpdateResult> => {
        const response = await api.post(`/preliminaryassessments/${id}/submit`);
        return response.data;
    },

    approve: async (id: string, request: ApprovalRequest): Promise<StatusUpdateResult> => {
        const response = await api.post(`/preliminaryassessments/${id}/approve`, request);
        return response.data;
    },

    reject: async (id: string, request: RejectionRequest): Promise<StatusUpdateResult> => {
        const response = await api.post(`/preliminaryassessments/${id}/reject`, request);
        return response.data;
    },
};

export default preliminaryAssessmentService;