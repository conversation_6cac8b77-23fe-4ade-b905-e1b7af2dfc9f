using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Http;

namespace LandTaxSystem.Infrastructure.Services
{
    public class FileService
    {
        private readonly IHostEnvironment _environment;

        public FileService(IHostEnvironment environment)
        {
            _environment = environment;
        }

        public async Task<string> SaveFile(IFormFile file, string userId, string propertyId)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is empty or null");

            // Create directory structure: /uploads/{year}/{month}/
            var yearMonth = DateTime.UtcNow.ToString("yyyy/MM");
            var uploadsFolder = Path.Combine(_environment.ContentRootPath, "uploads", yearMonth);

            if (!Directory.Exists(uploadsFolder))
                Directory.CreateDirectory(uploadsFolder);

            // Generate filename: {UserID}_{PropertyID}_{timestamp}.{ext}
            var fileExtension = Path.GetExtension(file.FileName);
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            var fileName = $"{userId}_{propertyId}_{timestamp}{fileExtension}";

            var filePath = Path.Combine(uploadsFolder, fileName);

            // Save file to disk
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Return relative path to store in database
            return Path.Combine("uploads", yearMonth, fileName);
        }
    }
}
