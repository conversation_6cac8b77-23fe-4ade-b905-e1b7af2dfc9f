import React, { useState, useEffect } from 'react';

interface AdminSearchBarProps {
  onSearch: (value: string) => void;
  placeholder?: string;
  initialValue?: string;
  className?: string;
  debounceTime?: number;
  buttonText?: string;
  showButton?: boolean;
}

const AdminSearchBar: React.FC<AdminSearchBarProps> = ({
  onSearch,
  placeholder = 'Search...',
  initialValue = '',
  className = '',
  debounceTime = 300,
  buttonText = 'Search',
  showButton = false,
}) => {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const [debouncedTerm, setDebouncedTerm] = useState(initialValue);

  // Update search term when initialValue changes
  useEffect(() => {
    setSearchTerm(initialValue);
    setDebouncedTerm(initialValue);
  }, [initialValue]);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, debounceTime);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceTime]);

  // Trigger search when debounced term changes
  useEffect(() => {
    if (!showButton) {
      onSearch(debouncedTerm);
    }
  }, [debouncedTerm, onSearch, showButton]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchTerm);
  };

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="join w-full">
        <div className="relative join-item flex-1">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={placeholder}
            className="input input-bordered w-full pr-10"
          />
          {searchTerm && (
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 btn btn-ghost btn-sm btn-circle"
              onClick={() => {
                setSearchTerm('');
                if (!showButton) onSearch('');
              }}
              aria-label="Clear search"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
        {showButton && (
          <button type="submit" className="btn join-item">
            {buttonText}
          </button>
        )}
      </div>
    </form>
  );
};

export default AdminSearchBar;