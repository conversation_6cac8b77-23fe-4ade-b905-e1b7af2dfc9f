import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { getMunicipalityTaxConfig } from "../services/taxConfigService";
import type { MunicipalityTaxConfig } from "../services/taxConfigService";
import TaxConfigEditor from "./TaxConfigEditor";
import { useAuth } from "../context/AuthContext";

const TaxConfigDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [taxConfig, setTaxConfig] = useState<MunicipalityTaxConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const fetchTaxConfig = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getMunicipalityTaxConfig(id);
        setTaxConfig(data);
        setError(null);
      } catch (err) {
        console.error("Error fetching tax configuration:", err);
        setError("Failed to load tax configuration details");
      } finally {
        setLoading(false);
      }
    };

    fetchTaxConfig();
  }, [id]);

  // Check if the user has permission to edit this tax config
  const canEdit = () => {
    if (!user || !taxConfig) return false;

    // Municipal officers can only edit their own municipality's configs
    if (user.role === "Officer" && user.municipalityId) {
      return user.municipalityId.toString() === taxConfig.municipalityId && !taxConfig.isFinalized;
    }

    // Central admins cannot edit tax configs (as per requirements)
    return false;
  };

  const handleUpdate = (updatedConfig: MunicipalityTaxConfig) => {
    setTaxConfig(updatedConfig);
    setIsEditing(false);
  };

  const handleFinalize = (finalizedConfig: MunicipalityTaxConfig) => {
    setTaxConfig(finalizedConfig);
    setIsEditing(false);
  };

  if (loading) {
    return <div className="text-center py-10">Loading tax configuration details...</div>;
  }

  if (error || !taxConfig) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4">
          <p>{error || "Tax configuration not found"}</p>
        </div>
        <button onClick={() => navigate("/tax-config")}>Back to Tax Configurations</button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Tax Configuration Details</h1>
        <button onClick={() => navigate("/tax-config")}>Back to Tax Configurations</button>
      </div>

      {isEditing ? (
        <TaxConfigEditor taxConfig={taxConfig} onUpdate={handleUpdate} onFinalize={handleFinalize} />
      ) : (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h2 className="text-xl font-semibold">{taxConfig.municipalityName}</h2>
              <p className="text-gray-600">Fiscal Year: {taxConfig.fiscalYearName}</p>
              <p className="text-gray-600">
                Status:{" "}
                {taxConfig.isFinalized ? (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Finalized
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Draft
                  </span>
                )}
              </p>
              {taxConfig.isFinalized && (
                <p className="text-gray-600">
                  Finalized by: {taxConfig.finalizedBy} on {new Date(taxConfig.finalizedAt || "").toLocaleString()}
                </p>
              )}
            </div>

            {canEdit() && (
              <button
                onClick={() => setIsEditing(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
              >
                Edit Configuration
              </button>
            )}
          </div>

          {/* Tax Slabs Section */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Tax Slabs</h3>
            {taxConfig.taxSlabsConfig.length === 0 ? (
              <p className="text-gray-500 italic">No tax slabs defined</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white">
                  <thead>
                    <tr>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Min Value</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Max Value</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Rate (%)</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {taxConfig.taxSlabsConfig.map((slab, index) => (
                      <tr key={index}>
                        <td className="py-2 px-4 border-b border-gray-200">{slab.minValue.toLocaleString()}</td>
                        <td className="py-2 px-4 border-b border-gray-200">{slab.maxValue.toLocaleString()}</td>
                        <td className="py-2 px-4 border-b border-gray-200">{(slab.rate * 100).toFixed(2)}%</td>
                        <td className="py-2 px-4 border-b border-gray-200">{slab.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Penalty Rules Section */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Penalty Rules</h3>
            {taxConfig.penaltyRules.length === 0 ? (
              <p className="text-gray-500 italic">No penalty rules defined</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white">
                  <thead>
                    <tr>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Days After Due</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Rate (%)</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Max Penalty (%)</th>
                      <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {taxConfig.penaltyRules.map((rule, index) => (
                      <tr key={index}>
                        <td className="py-2 px-4 border-b border-gray-200">{rule.daysAfterDue}</td>
                        <td className="py-2 px-4 border-b border-gray-200">{(rule.rate * 100).toFixed(2)}%</td>
                        <td className="py-2 px-4 border-b border-gray-200">{(rule.maxPenalty * 100).toFixed(2)}%</td>
                        <td className="py-2 px-4 border-b border-gray-200">{rule.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Valuation Rules Section */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Valuation Rules</h3>
            {!taxConfig.valuationRulesConfig ? (
              <p className="text-gray-500 italic">No valuation rules defined</p>
            ) : (
              <div className="space-y-4">
                {/* Land MVR */}
                <div>
                  <h4 className="font-semibold">Land Market Value Rate (per Sq. Meter)</h4>
                  <div className="overflow-x-auto mt-2">
                    <table className="min-w-full bg-white">
                      <thead>
                        <tr>
                          <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Land Type</th>
                          <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(taxConfig.valuationRulesConfig.landMVR).map(([key, value]) => (
                          <tr key={key}>
                            <td className="py-2 px-4 border-b border-gray-200">{key}</td>
                            <td className="py-2 px-4 border-b border-gray-200">{value.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Building Base Rate */}
                <div>
                  <h4 className="font-semibold">Building Base Rate (per Sq. Meter)</h4>
                  <div className="overflow-x-auto mt-2">
                    <table className="min-w-full bg-white">
                      <thead>
                        <tr>
                          <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Building Type</th>
                          <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left">Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(taxConfig.valuationRulesConfig.buildingBaseRatePerSqm).map(([key, value]) => (
                          <tr key={key}>
                            <td className="py-2 px-4 border-b border-gray-200">{key}</td>
                            <td className="py-2 px-4 border-b border-gray-200">{value.toLocaleString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Annual Depreciation Rate */}
                <div>
                  <h4 className="font-semibold">Annual Depreciation Rate</h4>
                  <p className="py-2 px-4 border-b border-gray-200">
                    {(taxConfig.valuationRulesConfig.annualDepreciationRate * 100).toFixed(2)}%
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="mt-4 text-sm text-gray-500">
            <p>Created: {new Date(taxConfig.createdAt).toLocaleString()}</p>
            <p>Last Updated: {new Date(taxConfig.updatedAt).toLocaleString()}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaxConfigDetail;
