import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/AuthContext';
import { negotiationService } from '../services/negotiationService';
import type { NegotiationDto } from '../services/negotiationService';
import { useNavigate } from 'react-router-dom';
import { AdminLayout } from '../components/admin';

const Negotiations: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [negotiations, setNegotiations] = useState<NegotiationDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedNegotiation, setSelectedNegotiation] = useState<NegotiationDto | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  const fetchNegotiations = useCallback(async () => {
    try {
      setLoading(true);
      let data;
      if (user?.role === 'Officer') {
        data = await negotiationService.getAllNegotiations();
      } else {
        data = await negotiationService.getUserNegotiations();
      }
      setNegotiations(data);
      setError(null);
    } catch (err) {
      setError('Failed to load negotiations. Please try again later.');
      console.error('Error fetching negotiations:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.role]);

  useEffect(() => {
    if (user) {
      fetchNegotiations();
    }
  }, [user, fetchNegotiations]);

  const handleViewNegotiation = (negotiation: NegotiationDto) => {
    setSelectedNegotiation(negotiation);
    setIsViewModalOpen(true);
  };

  const handleAcceptNegotiation = async (negotiationId: string) => {
    try {
      await negotiationService.acceptNegotiation(negotiationId);
      setIsViewModalOpen(false);
      fetchNegotiations();
    } catch (err) {
      setError('Failed to accept negotiation. Please try again.');
      console.error('Error accepting negotiation:', err);
    }
  };

  const handleRejectNegotiation = async (negotiationId: string) => {
    try {
      await negotiationService.rejectNegotiation(negotiationId, rejectionReason);
      setIsViewModalOpen(false);
      setRejectionReason('');
      fetchNegotiations();
    } catch (err) {
      setError('Failed to reject negotiation. Please try again.');
      console.error('Error rejecting negotiation:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return `NPR ${new Intl.NumberFormat('en-US').format(amount)}`;
  };

  const getStatusBadgeClass = (status?: string) => {
    if (!status) return 'badge badge-warning'; // Default to pending
    
    switch (status.toLowerCase()) {
      case 'pending':
        return 'badge badge-warning';
      case 'accepted':
        return 'badge badge-success';
      case 'rejected':
        return 'badge badge-error';
      default:
        return 'badge badge-ghost';
    }
  };

  return (
    <AdminLayout title={user?.role === 'Officer' ? 'Manage Negotiations' : 'My Negotiations'}>

      {error && (
        <div className="alert alert-error mb-4">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      ) : negotiations.length === 0 ? (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body text-center">
            <p className="text-base-content/70">No negotiations found.</p>
            {user?.role === 'Officer' && (
              <div className="card-actions justify-center">
                <button
                  onClick={() => navigate('/appeals')}
                  className="btn btn-primary"
                >
                  View Appeals to Negotiate
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {negotiations.map((negotiation) => (
            <div key={negotiation.negotiationId} className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <p className="text-sm font-medium text-primary truncate">
                      Negotiation for Property at {negotiation.propertyAddress}
                    </p>
                    <p className="mt-1 text-sm text-base-content/70">
                      Proposed on {formatDate(negotiation.negotiationDate)}
                    </p>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className={getStatusBadgeClass(negotiation.status)}>
                      {negotiation.status || 'Pending'}
                    </span>
                    <p className="mt-1 text-sm text-base-content/70">
                      Original: {formatCurrency(negotiation.originalAssessmentAmount || 0)}
                    </p>
                    <p className="text-sm font-medium text-success">
                      Negotiated: {formatCurrency(negotiation.negotiatedAmount)}
                    </p>
                  </div>
                </div>
                <div className="mt-2 sm:flex sm:justify-between">
                  <div className="sm:flex">
                    <p className="flex items-center text-sm text-base-content/70">
                      {user?.role === 'Officer'
                        ? `Taxpayer: ${negotiation.taxPayerName}`
                        : `Officer: ${negotiation.officerName}`}
                    </p>
                  </div>
                  <div className="card-actions">
                    <button
                      onClick={() => handleViewNegotiation(negotiation)}
                      className="btn btn-primary btn-sm"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* View Negotiation Modal */}
      <div className={`modal ${isViewModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box w-11/12 max-w-2xl">
          {selectedNegotiation && (
            <>
              <h3 className="text-lg font-bold">
                Negotiation Details
              </h3>
              <div className="mt-4 space-y-4">
                <div>
                  <h4 className="label-text text-base-content/70">Property</h4>
                  <p className="text-sm">{selectedNegotiation.propertyAddress}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="label-text text-base-content/70">Original Amount</h4>
                    <p className="text-sm">
                      {formatCurrency(selectedNegotiation.originalAssessmentAmount || 0)}
                    </p>
                  </div>
                  <div>
                    <h4 className="label-text text-base-content/70">Negotiated Amount</h4>
                    <p className="text-sm font-medium text-success">
                      {formatCurrency(selectedNegotiation.negotiatedAmount)}
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="label-text text-base-content/70">Taxpayer</h4>
                    <p className="text-sm">{selectedNegotiation.taxPayerName}</p>
                  </div>
                  <div>
                    <h4 className="label-text text-base-content/70">Officer</h4>
                    <p className="text-sm">{selectedNegotiation.officerName}</p>
                  </div>
                </div>
                <div>
                  <h4 className="label-text text-base-content/70">Date Proposed</h4>
                  <p className="text-sm">{formatDate(selectedNegotiation.negotiationDate)}</p>
                </div>
                <div>
                  <h4 className="label-text text-base-content/70">Status</h4>
                  <span className={getStatusBadgeClass(selectedNegotiation.status)}>
                    {selectedNegotiation.status || 'Pending'}
                  </span>
                </div>
                <div>
                  <h4 className="label-text text-base-content/70">Original Appeal Reason</h4>
                  <p className="text-sm">{selectedNegotiation.appealReason}</p>
                </div>
              </div>

              {user?.role !== 'Officer' && (!selectedNegotiation.status || selectedNegotiation.status === 'Pending') && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium">Respond to Negotiation</h4>
                  <div className="mt-2 space-y-4">
                    {/* Rejection reason input */}
                    <div className="form-control">
                      <label className="label" htmlFor="rejectionReason">
                        <span className="label-text">Reason for rejection (optional)</span>
                      </label>
                      <textarea
                        id="rejectionReason"
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        placeholder="Enter reason if rejecting..."
                        className="textarea textarea-bordered"
                        rows={3}
                      />
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleAcceptNegotiation(selectedNegotiation.negotiationId)}
                        className="btn btn-success flex-1"
                      >
                        Accept
                      </button>
                      <button
                        onClick={() => handleRejectNegotiation(selectedNegotiation.negotiationId)}
                        className="btn btn-error flex-1"
                      >
                        Reject
                      </button>
                    </div>
                  </div>
                </div>
              )}

              <div className="modal-action">
                <button
                  type="button"
                  onClick={() => setIsViewModalOpen(false)}
                  className="btn btn-ghost"
                >
                  Close
                </button>
              </div>
            </>
          )}
        </div>
        <form method="dialog" className="modal-backdrop">
          <button onClick={() => setIsViewModalOpen(false)}>close</button>
        </form>
      </div>
    </AdminLayout>
  );
};

export default Negotiations;
