using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Core.DTOs.User;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System;
using Microsoft.Extensions.Logging;
using LandTaxSystem.Core.Interfaces;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<UsersController> _logger;
        private readonly IEmailService _emailService; // Assuming IEmailService is defined elsewhere

        public UsersController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            ILogger<UsersController> logger,
            IEmailService emailService)
        {
            _userManager = userManager;
            _context = context;
            _logger = logger;
            _emailService = emailService;
        }

        // GET: api/users
        [HttpGet]
        [Authorize(Roles = "CentralAdmin,Officer")]
        public async Task<ActionResult<object>> GetUsers([FromQuery] UserListQueryDto query)
        {
            try
            {
                var usersQuery = _context.Users
                    .Include(u => u.Municipality)
                    .AsQueryable();

                var currentUser = await _userManager.GetUserAsync(User);

                if (User.IsInRole("Officer") && !User.IsInRole("CentralAdmin"))
                {
                    if (currentUser?.MunicipalityId != null)
                    {
                        usersQuery = usersQuery.Where(u => u.MunicipalityId == currentUser.MunicipalityId);
                    }
                    else
                    {
                        _logger.LogWarning($"Officer {currentUser?.Id} has no associated municipality. Returning empty list.");
                        return Ok(new { Users = new List<UserResponseDto>(), TotalCount = 0, Page = query.Page, PageSize = query.PageSize, TotalPages = 0 });
                    }
                }
                else if (query.MunicipalityId.HasValue) // CentralAdmin can filter by any municipality
                {
                    usersQuery = usersQuery.Where(u => u.MunicipalityId == query.MunicipalityId.Value);
                }

                if (!string.IsNullOrEmpty(query.Role))
                {
                    var usersInRole = await _userManager.GetUsersInRoleAsync(query.Role);
                    var userIdsInRole = usersInRole.Select(u => u.Id).ToList();
                    usersQuery = usersQuery.Where(u => userIdsInRole.Contains(u.Id));
                }

                if (!string.IsNullOrEmpty(query.Status))
                {
                    usersQuery = usersQuery.Where(u => u.Status == query.Status);
                }

                if (!string.IsNullOrEmpty(query.WardNumber))
                {
                    usersQuery = usersQuery.Where(u => u.WardNumber == query.WardNumber);
                }

                if (!string.IsNullOrEmpty(query.Search))
                {
                    usersQuery = usersQuery.Where(u =>
                        (u.FullName != null && u.FullName.Contains(query.Search)) ||
                        (u.Email != null && u.Email.Contains(query.Search)) ||
                        (u.PAN != null && u.PAN.Contains(query.Search)));
                }

                var totalCount = await usersQuery.CountAsync();
                var users = await usersQuery
                    .OrderBy(u => u.FullName) // Default sort
                    .Skip((query.Page - 1) * query.PageSize)
                    .Take(query.PageSize)
                    .ToListAsync();

                var userDtos = new List<UserResponseDto>();
                foreach (var user in users)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userDtos.Add(MapToUserResponseDto(user, roles.FirstOrDefault() ?? string.Empty));
                }

                return Ok(new
                {
                    Users = userDtos,
                    TotalCount = totalCount,
                    Page = query.Page,
                    PageSize = query.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / query.PageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users");
                return StatusCode(500, "An error occurred while retrieving users.");
            }
        }

        // GET: api/users/{id}
        [HttpGet("{id}")]
        [Authorize(Roles = "CentralAdmin,Officer")]
        public async Task<ActionResult<UserResponseDto>> GetUser(string id)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.Municipality)
                    .FirstOrDefaultAsync(u => u.Id == id);

                if (user == null) return NotFound("User not found.");

                if (User.IsInRole("Officer") && !User.IsInRole("CentralAdmin"))
                {
                    var officer = await _userManager.GetUserAsync(User);
                    if (user.MunicipalityId != officer?.MunicipalityId)
                    {
                        _logger.LogWarning($"Officer {officer?.Id} from municipality {officer?.MunicipalityId} attempted to access user {user.Id} from municipality {user.MunicipalityId}.");
                        return Forbid();
                    }
                }

                var roles = await _userManager.GetRolesAsync(user);
                return Ok(MapToUserResponseDto(user, roles.FirstOrDefault() ?? string.Empty));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user {UserId}", id);
                return StatusCode(500, "An error occurred while retrieving the user.");
            }
        }

        // POST: api/users
        [HttpPost]
        [Authorize(Roles = "CentralAdmin")]
        public async Task<ActionResult<UserResponseDto>> CreateUser(CreateUserDto createUserDto)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            try
            {
                if (createUserDto.MunicipalityId.HasValue && !await _context.Municipalities.AnyAsync(m => m.MunicipalityId == createUserDto.MunicipalityId.Value))
                {
                    return BadRequest("Invalid Municipality ID.");
                }

                var user = new ApplicationUser
                {
                    UserName = createUserDto.Email,
                    Email = createUserDto.Email,
                    FullName = $"{createUserDto.FirstName} {createUserDto.LastName}",
                    PhoneNumber = createUserDto.PhoneNumber,
                    PAN = createUserDto.PAN,
                    TwoGenerations = createUserDto.TwoGenerations,
                    MunicipalityId = createUserDto.MunicipalityId,
                    Status = "PendingApproval", // As per PRD
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    EmailConfirmed = false // Officer approval implies confirmation or separate flow
                };

                var result = await _userManager.CreateAsync(user, createUserDto.Password);
                if (!result.Succeeded)
                {
                    return BadRequest(new { Message = "User creation failed.", Errors = result.Errors.Select(e => e.Description) });
                }

                string assignedRole = "Citizen"; // Default role
                if (!string.IsNullOrEmpty(createUserDto.Role))
                {
                    if (!await _context.Roles.AnyAsync(r => r.Name == createUserDto.Role))
                        return BadRequest($"Role '{createUserDto.Role}' does not exist.");
                    assignedRole = createUserDto.Role;
                }
                await _userManager.AddToRoleAsync(user, assignedRole);

                _logger.LogInformation($"User {user.Id} created with status PendingApproval. Role: {assignedRole}");
                // TODO: Implement email notification for user registration (e.g., to user and/or admin/officer)

                if (user.MunicipalityId.HasValue)
                {
                    // Ensure Municipality is loaded for the response DTO
                    await _context.Entry(user).Reference(u => u.Municipality).LoadAsync();
                }

                return CreatedAtAction(nameof(GetUser), new { id = user.Id }, MapToUserResponseDto(user, assignedRole));
            }
            catch (DbUpdateException ex)
            {
                _logger.LogError(ex, "Database error creating user. PAN might be duplicate.");
                // Check for unique constraint violation, specifically for PAN
                if (ex.InnerException?.Message != null && ex.InnerException.Message.Contains("IX_Users_PAN", StringComparison.OrdinalIgnoreCase))
                {
                    return Conflict(new { Message = "A user with this PAN already exists." });
                }
                return StatusCode(500, "A database error occurred while creating the user.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                return StatusCode(500, "An unexpected error occurred while creating the user.");
            }
        }

        // PUT: api/users/{id}
        [HttpPut("{id}")]
        [Authorize(Roles = "CentralAdmin")]
        public async Task<ActionResult<UserResponseDto>> UpdateUser(string id, UpdateUserDto updateUserDto)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            try
            {
                var user = await _context.Users.Include(u => u.Municipality).FirstOrDefaultAsync(u => u.Id == id);
                if (user == null) return NotFound("User not found.");

                // Validate MunicipalityId if provided
                if (updateUserDto.MunicipalityId.HasValue && !await _context.Municipalities.AnyAsync(m => m.MunicipalityId == updateUserDto.MunicipalityId.Value))
                {
                    return BadRequest("Invalid Municipality ID.");
                }

                user.FullName = $"{updateUserDto.FirstName} {updateUserDto.LastName}";
                user.PhoneNumber = updateUserDto.PhoneNumber;
                user.MunicipalityId = updateUserDto.MunicipalityId;
                // PAN and TwoGenerations are not updatable via this endpoint by design in this iteration
                // Status is updated via a separate endpoint
                user.UpdatedAt = DateTime.UtcNow;

                var updateResult = await _userManager.UpdateAsync(user);
                if (!updateResult.Succeeded)
                {
                    return BadRequest(new { Message = "User update failed.", Errors = updateResult.Errors.Select(e => e.Description) });
                }

                // Handle Role Update
                string finalRole = (await _userManager.GetRolesAsync(user)).FirstOrDefault() ?? string.Empty;
                if (!string.IsNullOrEmpty(updateUserDto.Role))
                {
                    if (!await _context.Roles.AnyAsync(r => r.Name == updateUserDto.Role))
                        return BadRequest($"Role '{updateUserDto.Role}' does not exist.");

                    var currentRoles = await _userManager.GetRolesAsync(user);
                    if (!currentRoles.Contains(updateUserDto.Role))
                    {
                        var removeResult = await _userManager.RemoveFromRolesAsync(user, currentRoles);
                        if (!removeResult.Succeeded)
                            return BadRequest(new { Message = "Failed to remove existing roles.", Errors = removeResult.Errors.Select(e => e.Description) });

                        var addResult = await _userManager.AddToRoleAsync(user, updateUserDto.Role);
                        if (!addResult.Succeeded)
                            return BadRequest(new { Message = "Failed to add new role.", Errors = addResult.Errors.Select(e => e.Description) });
                        finalRole = updateUserDto.Role;
                    }
                }

                // Ensure Municipality is loaded if it was updated or for the response DTO
                if (user.MunicipalityId.HasValue && user.Municipality == null)
                {
                    await _context.Entry(user).Reference(u => u.Municipality).LoadAsync();
                }

                return Ok(MapToUserResponseDto(user, finalRole));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode(500, "An error occurred while updating the user.");
            }
        }

        // GET: api/users/submission/{submissionNumber}
        [HttpGet("submission/{submissionNumber}")]
        [AllowAnonymous] // Allow anonymous access to check submission status
        public async Task<ActionResult<SubmissionStatusDto>> GetSubmissionStatus(string submissionNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(submissionNumber))
                {
                    return BadRequest("Submission number is required.");
                }

                // First check if this is a user registration submission
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.SubmissionNumber == submissionNumber);

                if (user != null)
                {
                    return Ok(new SubmissionStatusDto
                    {
                        SubmissionNumber = submissionNumber,
                        Status = user.Status,
                        Type = "Registration",
                        SubmissionDate = user.CreatedAt,
                        Comments = user.Status == "Active" ? "Your registration has been approved." :
                                  user.Status == "Rejected" ? "Your registration has been rejected." :
                                  "Your registration is pending approval."
                    });
                }

                // If not found in users, check other submission types (property, appeals, etc.)
                // This would be expanded as other submission types are implemented

                // If submission not found anywhere
                return NotFound("Submission not found. Please verify the submission number and try again.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving submission status for {SubmissionNumber}", submissionNumber);
                return StatusCode(500, "An error occurred while retrieving the submission status.");
            }
        }

        // GET: api/users/taxpayers
        [HttpGet("taxpayers")]
        [Authorize(Roles = "Officer")]
        public async Task<ActionResult<IEnumerable<UserResponseDto>>> GetTaxpayers([FromQuery] Guid municipalityId, [FromQuery] string? wardNumber)
        {
            try
            {
                var officer = await _userManager.GetUserAsync(User);
                if (officer?.MunicipalityId == null)
                {
                    _logger.LogWarning($"Officer {officer?.Id} attempting to get taxpayers but has no associated municipality.");
                    return Forbid("Officer not associated with a municipality.");
                }

                // Ensure officer can only access taxpayers from their own municipality
                if (municipalityId != officer.MunicipalityId)
                {
                    _logger.LogWarning($"Officer {officer.Id} from municipality {officer.MunicipalityId} attempted to access taxpayers for different municipality {municipalityId}.");
                    return Forbid("Access denied to the specified municipality's taxpayers. Officers can only view taxpayers for their own municipality.");
                }

                // Get users who have filed tax returns for properties in the officer's municipality
                var taxpayersQuery = from rf in _context.ReturnFilings
                                     join p in _context.Properties on rf.PropertyId equals p.PropertyId
                                     join u in _context.Users on p.OwnerUserId equals u.Id
                                     where p.MunicipalityId == municipalityId && u.Status == "Active" && u.Role == "Citizen"
                                     select new { User = u, Property = p };

                if (!string.IsNullOrEmpty(wardNumber))
                {
                    taxpayersQuery = taxpayersQuery.Where(x => x.Property.WardNumber.ToString() == wardNumber);
                }

                var taxpayerData = await taxpayersQuery
                    .Select(x => x.User)
                    .Distinct()
                    .Include(u => u.Municipality)
                    .OrderBy(u => u.FullName)
                    .ToListAsync();

                var taxpayers = taxpayerData;

                var userDtos = new List<UserResponseDto>();
                foreach (var user in taxpayers)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userDtos.Add(MapToUserResponseDto(user, roles.FirstOrDefault() ?? string.Empty));
                }

                return Ok(userDtos);
            }
            catch (Exception ex)
            {
                var officerIdForLog = (await _userManager.GetUserAsync(User))?.Id ?? "UnknownOfficer";
                _logger.LogError(ex, "Error getting taxpayers for officer {OfficerId}", officerIdForLog);
                return StatusCode(500, "An error occurred while retrieving taxpayers.");
            }
        }

        // GET: api/users/pending
        [HttpGet("pending")]
        [Authorize(Roles = "Officer")]
        public async Task<ActionResult<IEnumerable<UserResponseDto>>> GetPendingUsers([FromQuery] Guid? municipalityIdFromQuery) // Changed int? to Guid?
        {
            try
            {
                var officer = await _userManager.GetUserAsync(User);
                if (officer?.MunicipalityId == null)
                {
                    _logger.LogWarning($"Officer {officer?.Id} attempting to get pending users but has no associated municipality.");
                    return Forbid("Officer not associated with a municipality.");
                }

                // PRD: "Officer Dashboard: GET /api/users/pending?municipalityId={id}"
                // This implies officer provides their municipality ID, or it's inferred.
                // For security, we will always use the officer's own municipality ID from their profile.
                // The query parameter can be used for future flexibility if CentralAdmin needs this, but for Officer, it's restricted.
                if (municipalityIdFromQuery.HasValue && municipalityIdFromQuery.Value != officer.MunicipalityId)
                {
                    _logger.LogWarning($"Officer {officer.Id} from municipality {officer.MunicipalityId} attempted to access pending users for different municipality {municipalityIdFromQuery.Value}. This is disallowed; defaulting to officer's municipality.");
                    // Silently ignore the query parameter for officers and use their own municipality ID.
                    // Alternatively, return Forbid() if strict adherence to query param matching is desired.
                    // For now, let's be strict as per PRD intent for officer's view.
                    // return Forbid("Access denied to the specified municipality's pending users. Officers can only view pending users for their own municipality.");
                }

                var targetMunicipalityId = officer.MunicipalityId.Value;

                var pendingUsersQuery = _context.Users
                    .Include(u => u.Municipality)
                    .Where(u => u.Status == "PendingApproval" && u.MunicipalityId == targetMunicipalityId);

                var pendingUsers = await pendingUsersQuery.OrderBy(u => u.CreatedAt).ToListAsync();

                var userDtos = new List<UserResponseDto>();
                foreach (var user in pendingUsers)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    userDtos.Add(MapToUserResponseDto(user, roles.FirstOrDefault() ?? string.Empty));
                }

                return Ok(userDtos);
            }
            catch (Exception ex)
            {
                var officerIdForLog = (await _userManager.GetUserAsync(User))?.Id ?? "UnknownOfficer";
                _logger.LogError(ex, "Error getting pending users for officer {OfficerId}", officerIdForLog);
                return StatusCode(500, "An error occurred while retrieving pending users.");
            }
        }

        // PUT: api/users/{id}/status
        [HttpPut("{id}/status")]
        [Authorize(Roles = "Officer")]
        public async Task<IActionResult> UpdateUserStatus(string id, [FromBody] UserStatusUpdateDto statusUpdateDto)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            try
            {
                var userToUpdate = await _context.Users.FindAsync(id);
                if (userToUpdate == null) return NotFound("User not found.");

                var officer = await _userManager.GetUserAsync(User);
                if (officer?.MunicipalityId == null || userToUpdate.MunicipalityId != officer.MunicipalityId)
                {
                    _logger.LogWarning($"Officer {officer?.Id} (Municipality: {officer?.MunicipalityId}) attempt to update status for user {userToUpdate.Id} (Municipality: {userToUpdate.MunicipalityId}) outside their jurisdiction.");
                    return Forbid("Officer cannot update status for users outside their municipality.");
                }

                if (userToUpdate.Status == statusUpdateDto.Status)
                {
                    return Ok(new { Message = $"User status is already '{statusUpdateDto.Status}'. No change made." });
                }

                // Validate new status
                if (statusUpdateDto.Status != "Active" && statusUpdateDto.Status != "Rejected")
                {
                    return BadRequest("Invalid status value. Must be 'Active' or 'Rejected'.");
                }

                // Ensure user is in 'PendingApproval' state before officer can act.
                // PRD implies officer acts on 'PendingApproval' users.
                // If requirement is to allow re-evaluation of Active/Rejected users, this check can be modified.
                if (userToUpdate.Status != "PendingApproval")
                {
                    _logger.LogWarning($"Officer {officer.Id} attempting to update status for user {userToUpdate.Id} whose status is '{userToUpdate.Status}', not 'PendingApproval'. Action denied or requires different flow.");
                    return BadRequest($"User status is currently '{userToUpdate.Status}'. Only users with 'PendingApproval' status can be approved or rejected through this workflow.");
                }

                // Update user status and timestamp
                userToUpdate.Status = statusUpdateDto.Status;
                userToUpdate.UpdatedAt = DateTime.UtcNow;

                if (userToUpdate.Status == "Active")
                {
                    // Mark email as confirmed when approving user
                    userToUpdate.EmailConfirmed = true;

                    // Log PAN information if it exists
                    if (!string.IsNullOrEmpty(userToUpdate.PAN))
                    {
                        _logger.LogInformation($"User {userToUpdate.Id} approved with existing PAN: {userToUpdate.PAN}");
                    }
                    else
                    {
                        _logger.LogWarning($"User {userToUpdate.Id} approved without a PAN. PAN should be set during registration.");
                    }

                    try
                    {
                        // Send approval notification without password
                        await _emailService.SendUserApprovalNotificationAsync(
                            userToUpdate.Email,
                            userToUpdate.FullName,
                            userToUpdate.PAN ?? "Not provided",
                            userToUpdate.UserName);

                        _logger.LogInformation($"User {userToUpdate.Id} approved. Notification sent to {userToUpdate.Email}.");
                    }
                    catch (Exception ex)
                    {
                        // Log but don't fail the operation if email sending fails
                        _logger.LogError(ex, $"Failed to send approval email to user {userToUpdate.Id}");
                    }
                }

                var result = await _userManager.UpdateAsync(userToUpdate);
                if (!result.Succeeded)
                {
                    return BadRequest(new { Message = "Failed to update user status.", Errors = result.Errors.Select(e => e.Description) });
                }

                _logger.LogInformation($"User {userToUpdate.Id} status updated to {userToUpdate.Status} by Officer {officer.Id}. Rejection Reason (if any): {statusUpdateDto.RejectionReason}");
                // TODO: Implement email notification service call here (e.g., user approved/rejected with reason)

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId} status", id);
                return StatusCode(500, "An error occurred while updating user status.");
            }
        }

        private UserResponseDto MapToUserResponseDto(ApplicationUser user, string role)
        {
            return new UserResponseDto
            {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Telephone = user.Telephone,
                PAN = user.PAN,
                TwoGenerations = user.TwoGenerations,
                SubmissionNumber = user.SubmissionNumber,
                Status = user.Status,
                MunicipalityId = user.MunicipalityId,
                MunicipalityName = user.Municipality?.Name, // Relies on Municipality being eager/explicitly loaded
                WardNumber = user.WardNumber,
                Role = role,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt,
                
                // Personal Information
                DateOfBirth = user.DateOfBirth,
                Gender = user.Gender,
                Nationality = user.Nationality,
                Profession = user.Profession,
                CitizenshipNumber = user.CitizenshipNumber,
                
                // Family Information
                FatherName = user.FatherName,
                MotherName = user.MotherName,
                GrandfatherName = user.GrandfatherName,
                GrandmotherName = user.GrandmotherName,
                MaritalStatus = user.MaritalStatus,
                SpouseName = user.SpouseName,
                IsMinor = user.IsMinor,
                GuardianName = user.GuardianName,
                GuardianRelation = user.GuardianRelation,
                
                // Address Information
                PermanentAddress = user.PermanentAddress,
                TemporaryAddress = user.TemporaryAddress,
                ToleStreet = user.ToleStreet,
                
                // Document Information
                DocumentPath = user.DocumentPath,
                
                // Document Metadata
                CitizenshipIssueDistrict = user.CitizenshipIssueDistrict,
                CitizenshipIssueDate = user.CitizenshipIssueDate,
                CitizenshipIssueOffice = user.CitizenshipIssueOffice,
                
                NationalIdNumber = user.NationalIdNumber,
                NationalIdIssueDistrict = user.NationalIdIssueDistrict,
                NationalIdIssueDate = user.NationalIdIssueDate,
                NationalIdIssueOffice = user.NationalIdIssueOffice,
                NationalIdDocumentPath = user.NationalIdDocumentPath,
                
                PanIssueDate = user.PanIssueDate,
                PanIssueDistrict = user.PanIssueDistrict,
                PanIssueOffice = user.PanIssueOffice,
                PanDocumentPath = user.PanDocumentPath,
                
                // Contact Preferences
                ContactPreferences = user.ContactPreferences
            };
        }


    }
}