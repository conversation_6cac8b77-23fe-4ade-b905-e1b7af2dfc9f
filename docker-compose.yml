services:
    api:
        build:
            context: .
            dockerfile: Dockerfile
        container_name: land-tax-system-api
        depends_on:
            db:
                condition: service_healthy
        environment:
            - ASPNETCORE_ENVIRONMENT=Development
            - ConnectionStrings__DefaultConnection=Host=db;Port=5432;Database=land_tax_system;Username=********;Password=********;
            - POSTGRES_USER=********
            - POSTGRES_PASSWORD=********
            - Jwt__Key=SuperSecretKeyForJWTAuthentication12345SuperSecretKeyForJWTAuthentication12345SuperSecretKeyForJWTAuthentication12345SuperSecretKeyForJWTAuthentication12345SuperSecretKeyForJWTAuthentication12345SuperSecretKeyForJWTAuthentication12345
            - Jwt__Issuer=LandTaxSystemAPI
            - Jwt__Audience=LandTaxSystemClient
            - EmailSettings__SmtpHost=mailhog
            - EmailSettings__SmtpPort=1025
            - EmailSettings__SenderEmail=<EMAIL>
            - EmailSettings__SenderName=Land Tax System
        ports:
            - "8080:8080"
        volumes:
            - ./uploads:/app/uploads
        networks:
            - app-network

    db:
        image: postgis/postgis:15-3.4
        container_name: land-tax-system-db
        environment:
            - POSTGRES_USER=********
            - POSTGRES_PASSWORD=********
            - POSTGRES_DB=land_tax_system
        ports:
            - "5432:5432"
        volumes:
            - ********_data:/var/lib/********ql/data
        networks:
            - app-network
        healthcheck:
            test: ["CMD-SHELL", "pg_isready -U ********"]
            interval: 5s
            timeout: 5s
            retries: 5
            start_period: 10s

    mailhog:
        image: mailhog/mailhog
        container_name: mailhog
        ports:
            - "1025:1025"
            - "8025:8025"
        networks:
            - app-network

networks:
    app-network:
        driver: bridge

volumes:
    ********_data:
