import React, { useState } from 'react';
import type { PropertyTaxSummary } from '../types';

interface FiscalYear {
  fiscalYearId: string;
  name: string;
  startDate: string;
  endDate: string;
  isActive: boolean;
}

interface UnderpaidAssessmentFormProps {
  properties: PropertyTaxSummary[];
  fiscalYears: FiscalYear[];
  activeFiscalYear: FiscalYear | null;
  onSubmit: (data: {
    propertyId: string;
    fiscalYearId: string;
    originalAmount?: number;
    overriddenValue?: number;
    overrideReason?: string;
  }) => Promise<void>;
  submitting: boolean;
  error: string;
}

const UnderpaidAssessmentForm: React.FC<UnderpaidAssessmentFormProps> = ({
  properties,
  fiscalYears,
  activeFiscalYear,
  onSubmit,
  submitting,
  error
}) => {
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>('');
  const [selectedFiscalYearId, setSelectedFiscalYearId] = useState<string>(activeFiscalYear?.fiscalYearId || '');
  const [overriddenValue, setOverriddenValue] = useState<string>('');
  const [overrideReason, setOverrideReason] = useState<string>('');
  const [formError, setFormError] = useState<string>('');

  // Get the selected property object
  const selectedProperty = selectedPropertyId ? 
    properties.find(p => p.propertyId === selectedPropertyId) : null;

  // Handle property selection change
  const handlePropertyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const propertyId = e.target.value;
    setSelectedPropertyId(propertyId);
    
    // Reset form error when property changes
    setFormError('');
  };

  // Handle fiscal year selection change
  const handleFiscalYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedFiscalYearId(e.target.value);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!selectedPropertyId) {
      setFormError('Please select an underpaid property');
      return;
    }

    if (!selectedFiscalYearId) {
      setFormError('Please select a fiscal year');
      return;
    }

    // Get the original amount from the selected property
    const originalAmount = selectedProperty?.calculatedTaxAmount;

    // Prepare data for submission
    const formData = {
      propertyId: selectedPropertyId,
      fiscalYearId: selectedFiscalYearId,
      originalAmount,
      overriddenValue: overriddenValue ? parseFloat(overriddenValue) : undefined,
      overrideReason: overrideReason || undefined
    };

    try {
      await onSubmit(formData);
      // Reset form after successful submission
      setSelectedPropertyId('');
      setOverriddenValue('');
      setOverrideReason('');
    } catch (err) {
      console.error('Error creating assessment:', err);
      setFormError('Failed to create assessment. Please try again.');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Create Assessment for Underpaid Property</h2>
      
      {(error || formError) && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error || formError}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="property" className="block text-gray-700 font-medium mb-2">
            Select Underpaid Property
          </label>
          <select
            id="property"
            value={selectedPropertyId}
            onChange={handlePropertyChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={submitting}
          >
            <option value="">-- Select a property --</option>
            {properties.map((property) => (
              <option key={property.propertyId} value={property.propertyId}>
                {property.propertyAddress} - Paid: ${property.amountPaid.toFixed(2)}, Expected: ${property.calculatedTaxAmount.toFixed(2)}
              </option>
            ))}
          </select>
        </div>

        <div className="mb-4">
          <label htmlFor="fiscalYear" className="block text-gray-700 font-medium mb-2">
            Fiscal Year
          </label>
          <select
            id="fiscalYear"
            value={selectedFiscalYearId}
            onChange={handleFiscalYearChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={submitting}
          >
            <option value="">-- Select fiscal year --</option>
            {fiscalYears.map((year) => (
              <option key={year.fiscalYearId} value={year.fiscalYearId}>
                {year.name}
              </option>
            ))}
          </select>
        </div>

        {selectedProperty && (
          <div className="mb-4 p-4 bg-gray-50 rounded-md">
            <h3 className="font-medium mb-2">Property Details</h3>
            <p><strong>Address:</strong> {selectedProperty.propertyAddress}</p>
            <p><strong>Fiscal Year:</strong> {selectedProperty.fiscalYearName}</p>
            <p><strong>Calculated Tax Amount:</strong> ${selectedProperty.calculatedTaxAmount.toFixed(2)}</p>
            <p><strong>Amount Paid:</strong> ${selectedProperty.amountPaid.toFixed(2)}</p>
            <p><strong>Unpaid Balance:</strong> ${selectedProperty.unpaidBalance.toFixed(2)}</p>
          </div>
        )}

        <div className="mb-4">
          <label htmlFor="overriddenValue" className="block text-gray-700 font-medium mb-2">
            Override Assessment Value (Optional)
          </label>
          <input
            type="number"
            id="overriddenValue"
            value={overriddenValue}
            onChange={(e) => setOverriddenValue(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter override amount if needed"
            disabled={submitting}
          />
        </div>

        <div className="mb-4">
          <label htmlFor="overrideReason" className="block text-gray-700 font-medium mb-2">
            Override Reason (Required if overriding value)
          </label>
          <textarea
            id="overrideReason"
            value={overrideReason}
            onChange={(e) => setOverrideReason(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
            placeholder="Explain reason for override"
            disabled={submitting}
          />
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            disabled={submitting}
          >
            {submitting ? 'Creating Assessment...' : 'Create Assessment'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UnderpaidAssessmentForm;
