import React from 'react';
import { useNotifications } from '../context/NotificationContext';

const NotificationBell: React.FC = () => {
  const { notifications, unreadCount, markAsRead, markAllAsRead, deleteNotification } = useNotifications();

  const handleMarkAsRead = async (id: string) => {
    await markAsRead(id);
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  const handleDelete = async (id: string) => {
    await deleteNotification(id);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <span role="img" aria-label="payment">💰</span>;
      case 'appeal':
        return <span role="img" aria-label="appeal">📝</span>;
      case 'negotiation':
        return <span role="img" aria-label="negotiation">🤝</span>;
      case 'registration':
        return <span role="img" aria-label="registration">✅</span>;
      case 'property_submission':
        return <span role="img" aria-label="property submission">🏠</span>;
      case 'assessment_update':
        return <span role="img" aria-label="assessment update">📈</span>;
      case 'assessment':
        return <span role="img" aria-label="assessment">📊</span>;
      case 'system':
      default:
        return <span role="img" aria-label="system">ℹ️</span>;
    }
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const pastDate = new Date(date);
    const diffMs = now.getTime() - pastDate.getTime();
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);

    if (diffSec < 60) return `${diffSec} seconds ago`;
    if (diffMin < 60) return `${diffMin} minutes ago`;
    if (diffHour < 24) return `${diffHour} hours ago`;
    if (diffDay < 30) return `${diffDay} days ago`;
    
    return pastDate.toLocaleDateString();
  };



  return (
    <div className="dropdown dropdown-end">
      <div tabIndex={0} role="button" className="btn btn-ghost btn-circle">
        <div className="indicator">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          {unreadCount > 0 && (
            <span className="badge badge-sm badge-error indicator-item">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </div>
      </div>
      
      <div tabIndex={0} className="dropdown-content z-[1] card card-compact w-80 p-0 shadow bg-base-100 text-base-content">
        <div className="card-body">
          <div className="flex justify-between items-center border-b pb-2">
            <h3 className="card-title text-sm">Notifications</h3>
            {unreadCount > 0 && (
              <button 
                onClick={handleMarkAllAsRead}
                className="btn btn-xs btn-ghost text-info"
              >
                Mark all as read
              </button>
            )}
          </div>
          
          <div className="max-h-80 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-center opacity-70 text-sm">
                No notifications
              </div>
            ) : (
              <div>
                {notifications.map((notification) => (
                  <div key={notification.id} className={`p-3 border-b ${notification.isRead ? '' : 'bg-base-200'}`}>
                    <div className="flex items-start">
                      <div className="mr-3">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-grow">
                        <div className={`text-sm ${notification.isRead ? 'font-normal' : 'font-semibold'}`}>
                          {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                        </div>
                        <div className="text-sm opacity-70">{notification.message}</div>
                        <div className="text-xs opacity-50 mt-1">{formatTimeAgo(notification.createdAt)}</div>
                      </div>
                      <div className="flex space-x-1">
                        {!notification.isRead && (
                          <button
                            onClick={() => handleMarkAsRead(notification.id)}
                            className="btn btn-ghost btn-xs btn-circle text-info"
                            title="Mark as read"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </button>
                        )}
                        <button
                          onClick={() => handleDelete(notification.id)}
                          className="btn btn-ghost btn-xs btn-circle text-error"
                          title="Delete"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationBell;
