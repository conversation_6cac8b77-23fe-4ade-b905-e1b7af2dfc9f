export interface RebateItem {
  id: number;
  fiscalYear: string;
  filingPeriod: string;
  taxPeriod: string;
  totalExemptedAmount: number;
  discountAmount: number;
}

export interface Rebate {
  id: number;
  officeCode: string;
  pan: string;
  accountType: string;
  name: string;
  exemptionDate: string;
  serialNo: string;
  scheme: string;
  isReversal: boolean;
  maNo: string;
  reason: string;
  rebateItems: RebateItem[];
}