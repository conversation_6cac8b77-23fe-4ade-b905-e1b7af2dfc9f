import api from './api';

export interface SpecialPenaltyDto {
  id?: string;
  taxpayerId: string;
  penaltyType: string;
  amount: number;
  reason: string;
  dueDate: string;
  status: string;
  createdAt?: string;
  updatedAt?: string;
  createdById?: string;
  updatedById?: string;
}

export interface CreateSpecialPenaltyDto {
  taxpayerId: string;
  penaltyType: string;
  amount: number;
  reason: string;
  dueDate: string;
  status: string;
}

export interface UpdateSpecialPenaltyDto {
  taxpayerId?: string;
  penaltyType?: string;
  amount?: number;
  reason?: string;
  dueDate?: string;
  status?: string;
}

export const specialPenaltyService = {
  // Get all special penalties
  getSpecialPenalties: async (): Promise<SpecialPenaltyDto[]> => {
    const response = await api.get<SpecialPenaltyDto[]>('/special-penalties');
    return response.data;
  },

  // Get special penalty by ID
  getSpecialPenaltyById: async (id: string): Promise<SpecialPenaltyDto> => {
    const response = await api.get<SpecialPenaltyDto>(`/special-penalties/${id}`);
    return response.data;
  },

  // Create new special penalty
  createSpecialPenalty: async (data: CreateSpecialPenaltyDto): Promise<SpecialPenaltyDto> => {
    const response = await api.post<SpecialPenaltyDto>('/special-penalties', data);
    return response.data;
  },

  // Update special penalty
  updateSpecialPenalty: async (id: string, data: UpdateSpecialPenaltyDto): Promise<SpecialPenaltyDto> => {
    const response = await api.put<SpecialPenaltyDto>(`/special-penalties/${id}`, data);
    return response.data;
  },

  // Delete special penalty
  deleteSpecialPenalty: async (id: string): Promise<void> => {
    await api.delete(`/special-penalties/${id}`);
  },

  // Get special penalties by taxpayer ID
  getSpecialPenaltiesByTaxpayer: async (taxpayerId: string): Promise<SpecialPenaltyDto[]> => {
    const response = await api.get<SpecialPenaltyDto[]>(`/special-penalties/taxpayer/${taxpayerId}`);
    return response.data;
  }
};

export default specialPenaltyService;