import React, { useState, useEffect } from 'react';
import { ropaniToSqM, bighaToSqM } from '../utils/areaConverter';
import type { RopaniSystem, BighaSystem } from '../utils/areaConverter';

export type AreaMeasurement = {
  unitSystem: 'ropani' | 'bigha';
  values: RopaniSystem | BighaSystem;
  squareMeters: number;
};

interface AreaInputProps {
  onChange: (area: AreaMeasurement) => void;
  initialValue?: AreaMeasurement;
}

const AreaInput: React.FC<AreaInputProps> = ({ onChange, initialValue }) => {
  const [unitSystem, setUnitSystem] = useState<'ropani' | 'bigha'>(initialValue?.unitSystem || 'ropani');
  const [ropaniState, setRopaniState] = useState<RopaniSystem>(initialValue?.unitSystem === 'ropani' ? initialValue.values as RopaniSystem : { ropani: 0, aana: 0, paisa: 0, daam: 0 });
  const [bighaState, setBighaState] = useState<BighaSystem>(initialValue?.unitSystem === 'bigha' ? initialValue.values as BighaSystem : { bigha: 0, kattha: 0, dhur: 0 });
  const [squareMeters, setSquareMeters] = useState<number>(initialValue?.squareMeters || 0);

  useEffect(() => {
    let totalSqM = 0;
    let values: RopaniSystem | BighaSystem;
    if (unitSystem === 'ropani') {
      totalSqM = ropaniToSqM(ropaniState);
      values = ropaniState;
    } else {
      totalSqM = bighaToSqM(bighaState);
      values = bighaState;
    }
    setSquareMeters(totalSqM);
    onChange({ unitSystem, values, squareMeters: totalSqM });
  }, [unitSystem, ropaniState, bighaState, onChange]);

  const handleRopaniChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRopaniState(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleBighaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBighaState(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  return (
    <div className="p-4 border rounded-lg">
      <div className="mb-4">
        <span className="text-lg font-semibold">Area Measurement</span>
        <div className="flex items-center mt-2">
          <label className="mr-4">
            <input type="radio" name="unitSystem" value="ropani" checked={unitSystem === 'ropani'} onChange={() => setUnitSystem('ropani')} className="mr-1" />
            Ropani System
          </label>
          <label>
            <input type="radio" name="unitSystem" value="bigha" checked={unitSystem === 'bigha'} onChange={() => setUnitSystem('bigha')} className="mr-1" />
            Bigha System
          </label>
        </div>
      </div>

      {unitSystem === 'ropani' && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div><label className="block text-sm font-medium text-gray-700">Ropani (रोपनी)</label><input type="number" name="ropani" value={ropaniState.ropani} onChange={handleRopaniChange} placeholder="e.g., 10" className="w-full p-2 border rounded mt-1" /></div>
          <div><label className="block text-sm font-medium text-gray-700">Aana (आना)</label><input type="number" name="aana" value={ropaniState.aana} onChange={handleRopaniChange} placeholder="e.g., 4" className="w-full p-2 border rounded mt-1" /></div>
          <div><label className="block text-sm font-medium text-gray-700">Paisa (पैसा)</label><input type="number" name="paisa" value={ropaniState.paisa} onChange={handleRopaniChange} placeholder="e.g., 2" className="w-full p-2 border rounded mt-1" /></div>
          <div><label className="block text-sm font-medium text-gray-700">Daam (दाम)</label><input type="number" name="daam" value={ropaniState.daam} onChange={handleRopaniChange} placeholder="e.g., 1" className="w-full p-2 border rounded mt-1" /></div>
        </div>
      )}

      {unitSystem === 'bigha' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div><label className="block text-sm font-medium text-gray-700">Bigha (बिघा)</label><input type="number" name="bigha" value={bighaState.bigha} onChange={handleBighaChange} placeholder="e.g., 1" className="w-full p-2 border rounded mt-1" /></div>
          <div><label className="block text-sm font-medium text-gray-700">Kattha (कट्ठा)</label><input type="number" name="kattha" value={bighaState.kattha} onChange={handleBighaChange} placeholder="e.g., 10" className="w-full p-2 border rounded mt-1" /></div>
          <div><label className="block text-sm font-medium text-gray-700">Dhur (धुर)</label><input type="number" name="dhur" value={bighaState.dhur} onChange={handleBighaChange} placeholder="e.g., 5" className="w-full p-2 border rounded mt-1" /></div>
        </div>
      )}

      <div className="mt-4 p-2 bg-gray-100 rounded">
        <span className="font-semibold">Total Area: </span>
        <span>{squareMeters.toFixed(2)} sq. meters</span>
      </div>
    </div>
  );
};

export default AreaInput;
