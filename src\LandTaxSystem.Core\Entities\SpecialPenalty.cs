using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LandTaxSystem.Core.Entities
{
    public class SpecialPenalty
    {
        [Key]
        public Guid SpecialPenaltyId { get; set; } = Guid.NewGuid();
        
        [Required]
        [StringLength(50)]
        public string SpecialPenaltyNo { get; set; } = string.Empty;
        
        [Required]
        public string TaxpayerId { get; set; } = string.Empty;
        
        [ForeignKey("TaxpayerId")]
        public virtual ApplicationUser Taxpayer { get; set; } = null!;
        
        [Required]
        [StringLength(50)]
        public string AccountType { get; set; } = string.Empty;
        
        [Required]
        public int TaxYear { get; set; }
        
        [Required]
        [StringLength(10)]
        public string Reason { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? OtherReason { get; set; }
        
        [StringLength(1000)]
        public string? ReasonDetails { get; set; }
        
        [Required]
        public DateTime EffectiveDate { get; set; }
        
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Active"; // Active, Cancelled, Paid
        
        // Audit fields
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        public string? CreatedById { get; set; }
        public string? UpdatedById { get; set; }
        
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser? CreatedBy { get; set; }
        
        [ForeignKey("UpdatedById")]
        public virtual ApplicationUser? UpdatedBy { get; set; }
    }
}