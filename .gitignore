# .NET Core
bin/
obj/
*.user
*.userosscache
*.suo
*.userprefs
.vs/
.vscode/
*.swp
*.*~
project.lock.json
project.fragment.lock.json
artifacts/

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio files
.vs/
*.nupkg
*.pfx
*.snk
*_i.c
*_p.c
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Docker volumes
postgres_data/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# User uploads
uploads/

# IDE-specific files
.idea/
*.sln.iml
.DS_Store

# NuGet
packages/
*.nupkg
*.snupkg
**/packages/*
!**/packages/build/

# Node.js (if you have a frontend)
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Added by Task Master AI
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific