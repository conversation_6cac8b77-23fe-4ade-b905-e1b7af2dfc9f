using System;

namespace LandTaxSystem.Core.DTOs.Assessment
{
    public class AssessmentLineItemDto
    {
        public Guid Id { get; set; }
        public int SerialNumber { get; set; }
        public string TaxDescription { get; set; } = string.Empty;
        public int FiscalYear { get; set; }
        public decimal AssessedAmount { get; set; }
        public decimal PenaltyAmount { get; set; }
        public decimal InterestAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
