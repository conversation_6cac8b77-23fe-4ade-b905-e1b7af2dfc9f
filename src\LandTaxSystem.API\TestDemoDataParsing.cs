using System;
using System.IO;
using System.Threading.Tasks;

namespace LandTaxSystem.API
{
    public class TestDemoDataParsing
    {
        public static async Task TestParsingAsync()
        {
            try
            {
                Console.WriteLine("Testing demo data parsing...");
                
                var parcelsPath = Path.Combine(Directory.GetCurrentDirectory(), "gis", "simulated_parcels.geojson");
                var buildingsPath = Path.Combine(Directory.GetCurrentDirectory(), "gis", "simulated_buildings.geojson");
                var taxRecordsPath = Path.Combine(Directory.GetCurrentDirectory(), "gis", "tax_pay_record.xlsx - Sheet1.csv");
                
                Console.WriteLine($"Checking file paths:");
                Console.WriteLine($"Parcels: {parcelsPath} - {(File.Exists(parcelsPath) ? "Found" : "Not found")}");
                Console.WriteLine($"Buildings: {buildingsPath} - {(File.Exists(buildingsPath) ? "Found" : "Not found")}");
                Console.WriteLine($"Tax Records: {taxRecordsPath} - {(File.Exists(taxRecordsPath) ? "Found" : "Not found")}");
                
                if (!File.Exists(parcelsPath) || !File.Exists(buildingsPath) || !File.Exists(taxRecordsPath))
                {
                    Console.WriteLine("Some files are missing. Cannot proceed with parsing test.");
                    return;
                }
                
                // Test parsing parcels data
                Console.WriteLine("\nTesting parcels data parsing...");
                var json = await File.ReadAllTextAsync(parcelsPath);
                Console.WriteLine($"Parcels file size: {json.Length} characters");
                
                // Test parsing buildings data
                Console.WriteLine("\nTesting buildings data parsing...");
                var buildingsJson = await File.ReadAllTextAsync(buildingsPath);
                Console.WriteLine($"Buildings file size: {buildingsJson.Length} characters");
                
                // Test parsing tax records
                Console.WriteLine("\nTesting tax records parsing...");
                var taxLines = await File.ReadAllLinesAsync(taxRecordsPath);
                Console.WriteLine($"Tax records file has {taxLines.Length} lines");
                
                if (taxLines.Length > 0)
                {
                    Console.WriteLine($"Header: {taxLines[0]}");
                    if (taxLines.Length > 1)
                    {
                        Console.WriteLine($"First data row: {taxLines[1]}");
                    }
                }
                
                Console.WriteLine("\nParsing test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during parsing test: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
