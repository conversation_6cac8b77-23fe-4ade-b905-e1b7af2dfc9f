using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Enums;
using LandTaxSystem.Core.Services;
using LandTaxSystem.Infrastructure.Data;

namespace LandTaxSystem.Infrastructure.Services
{
    public class PreliminaryAssessmentStatusService : IPreliminaryAssessmentStatusService
    {
        private readonly ApplicationDbContext _context;
        private readonly IPreliminaryAssessmentValidationService _validationService;
        
        public PreliminaryAssessmentStatusService(
            ApplicationDbContext context,
            IPreliminaryAssessmentValidationService validationService)
        {
            _context = context;
            _validationService = validationService;
        }
        
        public async Task<StatusUpdateResult> UpdateStatusAsync(Guid assessmentId, PreliminaryAssessmentStatus newStatus, string userId, string? reason = null)
        {
            var assessment = await _context.PreliminaryAssessments.FindAsync(assessmentId);
            if (assessment == null)
                return StatusUpdateResult.FailureResult("Assessment not found");
            
            if (!Enum.TryParse<PreliminaryAssessmentStatus>(assessment.Status, out var currentStatus))
                return StatusUpdateResult.FailureResult("Invalid current status");
            
            if (currentStatus == newStatus)
                return StatusUpdateResult.FailureResult("Assessment is already in the requested status");
            
            // Validate the status transition
            var validationResult = await _validationService.ValidateStatusTransitionAsync(assessmentId, newStatus, userId);
            if (!validationResult.IsValid)
                return StatusUpdateResult.FailureResult(string.Join("; ", validationResult.Errors));
            
            // Update the status
            assessment.Status = newStatus.ToString();
            assessment.UpdatedAt = DateTime.UtcNow;
            assessment.UpdatedBy = userId;
            
            // Create status history record
            var statusHistory = new PreliminaryAssessmentStatusHistory
            {
                Id = Guid.NewGuid(),
                PreliminaryAssessmentId = assessmentId,
                FromStatus = currentStatus.ToString(),
                ToStatus = newStatus.ToString(),
                ChangedBy = userId,
                ChangedAt = DateTime.UtcNow,
                Reason = reason
            };
            
            _context.PreliminaryAssessmentStatusHistories.Add(statusHistory);
            await _context.SaveChangesAsync();
            
            return StatusUpdateResult.SuccessResult(currentStatus, newStatus, userId);
        }
        
        public async Task<PreliminaryAssessmentStatus?> GetStatusAsync(Guid assessmentId)
        {
            var assessment = await _context.PreliminaryAssessments.FindAsync(assessmentId);
            if (assessment == null)
                return null;
            
            return Enum.TryParse<PreliminaryAssessmentStatus>(assessment.Status, out var status) ? status : null;
        }
        
        public async Task<PreliminaryAssessmentStatus[]> GetPossibleNextStatusesAsync(Guid assessmentId)
        {
            var currentStatus = await GetStatusAsync(assessmentId);
            if (!currentStatus.HasValue)
                return Array.Empty<PreliminaryAssessmentStatus>();
            
            var allStatuses = Enum.GetValues<PreliminaryAssessmentStatus>();
            return allStatuses.Where(status => currentStatus.Value.CanTransitionTo(status)).ToArray();
        }
        
        public async Task<StatusUpdateResult> SubmitForReviewAsync(Guid assessmentId, string userId)
        {
            // Additional validation for submission
            var assessment = await _context.PreliminaryAssessments
                .Include(a => a.TaxDetails)
                .FirstOrDefaultAsync(a => a.Id == assessmentId);
            
            if (assessment == null)
                return StatusUpdateResult.FailureResult("Assessment not found");
            
            if (!assessment.TaxDetails.Any())
                return StatusUpdateResult.FailureResult("Cannot submit assessment without tax details");
            
            if (string.IsNullOrWhiteSpace(assessment.TaxpayerName))
                return StatusUpdateResult.FailureResult("Taxpayer name is required for submission");
            
            return await UpdateStatusAsync(assessmentId, PreliminaryAssessmentStatus.PendingReview, userId, "Submitted for review");
        }
        
        public async Task<StatusUpdateResult> StartReviewAsync(Guid assessmentId, string userId)
        {
            return await UpdateStatusAsync(assessmentId, PreliminaryAssessmentStatus.UnderReview, userId, "Review started");
        }
        
        public async Task<StatusUpdateResult> ApproveAsync(Guid assessmentId, string userId, string? approvalNotes = null)
        {
            var reason = string.IsNullOrWhiteSpace(approvalNotes) ? "Assessment approved" : $"Assessment approved: {approvalNotes}";
            return await UpdateStatusAsync(assessmentId, PreliminaryAssessmentStatus.Approved, userId, reason);
        }
        
        public async Task<StatusUpdateResult> RejectAsync(Guid assessmentId, string userId, string rejectionReason)
        {
            if (string.IsNullOrWhiteSpace(rejectionReason))
                return StatusUpdateResult.FailureResult("Rejection reason is required");
            
            return await UpdateStatusAsync(assessmentId, PreliminaryAssessmentStatus.Rejected, userId, $"Assessment rejected: {rejectionReason}");
        }
        
        public async Task<StatusUpdateResult> FinalizeAsync(Guid assessmentId, string userId)
        {
            // Additional validation for finalization
            var hasPendingFinalAssessments = await _context.FinalAssessments
                .AnyAsync(fa => fa.PreliminaryAssessmentId == assessmentId && fa.Status != "Completed");
            
            if (hasPendingFinalAssessments)
                return StatusUpdateResult.FailureResult("Cannot finalize assessment with pending final assessments");
            
            return await UpdateStatusAsync(assessmentId, PreliminaryAssessmentStatus.Finalized, userId, "Assessment finalized");
        }
        
        public async Task<StatusUpdateResult> CancelAsync(Guid assessmentId, string userId, string cancellationReason)
        {
            if (string.IsNullOrWhiteSpace(cancellationReason))
                return StatusUpdateResult.FailureResult("Cancellation reason is required");
            
            // Check if assessment can be cancelled
            var currentStatus = await GetStatusAsync(assessmentId);
            if (currentStatus == PreliminaryAssessmentStatus.Finalized)
                return StatusUpdateResult.FailureResult("Cannot cancel a finalized assessment");
            
            return await UpdateStatusAsync(assessmentId, PreliminaryAssessmentStatus.Cancelled, userId, $"Assessment cancelled: {cancellationReason}");
        }
    }
}