using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.Entities
{
    public class Province
    {
        public Guid ProvinceId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public virtual ICollection<District> Districts { get; set; } = new List<District>();
    }
}
