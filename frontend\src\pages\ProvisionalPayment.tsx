import React, { useState, useEffect } from "react";
import { usePara<PERSON>, Link } from "react-router-dom";
import { paymentService, propertyService } from "../services/api";
import type { Property } from "../types";

// Extend the Property type to include optional estimatedTax
interface PropertyWithEstimatedTax extends Property {
  estimatedTax?: number;
}

const ProvisionalPayment: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  const [property, setProperty] = useState<PropertyWithEstimatedTax | null>(null);
  const [amount, setAmount] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState<boolean>(false);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const [error, setError] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [estimatedTax, setEstimatedTax] = useState<number | null>(null);

  useEffect(() => {
    if (!id) {
      setError("Property ID is missing. Cannot load payment data.");
      setLoading(false);
      return;
    }

    const loadPropertyData = async () => {
      setLoading(true);
      setError("");
      
      try {
        const propertyDetails = await propertyService.getById(id);
        
        // Cast to our extended type
        const propertyWithEstTax = propertyDetails as PropertyWithEstimatedTax;
        setProperty(propertyWithEstTax);
        
        // Set estimated tax if available from property data
        if (propertyWithEstTax.estimatedTax) {
          setEstimatedTax(propertyWithEstTax.estimatedTax);
          setAmount(propertyWithEstTax.estimatedTax);
        } else if (propertyDetails.taxDue) {
          setEstimatedTax(propertyDetails.taxDue);
          setAmount(propertyDetails.taxDue);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
        setError(`Failed to load property details: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    loadPropertyData();
  }, [id]);

  const handlePayment = async () => {
    if (!property || !id) {
      setError("Property information is missing. Please refresh the page.");
      return;
    }

    if (amount <= 0) {
      setError("Please enter a valid payment amount greater than 0.");
      return;
    }

    setProcessing(true);
    setError("");

    try {
      // Use the provisional payment service
      const response = await paymentService.initiateProvisionalPayment(
        id,
        amount,
        paymentMethod
      );
      
      console.log('Provisional payment response:', response);
      
      // Store the payment ID for receipt download
      if (response.internalPaymentId) {
        localStorage.setItem('lastPaymentId', response.internalPaymentId);
      }
      // Generate transaction ID for display
      setTransactionId(`TXN_${Date.now()}`);
      
      setPaymentSuccess(true);
    } catch (error: unknown) {
      setError(
        error instanceof Error
          ? error.message
          : "Payment processing failed. Please try again."
      );
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `NPR ${amount.toLocaleString()}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    );
  }

  if (error && !paymentSuccess) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="alert alert-error">
          <h2 className="text-xl font-semibold mb-2">
            Error Processing Payment
          </h2>
          <p>{error}</p>
          <Link to={`/property/${id}`} className="btn btn-primary mt-4 inline-block">
            Back to Property
          </Link>
        </div>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="alert alert-success">
          <div className="text-center">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-success/20 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  ></path>
                </svg>
              </div>
            </div>
            <h2 className="text-2xl font-bold mb-2">
              Payment Successful!
            </h2>
            <p className="mb-1">
              Your payment of {formatCurrency(amount)} has been processed successfully.
            </p>
            <p className="mb-4">
              Transaction ID: {transactionId}
            </p>
            <div className="mt-6 space-y-3">
              <p className="text-base-content/70">
                This is a provisional payment. An officer will review your property and create an assessment.
                You may need to make additional payments if the assessed tax amount is higher than your payment.
              </p>
              <div className="flex justify-center space-x-4 mt-6">
                <Link
                  to={`/property/${id}`}
                  className="btn btn-secondary"
                >
                  Back to Property
                </Link>
                {localStorage.getItem('lastPaymentId') && (
                  <Link
                    to={`/payment-receipt/${localStorage.getItem('lastPaymentId')}`}
                    className="btn btn-primary"
                  >
                    View Receipt
                  </Link>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h1 className="card-title text-2xl">Make Provisional Payment</h1>
        </div>
        <div className="card-body pt-0">
          {property && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2">
                Property Information
              </h2>
              <div className="bg-base-200 p-4 rounded-lg">
                <p className="text-base-content">
                  <span className="font-medium">Address:</span> {property.address}
                </p>
                <p className="text-base-content">
                  <span className="font-medium">Property ID:</span> {property.id}
                </p>
                {property.parcelNumber && (
                  <p className="text-base-content">
                    <span className="font-medium">Parcel Number:</span> {property.parcelNumber}
                  </p>
                )}
              </div>
            </div>
          )}

          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">
              Payment Details
            </h2>
            <div className="alert alert-info mb-4">
              <p>
                This is a provisional payment. No assessment has been created yet.
                An officer will review your property and create an assessment.
                You may need to make additional payments if the assessed tax amount is higher than your payment.
              </p>
            </div>

            {estimatedTax !== null && (
              <div className="mb-4">
                <p className="text-base-content">
                  <span className="font-medium">Estimated Tax Amount:</span>{" "}
                  {formatCurrency(estimatedTax)}
                </p>
              </div>
            )}

            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">Payment Amount (NPR)</span>
              </label>
              <input
                type="number"
                id="amount"
                value={amount}
                onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                className="input input-bordered"
                min="1"
                step="0.01"
                disabled={processing}
              />
            </div>

            <div className="form-control mb-6">
              <label className="label">
                <span className="label-text">Payment Method</span>
              </label>
              <div className="grid grid-cols-3 gap-4">
                <div
                  className={`border rounded-md p-3 text-center cursor-pointer ${
                    paymentMethod === "card"
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  onClick={() => setPaymentMethod("card")}
                >
                  <div className="font-medium">Credit/Debit Card</div>
                </div>
                <div
                  className={`border rounded-md p-3 text-center cursor-pointer ${
                    paymentMethod === "bank"
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  onClick={() => setPaymentMethod("bank")}
                >
                  <div className="font-medium">Bank Transfer</div>
                </div>
                <div
                  className={`border rounded-md p-3 text-center cursor-pointer ${
                    paymentMethod === "digital"
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  onClick={() => setPaymentMethod("digital")}
                >
                  <div className="font-medium">Digital Wallet</div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <Link
              to={`/property/${id}`}
              className="btn btn-secondary"
            >
              Cancel
            </Link>
            <button
              onClick={handlePayment}
              disabled={processing || amount <= 0}
              className="btn btn-primary"
            >
              {processing ? "Processing..." : `Pay ${formatCurrency(amount)}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProvisionalPayment;
