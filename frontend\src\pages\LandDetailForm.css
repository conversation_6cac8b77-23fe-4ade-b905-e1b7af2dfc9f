.land-detail-form-container {
  width: 100%;
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 0;
  font-family: inherit;
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
}

fieldset {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

legend {
  font-weight: bold;
  color: #555;
  padding: 0 10px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.form-grid-colspan-2 {
  grid-column: span 2;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 0.9em;
}

input[type="text"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.9em;
}

textarea {
  resize: vertical;
}

.checkbox-group,
.radio-group {
  margin-top: 10px;
  margin-bottom: 15px;
}

.checkbox-group label,
.radio-group label {
  font-weight: normal;
  margin-right: 15px;
  display: inline-flex;
  align-items: center;
}

.checkbox-group input[type="checkbox"],
.radio-group input[type="radio"] {
  margin-right: 5px;
}

.area-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  border: 1px dashed #eee;
  border-radius: 4px;
}

.area-inputs div {
  display: flex;
  flex-direction: column;
}

.area-inputs label {
  font-size: 0.8em;
  margin-bottom: 2px;
}

.form-actions {
  text-align: right;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}

button:hover {
  background-color: #0056b3;
}

button[type="button"] {
  background-color: #6c757d;
  margin-right: 10px;
}

button[type="button"]:hover {
  background-color: #545b62;
}
