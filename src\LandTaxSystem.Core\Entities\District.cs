using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.Entities
{
    public class District
    {
        public Guid DistrictId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        
        // Foreign key
        public Guid ProvinceId { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public virtual Province Province { get; set; } = null!;
        public virtual ICollection<Municipality> Municipalities { get; set; } = new List<Municipality>();
    }
}
