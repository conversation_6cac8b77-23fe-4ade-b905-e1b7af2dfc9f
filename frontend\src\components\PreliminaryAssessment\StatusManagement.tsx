import React, { useState } from "react";
import { PreliminaryAssessmentStatus } from "../../types/preliminaryAssessment";
import preliminaryAssessmentService from "../../services/preliminaryAssessmentService";

// Local interfaces for status management
interface StatusUpdateRequest {
  status: string;
  reason?: string;
}

interface ApprovalRequest {
  notes?: string;
}

interface RejectionRequest {
  reason: string;
}

interface StatusManagementProps {
  assessmentId: string;
  currentStatus: string;
  onStatusUpdate: (newStatus: string) => void;
  onError: (message: string) => void;
}

export const StatusManagement: React.FC<StatusManagementProps> = ({
  assessmentId,
  currentStatus,
  onStatusUpdate,
  onError,
}) => {
  const [loading, setLoading] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [approvalNotes, setApprovalNotes] = useState("");

  const getStatusColor = (status: string): string => {
    switch (status) {
      case PreliminaryAssessmentStatus.Draft:
        return "bg-gray-100 text-gray-800";
      case PreliminaryAssessmentStatus.PendingReview:
        return "bg-yellow-100 text-yellow-800";
      case PreliminaryAssessmentStatus.UnderReview:
        return "bg-blue-100 text-blue-800";
      case PreliminaryAssessmentStatus.Approved:
        return "bg-green-100 text-green-800";
      case PreliminaryAssessmentStatus.Rejected:
        return "bg-red-100 text-red-800";
      case PreliminaryAssessmentStatus.Finalized:
        return "bg-purple-100 text-purple-800";
      case PreliminaryAssessmentStatus.Cancelled:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getAvailableActions = (status: string): string[] => {
    switch (status) {
      case PreliminaryAssessmentStatus.Draft:
        return ["Submit for Review"];
      case PreliminaryAssessmentStatus.PendingReview:
        return ["Start Review", "Cancel"];
      case PreliminaryAssessmentStatus.UnderReview:
        return ["Approve", "Reject"];
      case PreliminaryAssessmentStatus.Approved:
        return ["Finalize"];
      default:
        return [];
    }
  };

  const handleAction = async (action: string) => {
    if (loading) return;

    try {
      setLoading(true);
      let result;

      switch (action) {
        case "Submit for Review":
          result = await preliminaryAssessmentService.submitForReview(assessmentId);
          break;
        case "Start Review": {
          const startReviewRequest: StatusUpdateRequest = {
            status: PreliminaryAssessmentStatus.UnderReview,
            reason: "Review started",
          };
          result = await preliminaryAssessmentService.updateStatus(assessmentId, startReviewRequest);
          break;
        }
        case "Approve":
          setShowApproveModal(true);
          return;
        case "Reject":
          setShowRejectModal(true);
          return;
        case "Finalize":
          result = await preliminaryAssessmentService.updateStatus(assessmentId, {
            status: PreliminaryAssessmentStatus.Finalized,
            reason: "Assessment finalized",
          });
          break;
        case "Cancel":
          result = await preliminaryAssessmentService.updateStatus(assessmentId, {
            status: PreliminaryAssessmentStatus.Cancelled,
            reason: "Assessment cancelled",
          });
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }

      if (result?.success && result.newStatus) {
        onStatusUpdate(result.newStatus);
      } else {
        onError(result?.message || "Status update failed");
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    try {
      setLoading(true);
      const approvalRequest: ApprovalRequest = {
        notes: approvalNotes,
      };
      const result = await preliminaryAssessmentService.approve(assessmentId, approvalRequest);

      if (result?.success && result.newStatus) {
        onStatusUpdate(result.newStatus);
        setShowApproveModal(false);
        setApprovalNotes("");
      } else {
        onError(result?.message || "Approval failed");
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      onError("Rejection reason is required");
      return;
    }

    try {
      setLoading(true);
      const rejectionRequest: RejectionRequest = {
        reason: rejectReason,
      };
      const result = await preliminaryAssessmentService.reject(assessmentId, rejectionRequest);

      if (result?.success && result.newStatus) {
        onStatusUpdate(result.newStatus);
        setShowRejectModal(false);
        setRejectReason("");
      } else {
        onError(result?.message || "Rejection failed");
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const availableActions = getAvailableActions(currentStatus);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <h3 className="text-lg font-semibold mb-4">Status Management</h3>

      {/* Current Status */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Current Status</label>
        <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(currentStatus)}`}>
          {currentStatus}
        </span>
      </div>

      {/* Available Actions */}
      {availableActions.length > 0 && (
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Available Actions</label>
          <div className="flex flex-wrap gap-2">
            {availableActions.map(action => (
              <button
                key={action}
                onClick={() => handleAction(action)}
                disabled={loading}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  action === "Approve"
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : action === "Reject"
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : action === "Cancel"
                    ? "bg-gray-600 hover:bg-gray-700 text-white"
                    : "bg-blue-600 hover:bg-blue-700 text-white"
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {loading ? "Processing..." : action}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Approval Modal */}
      {showApproveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h4 className="text-lg font-semibold mb-4">Approve Assessment</h4>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Approval Notes (Optional)</label>
              <textarea
                value={approvalNotes}
                onChange={e => setApprovalNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Add any notes about the approval..."
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowApproveModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleApprove}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? "Approving..." : "Approve"}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h4 className="text-lg font-semibold mb-4">Reject Assessment</h4>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Rejection Reason *</label>
              <textarea
                value={rejectReason}
                onChange={e => setRejectReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Please provide a reason for rejection..."
                required
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button onClick={() => setShowRejectModal(false)} className="px-4 py-2 text-gray-600 hover:text-gray-800">
                Cancel
              </button>
              <button
                onClick={handleReject}
                disabled={loading || !rejectReason.trim()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {loading ? "Rejecting..." : "Reject"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StatusManagement;