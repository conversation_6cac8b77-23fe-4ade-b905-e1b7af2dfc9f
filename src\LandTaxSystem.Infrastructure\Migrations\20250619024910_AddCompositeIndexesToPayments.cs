﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddCompositeIndexesToPayments : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payments_FiscalYears_FiscalYearId",
                table: "Payments");

            migrationBuilder.DropForeignKey(
                name: "FK_Payments_Properties_PropertyId",
                table: "Payments");

            migrationBuilder.DropIndex(
                name: "IX_Payments_PropertyId",
                table: "Payments");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_PropertyId_FiscalYearId",
                table: "Payments",
                columns: new[] { "PropertyId", "FiscalYearId" });

            migrationBuilder.CreateIndex(
                name: "IX_Payments_Provisional",
                table: "Payments",
                column: "Provisional");

            migrationBuilder.AddForeignKey(
                name: "FK_Payments_FiscalYears_FiscalYearId",
                table: "Payments",
                column: "FiscalYearId",
                principalTable: "FiscalYears",
                principalColumn: "FiscalYearId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Payments_Properties_PropertyId",
                table: "Payments",
                column: "PropertyId",
                principalTable: "Properties",
                principalColumn: "PropertyId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Payments_FiscalYears_FiscalYearId",
                table: "Payments");

            migrationBuilder.DropForeignKey(
                name: "FK_Payments_Properties_PropertyId",
                table: "Payments");

            migrationBuilder.DropIndex(
                name: "IX_Payments_PropertyId_FiscalYearId",
                table: "Payments");

            migrationBuilder.DropIndex(
                name: "IX_Payments_Provisional",
                table: "Payments");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_PropertyId",
                table: "Payments",
                column: "PropertyId");

            migrationBuilder.AddForeignKey(
                name: "FK_Payments_FiscalYears_FiscalYearId",
                table: "Payments",
                column: "FiscalYearId",
                principalTable: "FiscalYears",
                principalColumn: "FiscalYearId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Payments_Properties_PropertyId",
                table: "Payments",
                column: "PropertyId",
                principalTable: "Properties",
                principalColumn: "PropertyId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
