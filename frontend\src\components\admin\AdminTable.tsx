import { type ReactNode } from 'react';

export interface Column<T> {
  header: string;
  accessor: keyof T | ((item: T) => ReactNode);
  className?: string;
  headerClassName?: string;
}

interface AdminTableProps<T> {
  data: T[];
  columns: Column<T>[];
  keyField: keyof T;
  isLoading?: boolean;
  emptyMessage?: string;
  className?: string;
  zebra?: boolean;
  compact?: boolean;
  hover?: boolean;
  actions?: (item: T) => ReactNode;
  onRowClick?: (item: T) => void;
  rowClassName?: (item: T) => string;
}

function AdminTable<T>({
  data,
  columns,
  keyField,
  isLoading = false,
  emptyMessage = "No data available",
  className = "",
  zebra = true,
  compact = false,
  hover = true,
  actions,
  onRowClick,
  rowClassName
}: AdminTableProps<T>) {
  // Determine table classes
  const tableClasses = [
    'table',
    'w-full',
    zebra ? 'table-zebra' : '',
    compact ? 'table-xs' : '',
    hover ? 'table-hover' : '',
    className
  ].filter(Boolean).join(' ');

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full flex justify-center items-center py-12">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className="w-full py-12 text-center">
        <div className="text-base-content/50 text-lg">{emptyMessage}</div>
      </div>
    );
  }

  // Render cell content based on accessor type
  const renderCell = (item: T, accessor: Column<T>['accessor']): ReactNode => {
    if (typeof accessor === 'function') {
      return accessor(item);
    }
    
    const value = item[accessor];
    
    // Handle null/undefined values
    if (value === null || value === undefined) {
      return <span className="text-base-content/30">—</span>;
    }
    
    // Convert primitive values to string for ReactNode compatibility
    return String(value);
  };

  return (
    <div className="overflow-x-auto">
      <table className={tableClasses}>
        <thead>
          <tr>
            {columns.map((column, index) => (
              <th key={index} className={column.headerClassName}>
                {column.header}
              </th>
            ))}
            {actions && <th className="text-right">Actions</th>}
          </tr>
        </thead>
        <tbody>
          {data.map((item) => (
            <tr 
              key={String(item[keyField])} 
              className={`${onRowClick ? 'cursor-pointer' : ''} ${rowClassName ? rowClassName(item) : ''}`}
              onClick={onRowClick ? () => onRowClick(item) : undefined}
            >
              {columns.map((column, index) => (
                <td key={index} className={column.className}>
                  {renderCell(item, column.accessor)}
                </td>
              ))}
              {actions && (
                <td className="text-right">
                  {actions(item)}
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default AdminTable;