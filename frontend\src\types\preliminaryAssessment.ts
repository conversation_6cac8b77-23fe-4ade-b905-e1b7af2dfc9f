// Status constants for preliminary assessments
export const PreliminaryAssessmentStatus = {
  Draft: 'Draft',
  PendingReview: 'PendingReview',
  UnderReview: 'UnderReview',
  Approved: 'Approved',
  Rejected: 'Rejected',
  Finalized: 'Finalized',
  Cancelled: 'Cancelled'
} as const;

export type PreliminaryAssessmentStatus = typeof PreliminaryAssessmentStatus[keyof typeof PreliminaryAssessmentStatus];

// Status management interfaces
export interface StatusUpdateRequest {
  status: string;
  reason?: string;
}

export interface ApprovalRequest {
  notes?: string;
}

export interface RejectionRequest {
  reason: string;
}

export interface StatusUpdateResult {
  success: boolean;
  message: string;
  newStatus?: string;
  errors?: string[];
}

export interface PreliminaryAssessmentDetailDto {
  id?: string;
  serialNumber: number;
  filingPeriod: string;
  period: string;
  taxYear: string;
  assessedAmount: number;
  penalty: number;
  additionalAmount: number;
  interest: number;
  total: number;
  returnFilingId?: string;
}

export interface PreliminaryAssessmentDto {
  id: string;
  taxpayerRegistration: string;
  taxpayerName: string;
  address?: string;
  phone?: string;
  accountNumber?: string;
  fiscalYearId: string;
  municipalityId: string;
  assessmentPeriodFrom: Date;
  assessmentPeriodTo: Date;
  actSection?: string;
  rule?: string;
  bank?: string;
  branch?: string;
  reasonForAssessment?: string;
  appealNumber?: string;
  otherReasonDescription?: string;
  interestCalculationDate?: Date;
  preliminaryAssessmentDate?: Date;
  reason?: string;
  regulations?: string;
  taxDetails: PreliminaryAssessmentDetailDto[];
  grandTotal: number;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  email?: string;
  mobileNumber?: string;
  assessmentPeriod?: string;
}

export interface PreliminaryAssessmentCreateDto {
  taxpayerRegistration: string;
  taxpayerName: string;
  address?: string;
  phone?: string;
  accountNumber?: string;
  fiscalYearId: string;
  municipalityId: string;
  returnFilingId?: string;
  assessmentPeriodFrom: Date;
  assessmentPeriodTo: Date;
  actSection?: string;
  rule?: string;
  bank?: string;
  branch?: string;
  reasonForAssessment?: string;
  appealNumber?: string;
  otherReasonDescription?: string;
  interestCalculationDate?: Date;
  preliminaryAssessmentDate?: Date;
  reason?: string;
  regulations?: string;
  taxDetails: PreliminaryAssessmentDetailDto[];
  email?: string;
  mobileNumber?: string;
  assessmentPeriod?: string;
}

export interface PreliminaryAssessmentUpdateDto {
  taxpayerRegistration?: string;
  taxpayerName?: string;
  address?: string;
  phone?: string;
  accountNumber?: string;
  fiscalYearId?: string;
  municipalityId?: string;
  returnFilingId?: string;
  assessmentPeriodFrom?: Date;
  assessmentPeriodTo?: Date;
  actSection?: string;
  rule?: string;
  bank?: string;
  branch?: string;
  reasonForAssessment?: string;
  appealNumber?: string;
  otherReasonDescription?: string;
  interestCalculationDate?: Date;
  preliminaryAssessmentDate?: Date;
  reason?: string;
  regulations?: string;
  taxDetails?: PreliminaryAssessmentDetailDto[];
  email?: string;
  mobileNumber?: string;
  assessmentPeriod?: string;
}

export interface PreliminaryAssessmentListResponse {
  data: PreliminaryAssessmentDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface PreliminaryAssessmentSearchParams {
  pageNumber?: number;
  pageSize?: number;
  taxpayerRegistration?: string;
  taxpayerName?: string;
  fiscalYearId?: string;
  municipalityId?: string;
  fromDate?: Date;
  toDate?: Date;
}
