import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import type { FinalAssessmentDto, FinalAssessmentDetailDto } from '../../types/finalAssessment';
import finalAssessmentService from '../../services/finalAssessmentService';
import AdminLayout from '../admin/AdminLayout';
import AdminTable, { type Column } from '../admin/AdminTable';
import { ConfirmDialog } from '../common/ConfirmDialog';

export const FinalAssessmentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [assessment, setAssessment] = useState<FinalAssessmentDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [generatingPdf, setGeneratingPdf] = useState(false);

  const loadAssessment = useCallback(async (assessmentId: string) => {
    try {
      setLoading(true);
      const data = await finalAssessmentService.getById(assessmentId);
      setAssessment(data);
    } catch (error) {
      toast.error('Failed to load final assessment');
      console.error('Error loading assessment:', error);
      navigate('/final-assessments');
    } finally {
      setLoading(false);
    }
  }, [navigate]);

  useEffect(() => {
    if (id) {
      loadAssessment(id);
    }
  }, [id, loadAssessment]);

  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await finalAssessmentService.delete(id);
      toast.success('Final assessment deleted successfully');
      navigate('/final-assessments');
    } catch (error) {
      toast.error('Failed to delete final assessment');
      console.error('Error deleting assessment:', error);
    }
    setDeleteConfirm(false);
  };

  const handleGeneratePdf = async () => {
    if (!id || !assessment) return;
    
    try {
      setGeneratingPdf(true);
      const blob = await finalAssessmentService.generatePdfReport(id);
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `final-assessment-${assessment.taxpayerName.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('PDF generated successfully');
    } catch (error) {
      toast.error('Failed to generate PDF');
      console.error('Error generating PDF:', error);
    } finally {
      setGeneratingPdf(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!assessment) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Assessment Not Found</h3>
          <p className="text-gray-600 mb-4">The requested final assessment could not be found.</p>
          <Link
            to="/final-assessments"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Assessments
          </Link>
        </div>
      </AdminLayout>
    );
  }

  const taxDetailColumns: Column<FinalAssessmentDetailDto>[] = [
    { 
      header: 'S.N.', 
      accessor: 'serialNumber' as keyof FinalAssessmentDetailDto
    },
    {
            header: 'Fiscal Year',
            accessor: 'filingPeriod' as keyof FinalAssessmentDetailDto
        },
        {
            header: 'Fiscal Year Name',
            accessor: 'period' as keyof FinalAssessmentDetailDto
        },
    { 
      header: 'Tax Year', 
      accessor: 'taxYear' as keyof FinalAssessmentDetailDto
    },
    { 
      header: 'Assessed Amount', 
      accessor: (item: FinalAssessmentDetailDto) => `Rs. ${item.assessedAmount.toLocaleString()}`
    },
    { 
      header: 'Penalty Amount', 
      accessor: (item: FinalAssessmentDetailDto) => `Rs. ${item.penalty.toLocaleString()}`
    },
    { 
      header: 'Additional Amount', 
      accessor: (item: FinalAssessmentDetailDto) => `Rs. ${item.additionalAmount.toLocaleString()}`
    },
    { 
      header: 'Interest Amount', 
      accessor: (item: FinalAssessmentDetailDto) => `Rs. ${item.interest.toLocaleString()}`
    },
    { 
      header: 'Total Amount', 
      accessor: (item: FinalAssessmentDetailDto) => `Rs. ${item.total.toLocaleString()}`
    }
  ];

  const grandTotal = assessment.grandTotal || 0;

  return (
    <AdminLayout 
      title={`Assessment: ${assessment.taxpayerRegistration}`}
      subtitle="Final Assessment Details"
    >
      
      {/* Action Buttons */}
      <div className="mb-6 flex flex-wrap gap-2">
        <button
          onClick={handleGeneratePdf}
          disabled={generatingPdf}
          className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {generatingPdf ? 'Generating...' : 'Download PDF'}
        </button>
        <Link
          to={`/final-assessments/${assessment.id}/edit`}
          className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit
        </Link>
        <button
          onClick={() => setDeleteConfirm(true)}
          className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Delete
        </button>
        <Link
          to="/final-assessments"
          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to List
        </Link>
      </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Taxpayer Registration</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.taxpayerRegistration}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Taxpayer Name</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.taxpayerName}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.address || 'N/A'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Phone</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.phone || 'N/A'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Account Number</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.accountNumber || 'N/A'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                assessment.status === 'Active' ? 'bg-green-100 text-green-800' :
                assessment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {assessment.status}
              </span>
            </div>
          </div>
        </div>

        {/* Assessment Period */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Period</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Period From</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.assessmentPeriodFrom).toLocaleDateString()}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Period To</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.assessmentPeriodTo).toLocaleDateString()}
              </p>
            </div>
            {assessment.finalAssessmentDate && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Final Assessment Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(assessment.finalAssessmentDate).toLocaleDateString()}
                </p>
              </div>
            )}
            {assessment.interestCalculationDate && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Interest Calculation Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(assessment.interestCalculationDate).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Assessment Details */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {assessment.actSection && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Act Section</label>
                <p className="mt-1 text-sm text-gray-900">{assessment.actSection}</p>
              </div>
            )}
            {assessment.rule && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Rule</label>
                <p className="mt-1 text-sm text-gray-900">{assessment.rule}</p>
              </div>
            )}
            {assessment.reasonForAssessment && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Reason for Assessment</label>
                <p className="mt-1 text-sm text-gray-900">{assessment.reasonForAssessment}</p>
              </div>
            )}
            {assessment.appealNumber && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Appeal Number</label>
                <p className="mt-1 text-sm text-gray-900">{assessment.appealNumber}</p>
              </div>
            )}
          </div>
          {assessment.reason && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700">Reason</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.reason}</p>
            </div>
          )}
          {assessment.regulations && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700">Regulations</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.regulations}</p>
            </div>
          )}
        </div>

        {/* Tax Details */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Tax Details</h3>
          </div>
          <div className="p-6">
            <AdminTable
              columns={taxDetailColumns}
              data={assessment.taxDetails}
              keyField="serialNumber"
              isLoading={false}
              emptyMessage="No tax details found"
            />
            
            {/* Grand Total */}
            <div className="mt-6 border-t border-gray-200 pt-4">
              <div className="flex justify-end">
                <div className="text-right">
                  <p className="text-lg font-semibold text-gray-900">
                    Grand Total: Rs. {grandTotal.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Timestamps</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Created At</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.createdAt).toLocaleString()}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Updated At</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.updatedAt).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteConfirm}
        title="Delete Final Assessment"
        message={`Are you sure you want to delete the final assessment "${assessment.taxpayerRegistration} - ${assessment.taxpayerName}"? This action cannot be undone.`}
        confirmLabel="Delete"
        cancelLabel="Cancel"
        onConfirm={handleDelete}
        onCancel={() => setDeleteConfirm(false)}
        type="danger"
      />
    </AdminLayout>
  );
};