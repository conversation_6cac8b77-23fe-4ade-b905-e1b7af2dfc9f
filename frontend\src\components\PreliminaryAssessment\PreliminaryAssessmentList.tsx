import React, { useState, useEffect, useCallback } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import type {
    PreliminaryAssessmentDto,
    PreliminaryAssessmentSearchParams,
} from "../../types/preliminaryAssessment";
import { preliminaryAssessmentService } from "../../services/preliminaryAssessmentService";
import AdminLayout from "../admin/AdminLayout";
import AdminTable from "../admin/AdminTable";
import { Pagination } from "../common/Pagination";
import { SearchFilters } from "../common/SearchFilters";
import { ExportButton } from "../common/ExportButton";
import { ConfirmDialog } from "../common/ConfirmDialog";

export const PreliminaryAssessmentList: React.FC = () => {
    const navigate = useNavigate();
    const [assessments, setAssessments] = useState<PreliminaryAssessmentDto[]>(
        []
    );
    const [loading, setLoading] = useState(true);
    const [totalCount, setTotalCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(10);
    const [searchParams, setSearchParams] =
        useState<PreliminaryAssessmentSearchParams>({});
    const [deleteConfirm, setDeleteConfirm] = useState<{
        show: boolean;
        id: string;
        name: string;
    }>({ show: false, id: "", name: "" });
    const [exporting, setExporting] = useState(false);

    const loadAssessments = useCallback(async () => {
        try {
            setLoading(true);
            const params = {
                ...searchParams,
                pageNumber: currentPage,
                pageSize,
            };
            const response = await preliminaryAssessmentService.getAll(params);
            setAssessments(response.data);
            setTotalCount(response.totalCount);
        } catch (error) {
            toast.error("Failed to load preliminary assessments");
            console.error("Error loading assessments:", error);
        } finally {
            setLoading(false);
        }
    }, [searchParams, currentPage, pageSize]);

    useEffect(() => {
        loadAssessments();
    }, [currentPage, searchParams, loadAssessments]);

    const handleSearch = (filters: Record<string, string | undefined>) => {
        const newSearchParams: PreliminaryAssessmentSearchParams = {
            taxpayerRegistration: filters.taxpayerRegistration || undefined,
            taxpayerName: filters.taxpayerName || undefined,
            fromDate: filters.fromDate ? new Date(filters.fromDate) : undefined,
            toDate: filters.toDate ? new Date(filters.toDate) : undefined,
        };
        setSearchParams(newSearchParams);
        setCurrentPage(1);
    };

    const handleDelete = async (id: string) => {
        try {
            await preliminaryAssessmentService.delete(id);
            toast.success("Preliminary assessment deleted successfully");
            loadAssessments();
        } catch (error) {
            toast.error("Failed to delete preliminary assessment");
            console.error("Error deleting assessment:", error);
        }
        setDeleteConfirm({ show: false, id: "", name: "" });
    };

    const handleExport = async () => {
        try {
            setExporting(true);
            const blob = await preliminaryAssessmentService.exportToExcel(
                searchParams
            );

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `preliminary-assessments-${
                new Date().toISOString().split("T")[0]
            }.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast.success("Export completed successfully");
        } catch (error) {
            toast.error("Failed to export data");
            console.error("Error exporting data:", error);
        } finally {
            setExporting(false);
        }
    };

    const handleGeneratePdf = async (id: string, taxpayerName: string) => {
        try {
            const blob = await preliminaryAssessmentService.generatePdfReport(
                id
            );

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `preliminary-assessment-${taxpayerName.replace(
                /\s+/g,
                "-"
            )}-${new Date().toISOString().split("T")[0]}.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast.success("PDF generated successfully");
        } catch (error) {
            toast.error("Failed to generate PDF");
            console.error("Error generating PDF:", error);
        }
    };

    const columns = [
        {
            header: "Taxpayer Registration",
            accessor: (item: PreliminaryAssessmentDto) => (
                <Link
                    to={`/preliminary-assessments/${item.id}`}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                >
                    {item.taxpayerRegistration}
                </Link>
            ),
        },
        {
            header: "Taxpayer Name",
            accessor: "taxpayerName" as keyof PreliminaryAssessmentDto,
        },
        {
            header: "Period From",
            accessor: (item: PreliminaryAssessmentDto) =>
                new Date(item.assessmentPeriodFrom).toLocaleDateString(),
        },
        {
            header: "Period To",
            accessor: (item: PreliminaryAssessmentDto) =>
                new Date(item.assessmentPeriodTo).toLocaleDateString(),
        },
        {
            header: "Total Amount",
            accessor: (item: PreliminaryAssessmentDto) => {
                return `Rs. ${(item.grandTotal || 0).toLocaleString()}`;
            },
        },
        {
            header: "Created",
            accessor: (item: PreliminaryAssessmentDto) =>
                new Date(item.createdAt).toLocaleDateString(),
        },
    ];

    const searchFields = [
        {
            name: "taxpayerRegistration",
            label: "Taxpayer Registration",
            type: "text" as const,
            placeholder: "Enter taxpayer registration",
        },
        {
            name: "taxpayerName",
            label: "Taxpayer Name",
            type: "text" as const,
            placeholder: "Enter taxpayer name",
        },
        {
            name: "fromDate",
            label: "From Date",
            type: "date" as const,
        },
        {
            name: "toDate",
            label: "To Date",
            type: "date" as const,
        },
    ];

    const renderActions = (item: PreliminaryAssessmentDto) => (
        <div className="flex space-x-2">
            <button
                onClick={() =>
                    navigate(`/preliminary-assessments/${item.id}`)
                }
                className="px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                View
            </button>
            <button
                onClick={() =>
                    navigate(`/preliminary-assessments/${item.id}/edit`)
                }
                className="px-3 py-1 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
                Edit
            </button>
            <button
                onClick={() => handleGeneratePdf(item.id, item.taxpayerName)}
                className="px-3 py-1 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
                PDF
            </button>
            <button
                onClick={() =>
                    setDeleteConfirm({
                        show: true,
                        id: item.id,
                        name: `${item.taxpayerRegistration} - ${item.taxpayerName}`,
                    })
                }
                className="px-3 py-1 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
                Delete
            </button>
        </div>
    );

    const totalPages = Math.ceil(totalCount / pageSize);

    return (
        <AdminLayout
            title="Preliminary Tax Assessments"
            subtitle="Manage preliminary tax assessments"
        >
            {/* Action Bar */}
            <div className="bg-white rounded-lg shadow p-4 mb-6">
                <div className="flex justify-between items-center">
                    <nav className="text-sm breadcrumbs">
                        <ul>
                            <li>
                                <Link
                                    to="/admin"
                                    className="text-blue-600 hover:text-blue-800"
                                >
                                    Dashboard
                                </Link>
                            </li>
                            <li>Preliminary Assessments</li>
                        </ul>
                    </nav>
                    <div className="flex space-x-2">
                        <ExportButton
                            onExport={handleExport}
                            loading={exporting}
                        />
                        <Link
                            to="/preliminary-assessments/new"
                            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <svg
                                className="w-4 h-4 mr-2"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 4v16m8-8H4"
                                />
                            </svg>
                            Create Assessment
                        </Link>
                    </div>
                </div>
            </div>

            <div className="space-y-6">
                {/* Search Filters */}
                <div className="bg-white rounded-lg shadow p-6">
                    <SearchFilters
                        fields={searchFields}
                        onSearch={handleSearch}
                        onReset={() => {
                            setSearchParams({});
                            setCurrentPage(1);
                        }}
                    />
                </div>

                {/* Results Summary */}
                <div className="bg-white rounded-lg shadow p-4">
                    <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600">
                            Showing {(currentPage - 1) * pageSize + 1} to{" "}
                            {Math.min(currentPage * pageSize, totalCount)} of{" "}
                            {totalCount} results
                        </div>
                        <div className="text-sm text-gray-600">
                            Page {currentPage} of {totalPages}
                        </div>
                    </div>
                </div>

                {/* Assessments Table */}
                <div className="bg-white rounded-lg shadow">
                    <AdminTable
                        columns={columns}
                        data={assessments}
                        keyField="id"
                        isLoading={loading}
                        actions={renderActions}
                        emptyMessage="No preliminary assessments found"
                    />
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex justify-center">
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={setCurrentPage}
                        />
                    </div>
                )}
            </div>

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
                isOpen={deleteConfirm.show}
                title="Delete Preliminary Assessment"
                message={`Are you sure you want to delete the preliminary assessment "${deleteConfirm.name}"? This action cannot be undone.`}
                confirmLabel="Delete"
                cancelLabel="Cancel"
                onConfirm={() => handleDelete(deleteConfirm.id)}
                onCancel={() =>
                    setDeleteConfirm({ show: false, id: "", name: "" })
                }
                type="danger"
            />
        </AdminLayout>
    );
};