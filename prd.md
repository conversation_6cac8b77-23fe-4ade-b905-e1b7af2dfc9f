## GIS-Based Property Tax System (MVP)

**Version:** 1.2.2
**Date:** 2025-06-07

---

### Table of Contents
1. [Introduction & Objective](#introduction--objective)
2. [Project Progress & Next Steps](#project-progress--next-steps)
3. [High-Level Application Flow](#high-level-application-flow)
4. [Updated Core Modules & Features](#updated-core-modules--features)
5. [Data Models & Entities](#data-models--entities)
6. [API Endpoints Addenda](#api-endpoints-addenda)
7. [User Stories](#user-stories)
8. [Sequence & Activity Flows](#sequence--activity-flows)
9. [OpenAPI Spec Addenda](#openapi-spec-addenda)
10. [Milestones & Timeline](#milestones--timeline)

---

## 1. Introduction & Objective

This update integrates user verification, partial payments, manual assessment creation, and an appeals workflow into the MVP of the GIS-Based Property Tax System. It aligns with the high-level flow provided and ensures all stakeholders have clear requirements for implementation. This version specifically clarifies that assessments are never created automatically upon payment but are always manually created by officers. The system now includes a submission tracking system for all verification processes and allows login using a tax payer ID generated during user verification.

## 2. Project Progress & Next Steps

* **Completed:** Version 1.1 scope modules.
* **In Progress:** Tax Assessment migration, Reporting enhancements.
* **Next:** Implement user-officer verification, partial payment handling, appeals & negotiation modules, email notifications, and new API endpoints.

## 3. High-Level Application Flow

1. **User Registration**: 
   - System generates a unique **Submission Number** for tracking the verification process
   - **Personal Information**:
     - First Name, Middle Name, Last Name
     - Date of Birth
     - Gender
     - Nationality (dropdown, default: Nepali)
     - Is Minor? (Yes/No)
   - **Address Information**:
     - **Permanent Address**:
       - Province (dropdown)
       - District (dropdown)
       - Local Level (dropdown)
       - Ward (dropdown)
       - Tole
       - House Number
     - **Temporary Address**:
       - Province (dropdown)
       - District (dropdown)
       - Local Level (dropdown)
       - Ward (dropdown)
       - Tole
       - House Number
   - **Contact Information**:
     - Telephone
     - Mobile
     - Email
   - **Multiple Relative Information**:
     - Father's Name
     - Grandfather's Name
     - (Additional relatives as needed)
   - **Preferred Alert Service**:
     - SMS (checkbox)
     - Email (checkbox)
   - **Document Upload**: 
     - Citizenship Document (PDF/Image, max 5MB)
     - National ID (PDF/Image, max 5MB)
     - PAN (PDF/Image, max 5MB)
   - System-generated PAN after officer verification

2. **Officer Verification**: 
   - New user enters PendingApproval
   - Officer (tied to user municipality) reviews citizenship document
   - Officer verifies citizenship number and personal details
   - Upon approval, system auto-generates PAN number (which serves as Tax Payer ID)
   - Officer marks user as Active and system sends email notification with PAN
   - Submission status updated to 'Verified'

3. **Property Registration**: Citizen registers property (linked by OwnerUserID), including GIS parcel; status PendingReview. System generates a unique Submission Number for tracking the property verification process.
4. **Officer Property Verification**: Officer reviews property by municipality; Approve/Reject.
5. **Tax Calculation**: Upon property approval, system calculates the tax amount based on property details and applicable tax rates.
6. **Payment Initiation**: Citizen initiates payment for an approved property, referencing the propertyId and the amount they wish to pay.
7. **Payment Recording & Officer Notification**: System records the payment against the property and fiscal year. The officer is notified of the payment.
8. **Manual Assessment Creation**: The officer manually creates an assessment if the taxpayer pays less than what is calculated by the system. The assessment links to any existing payments for that property/fiscal year.
9. **User Assessment View**: Citizen sees assessed amount; can Pay or Appeal.
9. **Appeal & Negotiation**: Taxpayer submits appeal (reason); system generates a unique Submission Number for tracking; after external negotiation, officer enters `NegotiatedAmount` and date; system creates new tax-due record.
10. **Final Payment**: Citizen pays negotiated amount; email notification sent upon due creation and payment.

## 4. Updated Core Modules & Features

### 4.1 User Management & Authentication

* **Additional Fields**: PAN (unique, serves as TaxPayerID for login), TwoGenerations (string), SubmissionNumber (unique).
* **Login Options**: Email/Password or PAN/Password
* **Verification Workflow**:

  * New users default to `PendingApproval`.
  * Officer Dashboard: `GET /api/users/pending?municipalityId={id}`.
  * Actions: Approve (status -> Active) or Reject (status -> Rejected + reason).
  * Email notifications: registration, approval, rejection.

### 4.2 Property Management & Registration

* **No change** to linking (OwnerUserID sufficient).
* **Verification Workflow** unchanged: PendingReview → Approved/Rejected.

### 4.3 Tax Assessment & Payment Flow

* **Tax Calculation**: After property approval, the system calculates the tax amount based on property details, but does not create any assessment record.

* **Payment Initiation & Recording**:
  * Endpoint `POST /api/payments/initiate` accepts `propertyId`, `amount` (and implicitly `fiscalYearId` for the active year).
  * System records the payment against the property and fiscal year.
  * If payment amount is less than the estimated tax, the system flags it as `Partial=true`.
  * Officers are notified of new payments, especially partial ones.

* **Manual Assessment Creation**:
  * Officers manually create assessments via `POST /api/assessments` only if the taxpayer pays less than what is calculated by the system.
  * The assessment includes `PropertyId`, `FiscalYearId`, calculated value based on GIS data.
  * The system links any existing payments for that property/fiscal year to the new assessment.
  * Assessment status is set to `Underpaid` if sum of linked payments < TaxAmount, or `Paid` if sum >= TaxAmount.
  * **Important**: There is no concept of initial or final assessment - only a single assessment that is manually created by officers when needed.

### 4.4 Appeal & Negotiation Module

* **New Entity**: `Appeal`

  * Fields: `AppealID`, `AssessmentID`, `TaxPayerID`, `Reason`, `SubmittedAt`, `Status` (`Pending`, `Resolved`), `ResolvedAt`.
* **New Entity**: `Negotiation`

  * Fields: `NegotiationID`, `AppealID`, `OfficerID`, `NegotiatedAmount`, `NegotiationDate`.
* **Workflow**:

  1. Citizen: `POST /api/appeals` { assessmentId, reason } → Appeal `Pending`.
  2. Officer: reviews appeal; after external process: `POST /api/negotiations` { appealId, negotiatedAmount } → system:

     * Creates `Negotiation` record.
     * Marks Appeal `Resolved` with `ResolvedAt`.
     * Creates a new Assessment record with `TaxAmount = NegotiatedAmount`, status `Pending`, and sets `PreviousAssessmentID` to the original Assessment; previous payments remain linked to the original.
     * Email notification to taxpayer.
  3. Citizen pays via `POST /api/payments/initiate` for new due.

### 4.5 Notifications & Audit

* **Email Events**:

  * User registration submitted, approved, rejected.
  * Property status change.
  * New tax due created (initial and post-negotiation).
  * Appeal submitted, resolved.
* **AuditLog**: Log create/update for Users (status changes), Assessments, Appeals, Negotiations.

## 5. Data Models & Entities

### 5.1 New Entities

```sql
CREATE TABLE Appeal (
  AppealID UUID PRIMARY KEY,
  AssessmentID UUID REFERENCES Assessment(AssessmentID),
  TaxPayerID UUID REFERENCES "User"(UserID),
  Reason TEXT NOT NULL,
  SubmittedAt TIMESTAMPTZ DEFAULT NOW(),
  Status TEXT NOT NULL CHECK (Status IN ('Pending','Resolved'))
);

CREATE TABLE Negotiation (
  NegotiationID UUID PRIMARY KEY,
  AppealID UUID REFERENCES Appeal(AppealID),
  OfficerID UUID REFERENCES "User"(UserID),
  NegotiatedAmount NUMERIC(15,2) NOT NULL,
  NegotiationDate TIMESTAMPTZ DEFAULT NOW()
);
```

### 5.2 Updated Entities

* **User**: add `PAN TEXT UNIQUE` (serves as TaxPayerID for login), `SubmissionNumber TEXT UNIQUE`, `TwoGenerations TEXT`, `Status TEXT CHECK ('PendingApproval','Active','Rejected')`.
* **Payment**: add `Partial BOOLEAN DEFAULT FALSE`, `Provisional BOOLEAN DEFAULT FALSE`, `PropertyId UUID`, `FiscalYearId UUID` (to allow recording payments before assessment exists).
* **Assessment**: add status enum `Underpaid`, `InReview`; add field `PreviousAssessmentID` (UUID, nullable) referencing the original assessment for post-negotiation flows.

## 6. API Endpoints Addenda

| Method | Path                                   | Description                                    |
| ------ | -------------------------------------- | ---------------------------------------------- |
| GET    | /api/users/pending?municipalityId={id} | Get pending user approvals for officer         |
| PUT    | /api/users/{id}/status                 | Approve/Reject user                            |
| POST   | /api/payments/initiate                 | { propertyId, amount } records payment, no auto-assessment |
| POST   | /api/assessments                       | Officer manually creates assessment, links existing payments |
| POST   | /api/appeals                           | { assessmentId, reason } create appeal         |
| GET    | /api/appeals?userId={id}               | List appeals by user                           |
| POST   | /api/negotiations                      | { appealId, negotiatedAmount } resolve appeal  |

## 7. User Stories

* **Registration & Verification**

  1. As a Citizen, I register with PAN and generational info, then await officer approval.
  2. As an Officer, I view pending registrations in my municipality and approve or reject.
* **Payment & Manual Assessment**
  3\. As a Citizen, after my property is approved, I see the calculated tax amount and can initiate a payment.
  4\. As a Citizen, if I pay less than the calculated tax amount, the system records my payment.
  5\. As an Officer, I am notified of payments and manually create an assessment only if the taxpayer has paid less than the calculated amount, linking any existing payments.
* **Appeals & Negotiation**
  5\. As a Citizen, I appeal an assessment with a reason.
  6\. As an Officer, I enter a negotiated amount via negotiation endpoint.
  7\. As a Citizen, I receive new due and pay negotiated amount.

## 8. Sequence & Activity Flows

```mermaid
sequenceDiagram
  participant C as Citizen
  participant S as System
  participant O as Officer
  C->>S: POST /register (incl. PAN, generations)
  S->>S: create User(status=PendingApproval)
  S->>O: email notification
  O->>S: PUT /users/{id}/status Approve
  S->>C: email approved

  C->>S: POST /properties
  ...
  C->>S: POST /payments/initiate (propertyId, amount)
  S->>S: record Payment(provisional=true, partial=true if amount < estimated tax)
  S->>O: notify officer of new payment
  O->>S: POST /api/assessments (creates assessment, links payment)
  S->>S: Assessment status set to Underpaid if sum(payments) < TaxAmount

  C->>S: POST /appeals
  S->>S: create Appeal(Pending)
  S->>O: notify officer
  O->>S: POST /negotiations
  S->>S: create Negotiation, mark Appeal Resolved, create new Assessment(due)
  S->>C: email new due
  C->>S: POST /payments/initiate (negotiated)
```

## 9. OpenAPI Spec Addenda

* Add schemas for `UserStatusUpdate`, `PartialPaymentRequest`, `AssessmentTriggerRequest`, `AppealRequest`, `NegotiationRequest`.
* Document new endpoints under respective tags.

## 10. Milestones & Timeline

* **v1.2 (by 2025-06-21):** User/officer verification, partial payments, assessment trigger, new entities & endpoints.
* **v1.3 (by 2025-07-05):** Appeals & negotiation workflow, email notifications.
* **Testing & Deployment (by 2025-07-15):** End-to-end QA, staging deploy, user testing.

---

## Detailed Progress Tracker for v1.2 Features

This section tracks the implementation progress of the features introduced in PRD Version 1.2.

### Core Module: User Management & Authentication (v1.2 Enhancements)

**Feature: User Verification Workflow**
*   [ ] **Entities (`ApplicationUser.cs`)**
    *   [ ] Add `PAN` (string, unique) field (serves as TaxPayerID for login)
    *   [ ] Add `SubmissionNumber` (string, unique) field
    *   [ ] Add `TwoGenerations` (string) field
    *   [ ] Add `Status` (string: 'PendingApproval', 'Active', 'Rejected') field (replace/reconcile `IsActive`)
*   [ ] **DbContext (`ApplicationDbContext.cs`)**
    *   [ ] Configure `PAN` (unique constraint)
    *   [ ] Configure `SubmissionNumber` (unique constraint)
    *   [ ] Configure `TwoGenerations`
    *   [ ] Configure `Status` (default 'PendingApproval', check constraint if possible)
*   [ ] **DTOs**
    *   [ ] Create `UserStatusUpdateDto` (Status, RejectionReason?)
    *   [ ] Update `UserResponseDto` to include new fields (PAN, SubmissionNumber, TwoGenerations, Status)
    *   [ ] Update `LoginRequestDto` to accept either Email or PAN
*   [ ] **Controllers (`UsersController.cs`)**
    *   [ ] Implement `GET /api/users/pending?municipalityId={id}` (for Officers, filter by new Status and MunicipalityId)
    *   [ ] Implement `PUT /api/users/{id}/status` (for Officers, use `UserStatusUpdateDto`)
*   [ ] **Services**
    *   [ ] Implement email notification logic for user registration, approval, rejection (requires Email Service)

### Core Module: Tax Assessment & Payment Flow (v1.2 Enhancements)

**Feature: Partial Payments**
*   [ ] **Entities (`Payment.cs`)**
    *   [ ] Add `Partial` (bool, default false) field
*   [ ] **Entities (`Assessment.cs`)**
    *   [ ] Update `PaymentStatus` string to include 'Underpaid'
    *   [ ] Change `Payment` navigation property to `ICollection<Payment> Payments`
*   [ ] **DbContext (`ApplicationDbContext.cs`)**
    *   [ ] Configure `Payment.Partial` (default false)
    *   [ ] Update `Assessment` to `Payment` relationship (one-to-many)
*   [ ] **DTOs (`PaymentDtos.cs`)**
    *   [ ] Add `Amount` (decimal) field to `PaymentInitiateDto`
*   [ ] **Controllers (`PaymentsController.cs`)**
    *   [ ] Modify `POST /api/payments/initiate` to accept `Amount` from DTO
    *   [ ] Modify payment callback logic:
        *   [ ] Store actual `AmountPaid`
        *   [ ] Set `Payment.Partial` flag correctly
        *   [ ] Sum all payments for an assessment to update `Assessment.PaymentStatus` to 'Paid' or 'Underpaid'
*   [ ] **Services**
    *   [ ] Implement email notification for payment confirmation (requires Email Service)

**Feature: Assessment Trigger (Officer Override for Underpaid)**
*   [ ] **Entities (`Assessment.cs`)**
    *   [ ] Update `PaymentStatus` string to include 'InReview'
*   [ ] **DTOs**
    *   [ ] Create `AssessmentTriggerRequestDto` (`AssessmentId`)
*   [ ] **Controllers (`AssessmentsController.cs`)**
    *   [ ] Implement `POST /api/assessments/trigger` (for Officers, updates status to 'InReview')

### Core Module: Appeal & Negotiation Module (NEW)

**Feature: Appeal Submission & Management**
*   [ ] **Entities**
    *   [ ] Create `Appeal.cs` (AppealID, AssessmentID, TaxPayerID, SubmissionNumber, Reason, SubmittedAt, Status, ResolvedAt)
*   [ ] **DbContext (`ApplicationDbContext.cs`)**
    *   [ ] Add `DbSet<Appeal> Appeals`
    *   [ ] Configure `Appeal` entity (keys, relationships to Assessment & User, constraints, defaults)
*   [ ] **DTOs**
    *   [ ] Create `AppealRequestDto` (AssessmentId, Reason)
    *   [ ] Create `AppealResponseDto`
*   [ ] **Controllers**
    *   [ ] Create `AppealsController.cs`
    *   [ ] Implement `POST /api/appeals` (Citizen submits appeal)
    *   [ ] Implement `GET /api/appeals?userId={id}` (Citizen views their appeals)
    *   [ ] Implement `GET /api/appeals?municipalityId={id}` (Officer views appeals in their municipality - *consider adding*)
*   [ ] **Services**
    *   [ ] Implement email notification for appeal submission (requires Email Service)

**Feature: Negotiation & Resolution**
*   [ ] **Entities**
    *   [ ] Create `Negotiation.cs` (NegotiationID, AppealID, OfficerID, NegotiatedAmount, NegotiationDate)
    *   [ ] **Entities (`Assessment.cs`)**
        *   [ ] Add `PreviousAssessmentID` (Guid?, nullable) field
        *   [ ] Add navigation property `PreviousAssessment`
*   [ ] **DbContext (`ApplicationDbContext.cs`)**
    *   [ ] Add `DbSet<Negotiation> Negotiations`
    *   [ ] Configure `Negotiation` entity (keys, relationships to Appeal & User, defaults)
    *   [ ] Configure `Assessment.PreviousAssessmentID` relationship
*   [ ] **DTOs**
    *   [ ] Create `NegotiationRequestDto` (AppealId, NegotiatedAmount)
    *   [ ] Create `NegotiationResponseDto`
*   [ ] **Controllers**
    *   [ ] Create `NegotiationsController.cs`
    *   [ ] Implement `POST /api/negotiations` (Officer records negotiation)
        *   [ ] Logic to create `Negotiation` record
        *   [ ] Logic to mark `Appeal` as 'Resolved'
        *   [ ] Logic to create new `Assessment` record (linked via `PreviousAssessmentID`, `TaxAmount = NegotiatedAmount`, status 'Pending')
*   [ ] **Services**
    *   [ ] Implement email notification for new tax due post-negotiation and appeal resolution (requires Email Service)

### Core Module: Notifications & Audit (v1.2 Enhancements)

**Feature: Email Notifications**
*   [ ] **Services**
    *   [ ] Implement/Integrate a generic Email Service (e.g., using SendGrid, SMTP)
    *   [ ] Ensure all specified email events trigger notifications (User reg/approval/rejection, Property status, New tax due, Appeal submitted/resolved)

**Feature: AuditLog Enhancements**
*   [ ] **Services/Controllers**
    *   [ ] Ensure `AuditLog` entries are created for:
        *   [ ] User status changes
        *   [ ] Assessment creation/updates (especially status changes like 'Underpaid', 'InReview', post-negotiation)
        *   [ ] Appeal creation/status changes
        *   [ ] Negotiation creation

### General Backend Tasks
*   [ ] **Database Migrations**: Create and apply migrations for all entity and DbContext changes.
*   [ ] **Refactor `Assessment.AssessmentYear`**: Complete migration to `FiscalYearId` and remove `AssessmentYear` from entity and DbContext unique index.
*   [ ] **API Documentation (OpenAPI/Swagger)**: Update/generate for all new/modified endpoints and DTOs.

---

*End of PRD v1.2*