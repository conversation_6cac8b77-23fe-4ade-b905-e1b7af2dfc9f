import React from "react";
import { Link } from "react-router-dom";

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
}

interface AdminBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const AdminBreadcrumb: React.FC<AdminBreadcrumbProps> = ({ items, className = "" }) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className={`text-sm breadcrumbs ${className}`}>
      <ul>
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          
          return (
            <li key={index}>
              {item.href && !isLast ? (
                <Link 
                  to={item.href} 
                  className="flex items-center space-x-1 hover:text-primary transition-colors"
                >
                  {item.icon && <span className="w-4 h-4">{item.icon}</span>}
                  <span>{item.label}</span>
                </Link>
              ) : (
                <span className={`flex items-center space-x-1 ${isLast ? 'text-base-content font-medium' : 'text-base-content/70'}`}>
                  {item.icon && <span className="w-4 h-4">{item.icon}</span>}
                  <span>{item.label}</span>
                </span>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default AdminBreadcrumb;