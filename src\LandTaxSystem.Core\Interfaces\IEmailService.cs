using System;
using System.Threading.Tasks;
using LandTaxSystem.Core.Entities;

namespace LandTaxSystem.Core.Interfaces
{
    public interface IEmailService
    {
        Task SendRegistrationConfirmationAsync(string email, string fullName, Guid userId, string panNumber = null);
        Task SendPropertySubmissionAsync(string email, string fullName, Guid propertyId);
        Task SendAssessmentNotificationAsync(string email, string fullName, Guid assessmentId);
        Task SendPaymentNotificationToOfficer(Payment payment, string officerEmail, string municipalityName);
        Task SendNegotiationNotificationToTaxPayer(Negotiation negotiation, string taxpayerEmail);
        Task SendPropertyVerificationAsync(string email, string fullName, Guid propertyId);
        Task SendPaymentConfirmationToTaxPayer(Payment payment, string taxpayerEmail);
        Task SendUserApprovalNotificationAsync(string email, string fullName, string pan, string username);
    }
}