using System;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.Assessment
{
    public class AssessmentLineItemCreateDto
    {
        [Required]
        public int SerialNumber { get; set; }

        [Required]
        [StringLength(100)]
        public string TaxDescription { get; set; } = string.Empty;

        [Required]
        public int FiscalYear { get; set; }

        [Required]
        [Range(0, double.MaxValue)]
        public decimal AssessedAmount { get; set; }

        [Required]
        [Range(0, double.MaxValue)]
        public decimal PenaltyAmount { get; set; } = 0;

        [Required]
        [Range(0, double.MaxValue)]
        public decimal InterestAmount { get; set; } = 0;
    }
}
