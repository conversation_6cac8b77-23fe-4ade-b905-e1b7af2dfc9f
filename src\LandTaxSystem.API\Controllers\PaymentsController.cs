using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using LandTaxSystem.Core.DTOs.Payment;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using LandTaxSystem.Infrastructure.Services; // Added for GisService
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly GisService _gisService; // Changed from IGisService for direct use as per file view
        private readonly IEmailService _emailService;
        private readonly TaxConfigResolver _taxConfigResolver;
        private readonly ITaxCalculationService _taxCalculationService;

        public PaymentsController(ApplicationDbContext context, GisService gisService, IEmailService emailService, TaxConfigResolver taxConfigResolver, ITaxCalculationService taxCalculationService)
        {
            _context = context;
            _gisService = gisService;
            _emailService = emailService;
            _taxConfigResolver = taxConfigResolver;
            _taxCalculationService = taxCalculationService;
        }

        [HttpPost("initiate")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<PaymentResponseDto>> InitiatePayment(PaymentInitiateDto initiateDto)
        {
            // Determine payment flow based on whether AssessmentId is provided
            if (initiateDto.AssessmentId.HasValue)
            {
                // Assessment-based payment flow
                return await InitiateAssessmentBasedPayment(initiateDto);
            }
            else
            {
                // Property-based payment flow
                return await InitiatePropertyBasedPayment(initiateDto);
            }
        }

        /// <summary>
        /// Handles payment initiation for existing assessments
        /// </summary>
        private async Task<ActionResult<PaymentResponseDto>> InitiateAssessmentBasedPayment(PaymentInitiateDto initiateDto)
        {
            // Find the assessment
            var assessment = await _context.Assessments
                .Include(a => a.Property)
                    .ThenInclude(p => p.Municipality)
                .Include(a => a.Property)
                    .ThenInclude(p => p.Owner)
                .Include(a => a.Payments)
                .FirstOrDefaultAsync(a => a.AssessmentId == initiateDto.AssessmentId);

            if (assessment == null)
            {
                return NotFound("Assessment not found.");
            }

            // Verify property exists and matches
            if (assessment.PropertyId != initiateDto.PropertyId)
            {
                return BadRequest("Property ID does not match the assessment.");
            }

            // Check authorization - only property owner can pay
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (assessment.Property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to make payments for this property.");
            }

            // Validate payment amount against assessment
            var totalAlreadyPaid = assessment.Payments.Where(p => p.Status == "Success").Sum(p => p.AmountPaid);
            var remainingAmount = assessment.TaxAmount - totalAlreadyPaid;

            if (remainingAmount <= 0)
            {
                return BadRequest("This assessment has already been fully paid.");
            }

            // Create payment record linked to assessment
            var payment = new Payment
            {
                PaymentId = Guid.NewGuid(),
                PropertyId = assessment.PropertyId,
                FiscalYearId = assessment.FiscalYearId,
                AssessmentId = assessment.AssessmentId,
                AmountPaid = initiateDto.Amount,
                PaymentDate = DateTime.UtcNow,
                PaymentGateway = "DirectInternal",
                TransactionId = $"TXN_INIT_{DateTime.UtcNow.Ticks}",
                Status = "Success",
                Partial = (totalAlreadyPaid + initiateDto.Amount) < assessment.TaxAmount,
                Provisional = false, // Assessment-based payments are not provisional
                CreatedAt = DateTime.UtcNow
            };

            // Update assessment payment status
            var newTotalPaid = totalAlreadyPaid + payment.AmountPaid;
            if (newTotalPaid >= assessment.TaxAmount)
            {
                assessment.PaymentStatus = "Paid";
            }
            else
            {
                assessment.PaymentStatus = "Underpaid";
            }
            assessment.UpdatedAt = DateTime.UtcNow;

            _context.Payments.Add(payment);
            await _context.SaveChangesAsync();

            // Send payment confirmation email to taxpayer
            var taxpayerEmail = assessment.Property.Owner.Email;
            if (!string.IsNullOrEmpty(taxpayerEmail))
            {
                await _emailService.SendPaymentConfirmationToTaxPayer(
                    payment,
                    taxpayerEmail
                );
            }

            return Ok(new PaymentResponseDto
            {
                PaymentId = payment.PaymentId,
                AssessmentId = payment.AssessmentId,
                PropertyId = payment.PropertyId,
                FiscalYearId = payment.FiscalYearId,
                AmountPaid = payment.AmountPaid,
                PaymentDate = payment.PaymentDate,
                PaymentGateway = payment.PaymentGateway,
                TransactionId = payment.TransactionId,
                Status = payment.Status,
                Provisional = payment.Provisional,
                Partial = payment.Partial,
                CreatedAt = payment.CreatedAt
            });
        }

        /// <summary>
        /// Handles payment initiation for properties without existing assessments
        /// </summary>
        private async Task<ActionResult<PaymentResponseDto>> InitiatePropertyBasedPayment(PaymentInitiateDto initiateDto)
        {
            var property = await _context.Properties
                .Include(p => p.Municipality)
                .Include(p => p.Owner)
                .FirstOrDefaultAsync(p => p.PropertyId == initiateDto.PropertyId);

            if (property == null)
            {
                return NotFound("Property not found.");
            }

            if (property.Status != "Approved")
            {
                return BadRequest("Payments can only be initiated for approved properties.");
            }

            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to make payments for this property.");
            }

            // Determine fiscal year to use
            FiscalYear fiscalYear;
            if (initiateDto.FiscalYearId.HasValue)
            {
                fiscalYear = await _context.FiscalYears.FindAsync(initiateDto.FiscalYearId.Value);
                if (fiscalYear == null)
                {
                    return NotFound("Specified fiscal year not found.");
                }
            }
            else
            {
                fiscalYear = await _context.FiscalYears.FirstOrDefaultAsync(fy => fy.IsActive);
                if (fiscalYear == null)
                {
                    return BadRequest("No active fiscal year found. Cannot process payment.");
                }
            }

            // Check if return filing has been submitted for this property and fiscal year
            var returnFiling = await _context.ReturnFilings
                .FirstOrDefaultAsync(rf => rf.PropertyId == initiateDto.PropertyId && rf.FiscalYearId == fiscalYear.FiscalYearId);

            if (returnFiling == null)
            {
                return BadRequest("Return filing must be submitted before making a payment. Please file your tax return first.");
            }

            // Calculate estimated tax using the new tax calculation service
            try
            {
                var taxCalculation = await _taxCalculationService.CalculateTaxAsync(property, fiscalYear.FiscalYearId);
                var estimatedTaxAmount = taxCalculation.TaxAmount;

                // Check if an assessment already exists for this property and fiscal year
                var existingAssessment = await _context.Assessments
                    .Include(a => a.Payments)
                    .FirstOrDefaultAsync(a => a.PropertyId == initiateDto.PropertyId && a.FiscalYearId == fiscalYear.FiscalYearId);

                // Create payment record
                var payment = new Payment
                {
                    PaymentId = Guid.NewGuid(),
                    PropertyId = initiateDto.PropertyId,
                    FiscalYearId = fiscalYear.FiscalYearId,
                    AssessmentId = existingAssessment?.AssessmentId, // Link to assessment if it exists
                    AmountPaid = initiateDto.Amount,
                    PaymentDate = DateTime.UtcNow,
                    PaymentGateway = "DirectInternal",
                    TransactionId = $"TXN_INIT_{DateTime.UtcNow.Ticks}",
                    Status = "Success",
                    Partial = initiateDto.Amount < estimatedTaxAmount,
                    Provisional = existingAssessment == null, // Mark as provisional if no assessment exists
                    CreatedAt = DateTime.UtcNow
                };

                // If assessment exists, update its payment status
                if (existingAssessment != null)
                {
                    var totalAlreadyPaid = existingAssessment.Payments.Where(p => p.Status == "Success").Sum(p => p.AmountPaid);
                    var newTotalPaid = totalAlreadyPaid + payment.AmountPaid;

                    if (newTotalPaid >= existingAssessment.TaxAmount)
                    {
                        existingAssessment.PaymentStatus = "Paid";
                    }
                    else
                    {
                        existingAssessment.PaymentStatus = "Underpaid";
                    }
                    existingAssessment.UpdatedAt = DateTime.UtcNow;
                }

                _context.Payments.Add(payment);
                await _context.SaveChangesAsync();

                // Send payment confirmation email to taxpayer
                var taxpayerEmail = property.Owner.Email;
                if (!string.IsNullOrEmpty(taxpayerEmail))
                {
                    await _emailService.SendPaymentConfirmationToTaxPayer(
                        payment,
                        taxpayerEmail
                    );
                }

                // If this is a provisional payment, notify officers
                if (payment.Provisional)
                {
                    await NotifyOfficersOfProvisionalPayment(payment, property);
                }

                return Ok(new PaymentResponseDto
                {
                    PaymentId = payment.PaymentId,
                    AssessmentId = payment.AssessmentId,
                    PropertyId = payment.PropertyId,
                    FiscalYearId = payment.FiscalYearId,
                    AmountPaid = payment.AmountPaid,
                    PaymentDate = payment.PaymentDate,
                    PaymentGateway = payment.PaymentGateway,
                    TransactionId = payment.TransactionId,
                    Status = payment.Status,
                    Provisional = payment.Provisional,
                    Partial = payment.Partial,
                    CreatedAt = payment.CreatedAt
                });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error calculating tax or processing payment: {ex.Message}");
            }
        }

        /// <summary>
        /// Notifies officers of provisional payments
        /// </summary>
        private async Task NotifyOfficersOfProvisionalPayment(Payment payment, Property property)
        {
            try
            {
                if (property.Municipality != null)
                {
                    // Find officers assigned to this municipality
                    var officers = await _context.Users
                        .Where(u => u.MunicipalityId == property.MunicipalityId &&
                               u.Role == "Officer")
                        .ToListAsync();

                    // Send email notification to each officer
                    foreach (var officer in officers)
                    {
                        if (!string.IsNullOrEmpty(officer.Email))
                        {
                            await _emailService.SendPaymentNotificationToOfficer(
                                payment,
                                officer.Email,
                                property.Municipality.Name);

                            // Create in-app notification
                            var notification = new Notification
                            {
                                UserId = officer.Id,
                                Message = $"New provisional payment of NPR {payment.AmountPaid:N2} received for property in {property.Municipality.Name}",
                                Type = "payment",
                                IsRead = false,
                                RelatedEntityId = payment.PaymentId,
                                RelatedEntityType = "Payment"
                            };

                            _context.Notifications.Add(notification);
                        }
                    }

                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the payment process
                Console.WriteLine($"Error sending officer notifications: {ex.Message}");
            }
        }

        [HttpPost("callback/mock")]
        [AllowAnonymous]
        public async Task<IActionResult> ProcessMockPaymentCallback(PaymentCallbackDto callbackDto)
        {
            // In a real implementation, we would validate the callback using signatures, etc.

            // Find the assessment if AssessmentId is provided
            if (callbackDto.AssessmentId == Guid.Empty)
            {
                return BadRequest("Assessment ID is required");
            }

            var assessment = await _context.Assessments
                .Include(a => a.Property)
                .FirstOrDefaultAsync(a => a.AssessmentId == callbackDto.AssessmentId);

            if (assessment == null)
            {
                return NotFound("Assessment not found");
            }

            if (callbackDto.Status == "Success")
            {
                // Create a payment record
                var payment = new Payment
                {
                    PaymentId = callbackDto.InternalPaymentId,
                    AssessmentId = callbackDto.AssessmentId,
                    PropertyId = assessment.PropertyId,
                    FiscalYearId = assessment.FiscalYearId,
                    AmountPaid = assessment.TaxAmount,
                    PaymentDate = DateTime.UtcNow,
                    PaymentGateway = "SandboxMock",
                    TransactionId = callbackDto.TransactionId,
                    Status = "Success",
                    Partial = false, // Full payment
                    Provisional = false, // Not provisional since assessment exists
                    CreatedAt = DateTime.UtcNow
                };

                _context.Payments.Add(payment);

                // Update assessment payment status
                assessment.PaymentStatus = "Paid";
                assessment.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new { Message = "Payment processed successfully" });
            }
            else
            {
                // Record the failed payment attempt
                var payment = new Payment
                {
                    PaymentId = callbackDto.InternalPaymentId,
                    AssessmentId = callbackDto.AssessmentId,
                    PropertyId = assessment.PropertyId,
                    FiscalYearId = assessment.FiscalYearId,
                    AmountPaid = assessment.TaxAmount,
                    PaymentDate = DateTime.UtcNow,
                    PaymentGateway = "SandboxMock",
                    TransactionId = callbackDto.TransactionId,
                    Status = "Failed",
                    Partial = false,
                    Provisional = false,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Payments.Add(payment);
                await _context.SaveChangesAsync();

                return BadRequest(new { Message = "Payment failed" });
            }
        }

        [HttpPost("provisional")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<PaymentResponseDto>> ProcessProvisionalPayment(ProvisionalPaymentDto paymentDto)
        {
            // Check if property exists
            var property = await _context.Properties
                .Include(p => p.Municipality)
                .FirstOrDefaultAsync(p => p.PropertyId == paymentDto.PropertyId);

            if (property == null)
            {
                return NotFound("Property not found");
            }

            // Check if the user is the property owner
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to pay for this property");
            }

            // Get the current fiscal year
            var currentFiscalYear = await _context.FiscalYears
                .OrderByDescending(f => f.EndDate)
                .FirstOrDefaultAsync(f => f.IsActive);

            if (currentFiscalYear == null)
            {
                return BadRequest("No active fiscal year found");
            }

            // Process the payment (mock processing for MVP)
            var paymentId = Guid.NewGuid();
            var transactionId = $"PROV_{DateTime.UtcNow.Ticks}";

            // Create payment record with provisional flag
            var payment = new Payment
            {
                PaymentId = paymentId,
                AssessmentId = null, // No assessment for provisional payments
                PropertyId = paymentDto.PropertyId,
                FiscalYearId = currentFiscalYear.FiscalYearId,
                AmountPaid = paymentDto.Amount,
                PaymentDate = DateTime.UtcNow,
                PaymentGateway = paymentDto.PaymentMethod == "card" ? "Mock Card Gateway" :
                                 paymentDto.PaymentMethod == "bank" ? "Mock Bank Transfer" : "Mock Digital Wallet",
                TransactionId = transactionId,
                Status = "Success",
                Provisional = true, // Mark as provisional payment
                Partial = false,   // Not a partial payment
                CreatedAt = DateTime.UtcNow
            };

            _context.Payments.Add(payment);
            await _context.SaveChangesAsync();

            // TODO: Notify tax officers about the provisional payment

            return new PaymentResponseDto
            {
                PaymentId = payment.PaymentId,
                AssessmentId = payment.AssessmentId,
                PropertyId = payment.PropertyId,
                FiscalYearId = payment.FiscalYearId,
                AmountPaid = payment.AmountPaid,
                PaymentDate = payment.PaymentDate,
                PaymentGateway = payment.PaymentGateway,
                TransactionId = payment.TransactionId,
                Status = payment.Status,
                Provisional = payment.Provisional,
                Partial = payment.Partial,
                CreatedAt = payment.CreatedAt
            };
        }

        [HttpPost]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<PaymentResponseDto>> ProcessDirectPayment(DirectPaymentDto paymentDto)
        {
            // Find the assessment
            var assessment = await _context.Assessments
                .Include(a => a.Property)
                .FirstOrDefaultAsync(a => a.AssessmentId == paymentDto.AssessmentId);

            if (assessment == null)
            {
                return NotFound("Assessment not found");
            }

            // Check if the user is the property owner
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (assessment.Property.OwnerUserId != userId)
            {
                return Forbid("You are not authorized to pay for this assessment");
            }

            // Check if the assessment has already been paid
            if (assessment.PaymentStatus == "Paid")
            {
                return BadRequest("This assessment has already been paid");
            }

            // Process the payment (mock processing for MVP)
            // In a real implementation, this would call a payment gateway API

            var paymentId = Guid.NewGuid();
            var transactionId = $"TXN_{DateTime.UtcNow.Ticks}";

            // Create payment record
            var payment = new Payment
            {
                PaymentId = paymentId,
                AssessmentId = paymentDto.AssessmentId,
                AmountPaid = assessment.TaxAmount,
                PaymentDate = DateTime.UtcNow,
                PaymentGateway = paymentDto.PaymentMethod == "card" ? "Mock Card Gateway" :
                                 paymentDto.PaymentMethod == "bank" ? "Mock Bank Transfer" : "Mock Digital Wallet",
                TransactionId = transactionId,
                Status = "Success",
                CreatedAt = DateTime.UtcNow
            };

            _context.Payments.Add(payment);

            // Update assessment payment status
            assessment.PaymentStatus = "Paid";
            assessment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return new PaymentResponseDto
            {
                PaymentId = payment.PaymentId,
                AssessmentId = payment.AssessmentId,
                AmountPaid = payment.AmountPaid,
                PaymentDate = payment.PaymentDate,
                PaymentGateway = payment.PaymentGateway,
                TransactionId = payment.TransactionId,
                Status = payment.Status,
                CreatedAt = payment.CreatedAt
            };
        }

        [HttpPost("bulk")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<BulkPaymentResponseDto>> ProcessBulkPayment(BulkPaymentDto bulkPaymentDto)
        {
            if (bulkPaymentDto.AssessmentIds.Count == 0)
            {
                return BadRequest("No assessment IDs provided");
            }

            // Get the current user ID
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

            // Get all assessments
            var assessments = await _context.Assessments
                .Include(a => a.Property)
                .Where(a => bulkPaymentDto.AssessmentIds.Contains(a.AssessmentId))
                .ToListAsync();

            // Validate assessments
            if (assessments.Count != bulkPaymentDto.AssessmentIds.Count)
            {
                return NotFound("One or more assessments not found");
            }

            // Check ownership and payment status
            foreach (var assessment in assessments)
            {
                if (assessment.Property.OwnerUserId != userId)
                {
                    return Forbid("You are not authorized to pay for one or more of these assessments");
                }

                if (assessment.PaymentStatus == "Paid")
                {
                    return BadRequest($"Assessment {assessment.AssessmentId} has already been paid");
                }
            }

            // Process bulk payment (mock processing for MVP)
            var batchId = Guid.NewGuid();
            var transactionId = $"BATCH_{DateTime.UtcNow.Ticks}";
            var totalAmount = assessments.Sum(a => a.TaxAmount);
            var paymentDate = DateTime.UtcNow;
            var paymentGateway = bulkPaymentDto.PaymentMethod == "card" ? "Mock Card Gateway" :
                                bulkPaymentDto.PaymentMethod == "bank" ? "Mock Bank Transfer" : "Mock Digital Wallet";

            var paymentResponses = new List<PaymentResponseDto>();

            // Create payment records for each assessment
            foreach (var assessment in assessments)
            {
                var paymentId = Guid.NewGuid();

                var payment = new Payment
                {
                    PaymentId = paymentId,
                    AssessmentId = assessment.AssessmentId,
                    AmountPaid = assessment.TaxAmount,
                    PaymentDate = paymentDate,
                    PaymentGateway = paymentGateway,
                    TransactionId = transactionId,
                    Status = "Success",
                    CreatedAt = paymentDate,
                    BatchPaymentId = batchId
                };

                _context.Payments.Add(payment);

                // Update assessment payment status
                assessment.PaymentStatus = "Paid";
                assessment.UpdatedAt = paymentDate;

                paymentResponses.Add(new PaymentResponseDto
                {
                    PaymentId = payment.PaymentId,
                    AssessmentId = payment.AssessmentId,
                    AmountPaid = payment.AmountPaid,
                    PaymentDate = payment.PaymentDate,
                    PaymentGateway = payment.PaymentGateway,
                    TransactionId = payment.TransactionId,
                    Status = payment.Status,
                    CreatedAt = payment.CreatedAt
                });
            }

            await _context.SaveChangesAsync();

            return new BulkPaymentResponseDto
            {
                BatchPaymentId = batchId,
                Payments = paymentResponses,
                TotalAmountPaid = totalAmount,
                PaymentDate = paymentDate,
                PaymentGateway = paymentGateway,
                TransactionId = transactionId,
                Status = "Success"
            };
        }

        [HttpGet("property/{propertyId}")]
        [Authorize]
        public async Task<ActionResult<PaymentHistoryDto>> GetPropertyPaymentHistory(Guid propertyId)
        {
            // Check if property exists
            var property = await _context.Properties.FindAsync(propertyId);
            if (property == null)
            {
                return NotFound("Property not found");
            }

            // Check authorization - only owner or officers from the property's municipality can view
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (property.OwnerUserId != userId &&
                !(userRole == "Officer" && officerMunicipalityId == property.MunicipalityId.ToString()) &&
                userRole != "CentralAdmin")
            {
                return Forbid();
            }

            // Get all payments for the property
            // Get all payments for this property (both with and without assessments)
            var payments = await _context.Payments
                .Where(p => p.PropertyId == propertyId)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();

            var paymentResponses = payments.Select(p => new PaymentResponseDto
            {
                PaymentId = p.PaymentId,
                AssessmentId = p.AssessmentId, // Now nullable
                PropertyId = p.PropertyId,
                FiscalYearId = p.FiscalYearId,
                AmountPaid = p.AmountPaid,
                PaymentDate = p.PaymentDate,
                PaymentGateway = p.PaymentGateway ?? string.Empty,
                TransactionId = p.TransactionId,
                Status = p.Status ?? "Unknown",
                Provisional = p.Provisional,
                Partial = p.Partial,
                CreatedAt = p.CreatedAt
            }).ToList();

            return new PaymentHistoryDto
            {
                PropertyId = propertyId,
                Payments = paymentResponses
            };
        }

        [HttpGet("receipt/{paymentId}")]
        [Authorize]
        public async Task<ActionResult> GetPaymentReceipt(Guid paymentId)
        {
            // Find the payment
            var payment = await _context.Payments
                .Include(p => p.Assessment)
                    .ThenInclude(a => a.Property)
                        .ThenInclude(p => p.Owner)
                .Include(p => p.Assessment)
                    .ThenInclude(a => a.Property)
                        .ThenInclude(p => p.Municipality)
                // Also include direct property reference for provisional payments
                .Include(p => p.Property)
                    .ThenInclude(p => p.Owner)
                .Include(p => p.Property)
                    .ThenInclude(p => p.Municipality)
                .FirstOrDefaultAsync(p => p.PaymentId == paymentId);

            if (payment == null)
            {
                return NotFound("Payment not found");
            }

            // Check authorization - only owner or officers from the property's municipality can view
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            // Get property info - either from assessment or directly from payment
            var property = payment.Assessment?.Property ?? payment.Property;

            if (property == null)
            {
                return NotFound("Property information not found");
            }

            if (property.OwnerUserId != userId &&
                !(userRole == "Officer" && officerMunicipalityId == property.MunicipalityId.ToString()) &&
                userRole != "CentralAdmin")
            {
                return Forbid();
            }

            // Generate receipt data
            var receiptDto = new ReceiptDto
            {
                PaymentId = payment.PaymentId,
                PropertyAddress = property.Address,
                OwnerName = property.Owner?.FullName ?? "Unknown Owner",
                AssessmentYear = payment.Assessment?.AssessmentYear ?? DateTime.Now.Year, // Use current year if no assessment
                AmountPaid = payment.AmountPaid,
                PaymentDate = payment.PaymentDate,
                TransactionId = payment.TransactionId ?? "",
                PaymentMethod = payment.PaymentGateway ?? "Unknown",
                MunicipalityName = property.Municipality?.Name ?? "Unknown Municipality"
            };

            // Generate HTML receipt
            var html = GenerateReceiptHtml(receiptDto);

            // For MVP, we return HTML that can be rendered by the browser
            // In a production app, you'd typically generate a PDF and return that
            return Content(html, "text/html");
        }

        private string GenerateReceiptHtml(ReceiptDto receipt)
        {
            // Simple HTML receipt template
            var html = $@"<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Tax Payment Receipt</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 0; color: #333; }}
        .receipt {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .title {{ font-size: 24px; font-weight: bold; color: #2563EB; }}
        .receipt-id {{ font-size: 14px; color: #666; margin-top: 5px; }}
        .logo {{ margin-bottom: 15px; }}
        .section {{ margin-bottom: 20px; }}
        .section-title {{ font-size: 16px; font-weight: bold; margin-bottom: 10px; }}
        .info-row {{ display: flex; margin-bottom: 8px; }}
        .info-label {{ width: 200px; font-weight: bold; }}
        .info-value {{ flex: 1; }}
        .amount {{ font-size: 20px; font-weight: bold; color: #059669; }}
        .footer {{ margin-top: 40px; font-size: 12px; color: #666; text-align: center; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .official {{ margin-top: 40px; font-style: italic; }}
    </style>
</head>
<body>
    <div class='receipt'>
        <div class='header'>
            <div class='logo'>🏢</div>
            <div class='title'>{receipt.MunicipalityName}</div>
            <div>Property Tax Payment Receipt</div>
            <div class='receipt-id'>Receipt #: {receipt.PaymentId}</div>
        </div>
        
        <div class='section'>
            <div class='section-title'>Property Details</div>
            <div class='info-row'>
                <div class='info-label'>Property Address:</div>
                <div class='info-value'>{receipt.PropertyAddress}</div>
            </div>
            <div class='info-row'>
                <div class='info-label'>Owner Name:</div>
                <div class='info-value'>{receipt.OwnerName}</div>
            </div>
        </div>
        
        <div class='section'>
            <div class='section-title'>Payment Details</div>
            <div class='info-row'>
                <div class='info-label'>Assessment Year:</div>
                <div class='info-value'>{receipt.AssessmentYear}</div>
            </div>
            <div class='info-row'>
                <div class='info-label'>Payment Date:</div>
                <div class='info-value'>{receipt.PaymentDate.ToString("MMMM dd, yyyy HH:mm:ss")}</div>
            </div>
            <div class='info-row'>
                <div class='info-label'>Transaction ID:</div>
                <div class='info-value'>{receipt.TransactionId}</div>
            </div>
            <div class='info-row'>
                <div class='info-label'>Payment Method:</div>
                <div class='info-value'>{receipt.PaymentMethod}</div>
            </div>
            <div class='info-row'>
                <div class='info-label'>Amount Paid:</div>
                <div class='info-value amount'>NPR {receipt.AmountPaid.ToString("#,##0.00")}</div>
            </div>
        </div>
        
        <div class='section'>
            <div class='official'>This is an official receipt. Thank you for your payment.</div>
        </div>
        
        <div class='footer'>
            <p>This receipt was automatically generated on {DateTime.Now.ToString("MMMM dd, yyyy")}.</p>
            <p>For any questions, please contact the {receipt.MunicipalityName} Tax Office.</p>
        </div>
    </div>
</body>
</html>";

            return html;
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<PaymentResponseDto>> GetPayment(Guid id)
        {
            var payment = await _context.Payments
                .Include(p => p.Assessment)
                    .ThenInclude(a => a.Property)
                // Also include direct property reference for provisional payments
                .Include(p => p.Property)
                .FirstOrDefaultAsync(p => p.PaymentId == id);

            if (payment == null)
            {
                return NotFound();
            }

            // Get property info - either from assessment or directly from payment
            var property = payment.Assessment?.Property ?? payment.Property;

            if (property == null)
            {
                return NotFound("Property information not found");
            }

            // Check authorization - only owner or officers from the property's municipality can view
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userRole = User.FindFirstValue(ClaimTypes.Role);
            var officerMunicipalityId = User.FindFirstValue("MunicipalityId");

            if (property.OwnerUserId != userId &&
                !(userRole == "Officer" && officerMunicipalityId == property.MunicipalityId.ToString()) &&
                userRole != "CentralAdmin")
            {
                return Forbid();
            }

            return new PaymentResponseDto
            {
                PaymentId = payment.PaymentId,
                AssessmentId = payment.AssessmentId, // Now nullable
                PropertyId = payment.PropertyId,
                FiscalYearId = payment.FiscalYearId,
                AmountPaid = payment.AmountPaid,
                PaymentDate = payment.PaymentDate,
                PaymentGateway = payment.PaymentGateway,
                TransactionId = payment.TransactionId,
                Status = payment.Status,
                Provisional = payment.Provisional,
                Partial = payment.Partial,
                CreatedAt = payment.CreatedAt
            };
        }

        [HttpPost("pay-tax")]
        [Authorize(Policy = "RequireCitizenRole")]
        public async Task<ActionResult<PaymentResponseDto>> PayTaxDirectly(PropertyTaxPaymentDto paymentDto)
        {
            // Validate property exists and is approved
            var property = await _context.Properties
                .Include(p => p.Municipality)
                .Include(p => p.Owner)
                .FirstOrDefaultAsync(p => p.PropertyId == paymentDto.PropertyId);

            if (property == null)
            {
                return NotFound("Property not found.");
            }

            if (property.Status != "Approved")
            {
                return BadRequest("Tax can only be paid for approved properties.");
            }

            // Check if user is the property owner
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (property.OwnerUserId != userId)
            {
                return Forbid("You can only pay tax for your own properties.");
            }

            // Validate fiscal year exists
            var fiscalYear = await _context.FiscalYears.FindAsync(paymentDto.FiscalYearId);
            if (fiscalYear == null)
            {
                return NotFound("Fiscal year not found.");
            }

            // Check if return filing has been submitted for this property and fiscal year
            var returnFiling = await _context.ReturnFilings
                .FirstOrDefaultAsync(rf => rf.PropertyId == paymentDto.PropertyId && rf.FiscalYearId == paymentDto.FiscalYearId);

            if (returnFiling == null)
            {
                return BadRequest("Return filing must be submitted before making a payment. Please file your tax return first.");
            }

            try
            {
                // Calculate expected tax using the tax calculation service
                var taxCalculation = await _taxCalculationService.CalculateTaxAsync(property, paymentDto.FiscalYearId);
                var expectedTax = taxCalculation.TaxAmount;

                // Handle scenarios where payment amount >= expected tax (full payment or overpayment)
                if (paymentDto.AmountPaid >= expectedTax)
                {
                    // Create payment record
                    var payment = new Payment
                    {
                        PaymentId = Guid.NewGuid(),
                        PropertyId = paymentDto.PropertyId,
                        FiscalYearId = paymentDto.FiscalYearId,
                        AssessmentId = null, // No assessment for direct payments
                        AmountPaid = paymentDto.AmountPaid,
                        PaymentDate = DateTime.UtcNow,
                        PaymentGateway = GetPaymentGateway(paymentDto.PaymentMethod),
                        TransactionId = paymentDto.TransactionId ?? GenerateTransactionId(),
                        Status = "Success",
                        Partial = false,
                        Provisional = false, // This is a direct tax payment, not provisional
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.Payments.Add(payment);
                    await _context.SaveChangesAsync();

                    // Send payment confirmation email to taxpayer
                    var taxpayerEmail = property.Owner.Email;
                    if (!string.IsNullOrEmpty(taxpayerEmail))
                    {
                        await _emailService.SendPaymentConfirmationToTaxPayer(
                            payment,
                            taxpayerEmail
                        );
                    }
                    // Return success response
                    return Ok(new PaymentResponseDto
                    {
                        PaymentId = payment.PaymentId,
                        AssessmentId = payment.AssessmentId,
                        PropertyId = payment.PropertyId,
                        FiscalYearId = payment.FiscalYearId,
                        AmountPaid = payment.AmountPaid,
                        PaymentDate = payment.PaymentDate,
                        PaymentGateway = payment.PaymentGateway,
                        TransactionId = payment.TransactionId,
                        Status = payment.Status,
                        Provisional = payment.Provisional,
                        Partial = payment.Partial,
                        CreatedAt = payment.CreatedAt
                    });
                }
                else
                {
                    // Handle underpayment - create partial payment and assessment for deficit
                    var deficit = expectedTax - paymentDto.AmountPaid;

                    // Create payment record for the partial payment
                    var partialPayment = new Payment
                    {
                        PaymentId = Guid.NewGuid(),
                        PropertyId = paymentDto.PropertyId,
                        FiscalYearId = paymentDto.FiscalYearId,
                        AssessmentId = null, // Will be linked to assessment after creation
                        AmountPaid = paymentDto.AmountPaid,
                        PaymentDate = DateTime.UtcNow,
                        PaymentGateway = GetPaymentGateway(paymentDto.PaymentMethod),
                        TransactionId = paymentDto.TransactionId ?? GenerateTransactionId(),
                        Status = "Success",
                        Partial = true, // Mark as partial payment
                        Provisional = false, // This is a direct tax payment
                        CreatedAt = DateTime.UtcNow
                    };

                    _context.Payments.Add(partialPayment);
                    await _context.SaveChangesAsync();

                    // Send payment confirmation email to taxpayer
                    var taxpayerEmail = property.Owner.Email;
                    if (!string.IsNullOrEmpty(taxpayerEmail))
                    {
                        await _emailService.SendPaymentConfirmationToTaxPayer(
                             partialPayment,
                            taxpayerEmail
                        );
                    }

                    // Create assessment for the deficit amount
                    try
                    {
                        var assessment = await CreateAssessmentForUnderpayment(property, fiscalYear, deficit, partialPayment);

                        // Link the payment to the created assessment
                        partialPayment.AssessmentId = assessment.AssessmentId;
                        await _context.SaveChangesAsync();

                        // Return success response indicating partial payment and assessment creation
                        return Ok(new PaymentResponseDto
                        {
                            PaymentId = partialPayment.PaymentId,
                            AssessmentId = assessment.AssessmentId,
                            PropertyId = partialPayment.PropertyId,
                            FiscalYearId = partialPayment.FiscalYearId,
                            AmountPaid = partialPayment.AmountPaid,
                            PaymentDate = partialPayment.PaymentDate,
                            PaymentGateway = partialPayment.PaymentGateway,
                            TransactionId = partialPayment.TransactionId,
                            Status = partialPayment.Status,
                            Provisional = partialPayment.Provisional,
                            Partial = partialPayment.Partial,
                            CreatedAt = partialPayment.CreatedAt,
                            // Additional info for underpayment scenario
                            Message = $"Partial payment of {paymentDto.AmountPaid:F2} received. Assessment created for remaining amount of {deficit:F2}.",
                            ExpectedTaxAmount = expectedTax,
                            DeficitAmount = deficit,
                            AssessmentCreated = true
                        });
                    }
                    catch (Exception assessmentEx)
                    {
                        // If assessment creation fails, we should still have the partial payment recorded
                        // but we need to inform the user
                        return Ok(new PaymentResponseDto
                        {
                            PaymentId = partialPayment.PaymentId,
                            AssessmentId = null,
                            PropertyId = partialPayment.PropertyId,
                            FiscalYearId = partialPayment.FiscalYearId,
                            AmountPaid = partialPayment.AmountPaid,
                            PaymentDate = partialPayment.PaymentDate,
                            PaymentGateway = partialPayment.PaymentGateway,
                            TransactionId = partialPayment.TransactionId,
                            Status = partialPayment.Status,
                            Provisional = partialPayment.Provisional,
                            Partial = partialPayment.Partial,
                            CreatedAt = partialPayment.CreatedAt,
                            Message = $"Partial payment of {paymentDto.AmountPaid:F2} received, but assessment creation failed: {assessmentEx.Message}. Please contact support.",
                            ExpectedTaxAmount = expectedTax,
                            DeficitAmount = deficit,
                            AssessmentCreated = false
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return BadRequest($"Error processing payment: {ex.Message}");
            }
        }

        // Helper methods
        private string GetPaymentGateway(string paymentMethod)
        {
            return paymentMethod switch
            {
                "card" => "PaymentGatewayCard",
                "bank" => "PaymentGatewayBank",
                "digital" => "PaymentGatewayDigital",
                _ => "PaymentGatewayDefault"
            };
        }

        private string GenerateTransactionId()
        {
            return $"TXN-{DateTime.UtcNow:yyyyMMddHHmmss}-{Guid.NewGuid().ToString()[..8]}";
        }

        // Helper method to create assessment for underpayment scenarios
        private async Task<Assessment> CreateAssessmentForUnderpayment(Property property, FiscalYear fiscalYear, decimal deficitAmount, Payment partialPayment)
        {
            // Calculate property value for assessment
            decimal calculatedValue;

            // Try to get active tax configuration for the municipality and fiscal year
            var taxConfig = await _context.MunicipalityTaxConfigs
                .FirstOrDefaultAsync(tc => tc.MunicipalityId == property.MunicipalityId &&
                                         tc.FiscalYearId == fiscalYear.FiscalYearId);

            if (taxConfig == null)
            {
                // Fallback to municipality defaults
                calculatedValue = _gisService.CalculatePropertyValue(property, property.Municipality?.ValuationRulesConfigJson ?? "{}");
            }
            else
            {
                // Use active tax configuration
                var valuationRules = System.Text.Json.JsonSerializer.Deserialize<Core.Models.ValuationRulesConfig>(
                    taxConfig.ValuationRulesConfigJson ?? "{}") ?? new Core.Models.ValuationRulesConfig();
                calculatedValue = _gisService.CalculatePropertyValue(property, valuationRules);
            }

            // Parse the fiscal year name to get year number (assuming format like "2023-24" or "2080-81")
            int assessmentYear = DateTime.Now.Year;
            if (!string.IsNullOrEmpty(fiscalYear.Name))
            {
                // Try to extract year from fiscal year name
                var yearParts = fiscalYear.Name.Split('-');
                if (yearParts.Length > 0 && int.TryParse(yearParts[0], out int parsedYear))
                {
                    assessmentYear = parsedYear;
                }
            }

            // Create the assessment for the deficit
            var assessment = new Assessment
            {
                AssessmentId = Guid.NewGuid(),
                PropertyId = property.PropertyId,
                FiscalYearId = fiscalYear.FiscalYearId,
                AssessmentYear = assessmentYear,
                AssessmentDate = DateTime.UtcNow,
                CalculatedValue = calculatedValue,
                FinalAssessedValue = calculatedValue, // Use calculated value as final value
                TaxAmount = deficitAmount, // Tax amount is the deficit that needs to be paid
                PaymentStatus = "Underpaid", // Status indicating partial payment received
                AssessedByOfficerId = null, // System-generated assessment
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,

                // Additional assessment properties
                OriginalAmount = deficitAmount,
                OverriddenValue = null,
                OverrideReason = null,
                UpperAssessment = deficitAmount,
                LowerAssessment = deficitAmount,
                ActualAssessment = deficitAmount,
                PenaltyPercent = property.Municipality?.DefaultPenaltyPercent ?? 0,
                DiscountPercent = property.Municipality?.DefaultDiscountPercent ?? 0,
                Origin = Core.Enums.AssessmentOrigin.UnderPaid // Mark as underpayment-generated
            };

            _context.Assessments.Add(assessment);
            await _context.SaveChangesAsync();

            return assessment;
        }
    }
}
