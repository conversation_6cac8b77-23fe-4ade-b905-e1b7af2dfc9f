import React, { useState, useEffect } from 'react';
import * as api from '../services/api';

interface Negotiation {
  id: string;
  appealId: string;
  assessmentId: string;
  propertyAddress: string;
  initialAmount: number;
  negotiatedAmount: number;
  officerComments: string;
  citizenComments: string;
  status: 'Pending' | 'InProgress' | 'Accepted' | 'Rejected' | 'Finalized';
  createdAt: string;
  updatedAt: string;
}

interface NegotiationViewProps {
  negotiationId: string;
  onPaymentClick?: () => void;
  onClose: () => void;
}

const NegotiationView: React.FC<NegotiationViewProps> = ({
  negotiationId,
  onPaymentClick,
  onClose
}) => {
  const [negotiation, setNegotiation] = useState<Negotiation | null>(null);
  const [citizenResponse, setCitizenResponse] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchNegotiation = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/negotiations/${negotiationId}`);
        setNegotiation((response as { data: unknown }).data as Negotiation);
      } catch (err) {
        console.error('Failed to fetch negotiation:', err);
        setError('Failed to load negotiation details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchNegotiation();
  }, [negotiationId]);

  const handleAccept = async () => {
    if (!negotiation) return;
    
    try {
      setSubmitting(true);
      await api.put(`/negotiations/${negotiationId}/accept`, {
        comments: citizenResponse
      });
      
      // Refresh negotiation data
      const response = await api.get(`/negotiations/${negotiationId}`);
      setNegotiation((response as { data: unknown }).data as Negotiation);
      setCitizenResponse('');
    } catch (err) {
      console.error('Failed to accept negotiation:', err);
      setError('Failed to accept negotiation. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!negotiation) return;
    
    try {
      setSubmitting(true);
      await api.put(`/negotiations/${negotiationId}/reject`, {
        comments: citizenResponse
      });
      
      // Refresh negotiation data
      const response = await api.get(`/negotiations/${negotiationId}`);
      setNegotiation((response as { data: unknown }).data as Negotiation);
      setCitizenResponse('');
    } catch (err) {
      console.error('Failed to reject negotiation:', err);
      setError('Failed to reject negotiation. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="flex justify-center">
            <span className="loading loading-spinner loading-lg"></span>
          </div>
          <p className="text-center">Loading negotiation details...</p>
        </div>
      </div>
    );
  }

  if (!negotiation) {
    return (
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">Negotiation Not Found</h2>
          <div className="alert alert-error">
            <span>{error || 'The requested negotiation could not be found.'}</span>
          </div>
          <div className="card-actions justify-end">
            <button
              onClick={onClose}
              className="btn btn-ghost"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Pending':
        return 'badge badge-warning';
      case 'InProgress':
        return 'badge badge-info';
      case 'Accepted':
        return 'badge badge-success';
      case 'Rejected':
        return 'badge badge-error';
      case 'Finalized':
        return 'badge badge-secondary';
      default:
        return 'badge badge-ghost';
    }
  };

  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">Negotiation Details</h2>
        
        {error && (
          <div className="alert alert-error">
            <span>{error}</span>
          </div>
        )}
      
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-semibold">Property Information</h3>
            <span className={getStatusBadgeClass(negotiation.status)}>
              {negotiation.status}
            </span>
          </div>
          <div className="bg-base-200 p-3 rounded">
            <p><span className="font-medium">Property Address:</span> {negotiation.propertyAddress}</p>
            <p><span className="font-medium">Created:</span> {formatDate(negotiation.createdAt)}</p>
            <p><span className="font-medium">Last Updated:</span> {formatDate(negotiation.updatedAt)}</p>
          </div>
        </div>
      
        <div className="mb-6">
          <h3 className="font-semibold mb-2">Negotiation Details</h3>
          <div className="bg-base-200 p-3 rounded">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Initial Tax Amount:</span>
              <span className="font-bold">${negotiation.initialAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium">Negotiated Amount:</span>
              <span className="font-bold text-info">${negotiation.negotiatedAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between mb-2">
              <span className="font-medium">Difference:</span>
              <span className={negotiation.initialAmount > negotiation.negotiatedAmount ? 'text-success font-bold' : 'text-error font-bold'}>
                ${Math.abs(negotiation.initialAmount - negotiation.negotiatedAmount).toFixed(2)}
                {negotiation.initialAmount > negotiation.negotiatedAmount ? ' less' : ' more'}
              </span>
            </div>
          </div>
        </div>
      
        <div className="mb-6">
          <h3 className="font-semibold mb-2">Officer Comments</h3>
          <div className="alert alert-info">
            <span>{negotiation.officerComments || 'No comments provided.'}</span>
          </div>
        </div>
      
        {negotiation.citizenComments && (
          <div className="mb-6">
            <h3 className="font-semibold mb-2">Your Previous Response</h3>
            <div className="alert alert-success">
              <span>{negotiation.citizenComments}</span>
            </div>
          </div>
        )}
      
        {negotiation.status === 'InProgress' && (
          <div className="form-control mb-6">
            <label className="label">
              <span className="label-text font-semibold">Your Response</span>
            </label>
            <textarea
              className="textarea textarea-bordered w-full"
              rows={4}
              value={citizenResponse}
              onChange={(e) => setCitizenResponse(e.target.value)}
              placeholder="Enter your comments about this negotiation..."
            />
          </div>
        )}
      
        <div className="card-actions justify-between mt-6">
          <button
            onClick={onClose}
            className="btn btn-ghost"
          >
            Close
          </button>
          
          {negotiation.status === 'InProgress' && (
            <div className="flex space-x-2">
              <button
                onClick={handleReject}
                disabled={submitting}
                className="btn btn-error"
              >
                {submitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Rejecting...
                  </>
                ) : (
                  'Reject'
                )}
              </button>
              <button
                onClick={handleAccept}
                disabled={submitting}
                className="btn btn-success"
              >
                {submitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Accepting...
                  </>
                ) : (
                  'Accept'
                )}
              </button>
            </div>
          )}
          
          {negotiation.status === 'Finalized' && onPaymentClick && (
            <button
              onClick={onPaymentClick}
              className="btn btn-primary"
            >
              Pay Negotiated Amount
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NegotiationView;
