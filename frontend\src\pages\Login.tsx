import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    emailOrPAN: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      await login(formData.emailOrPAN, formData.password);
      navigate("/dashboard");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-base-200 py-12 px-4 sm:px-6 lg:px-8">
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="text-center mb-6">
            <h2 className="card-title text-3xl font-bold justify-center mb-2">Sign in to your account</h2>
            <p className="text-base-content/70">Land Tax Management System</p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="alert alert-error">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="stroke-current shrink-0 h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>{error}</span>
              </div>
            )}

            <div className="space-y-4">
              <div className="form-control">
                <label className="label" htmlFor="emailOrPAN">
                  <span className="label-text font-medium">Email Address or PAN</span>
                </label>
                <input
                  id="emailOrPAN"
                  name="emailOrPAN"
                  type="text"
                  autoComplete="username"
                  required
                  className="input input-bordered w-full"
                  placeholder="Enter your email or PAN"
                  value={formData.emailOrPAN}
                  onChange={handleChange}
                />
              </div>

              <div className="form-control">
                <label className="label" htmlFor="password">
                  <span className="label-text font-medium">Password</span>
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  className="input input-bordered w-full"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleChange}
                />
              </div>
            </div>

            <div className="form-control mt-6">
              <button type="submit" disabled={isLoading} className="btn btn-primary w-full">
                {isLoading ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Signing in...
                  </>
                ) : (
                  "Sign in"
                )}
              </button>
            </div>

            {/* Demo Credentials Section */}
            <div className="divider">Demo Credentials</div>

            <div className="space-y-3">
              <div className="card bg-info/10 border border-info/20">
                <div className="card-body p-3">
                  <div className="text-sm">
                    <div className="font-semibold text-info">Citizen:</div>
                    <div className="text-xs opacity-70 mb-2">
                      Email: <EMAIL>
                      <br />
                      Password: Citizen@123
                    </div>
                    <button
                      type="button"
                      onClick={() =>
                        setFormData({
                          emailOrPAN: "<EMAIL>",
                          password: "Citizen@123",
                        })
                      }
                      className="btn btn-info btn-sm"
                    >
                      Use these credentials
                    </button>
                  </div>
                </div>
              </div>

              <div className="card bg-success/10 border border-success/20">
                <div className="card-body p-3">
                  <div className="text-sm">
                    <div className="font-semibold text-success">Officer:</div>
                    <div className="text-xs opacity-70 mb-2">
                      Email: <EMAIL>
                      <br />
                      Password: Officer@123
                    </div>
                    <button
                      type="button"
                      onClick={() =>
                        setFormData({
                          emailOrPAN: "<EMAIL>",
                          password: "Officer@123",
                        })
                      }
                      className="btn btn-success btn-sm"
                    >
                      Use these credentials
                    </button>
                  </div>
                </div>
              </div>

              <div className="card bg-secondary/10 border border-secondary/20">
                <div className="card-body p-3">
                  <div className="text-sm">
                    <div className="font-semibold text-secondary">Central Admin:</div>
                    <div className="text-xs opacity-70 mb-2">
                      Email: <EMAIL>
                      <br />
                      Password: Admin@123
                    </div>
                    <button
                      type="button"
                      onClick={() =>
                        setFormData({
                          emailOrPAN: "<EMAIL>",
                          password: "Admin@123",
                        })
                      }
                      className="btn btn-secondary btn-sm"
                    >
                      Use these credentials
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center space-y-2 pt-4">
              <p className="text-sm">
                Don't have an account?{" "}
                <Link to="/register" className="link link-primary">
                  Sign up
                </Link>
              </p>
              <p className="text-sm">
                Have a submission number?{" "}
                <Link to="/track-submission" className="link link-primary">
                  Track your submission
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
