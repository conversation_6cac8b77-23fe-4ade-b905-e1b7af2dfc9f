using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetTopologySuite.Geometries;
using NetTopologySuite;
using NetTopologySuite.IO;

namespace LandTaxSystem.API
{
    public class SeedDemoData
    {
        private static readonly Random _random = new Random(42);
        private static readonly string[] _nepaliNames = {
            "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
            "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Thapa", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>",
            "<PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON> <PERSON> <PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>",
            "<PERSON><PERSON> Shahi", "Saroj Thapa", "Sunita Pandey", "Usha Kunwar"
        };

        public static async Task SeedFromDemoFilesAsync(IServiceProvider serviceProvider,
            string parcelsPath, string buildingsPath, string taxRecordsPath)
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();

            // Check if demo data already exists
            var existingDemoProperties = await dbContext.Properties
                .Where(p => p.ParcelNumber != null && p.ParcelNumber.StartsWith("pokhara_metropolitan_city_15_1000_"))
                .CountAsync();

            if (existingDemoProperties > 0)
            {
                Console.WriteLine($"Demo properties already exist ({existingDemoProperties} found). Skipping seeding.");
                return;
            }

            Console.WriteLine("Starting demo data seeding...");

            // Get required entities
            var municipality = await dbContext.Municipalities
                .FirstOrDefaultAsync(m => m.Name == "Pokhara Metropolitan City");

            if (municipality == null)
            {
                Console.WriteLine("Pokhara Metropolitan City not found. Cannot seed demo data.");
                return;
            }

            // Get fiscal years for tax records
            var fiscalYears = await dbContext.FiscalYears.ToListAsync();
            var fiscalYearMap = new Dictionary<string, Guid>
            {
                { "fy_2076_2077", fiscalYears.FirstOrDefault(f => f.Name == "2076/77")?.FiscalYearId ?? Guid.NewGuid() },
                { "fy_2077_2078", fiscalYears.FirstOrDefault(f => f.Name == "2077/78")?.FiscalYearId ?? Guid.NewGuid() },
                { "fy_2078_2079", fiscalYears.FirstOrDefault(f => f.Name == "2078/79")?.FiscalYearId ?? Guid.NewGuid() },
                { "fy_2079_2080", fiscalYears.FirstOrDefault(f => f.Name == "2079/80")?.FiscalYearId ?? Guid.NewGuid() },
                { "fy_2080_2081", fiscalYears.FirstOrDefault(f => f.Name == "2080/81")?.FiscalYearId ?? Guid.NewGuid() },
                { "fy_2081_2082", fiscalYears.FirstOrDefault(f => f.Name == "2081/82")?.FiscalYearId ?? Guid.NewGuid() }
            };

            // Create missing fiscal years if needed
            await CreateMissingFiscalYears(dbContext, fiscalYearMap);

            // Parse data files
            var parcelsData = await ParseParcelsDataAsync(parcelsPath);
            var buildingsData = await ParseBuildingsDataAsync(buildingsPath);
            var taxRecordsData = await ParseTaxRecordsAsync(taxRecordsPath);

            // Create users for property owners
            var userMap = await CreatePropertyOwnersAsync(userManager, municipality.MunicipalityId, parcelsData);

            // Create properties from parcels data
            var properties = await CreatePropertiesAsync(dbContext, municipality, parcelsData, buildingsData, userMap);

            // Create tax payment records
            await CreateTaxPaymentRecordsAsync(dbContext, properties, taxRecordsData, fiscalYearMap);

            await dbContext.SaveChangesAsync();
            Console.WriteLine($"Successfully seeded {properties.Count} demo properties with associated data.");
        }

        private static async Task<List<ParcelData>> ParseParcelsDataAsync(string filePath)
        {
            var json = await File.ReadAllTextAsync(filePath);
            var geoJson = JsonSerializer.Deserialize<GeoJsonFeatureCollection>(json);

            var parcels = new List<ParcelData>();
            var geometryFactory = NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);

            foreach (var feature in geoJson.features)
            {
                var props = feature.properties;

                // Parse coordinates based on geometry type
                Polygon polygon;
                if (feature.geometry.type == "Polygon")
                {
                    // For polygons, coordinates is [[[x,y], [x,y], ...]]
                    var coordinatesJson = (JsonElement)feature.geometry.coordinates;
                    var ring = coordinatesJson[0]; // First ring of polygon

                    var coords = new List<Coordinate>();
                    foreach (var point in ring.EnumerateArray())
                    {
                        var x = point[0].GetDouble();
                        var y = point[1].GetDouble();
                        coords.Add(new Coordinate(x, y));
                    }

                    // Ensure polygon is closed
                    if (!coords.First().Equals2D(coords.Last()))
                    {
                        coords.Add(coords.First());
                    }

                    polygon = geometryFactory.CreatePolygon(coords.ToArray());
                }
                else
                {
                    // Skip non-polygon geometries for parcels
                    continue;
                }

                parcels.Add(new ParcelData
                {
                    ParcelId = props.parcel_id,
                    District = props.district,
                    Municipality = props.municipality,
                    WardNo = props.wardno,
                    MapSheetNo = props.mapsheetno,
                    ParcelNo = props.parcelno,
                    Area = props.area,
                    OwnerId = props.owner_id,
                    OwnerName = props.owner_name,
                    ValuationMicrozone = props.valuation_microzone,
                    LanduseCategory = props.landuse_category,
                    SlopeCategory = props.slope_category,
                    ShapeCategory = props.shape_category,
                    FrontageCategory = props.frontage_category,
                    Geometry = polygon
                });
            }

            return parcels;
        }

        private static async Task<List<BuildingData>> ParseBuildingsDataAsync(string filePath)
        {
            var json = await File.ReadAllTextAsync(filePath);
            var geoJson = JsonSerializer.Deserialize<GeoJsonFeatureCollection>(json);

            var buildings = new List<BuildingData>();

            foreach (var feature in geoJson.features)
            {
                var props = feature.properties;

                buildings.Add(new BuildingData
                {
                    BuildingId = props.building_id,
                    ParcelId = props.parcel_id,
                    PlinthArea = props.plinth_area,
                    TotalArea = props.total_area,
                    Use = props.use,
                    Structure = props.structure,
                    ConstructionDate = DateTime.TryParse(props.construction_date, out var date) ? date : DateTime.Now.AddYears(-10)
                });
            }

            return buildings;
        }

        private static async Task<Dictionary<string, Dictionary<string, bool>>> ParseTaxRecordsAsync(string filePath)
        {
            var lines = await File.ReadAllLinesAsync(filePath);
            var records = new Dictionary<string, Dictionary<string, bool>>();

            if (lines.Length == 0) return records;

            var headers = lines[0].Split(',');
            var fiscalYearColumns = headers.Skip(1).ToArray(); // Skip parcel_id column

            for (int i = 1; i < lines.Length; i++)
            {
                var values = lines[i].Split(',');
                var parcelId = values[0];
                var payments = new Dictionary<string, bool>();

                for (int j = 1; j < values.Length && j - 1 < fiscalYearColumns.Length; j++)
                {
                    payments[fiscalYearColumns[j - 1]] = !string.IsNullOrWhiteSpace(values[j]) && values[j].ToLower() == "yes";
                }

                records[parcelId] = payments;
            }

            return records;
        }

        private static async Task CreateMissingFiscalYears(ApplicationDbContext dbContext, Dictionary<string, Guid> fiscalYearMap)
        {
            var existingFiscalYears = await dbContext.FiscalYears.Select(f => f.Name).ToListAsync();

            var fiscalYearsToCreate = new List<FiscalYear>();
            var fiscalYearDefinitions = new Dictionary<string, (string name, DateTime start, DateTime end)>
            {
                { "fy_2076_2077", ("2076/77", new DateTime(2019, 7, 16, 0, 0, 0, DateTimeKind.Utc), new DateTime(2020, 7, 15, 23, 59, 59, DateTimeKind.Utc)) },
                { "fy_2077_2078", ("2077/78", new DateTime(2020, 7, 16, 0, 0, 0, DateTimeKind.Utc), new DateTime(2021, 7, 15, 23, 59, 59, DateTimeKind.Utc)) },
                { "fy_2078_2079", ("2078/79", new DateTime(2021, 7, 16, 0, 0, 0, DateTimeKind.Utc), new DateTime(2022, 7, 15, 23, 59, 59, DateTimeKind.Utc)) },
                { "fy_2079_2080", ("2079/80", new DateTime(2022, 7, 16, 0, 0, 0, DateTimeKind.Utc), new DateTime(2023, 7, 15, 23, 59, 59, DateTimeKind.Utc)) },
                { "fy_2080_2081", ("2080/81", new DateTime(2023, 7, 16, 0, 0, 0, DateTimeKind.Utc), new DateTime(2024, 7, 15, 23, 59, 59, DateTimeKind.Utc)) },
                { "fy_2081_2082", ("2081/82", new DateTime(2024, 7, 16, 0, 0, 0, DateTimeKind.Utc), new DateTime(2025, 7, 15, 23, 59, 59, DateTimeKind.Utc)) }
            };

            foreach (var kvp in fiscalYearDefinitions)
            {
                if (!existingFiscalYears.Contains(kvp.Value.name))
                {
                    var fiscalYear = new FiscalYear
                    {
                        FiscalYearId = fiscalYearMap[kvp.Key],
                        Name = kvp.Value.name,
                        StartDate = kvp.Value.start,
                        EndDate = kvp.Value.end,
                        IsActive = kvp.Value.name == "2080/81", // Make 2080/81 active
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    fiscalYearsToCreate.Add(fiscalYear);
                }
            }

            if (fiscalYearsToCreate.Any())
            {
                dbContext.FiscalYears.AddRange(fiscalYearsToCreate);
                await dbContext.SaveChangesAsync();
                Console.WriteLine($"Created {fiscalYearsToCreate.Count} missing fiscal years.");
            }
        }

        private static async Task<Dictionary<string, string>> CreatePropertyOwnersAsync(
            UserManager<ApplicationUser> userManager, Guid municipalityId, List<ParcelData> parcelsData)
        {
            var userMap = new Dictionary<string, string>();
            var uniqueOwners = parcelsData
                .Where(p => p.OwnerId != "100") // Skip government properties
                .GroupBy(p => new { p.OwnerId, p.OwnerName })
                .Select(g => g.Key)
                .ToList();

            Console.WriteLine($"Creating {uniqueOwners.Count} property owners...");

            foreach (var owner in uniqueOwners)
            {
                var email = $"{owner.OwnerName.Replace(" ", "").ToLower()}@demo.com";
                var existingUser = await userManager.FindByEmailAsync(email);

                if (existingUser == null)
                {
                    var user = new ApplicationUser
                    {
                        UserName = email,
                        Email = email,
                        EmailConfirmed = true,
                        FullName = owner.OwnerName,
                        Role = "Citizen",
                        Status = "Active",
                        DateOfBirth = DateTime.UtcNow.AddYears(-_random.Next(25, 70)),
                        Gender = _random.Next(2) == 0 ? "Male" : "Female",
                        Nationality = "Nepali",
                        CitizenshipNumber = $"15-02-77-{_random.Next(10000, 99999)}",
                        PermanentAddress = $"Ward 15, Pokhara Metropolitan City",
                        WardNumber = "15",
                        ToleStreet = $"Tole {_random.Next(1, 10)}",
                        MunicipalityId = municipalityId,
                        PAN = $"30{_random.Next(1000000, 9999999)}",
                        PhoneNumber = $"98{_random.Next(10000000, 99999999)}",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    var result = await userManager.CreateAsync(user, "Demo@123");
                    if (result.Succeeded)
                    {
                        await userManager.AddToRoleAsync(user, "Citizen");
                        userMap[owner.OwnerId] = user.Id;
                    }
                    else
                    {
                        Console.WriteLine($"Failed to create user for {owner.OwnerName}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                    }
                }
                else
                {
                    userMap[owner.OwnerId] = existingUser.Id;
                }
            }

            return userMap;
        }

        private static async Task<List<Property>> CreatePropertiesAsync(
            ApplicationDbContext dbContext, Municipality municipality,
            List<ParcelData> parcelsData, List<BuildingData> buildingsData,
            Dictionary<string, string> userMap)
        {
            var properties = new List<Property>();
            var buildingsByParcel = buildingsData.GroupBy(b => b.ParcelId).ToDictionary(g => g.Key, g => g.ToList());

            Console.WriteLine($"Creating {parcelsData.Count} properties...");

            foreach (var parcel in parcelsData)
            {
                // Skip government properties for now (owner_id = "100")
                if (parcel.OwnerId == "100" || !userMap.ContainsKey(parcel.OwnerId))
                    continue;

                var buildings = buildingsByParcel.GetValueOrDefault(parcel.ParcelId, new List<BuildingData>());
                var mainBuilding = buildings.FirstOrDefault();

                var property = new Property
                {
                    PropertyId = Guid.NewGuid(),
                    OwnerUserId = userMap[parcel.OwnerId],
                    MunicipalityId = municipality.MunicipalityId,
                    WardNumber = parcel.WardNo,
                    Street = $"Map Sheet {parcel.MapSheetNo}",
                    ParcelNumber = parcel.ParcelId,
                    Address = $"Parcel {parcel.ParcelNo}, Ward {parcel.WardNo}, {parcel.Municipality}",
                    LandAreaSqM = (decimal)parcel.Area,
                    UsageType = MapLandUseToUsageType(parcel.LanduseCategory),
                    ParcelGeometry = parcel.Geometry,

                    // Building details from buildings data
                    BuildingBuiltUpAreaSqM = mainBuilding != null ? (decimal?)mainBuilding.PlinthArea : null,
                    BuildingConstructionType = MapStructureToConstructionType(mainBuilding?.Structure),
                    BuildingConstructionYear = mainBuilding?.ConstructionDate.Year,

                    // Additional fields from parcel data
                    SheetNo = parcel.MapSheetNo,
                    Status = "Approved",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    RegistrationDate = DateTime.UtcNow.AddDays(-_random.Next(30, 365))
                };

                // Generate Nepali land measurements (approximate conversion from sq meters)
                GenerateNepaliMeasurements(property, (decimal)parcel.Area);

                properties.Add(property);
            }

            dbContext.Properties.AddRange(properties);
            await dbContext.SaveChangesAsync();

            return properties;
        }

        private static async Task CreateTaxPaymentRecordsAsync(
            ApplicationDbContext dbContext, List<Property> properties,
            Dictionary<string, Dictionary<string, bool>> taxRecordsData,
            Dictionary<string, Guid> fiscalYearMap)
        {
            var payments = new List<Payment>();
            var propertyByParcelId = properties.ToDictionary(p => p.ParcelNumber, p => p);

            Console.WriteLine("Creating tax payment records...");

            foreach (var taxRecord in taxRecordsData)
            {
                if (!propertyByParcelId.ContainsKey(taxRecord.Key))
                    continue;

                var property = propertyByParcelId[taxRecord.Key];

                foreach (var fiscalYearPayment in taxRecord.Value)
                {
                    if (fiscalYearPayment.Value && fiscalYearMap.ContainsKey(fiscalYearPayment.Key))
                    {
                        var payment = new Payment
                        {
                            PaymentId = Guid.NewGuid(),
                            PropertyId = property.PropertyId,
                            FiscalYearId = fiscalYearMap[fiscalYearPayment.Key],
                            AmountPaid = _random.Next(5000, 25000), // Random amount between 5k-25k
                            PaymentDate = GenerateRandomPaymentDate(fiscalYearPayment.Key),
                            PaymentGateway = "DemoPayment",
                            TransactionId = $"DEMO_{Guid.NewGuid().ToString("N")[..8].ToUpper()}",
                            Status = "Success",
                            CreatedAt = DateTime.UtcNow
                        };

                        payments.Add(payment);
                    }
                }
            }

            dbContext.Payments.AddRange(payments);
            await dbContext.SaveChangesAsync();
            Console.WriteLine($"Created {payments.Count} tax payment records.");
        }

        private static string MapLandUseToUsageType(string landuseCategory)
        {
            return landuseCategory?.ToLower() switch
            {
                "residential_self_occupied" => "Residential",
                "residential_partially_occupied" => "Residential",
                "residential_fully_let_out" => "Residential",
                "commercial" => "Commercial",
                _ => "Residential"
            };
        }

        private static string? MapStructureToConstructionType(string? structure)
        {
            return structure?.ToLower() switch
            {
                "rcc_frame_with_rcc_roof" => "RCC",
                "rcc_frame_with_non_rcc_roof" => "RCC",
                "load_bearing_with_rcc_roof" => "BrickMud",
                "temporary_or_kucha" => "Other",
                _ => null
            };
        }

        private static void GenerateNepaliMeasurements(Property property, decimal areaSqM)
        {
            // Approximate conversion: 1 Ropani ≈ 508.72 sq meters
            var totalRopani = areaSqM / 508.72m;

            property.Ropani = (int)totalRopani;
            var remainingRopani = totalRopani - property.Ropani.Value;

            // 1 Ropani = 16 Aana
            var totalAana = remainingRopani * 16;
            property.Aana = (int)totalAana;
            var remainingAana = totalAana - property.Aana.Value;

            // 1 Aana = 4 Paisa
            property.Paisa = (int)(remainingAana * 4);
        }

        private static DateTime GenerateRandomPaymentDate(string fiscalYear)
        {
            var baseDate = fiscalYear switch
            {
                "fy_2076_2077" => new DateTime(2020, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                "fy_2077_2078" => new DateTime(2021, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                "fy_2078_2079" => new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                "fy_2079_2080" => new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                "fy_2080_2081" => new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                "fy_2081_2082" => new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                _ => DateTime.UtcNow
            };

            return baseDate.AddDays(_random.Next(0, 365));
        }
    }

    // Data transfer classes
    public class ParcelData
    {
        public string ParcelId { get; set; } = string.Empty;
        public string District { get; set; } = string.Empty;
        public string Municipality { get; set; } = string.Empty;
        public int WardNo { get; set; }
        public string MapSheetNo { get; set; } = string.Empty;
        public int ParcelNo { get; set; }
        public double Area { get; set; }
        public string OwnerId { get; set; } = string.Empty;
        public string OwnerName { get; set; } = string.Empty;
        public int? ValuationMicrozone { get; set; }
        public string? LanduseCategory { get; set; }
        public string? SlopeCategory { get; set; }
        public string? ShapeCategory { get; set; }
        public string? FrontageCategory { get; set; }
        public Polygon Geometry { get; set; } = null!;
    }

    public class BuildingData
    {
        public string BuildingId { get; set; } = string.Empty;
        public string ParcelId { get; set; } = string.Empty;
        public double PlinthArea { get; set; }
        public double TotalArea { get; set; }
        public string Use { get; set; } = string.Empty;
        public string Structure { get; set; } = string.Empty;
        public DateTime ConstructionDate { get; set; }
    }

    // GeoJSON parsing classes
    public class GeoJsonFeatureCollection
    {
        public string type { get; set; } = string.Empty;
        public List<GeoJsonFeature> features { get; set; } = new();
    }

    public class GeoJsonFeature
    {
        public string type { get; set; } = string.Empty;
        public GeoJsonProperties properties { get; set; } = new();
        public GeoJsonGeometry geometry { get; set; } = new();
    }

    public class GeoJsonProperties
    {
        public int fid { get; set; }
        public string parcel_id { get; set; } = string.Empty;
        public string district { get; set; } = string.Empty;
        public string municipality { get; set; } = string.Empty;
        public int wardno { get; set; }
        public string mapsheetno { get; set; } = string.Empty;
        public int parcelno { get; set; }
        public double area { get; set; }
        public string owner_id { get; set; } = string.Empty;
        public string owner_name { get; set; } = string.Empty;
        public int? valuation_microzone { get; set; }
        public string? landuse_category { get; set; }
        public string? slope_category { get; set; }
        public string? shape_category { get; set; }
        public string? frontage_category { get; set; }

        // Building properties
        public string? building_id { get; set; }
        public double plinth_area { get; set; }
        public double total_area { get; set; }
        public string? use { get; set; }
        public string? structure { get; set; }
        public string? construction_date { get; set; }
    }

    public class GeoJsonGeometry
    {
        public string type { get; set; } = string.Empty;
        public object coordinates { get; set; } = new object();
    }
}
