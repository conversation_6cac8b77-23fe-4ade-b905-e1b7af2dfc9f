import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import api from "../services/api";
import type { Property } from "../types";
import { AdminLayout } from "../components/admin";

const MyProperties: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [filter, setFilter] = useState<string>("all");

  useEffect(() => {
    loadProperties();
  }, []);

  const loadProperties = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await api.get("/properties/mine");
      // Map backend property fields to frontend property model
      const mappedProperties = response.data.map((prop: {
        propertyId: string;
        ownerUserId: string;
        municipalityId: string;
        parcelGeoJson: string | { type: string; coordinates: number[][][]; };
        landAreaSqM: number;
        usageType: string;
        buildingBuiltUpAreaSqM?: number;
        buildingConstructionType?: string;
        buildingConstructionYear?: number;
        taxDue?: number;
        status?: string;
        [key: string]: unknown; // For other properties we might not explicitly list
      }) => {
        return {
          ...prop,
          // Map propertyId to id for frontend compatibility
          id: prop.propertyId,
          // Add any missing fields or map differently named fields
          landArea: prop.landAreaSqM,
          builtUpArea: prop.buildingBuiltUpAreaSqM,
          constructionType: prop.buildingConstructionType,
          constructionYear: prop.buildingConstructionYear,
          parcelGeoJson: prop.parcelGeoJson
        };
      });
      
      setProperties(mappedProperties);
    } catch (error) {
      console.error("Failed to load properties:", error);
      setError("Failed to load your properties. Please try again later.");
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Approved":
        return "badge badge-success";
      case "PendingReview":
        return "badge badge-warning";
      case "Rejected":
        return "badge badge-error";
      default:
        return "badge badge-ghost";
    }
  };

  const filteredProperties = properties.filter((property) => {
    if (filter === "all") return true;
    return property.status === filter;
  });

  const getQuickActions = (property: Property) => {
    const actions = [];

    // Always add the View Details link
    actions.push(
      <Link
        key="view"
        to={`/properties/${property.id}`}
        className="btn btn-ghost btn-sm"
      >
        View Details
      </Link>
    );

    if (
      property.status === "Approved" &&
      property.taxDue &&
      property.taxDue > 0
    ) {
      actions.push(
        <Link
          key="pay"
          to={`/tax-payment-options/${property.id}`}
          className="btn btn-secondary btn-sm"
        >
          Pay Tax (NPR {property.taxDue.toLocaleString()})
        </Link>
      );
    }

    if (property.status === "Rejected") {
      actions.push(
        <button
          key="resubmit"
          onClick={() => {
            /* TODO: Implement resubmit */
          }}
          className="btn btn-info btn-sm"
        >
          Re-submit
        </button>
      );
    }

    return actions;
  };

  if (loading) {
    return (
      <AdminLayout title="My Properties">
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </AdminLayout>
    );
  }
  
  if (error) {
    return (
      <AdminLayout title="My Properties">
        <div className="alert alert-error">
          <div>
            <h2 className="text-xl font-semibold mb-2">Error Loading Properties</h2>
            <p>{error}</p>
            <button 
              onClick={() => loadProperties()} 
              className="btn btn-primary mt-4"
            >
              Try Again
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="My Properties"
      subtitle="Manage your registered properties and view their status"
    >
      <div className="flex justify-end mb-6">
        <div className="flex space-x-3">
          <Link to="/bulk-tax-payment" className="btn btn-secondary">
            Bulk Tax Payment
            {properties.filter(p => p.status === "Approved" && p.taxDue && p.taxDue > 0).length > 0 && (
              <span className="badge badge-secondary ml-2">
                {properties.filter(p => p.status === "Approved" && p.taxDue && p.taxDue > 0).length}
              </span>
            )}
          </Link>
          <Link to="/properties/register" className="btn btn-primary">
            Register New Property
          </Link>
        </div>
      </div>

          {/* Filters */}
          <div className="divider"></div>
          <div className="tabs tabs-boxed mb-6">
            <button
              onClick={() => setFilter("all")}
              className={`tab ${filter === "all" ? "tab-active" : ""}`}
            >
              All ({properties.length})
            </button>
            <button
              onClick={() => setFilter("Approved")}
              className={`tab ${filter === "Approved" ? "tab-active" : ""}`}
            >
              Approved (
              {properties.filter((p) => p.status === "Approved").length})
            </button>
            <button
              onClick={() => setFilter("PendingReview")}
              className={`tab ${filter === "PendingReview" ? "tab-active" : ""}`}
            >
              Pending (
              {properties.filter((p) => p.status === "PendingReview").length})
            </button>
            <button
              onClick={() => setFilter("Rejected")}
              className={`tab ${filter === "Rejected" ? "tab-active" : ""}`}
            >
              Rejected (
              {properties.filter((p) => p.status === "Rejected").length})
            </button>
          </div>

          {/* Properties List */}
          {filteredProperties.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-base-content/30 text-6xl mb-4">🏠</div>
              <h3 className="text-lg font-medium text-base-content mb-2">
                {filter === "all"
                  ? "No properties registered"
                  : `No ${filter.toLowerCase()} properties`}
              </h3>
              <p className="text-base-content/70 mb-6">
                {filter === "all"
                  ? "Start by registering your first property to manage your tax obligations."
                  : `You don't have any ${filter.toLowerCase()} properties at the moment.`}
              </p>
              {filter === "all" && (
                <Link to="/properties/register" className="btn btn-primary">
                  Register Your First Property
                </Link>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProperties.map((property) => (
                <div
                  key={property.id}
                  className="card bg-base-100 border border-base-300 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="card-body">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="card-title text-lg">
                            {property.address}
                          </h3>
                          <div className={getStatusBadge(property.status)}>
                            {property.status === "PendingReview"
                              ? "Pending Review"
                              : property.status}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-base-content/70 mb-4">
                          <div>
                            <span className="font-medium">Municipality:</span>
                            <div>{property.municipalityName}</div>
                          </div>
                          <div>
                            <span className="font-medium">Ward No:</span>
                            <div>Ward {property.wardNumber}</div>
                          </div>
                          <div>
                            <span className="font-medium">Parcel No:</span>
                            <div>{property.parcelNumber || 'N/A'}</div>
                          </div>
                          <div>
                            <span className="font-medium">Land Area:</span>
                            <div>{property.landArea} sq.m</div>
                          </div>
                          <div>
                            <span className="font-medium">Usage Type:</span>
                            <div>{property.usageType}</div>
                          </div>
                          <div>
                            <span className="font-medium">Registered:</span>
                            <div>
                              {new Date(
                                property.registrationDate
                              ).toLocaleDateString()}
                            </div>
                          </div>
                        </div>

                        {property.builtUpArea && (
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-base-content/70 mb-4">
                            <div>
                              <span className="font-medium">Built-up Area:</span>
                              <div>{property.builtUpArea} sq.m</div>
                            </div>
                            <div>
                              <span className="font-medium">Construction Type:</span>
                              <div>{property.constructionType}</div>
                            </div>
                            <div>
                              <span className="font-medium">Construction Year:</span>
                              <div>{property.constructionYear}</div>
                            </div>
                          </div>
                        )}

                        {property.status === "Rejected" && property.rejectionReason && (
                          <div className="alert alert-error mb-4">
                            <div className="flex items-center">
                              <div className="text-error mr-3">⚠️</div>
                              <div>
                                <h4 className="text-sm font-medium">
                                  Rejection Reason:
                                </h4>
                                <p className="text-sm mt-1">
                                  {property.rejectionReason}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}

                        {property.status === "Approved" &&
                          property.taxDue &&
                          property.taxDue > 0 && (
                            <div className="alert alert-warning mb-4">
                              <div className="flex items-center">
                                <div className="text-warning mr-3">💰</div>
                                <div>
                                  <h4 className="text-sm font-medium">
                                    Tax Due:
                                  </h4>
                                  <p className="text-sm">
                                    NPR {property.taxDue.toLocaleString()}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                      </div>

                      <div className="flex flex-col space-y-2 ml-6">
                        {getQuickActions(property).map((action, index) => (
                          <div key={index}>{action}</div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
    </AdminLayout>
  );
};

export default MyProperties;