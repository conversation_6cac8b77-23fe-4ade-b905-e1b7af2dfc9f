import React from 'react';

interface AdminSummaryCardProps {
  title: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';
}

const AdminSummaryCard: React.FC<AdminSummaryCardProps> = ({
  title,
  description,
  children,
  className = '',
  actions,
  icon,
  color
}) => {
  // Function to get color classes
  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'primary': return 'bg-primary/10 text-primary';
      case 'secondary': return 'bg-secondary/10 text-secondary';
      case 'accent': return 'bg-accent/10 text-accent';
      case 'info': return 'bg-info/10 text-info';
      case 'success': return 'bg-success/10 text-success';
      case 'warning': return 'bg-warning/10 text-warning';
      case 'error': return 'bg-error/10 text-error';
      default: return '';
    }
  };

  return (
    <div className={`card bg-base-100 shadow-xl ${className}`}>
      <div className="card-body">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            {icon && (
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-4 ${getColorClasses(color)}`}>
                {icon}
              </div>
            )}
            <div>
              <h2 className="card-title text-lg">{title}</h2>
              {description && <p className="text-base-content/70 mt-1">{description}</p>}
            </div>
          </div>
          {actions && <div className="card-actions">{actions}</div>}
        </div>
        {children && <div className="mt-4">{children}</div>}
      </div>
    </div>
  );
};

export default AdminSummaryCard;