using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using LandTaxSystem.Core.DTOs.Report;
using LandTaxSystem.Core.Interfaces;
using LandTaxSystem.Infrastructure.Data;
using System.Text;
using iTextSharp.text;
using iTextSharp.text.pdf;

namespace LandTaxSystem.Infrastructure.Services
{
    public class ReportService : IReportService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ReportService> _logger;

        public ReportService(ApplicationDbContext context, ILogger<ReportService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<OutstandingTaxReportResponseDto> GetOutstandingTaxReportAsync(OutstandingTaxReportRequestDto request)
        {
            var query = from property in _context.Properties
                       join owner in _context.Users on property.OwnerUserId equals owner.Id
                       join municipality in _context.Municipalities on property.MunicipalityId equals municipality.MunicipalityId
                       join assessment in _context.Assessments on property.PropertyId equals assessment.PropertyId
                       join fiscalYear in _context.FiscalYears on assessment.FiscalYearId equals fiscalYear.FiscalYearId
                       where assessment.PaymentStatus != "Paid"
                       select new { property, owner, municipality, assessment, fiscalYear };

            // Apply filters
            if (request.MunicipalityId.HasValue)
            {
                query = query.Where(x => x.property.MunicipalityId == request.MunicipalityId.Value);
            }

            if (!string.IsNullOrEmpty(request.Ward))
            {
                query = query.Where(x => x.property.WardNumber.ToString() == request.Ward);
            }

            if (!string.IsNullOrEmpty(request.FiscalYear))
            {
                query = query.Where(x => x.fiscalYear.Name == request.FiscalYear);
            }

            if (!string.IsNullOrEmpty(request.TaxpayerNumber))
            {
                query = query.Where(x => x.owner.Id.Contains(request.TaxpayerNumber));
            }

            var results = await query.ToListAsync();

            var records = new List<OutstandingTaxRecordDto>();

            foreach (var result in results)
            {
                // Calculate outstanding amount
                var totalPayments = await _context.Payments
                    .Where(p => p.AssessmentId == result.assessment.AssessmentId && p.Status == "Success")
                    .SumAsync(p => p.AmountPaid);

                var outstandingAmount = result.assessment.TotalPayable - totalPayments;

                if (outstandingAmount > 0)
                {
                    records.Add(new OutstandingTaxRecordDto
                    {
                        TaxpayerNumber = result.owner.Id,
                        TaxpayerName = result.owner.FullName,
                        TaxpayerAddress = result.property.Address,
                        TaxpayerContacts = result.owner.PhoneNumber ?? "",
                        FiscalYear = result.fiscalYear.Name,
                        Ward = result.property.WardNumber.ToString(),
                        ParcelNumber = result.property.ParcelNumber,
                        OutstandingDue = outstandingAmount
                    });
                }
            }

            return new OutstandingTaxReportResponseDto
            {
                Records = records,
                TotalCount = records.Count
            };
        }

        public async Task<string> ExportOutstandingTaxReportToCsvAsync(OutstandingTaxReportRequestDto request)
        {
            var reportData = await GetOutstandingTaxReportAsync(request);
            var csv = new StringBuilder();
            
            // Add header
            csv.AppendLine("Taxpayer No.,Name,Address,Contacts,Fiscal Year,Ward,Parcel No.,Outstanding Due");
            
            // Add data rows
            foreach (var record in reportData.Records)
            {
                csv.AppendLine($"\"{record.TaxpayerNumber}\",\"{record.TaxpayerName}\",\"{record.TaxpayerAddress}\",\"{record.TaxpayerContacts}\",\"{record.FiscalYear}\",\"{record.Ward}\",\"{record.ParcelNumber}\",{record.OutstandingDue:F2}");
            }
            
            return csv.ToString();
        }

        public async Task<byte[]> ExportOutstandingTaxReportToPdfAsync(OutstandingTaxReportRequestDto request)
        {
            var reportData = await GetOutstandingTaxReportAsync(request);
            
            using var memoryStream = new MemoryStream();
            var document = new Document(PageSize.A4.Rotate(), 25, 25, 30, 30);
            var writer = PdfWriter.GetInstance(document, memoryStream);
            
            document.Open();
            
            // Add title
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 16);
            var title = new Paragraph("Outstanding Tax Report", titleFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 20
            };
            document.Add(title);
            
            // Create table
            var table = new PdfPTable(8)
            {
                WidthPercentage = 100
            };
            
            // Add headers
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 10);
            table.AddCell(new PdfPCell(new Phrase("Taxpayer No.", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Name", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Address", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Contacts", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Fiscal Year", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Ward", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Parcel No.", headerFont)));
            table.AddCell(new PdfPCell(new Phrase("Outstanding Due", headerFont)));
            
            // Add data rows
            var cellFont = FontFactory.GetFont(FontFactory.HELVETICA, 9);
            foreach (var record in reportData.Records)
            {
                table.AddCell(new PdfPCell(new Phrase(record.TaxpayerNumber, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.TaxpayerName, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.TaxpayerAddress, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.TaxpayerContacts, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.FiscalYear, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.Ward, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.ParcelNumber, cellFont)));
                table.AddCell(new PdfPCell(new Phrase(record.OutstandingDue.ToString("C"), cellFont)));
            }
            
            document.Add(table);
            document.Close();
            
            return memoryStream.ToArray();
        }
    }
}