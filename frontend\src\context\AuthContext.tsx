import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  type ReactNode,
} from "react";
import { jwtDecode } from "jwt-decode";
import { authService } from "../services/api";

interface AuthUser {
  id: string;
  fullName: string;
  email: string;
  role: "Citizen" | "Officer" | "CentralAdmin";
  municipalityId?: string;
  municipalityName?: string;
}

interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (emailOrPAN: string, password: string) => Promise<void>;
  register: (
    fullName: string,
    email: string,
    password: string,
    confirmPassword: string,
    phoneNumber?: string
  ) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem("token");
    if (token) {
      try {
        const decoded = jwtDecode<{
          nameid: string;
          unique_name: string;
          email: string;
          role: string;
          MunicipalityId?: string;
          MunicipalityName?: string;
          exp: number;
        }>(token);

        // Check if token is expired
        const currentTime = Date.now() / 1000;
        if (decoded.exp < currentTime) {
          // Token expired
          localStorage.removeItem("token");
          setUser(null);
        } else {
          // Set user
          setUser({
            id: decoded.nameid,
            fullName: decoded.unique_name,
            email: decoded.email,
            role: decoded.role as "Citizen" | "Officer" | "CentralAdmin",
            municipalityId: decoded.MunicipalityId,
            municipalityName: decoded.MunicipalityName,
          });
        }
      } catch {
        // Invalid token
        localStorage.removeItem("token");
        setUser(null);
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (emailOrPAN: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await authService.login(emailOrPAN, password);

      // Decode token to get user info
      const decoded = jwtDecode<{
        nameid: string;
        unique_name: string;
        email: string;
        role: string;
        MunicipalityId?: string;
        MunicipalityName?: string;
      }>(response.token);
      setUser({
        id: decoded.nameid,
        fullName: decoded.unique_name,
        email: decoded.email,
        role: decoded.role as "Citizen" | "Officer" | "CentralAdmin",
        municipalityId: decoded.MunicipalityId,
        municipalityName: decoded.MunicipalityName,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (
    fullName: string,
    email: string,
    password: string,
    confirmPassword: string,
    phoneNumber?: string
  ) => {
    setIsLoading(true);
    try {
      await authService.register(
        fullName,
        email,
        password,
        confirmPassword,
        phoneNumber
      );
      // After registration, user needs to login
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// eslint-disable-next-line react-refresh/only-export-components
export { useAuth };
