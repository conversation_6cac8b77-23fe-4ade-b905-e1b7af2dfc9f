using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.DTOs.Rebate
{
    public class RebateDto
    {
        public int Id { get; set; }
        public string? OfficeCode { get; set; }
        public string? Pan { get; set; }
        public string? AccountType { get; set; }
        public string? Name { get; set; }
        public DateTime? ExemptionDate { get; set; }
        public string? SerialNo { get; set; }
        public string? Scheme { get; set; }
        public bool IsReversal { get; set; }
        public string? MaNo { get; set; }
        public string? Reason { get; set; }
        public List<RebateItemDto>? RebateItems { get; set; }
    }
}