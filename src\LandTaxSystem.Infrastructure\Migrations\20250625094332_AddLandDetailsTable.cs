﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddLandDetailsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LandDetails",
                columns: table => new
                {
                    LandDetailId = table.Column<Guid>(type: "uuid", nullable: false),
                    PropertyId = table.Column<Guid>(type: "uuid", nullable: false),
                    OldVdc = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OldWardNo = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CurrentWardNo = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    KittaNo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    MapNo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    FiscalYear = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    OtherDetails = table.Column<string>(type: "text", nullable: true),
                    IsMultiRateValuation = table.Column<bool>(type: "boolean", nullable: false),
                    IsTemporaryHouse = table.Column<bool>(type: "boolean", nullable: false),
                    IsCultivable = table.Column<bool>(type: "boolean", nullable: false),
                    MeasurementUnit = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    AreaBigha = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    AreaKattha = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    AreaDhur = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    AreaRopani = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    AreaAana = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    AreaPaisa = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    AreaDaam = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    TaxpayerPrice = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    TaxpayerLandRevenuePrice = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    DeactivatePlot = table.Column<bool>(type: "boolean", nullable: false),
                    LastFyForInclusion = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    LandRevenueApplicable = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    StructureAreaLength = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    StructureAreaBreadth = table.Column<decimal>(type: "numeric(18,2)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LandDetails", x => x.LandDetailId);
                    table.ForeignKey(
                        name: "FK_LandDetails_Properties_PropertyId",
                        column: x => x.PropertyId,
                        principalTable: "Properties",
                        principalColumn: "PropertyId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LandDetails_FiscalYear",
                table: "LandDetails",
                column: "FiscalYear");

            migrationBuilder.CreateIndex(
                name: "IX_LandDetails_KittaNo",
                table: "LandDetails",
                column: "KittaNo");

            migrationBuilder.CreateIndex(
                name: "IX_LandDetails_MapNo",
                table: "LandDetails",
                column: "MapNo");

            migrationBuilder.CreateIndex(
                name: "IX_LandDetails_PropertyId",
                table: "LandDetails",
                column: "PropertyId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LandDetails");
        }
    }
}
