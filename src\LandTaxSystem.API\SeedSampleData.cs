using System;
using System.Linq;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetTopologySuite.Geometries;
using NetTopologySuite;

namespace LandTaxSystem.API
{
    public class SeedSampleData
    {
        public static void SeedPropertiesAndAssessments(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Check if properties already exist
            if (dbContext.Properties.Any())
            {
                Console.WriteLine("Properties already exist. Skipping sample data seeding.");
                return;
            }

            // Get required entities
            var municipality = dbContext.Municipalities.FirstOrDefault();
            var fiscalYear = dbContext.FiscalYears.FirstOrDefault(f => f.IsActive);
            var citizen = dbContext.Users.FirstOrDefault(u => u.Email == "<EMAIL>");

            if (municipality == null || fiscalYear == null || citizen == null)
            {
                Console.WriteLine("Required entities not found. Cannot seed sample data.");
                return;
            }

            Console.WriteLine("Seeding sample properties, assessments, and payments...");

            // Create geometry factory for creating polygon geometries
            var geometryFactory = NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);

            // Create sample properties
            var properties = new[]
            {
                new Property
                {
                    PropertyId = Guid.NewGuid(),
                    MunicipalityId = municipality.MunicipalityId,
                    OwnerUserId = citizen.Id,
                    Address = "123 Lakeside Road, Ward 6, Pokhara",
                    WardNumber = 6,
                    Street = "Lakeside Road",
                    ParcelNumber = "001",
                    LandAreaSqM = 200,
                    UsageType = "Residential",
                    BuildingConstructionYear = 2015,
                    BuildingBuiltUpAreaSqM = 150,
                    BuildingConstructionType = "RCC",
                    ParcelGeometry = geometryFactory.CreatePolygon(new Coordinate[]
                    {
                        new Coordinate(83.9856, 28.2096),
                        new Coordinate(83.9861, 28.2096),
                        new Coordinate(83.9861, 28.2099),
                        new Coordinate(83.9856, 28.2099),
                        new Coordinate(83.9856, 28.2096)
                    }),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Property
                {
                    PropertyId = Guid.NewGuid(),
                    MunicipalityId = municipality.MunicipalityId,
                    OwnerUserId = citizen.Id,
                    Address = "456 Mahendra Pool, Ward 17, Pokhara",
                    WardNumber = 17,
                    Street = "Mahendra Pool",
                    ParcelNumber = "002",
                    LandAreaSqM = 300,
                    UsageType = "Commercial",
                    BuildingConstructionYear = 2018,
                    BuildingBuiltUpAreaSqM = 250,
                    BuildingConstructionType = "RCC",
                    ParcelGeometry = geometryFactory.CreatePolygon(new Coordinate[]
                    {
                        new Coordinate(83.9870, 28.2110),
                        new Coordinate(83.9875, 28.2110),
                        new Coordinate(83.9875, 28.2115),
                        new Coordinate(83.9870, 28.2115),
                        new Coordinate(83.9870, 28.2110)
                    }),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Property
                {
                    PropertyId = Guid.NewGuid(),
                    MunicipalityId = municipality.MunicipalityId,
                    OwnerUserId = citizen.Id,
                    Address = "789 Prithvi Highway, Ward 25, Pokhara",
                    WardNumber = 25,
                    Street = "Prithvi Highway",
                    ParcelNumber = "003",
                    LandAreaSqM = 500,
                    UsageType = "Industrial",
                    BuildingConstructionYear = 2020,
                    BuildingBuiltUpAreaSqM = 400,
                    BuildingConstructionType = "RCC",
                    ParcelGeometry = geometryFactory.CreatePolygon(new Coordinate[]
                    {
                        new Coordinate(83.9880, 28.2120),
                        new Coordinate(83.9890, 28.2120),
                        new Coordinate(83.9890, 28.2130),
                        new Coordinate(83.9880, 28.2130),
                        new Coordinate(83.9880, 28.2120)
                    }),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            dbContext.Properties.AddRange(properties);
            dbContext.SaveChanges();

            // Create sample assessments for each property
            foreach (var property in properties)
            {
                var assessedValue = property.UsageType switch
                {
                    "Residential" => 1500000m,
                    "Commercial" => 3000000m,
                    "Industrial" => 2500000m,
                    _ => 1000000m
                };
                var taxAmount = property.UsageType switch
                {
                    "Residential" => 4500m,
                    "Commercial" => 12000m,
                    "Industrial" => 8500m,
                    _ => 3000m
                };
                
                var assessment = new Assessment
                {
                    AssessmentId = Guid.NewGuid(),
                    PropertyId = property.PropertyId,
                    FiscalYearId = fiscalYear.FiscalYearId,
                    AssessmentYear = DateTime.UtcNow.Year,
                    CalculatedValue = assessedValue,
                    FinalAssessedValue = assessedValue,
                    TaxAmount = taxAmount,
                    UpperAssessment = assessedValue,
                    LowerAssessment = assessedValue * 0.8m,
                    ActualAssessment = assessedValue,
                    PaymentStatus = "Pending", // This will make them show up in outstanding tax report
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                dbContext.Assessments.Add(assessment);
            }

            // Create one paid assessment to test the filtering
            var paidProperty = new Property
            {
                PropertyId = Guid.NewGuid(),
                MunicipalityId = municipality.MunicipalityId,
                OwnerUserId = citizen.Id,
                Address = "999 Paid Property, Ward 4",
                WardNumber = 4,
                Street = "Paid Street",
                ParcelNumber = "004",
                LandAreaSqM = 150,
                UsageType = "Residential",
                BuildingConstructionYear = 2019,
                BuildingBuiltUpAreaSqM = 120,
                BuildingConstructionType = "RCC",
                ParcelGeometry = geometryFactory.CreatePolygon(new Coordinate[]
                {
                    new Coordinate(85.3280, 27.7210),
                    new Coordinate(85.3285, 27.7210),
                    new Coordinate(85.3285, 27.7215),
                    new Coordinate(85.3280, 27.7215),
                    new Coordinate(85.3280, 27.7210)
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Properties.Add(paidProperty);
            dbContext.SaveChanges();

            var paidAssessment = new Assessment
            {
                AssessmentId = Guid.NewGuid(),
                PropertyId = paidProperty.PropertyId,
                FiscalYearId = fiscalYear.FiscalYearId,
                AssessmentYear = DateTime.UtcNow.Year,
                CalculatedValue = 1200000m,
                FinalAssessedValue = 1200000m,
                TaxAmount = 3600m,
                UpperAssessment = 1200000m,
                LowerAssessment = 960000m,
                ActualAssessment = 1200000m,
                PaymentStatus = "Paid",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Assessments.Add(paidAssessment);

            // Create a payment record for the paid assessment
            var payment = new Payment
            {
                PaymentId = Guid.NewGuid(),
                AssessmentId = paidAssessment.AssessmentId,
                PropertyId = paidProperty.PropertyId,
                FiscalYearId = fiscalYear.FiscalYearId,
                AmountPaid = 3600m,
                PaymentDate = DateTime.UtcNow.AddDays(-30),
                PaymentGateway = "Online",
                TransactionId = "TXN123456",
                Status = "Success",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Payments.Add(payment);
            dbContext.SaveChanges();

            Console.WriteLine($"Successfully seeded {properties.Length + 1} properties, {properties.Length + 1} assessments, and 1 payment.");
        }
    }
}