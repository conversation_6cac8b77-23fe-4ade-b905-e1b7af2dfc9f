using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using LandTaxSystem.Core.DTOs.PreliminaryAssessment;
using LandTaxSystem.Core.Interfaces;

namespace LandTaxSystem.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PreliminaryAssessmentsController : ControllerBase
    {
        private readonly IPreliminaryAssessmentService _preliminaryAssessmentService;

        public PreliminaryAssessmentsController(IPreliminaryAssessmentService preliminaryAssessmentService)
        {
            _preliminaryAssessmentService = preliminaryAssessmentService;
        }

        /// <summary>
        /// Get all preliminary assessments with pagination
        /// </summary>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>List of preliminary assessments</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<PreliminaryAssessmentListDto>>> GetAll(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var assessments = await _preliminaryAssessmentService.GetAllAsync(page, pageSize);
                var totalCount = await _preliminaryAssessmentService.GetTotalCountAsync();
                
                Response.Headers["X-Total-Count"] = totalCount.ToString();
                Response.Headers["X-Page"] = page.ToString();
                Response.Headers["X-Page-Size"] = pageSize.ToString();
                
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get a specific preliminary assessment by ID
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Preliminary assessment details</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<PreliminaryAssessmentResponseDto>> GetById(Guid id)
        {
            try
            {
                var assessment = await _preliminaryAssessmentService.GetByIdAsync(id);
                if (assessment == null)
                {
                    return NotFound(new { message = "Assessment not found" });
                }
                return Ok(assessment);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Get preliminary assessments by municipality
        /// </summary>
        /// <param name="municipalityId">Municipality ID</param>
        /// <returns>List of preliminary assessments for the municipality</returns>
        [HttpGet("by-municipality/{municipalityId}")]
        public async Task<ActionResult<IEnumerable<PreliminaryAssessmentListDto>>> GetByMunicipality(Guid municipalityId)
        {
            try
            {
                var assessments = await _preliminaryAssessmentService.GetByMunicipalityAsync(municipalityId);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get preliminary assessments by fiscal year
        /// </summary>
        /// <param name="fiscalYearId">Fiscal year ID</param>
        /// <returns>List of preliminary assessments for the fiscal year</returns>
        [HttpGet("by-fiscal-year/{fiscalYearId}")]
        public async Task<ActionResult<IEnumerable<PreliminaryAssessmentListDto>>> GetByFiscalYear(Guid fiscalYearId)
        {
            try
            {
                var assessments = await _preliminaryAssessmentService.GetByFiscalYearAsync(fiscalYearId);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }

        /// <summary>
        /// Get preliminary assessments by taxpayer registration
        /// </summary>
        /// <param name="taxpayerRegistration">Taxpayer registration number</param>
        /// <returns>List of preliminary assessments for the taxpayer</returns>
        [HttpGet("by-taxpayer/{taxpayerRegistration}")]
        public async Task<ActionResult<IEnumerable<PreliminaryAssessmentListDto>>> GetByTaxpayerRegistration(string taxpayerRegistration)
        {
            try
            {
                var assessments = await _preliminaryAssessmentService.GetByTaxpayerRegistrationAsync(taxpayerRegistration);
                return Ok(assessments);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving assessments", error = ex.Message });
            }
        }



        /// <summary>
        /// Create a new preliminary assessment
        /// </summary>
        /// <param name="dto">Preliminary assessment creation data</param>
        /// <returns>Created preliminary assessment</returns>
        [HttpPost]
        public async Task<ActionResult<PreliminaryAssessmentResponseDto>> Create([FromBody] PreliminaryAssessmentCreateDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if taxpayer registration already exists
                var exists = await _preliminaryAssessmentService.ExistsAsync(dto.TaxpayerRegistration);
                if (exists)
                {
                    return Conflict(new { message = "Assessment for this taxpayer registration already exists" });
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "User not authenticated" });
                }

                var assessment = await _preliminaryAssessmentService.CreateAsync(dto, userId);
                return CreatedAtAction(nameof(GetById), new { id = assessment.Id }, assessment);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while creating the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Update an existing preliminary assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="dto">Preliminary assessment update data</param>
        /// <returns>Updated preliminary assessment</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<PreliminaryAssessmentResponseDto>> Update(Guid id, [FromBody] PreliminaryAssessmentUpdateDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "User not authenticated" });
                }

                var assessment = await _preliminaryAssessmentService.UpdateAsync(id, dto, userId);
                return Ok(assessment);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while updating the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Delete a preliminary assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            try
            {
                var result = await _preliminaryAssessmentService.DeleteAsync(id);
                if (!result)
                {
                    return NotFound(new { message = "Assessment not found" });
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Check if taxpayer registration exists
        /// </summary>
        /// <param name="taxpayerRegistration">Taxpayer registration number</param>
        /// <returns>Existence status</returns>
        [HttpGet("exists/{taxpayerRegistration}")]
        public async Task<ActionResult<bool>> Exists(string taxpayerRegistration)
        {
            try
            {
                var exists = await _preliminaryAssessmentService.ExistsAsync(taxpayerRegistration);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while checking assessment existence", error = ex.Message });
            }
        }

        /// <summary>
        /// Update assessment status
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="request">Status update request</param>
        /// <returns>Status update result</returns>
        [HttpPut("{id}/status")]
        public async Task<ActionResult> UpdateStatus(Guid id, [FromBody] StatusUpdateRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "system";
                
                // This would require injecting the status service
                // var result = await _statusService.UpdateStatusAsync(id, request.Status, userId, request.Reason);
                
                // For now, return a placeholder response
                return Ok(new { message = "Status update functionality will be implemented with dependency injection" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while updating status", error = ex.Message });
            }
        }

        /// <summary>
        /// Submit assessment for review
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <returns>Status update result</returns>
        [HttpPost("{id}/submit")]
        public async Task<ActionResult> SubmitForReview(Guid id)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "system";
                
                // This would require injecting the status service
                // var result = await _statusService.SubmitForReviewAsync(id, userId);
                
                return Ok(new { message = "Submit for review functionality will be implemented with dependency injection" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while submitting for review", error = ex.Message });
            }
        }

        /// <summary>
        /// Approve assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="request">Approval request</param>
        /// <returns>Status update result</returns>
        [HttpPost("{id}/approve")]
        public async Task<ActionResult> Approve(Guid id, [FromBody] ApprovalRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "system";
                
                // This would require injecting the status service
                // var result = await _statusService.ApproveAsync(id, userId, request.Notes);
                
                return Ok(new { message = "Approval functionality will be implemented with dependency injection" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while approving assessment", error = ex.Message });
            }
        }

        /// <summary>
        /// Reject assessment
        /// </summary>
        /// <param name="id">Assessment ID</param>
        /// <param name="request">Rejection request</param>
        /// <returns>Status update result</returns>
        [HttpPost("{id}/reject")]
        public async Task<ActionResult> Reject(Guid id, [FromBody] RejectionRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "system";
                
                // This would require injecting the status service
                // var result = await _statusService.RejectAsync(id, userId, request.Reason);
                
                return Ok(new { message = "Rejection functionality will be implemented with dependency injection" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while rejecting assessment", error = ex.Message });
            }
        }
    }

    // DTOs for status management
    public class StatusUpdateRequest
    {
        public string Status { get; set; } = string.Empty;
        public string? Reason { get; set; }
    }

    public class ApprovalRequest
    {
        public string? Notes { get; set; }
    }

    public class RejectionRequest
    {
        public string Reason { get; set; } = string.Empty;
    }
}