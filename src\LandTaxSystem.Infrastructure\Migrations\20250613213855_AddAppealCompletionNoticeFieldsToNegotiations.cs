﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LandTaxSystem.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAppealCompletionNoticeFieldsToNegotiations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "NegotiationId",
                table: "TaxPeriods",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AppealBody",
                table: "Negotiations",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "DecidedFee",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "DecidedInterest",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "DecidedPenalty",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "DecidedTax",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "DecisionDate",
                table: "Negotiations",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DecisionNumber",
                table: "Negotiations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsWithdrawn",
                table: "Negotiations",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "OriginalFee",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "OriginalInterest",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "OriginalPenalty",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "OriginalTax",
                table: "Negotiations",
                type: "numeric",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "PANNumber",
                table: "Negotiations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxOfficeAddress",
                table: "Negotiations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaxpayerName",
                table: "Negotiations",
                type: "text",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxPeriods_NegotiationId",
                table: "TaxPeriods",
                column: "NegotiationId");

            migrationBuilder.AddForeignKey(
                name: "FK_TaxPeriods_Negotiations_NegotiationId",
                table: "TaxPeriods",
                column: "NegotiationId",
                principalTable: "Negotiations",
                principalColumn: "NegotiationId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TaxPeriods_Negotiations_NegotiationId",
                table: "TaxPeriods");

            migrationBuilder.DropIndex(
                name: "IX_TaxPeriods_NegotiationId",
                table: "TaxPeriods");

            migrationBuilder.DropColumn(
                name: "NegotiationId",
                table: "TaxPeriods");

            migrationBuilder.DropColumn(
                name: "AppealBody",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "DecidedFee",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "DecidedInterest",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "DecidedPenalty",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "DecidedTax",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "DecisionDate",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "DecisionNumber",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "IsWithdrawn",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "OriginalFee",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "OriginalInterest",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "OriginalPenalty",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "OriginalTax",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "PANNumber",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "TaxOfficeAddress",
                table: "Negotiations");

            migrationBuilder.DropColumn(
                name: "TaxpayerName",
                table: "Negotiations");
        }
    }
}
