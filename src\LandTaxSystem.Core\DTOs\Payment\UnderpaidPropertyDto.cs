using System;
using System.Collections.Generic;

namespace LandTaxSystem.Core.DTOs.Payment
{
    public class EligiblePropertyDto
    {
        public Guid PropertyId { get; set; }
        public Guid FiscalYearId { get; set; }
        public string PropertyAddress { get; set; } = string.Empty;
        public string FiscalYearName { get; set; } = string.Empty;
        public decimal CalculatedTaxAmount { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal UnpaidBalance { get; set; }
        public List<Guid> PaymentIds { get; set; } = new List<Guid>();
        
        // For display in dropdown
        public string DisplayText => $"{PropertyAddress} - Tax Due: NPR {UnpaidBalance:N2} ({FiscalYearName})";
    }
}
