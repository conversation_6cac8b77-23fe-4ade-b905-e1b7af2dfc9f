using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using LandTaxSystem.Core.Entities;
using LandTaxSystem.Core.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace LandTaxSystem.Infrastructure.Data
{
    public static class DbSeeder
    {
        public static async Task SeedDatabase(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var context = services.GetRequiredService<ApplicationDbContext>();
                var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
                var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();

                logger.LogInformation("Starting database seeding process...");

                // First check if municipalities table exists
                bool municipalitiesTableExists = true;
                try
                {
                    // Try to check if the table exists by querying it
                    municipalitiesTableExists = await context.Database.ExecuteSqlRawAsync("SELECT COUNT(*) FROM \"Municipalities\" LIMIT 1") >= 0;
                }
                catch (Exception)
                {
                    // If this fails, the table doesn't exist yet
                    municipalitiesTableExists = false;
                }

                // If municipalities table exists but districts table doesn't, we need to clear municipalities first
                if (municipalitiesTableExists)
                {
                    bool districtsTableExists = true;
                    try
                    {
                        // Try to check if districts table exists
                        districtsTableExists = await context.Database.ExecuteSqlRawAsync("SELECT COUNT(*) FROM \"Districts\" LIMIT 1") >= 0;
                    }
                    catch (Exception)
                    {
                        // If this fails, the table doesn't exist yet
                        districtsTableExists = false;
                    }

                    if (!districtsTableExists)
                    {
                        logger.LogWarning("Found existing municipalities without districts. Clearing municipalities table to avoid foreign key constraint violations.");
                        try
                        {
                            // Clear municipalities table directly with SQL to avoid issues with missing tables
                            await context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE \"Municipalities\" CASCADE");
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, "Failed to clear municipalities table.");
                        }
                    }
                }

                // Apply pending migrations
                logger.LogInformation("Applying database migrations...");
                await context.Database.MigrateAsync();

                // Seed roles first
                logger.LogInformation("Seeding roles...");
                await SeedRolesAsync(roleManager);

                // Seed roles and users
                logger.LogInformation("Seeding users...");
                await SeedUsersAsync(userManager);

                // Migrate existing users to Identity roles if they're not already assigned
                logger.LogInformation("Migrating existing users to roles...");
                await MigrateExistingUsersToRoles(userManager);

                // Seed provinces first
                logger.LogInformation("Seeding provinces...");
                await SeedProvincesAsync(context);

                // Seed districts next
                logger.LogInformation("Seeding districts...");
                await SeedDistrictsAsync(context);

                // Seed municipalities last (they depend on districts)
                logger.LogInformation("Seeding municipalities...");
                await SeedMunicipalitiesAsync(context);

                // Assign municipality to officer
                logger.LogInformation("Assigning municipality to officer...");
                await AssignMunicipalityToOfficer(context);

                // Seed fiscal years
                logger.LogInformation("Seeding fiscal years...");
                await SeedFiscalYearsAsync(context);
            }
            catch (Exception ex)
            {
                var logger = services.GetRequiredService<ILogger<ApplicationDbContext>>();
                logger.LogError(ex, "An error occurred while seeding the database.");
            }
        }

        private static async Task SeedRolesAsync(RoleManager<IdentityRole> roleManager)
        {
            string[] roles = { "CentralAdmin", "Officer", "Citizen", "PropertyOwner", "MunicipalOfficer" };

            foreach (string role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role))
                {
                    await roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }
        private static async Task SeedUsersAsync(UserManager<ApplicationUser> userManager)
        {
            // Create admin user if it doesn't exist
            if (await userManager.FindByEmailAsync("<EMAIL>") == null)
            {
                var admin = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FullName = "System Administrator",
                    Role = "CentralAdmin",
                    EmailConfirmed = true,
                    Status = "Active"
                };

                var result = await userManager.CreateAsync(admin, "Admin@123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(admin, "CentralAdmin");
                }
            }

            // Create officer user if it doesn't exist
            if (await userManager.FindByEmailAsync("<EMAIL>") == null)
            {
                var officer = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FullName = "Municipal Officer",
                    Role = "Officer",
                    EmailConfirmed = true,
                    Status = "Active"
                    // MunicipalityId will be set after municipality is created
                };

                var result = await userManager.CreateAsync(officer, "Officer@123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(officer, "Officer");
                }
            }

            // Create citizen user if it doesn't exist
            if (await userManager.FindByEmailAsync("<EMAIL>") == null)
            {
                var citizen = new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    FullName = "John Citizen",
                    Role = "Citizen",
                    EmailConfirmed = true,
                    Status = "Active"
                };

                var result = await userManager.CreateAsync(citizen, "Citizen@123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(citizen, "Citizen");
                }
            }
        }

        private static async Task MigrateExistingUsersToRoles(UserManager<ApplicationUser> userManager)
        {
            // Find all users
            var users = await userManager.Users.ToListAsync();

            foreach (var user in users)
            {
                // Check if user has a role already
                var roles = await userManager.GetRolesAsync(user);
                if (roles.Count == 0)
                {
                    // Assign default role based on user type
                    if (user.Email == "<EMAIL>")
                    {
                        await userManager.AddToRoleAsync(user, "CentralAdmin");
                    }
                    else if (user.Email == "<EMAIL>")
                    {
                        await userManager.AddToRoleAsync(user, "MunicipalOfficer");
                    }
                    else
                    {
                        await userManager.AddToRoleAsync(user, "Citizen");
                    }
                }
            }
        }

        private static async Task SeedProvincesAsync(ApplicationDbContext context)
        {
            // Check if any provinces exist
            if (!await context.Provinces.AnyAsync())
            {
                // Create provinces based on Nepal's administrative divisions
                var provinces = new List<Province>
                {
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000001"),
                        Name = "Koshi",
                        Code = "P1",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000002"),
                        Name = "Madhesh",
                        Code = "P2",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000003"),
                        Name = "Bagmati",
                        Code = "P3",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000004"),
                        Name = "Gandaki",
                        Code = "P4",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000005"),
                        Name = "Lumbini",
                        Code = "P5",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000006"),
                        Name = "Karnali",
                        Code = "P6",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Province
                    {
                        ProvinceId = Guid.Parse("10000000-0000-0000-0000-000000000007"),
                        Name = "Sudurpashchim",
                        Code = "P7",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.Provinces.AddRange(provinces);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SeedDistrictsAsync(ApplicationDbContext context)
        {
            // Check if any districts exist
            if (!await context.Districts.AnyAsync())
            {
                // Get all provinces to reference them
                var provinces = await context.Provinces.ToListAsync();
                if (!provinces.Any())
                {
                    throw new InvalidOperationException("Cannot seed districts because provinces have not been seeded.");
                }

                // Create a dictionary for easier lookup
                var provinceDict = provinces.ToDictionary(p => p.Name, p => p.ProvinceId);

                // Create districts based on Nepal's administrative divisions
                var districts = new List<District>
                {
                    // Bagmati Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000001"),
                        Name = "Kathmandu",
                        Code = "D01",
                        ProvinceId = provinceDict["Bagmati"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000002"),
                        Name = "Lalitpur",
                        Code = "D02",
                        ProvinceId = provinceDict["Bagmati"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000003"),
                        Name = "Bhaktapur",
                        Code = "D03",
                        ProvinceId = provinceDict["Bagmati"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    
                    // Koshi Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000004"),
                        Name = "Morang",
                        Code = "D04",
                        ProvinceId = provinceDict["Koshi"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000005"),
                        Name = "Sunsari",
                        Code = "D05",
                        ProvinceId = provinceDict["Koshi"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    
                    // Madhesh Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000006"),
                        Name = "Dhanusha",
                        Code = "D06",
                        ProvinceId = provinceDict["Madhesh"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    
                    // Gandaki Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000007"),
                        Name = "Kaski",
                        Code = "D07",
                        ProvinceId = provinceDict["Gandaki"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    
                    // Lumbini Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000008"),
                        Name = "Rupandehi",
                        Code = "D08",
                        ProvinceId = provinceDict["Lumbini"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    
                    // Karnali Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000009"),
                        Name = "Surkhet",
                        Code = "D09",
                        ProvinceId = provinceDict["Karnali"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    
                    // Sudurpashchim Province districts
                    new District
                    {
                        DistrictId = Guid.Parse("*************-0000-0000-000000000010"),
                        Name = "Kailali",
                        Code = "D10",
                        ProvinceId = provinceDict["Sudurpashchim"],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.Districts.AddRange(districts);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SeedMunicipalitiesAsync(ApplicationDbContext context)
        {
            // Check if any municipalities exist
            if (!await context.Municipalities.AnyAsync())
            {
                try
                {
                    // Get all districts to reference them
                    var districts = await context.Districts.ToListAsync();
                    if (!districts.Any())
                    {
                        throw new InvalidOperationException("Cannot seed municipalities because districts have not been seeded.");
                    }

                    // Create a dictionary for easier lookup
                    var districtDict = new Dictionary<string, Guid>();
                    foreach (var district in districts)
                    {
                        districtDict[district.Name] = district.DistrictId;
                    }

                    // Verify all required districts exist
                    var requiredDistricts = new[] { "Kathmandu", "Lalitpur", "Bhaktapur", "Morang", "Sunsari", "Dhanusha", "Kaski", "Rupandehi", "Surkhet", "Kailali" };
                    foreach (var district in requiredDistricts)
                    {
                        if (!districtDict.ContainsKey(district))
                        {
                            throw new InvalidOperationException($"Required district '{district}' not found in database. Cannot seed municipalities.");
                        }
                    }

                    // Create the main municipalities that users can register properties in
                    var municipalities = new List<Municipality>
                    {
                        // Bagmati Province - Kathmandu District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Kathmandu Metropolitan City",
                            DistrictId = districtDict["Kathmandu"],
                            WardCount = 32,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Bagmati Province - Lalitpur District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Lalitpur Metropolitan City",
                            DistrictId = districtDict["Lalitpur"],
                            WardCount = 29,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Bagmati Province - Bhaktapur District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Bhaktapur Municipality",
                            DistrictId = districtDict["Bhaktapur"],
                            WardCount = 10,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Koshi Province - Morang District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Biratnagar Metropolitan City",
                            DistrictId = districtDict["Morang"],
                            WardCount = 19,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Koshi Province - Sunsari District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Dharan Sub-Metropolitan City",
                            DistrictId = districtDict["Sunsari"],
                            WardCount = 20,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Madhesh Province - Dhanusha District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Janakpur Sub-Metropolitan City",
                            DistrictId = districtDict["Dhanusha"],
                            WardCount = 25,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Gandaki Province - Kaski District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Pokhara Metropolitan City",
                            DistrictId = districtDict["Kaski"],
                            WardCount = 33,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Lumbini Province - Rupandehi District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Butwal Sub-Metropolitan City",
                            DistrictId = districtDict["Rupandehi"],
                            WardCount = 19,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Karnali Province - Surkhet District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Birendranagar Municipality",
                            DistrictId = districtDict["Surkhet"],
                            WardCount = 16,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        },
                        
                        // Sudurpashchim Province - Kailali District
                        new Municipality
                        {
                            MunicipalityId = Guid.Parse("123e4567-e89b-12d3-a456-************"),
                            Name = "Dhangadhi Sub-Metropolitan City",
                            DistrictId = districtDict["Kailali"],
                            WardCount = 19,
                            ValuationRulesConfigJson = CreateSampleValuationRules(),
                            TaxSlabsConfigJson = CreateSampleTaxSlabs(),
                            ExemptionRulesConfigJson = CreateSampleExemptionRules(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        }
                    };

                    context.Municipalities.AddRange(municipalities);
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    throw new Exception($"Error seeding municipalities: {ex.Message}", ex);
                }
            }

        }

        private static async Task SeedFiscalYearsAsync(ApplicationDbContext context)
        {
            // Check if any fiscal years exist
            if (!await context.FiscalYears.AnyAsync())
            {
                var fiscalYears = new List<FiscalYear>
                {
                    new FiscalYear
                    {
                        FiscalYearId = Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
                        Name = "2080/81",
                        StartDate = new DateTime(2023, 7, 16, 0, 0, 0, DateTimeKind.Utc),
                        EndDate = new DateTime(2024, 7, 15, 23, 59, 59, DateTimeKind.Utc),
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new FiscalYear
                    {
                        FiscalYearId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Name = "2079/80",
                        StartDate = new DateTime(2022, 7, 16, 0, 0, 0, DateTimeKind.Utc),
                        EndDate = new DateTime(2023, 7, 15, 23, 59, 59, DateTimeKind.Utc),
                        IsActive = false,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.FiscalYears.AddRange(fiscalYears);
                await context.SaveChangesAsync();
            }
        }

        private static async Task AssignMunicipalityToOfficer(ApplicationDbContext context)
        {
            // Get Pokhara Metropolitan City as the default municipality
            var pokhara = await context.Municipalities.FirstOrDefaultAsync(m => m.Name == "Pokhara Metropolitan City");
            if (pokhara == null) return;

            // Assign it to the officer
            var officer = await context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
            if (officer != null)
            {
                officer.MunicipalityId = pokhara.MunicipalityId;
                officer.PermanentAddress = "Ward 1, Pokhara Metropolitan City";
                officer.WardNumber = "1";
                officer.ToleStreet = "Lakeside";
                await context.SaveChangesAsync();
            }

            // Also assign Pokhara to the default citizen user
            var citizen = await context.Users.FirstOrDefaultAsync(u => u.Email == "<EMAIL>");
            if (citizen != null)
            {
                citizen.MunicipalityId = pokhara.MunicipalityId;
                citizen.PermanentAddress = "Ward 2, Pokhara Metropolitan City";
                citizen.WardNumber = "2";
                citizen.ToleStreet = "Bagar";
                await context.SaveChangesAsync();
            }
        }

        private static string CreateSampleValuationRules()
        {
            var valuationRules = new ValuationRulesConfig
            {
                LandMVR = new Dictionary<string, decimal>
                {
                    { "Residential", 5000m },
                    { "Commercial", 10000m },
                    { "Industrial", 8000m },
                    { "Agricultural", 2000m }
                },
                BuildingBaseRatePerSqm = new Dictionary<string, decimal>
                {
                    { "RCC", 15000m },
                    { "BrickMud", 7000m },
                    { "Other", 5000m }
                },
                AnnualDepreciationRate = 0.01m
            };

            return JsonSerializer.Serialize(valuationRules);
        }

        private static string CreateSampleTaxSlabs()
        {
            var taxSlabs = new List<LegacyTaxSlab>
            {
                new LegacyTaxSlab
                {
                    MinAssessedValue = 0,
                    MaxAssessedValue = 500000,
                    RatePercent = 0.1m,
                    FixedAmount = 0
                },
                new LegacyTaxSlab
                {
                    MinAssessedValue = 500001,
                    MaxAssessedValue = 1000000,
                    RatePercent = 0.2m,
                    FixedAmount = 500
                },
                new LegacyTaxSlab
                {
                    MinAssessedValue = 1000001,
                    MaxAssessedValue = 5000000,
                    RatePercent = 0.3m,
                    FixedAmount = 1500
                },
                new LegacyTaxSlab
                {
                    MinAssessedValue = 5000001,
                    MaxAssessedValue = 0, // 0 means no upper limit
                    RatePercent = 0.4m,
                    FixedAmount = 13500
                }
            };

            return JsonSerializer.Serialize(taxSlabs);
        }

        private static string CreateSampleExemptionRules()
        {
            var exemptionRules = new ExemptionRulesConfig
            {
                Rules = new List<ExemptionRule>
                {
                    new ExemptionRule
                    {
                        Condition = "property.UsageType == 'Agricultural' && property.LandAreaSqM < 200",
                        DiscountPercent = 100
                    },
                    new ExemptionRule
                    {
                        Condition = "property.UsageType == 'Residential' && !property.BuildingConstructionYear.HasValue",
                        DiscountPercent = 50
                    }
                }
            };

            return JsonSerializer.Serialize(exemptionRules);
        }
    }
}
