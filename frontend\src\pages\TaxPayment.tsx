import React, { useState, useEffect, useCallback } from "react";
import {
  useParams,
  useSearchParams,
  Link,
  useNavigate,
} from "react-router-dom";
import {
  paymentService,
  propertyService,
  assessmentService,
} from "../services/api";
import type { Property, Assessment } from "../types";

// Interface for the raw data structure that might come from the backend
interface RawBackendAssessment extends Omit<Assessment, "id"> {
  id?: string; // Frontend might expect 'id'
  assessmentId?: string; // Backend often uses 'assessmentId'
}

// Helper function to validate UUID format
const isValidUUID = (id: string): boolean => {
  if (!id) return false;
  const uuidPattern =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidPattern.test(id);
};

const TaxPayment: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const [property, setProperty] = useState<Property | null>(null);
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState<boolean>(false);
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const [error, setError] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState("card");

  // URL Cleanup Effect
  useEffect(() => {
    const assessmentQueryValue = searchParams.get("assessment");
    if (
      assessmentQueryValue === "undefined" ||
      assessmentQueryValue === "null" ||
      assessmentQueryValue === ""
    ) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete("assessment");
      setSearchParams(newSearchParams, { replace: true });
    }
  }, [searchParams, setSearchParams]);

  const loadPaymentData = useCallback(
    async (currentPropertyId: string | undefined) => {
      if (!currentPropertyId) {
        setError("Property ID is missing. Cannot load payment data.");
        setLoading(false);
        return;
      }

      setLoading(true);
      setError("");

      const assessmentIdFromQuery = searchParams.get("assessment");
      let rawFetchedAssessment: RawBackendAssessment | null = null;

      try {
        // Corrected: Use propertyService.getById for fetching property details
        const propertyDetails = await propertyService.getById(
          currentPropertyId
        );
        setProperty(propertyDetails);

        // Attempt to fetch specific assessment if ID is provided in URL
        if (assessmentIdFromQuery && isValidUUID(assessmentIdFromQuery)) {
          const specificAssessment = await assessmentService.getById(
            assessmentIdFromQuery
          );
          // Verify that the fetched assessment belongs to the current property
          if (
            specificAssessment &&
            specificAssessment.propertyId === currentPropertyId
          ) {
            rawFetchedAssessment = specificAssessment as RawBackendAssessment;
          }
        }

        // If no valid specific assessment was found (or none was requested), try to get the latest pending assessment
        // Check if assessment ID is valid, handling potential undefined values
        const assessmentIdToCheck =
          rawFetchedAssessment?.assessmentId || rawFetchedAssessment?.id || "";
        if (!rawFetchedAssessment || !isValidUUID(assessmentIdToCheck)) {
          // Fetching all assessments for property to find the latest pending as fallback.
          // Ensure currentPropertyId is a string before passing to the API
          const propertyIdForApi =
            typeof currentPropertyId === "string" ? currentPropertyId : "";
          const allAssessmentsForProperty =
            await assessmentService.getByProperty(propertyIdForApi);

          if (
            allAssessmentsForProperty &&
            allAssessmentsForProperty.length > 0
          ) {
            const pendingAssessments = allAssessmentsForProperty
              .filter((asm) => asm.paymentStatus === "Pending")
              .sort(
                (a, b) =>
                  new Date(b.assessmentDate).getTime() -
                  new Date(a.assessmentDate).getTime()
              );

            if (pendingAssessments.length > 0) {
              const latestPendingAssessment = pendingAssessments[0];
              rawFetchedAssessment =
                latestPendingAssessment as RawBackendAssessment;
            }
          }
        }

        if (rawFetchedAssessment) {
          const mappedAssessment: Assessment = {
            ...rawFetchedAssessment,
            // Ensure id is a string, using empty string as fallback if both are undefined
            id: (
              rawFetchedAssessment.assessmentId ||
              rawFetchedAssessment.id ||
              ""
            ).toString(),
          };

          if (!mappedAssessment.id || !isValidUUID(mappedAssessment.id)) {
            setError(
              "Assessment data is corrupt. Please return to the property page and select an assessment."
            );
            setTimeout(() => navigate(`/property/${currentPropertyId}`), 3000);
          } else {
            setAssessment(mappedAssessment);
          }
        } else {
          setError(
            "No pending assessments found for this property. Please check the property details page or contact support if assessments are expected."
          );
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : "An unknown error occurred during payment.";
        setError(
          `Payment failed: ${errorMessage}. Please try again or contact support.`
        );
      } finally {
        setLoading(false);
      }
    },
    [navigate, searchParams, setError, setLoading, setProperty, setAssessment]
  ); // Removed assessmentIdFromQuery, propertyId as they are accessed within or stable

  useEffect(() => {
    // propertyId (as 'id') comes from useParams and is stable for this route instance
    loadPaymentData(id);
  }, [id, loadPaymentData, searchParams]); // id (propertyId) is used, loadPaymentData and searchParams are its key deps

  // Define handlePayment using useCallback to avoid dependency issues
  const handlePayment = useCallback(async () => {
    if (!assessment) {
      setError("No assessment data available. Please refresh the page.");
      return;
    }

    setProcessing(true);
    setError("");

    try {
      // Ensure the assessment has a valid ID before proceeding
      if (
        !assessment.id ||
        assessment.id === "undefined" ||
        assessment.id === "null"
      ) {
        setError(
          "Invalid assessment ID. Please return to the property page and try again."
        );
        setProcessing(false);

        // Redirect back to property page
        setTimeout(() => {
          navigate(`/property/${id}`);
        }, 2000);

        return;
      }

      // Validate GUID format using a regex pattern
      const guidPattern =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!guidPattern.test(assessment.id)) {
        setError(
          "Invalid assessment ID format. Please return to the property page and select a valid assessment."
        );
        setProcessing(false);
        return; // Stop processing if ID is not a valid GUID
      }

      // Format the payment data according to our DirectPaymentDto
      const paymentData = {
        assessmentId: assessment.id,
        paymentMethod: paymentMethod, // 'card', 'bank', or 'digital'
      };

      console.log("Sending payment data:", paymentData);

      // Use the payment service instead of direct API call
      const response = await paymentService.processDirectPayment(
        assessment.id,
        paymentMethod
      );

      console.log("Payment response:", response);

      // Store the payment ID for receipt download
      if (response.paymentId) {
        localStorage.setItem("lastPaymentId", response.paymentId);
        // Store transaction ID for display
        setTransactionId(`TXN_${Date.now()}`);
      } else {
        setTransactionId(`TXN_${Date.now()}`);
      }

      setPaymentSuccess(true);
    } catch (error: unknown) {
      setError(
        error instanceof Error
          ? error.message
          : "Payment processing failed. Please try again."
      );
    } finally {
      setProcessing(false);
    }
  }, [
    assessment,
    id,
    navigate,
    paymentMethod,
    setError,
    setPaymentSuccess,
    setProcessing,
  ]);

  // Effect to attempt payment again if assessment is updated during payment processing
  const [retryPayment, setRetryPayment] = useState(false);

  // Add effect to retry payment after assessment ID is fixed
  useEffect(() => {
    if (retryPayment && assessment && assessment.id && !processing) {
      setRetryPayment(false);
      setTimeout(() => {
        handlePayment();
      }, 200); // Small delay to ensure state is fully updated
    }
  }, [assessment, retryPayment, processing, handlePayment]);

  const formatCurrency = (amount: number) => {
    return `NPR ${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <span className="loading loading-spinner loading-lg text-blue-500"></span>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="alert alert-success">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div>
            <h2 className="text-2xl font-bold mb-2">Payment Successful!</h2>
            <p className="mb-6">
              Your tax payment has been processed successfully. You should
              receive a confirmation receipt shortly.
            </p>
          </div>
        </div>

        {assessment && (
          <div className="card bg-base-100 shadow-xl mt-6">
            <div className="card-body">
              <h3 className="card-title mb-3">Payment Details</h3>
              <div className="space-y-2 text-sm">
                {/* Location Information */}
                <div className="pb-3 mb-2 border-b border-base-300">
                  <h4 className="font-medium text-base-content mb-2">
                    Location Details
                  </h4>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    <div>
                      <span className="text-base-content/70 block">
                        Province:
                      </span>
                      <span className="text-base-content">
                        {property?.province || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        District:
                      </span>
                      <span className="text-base-content">
                        {property?.district || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Municipality:
                      </span>
                      <span className="text-base-content font-medium">
                        {property?.municipalityName}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Ward Number:
                      </span>
                      <span className="text-base-content font-medium">
                        {property?.wardNumber || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Street/Tole:
                      </span>
                      <span className="text-base-content">
                        {property?.street || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Parcel Number:
                      </span>
                      <span className="text-base-content font-medium">
                        {property?.parcelNumber || "Not specified"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Payment Details */}
                <h4 className="font-medium text-base-content mb-2">
                  Payment Details
                </h4>
                <div className="flex justify-between">
                  <span className="text-base-content/70">
                    Property Address:
                  </span>
                  <span className="text-base-content">{property?.address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">Assessment Year:</span>
                  <span className="text-base-content">
                    {assessment.assessmentYear}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">Amount Paid:</span>
                  <span className="font-semibold text-base-content">
                    {formatCurrency(assessment.taxAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">Transaction ID:</span>
                  <span className="text-base-content">{transactionId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">Payment Date:</span>
                  <span className="text-base-content">
                    {formatDate(new Date().toISOString())}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-wrap justify-center gap-3 mt-6">
          <Link to="/properties" className="btn btn-primary">
            Back to Properties
          </Link>
          <button onClick={() => window.print()} className="btn btn-secondary">
            Print Receipt
          </button>
          <button
            onClick={async () => {
              const paymentId = localStorage.getItem("lastPaymentId");
              if (paymentId) {
                try {
                  const receiptData = await paymentService.getReceipt(
                    paymentId
                  );

                  let blob;
                  if (typeof receiptData === "string") {
                    blob = new Blob([receiptData], { type: "text/html" });
                  } else if (receiptData instanceof Blob) {
                    blob = receiptData;
                  } else {
                    if (typeof receiptData === "object" && receiptData !== null && 'url' in receiptData && typeof (receiptData as { url: string }).url === 'string') {
                      window.open((receiptData as { url: string }).url, "_blank");
                      return;
                    }
                    // console.error('Unexpected receipt data format:', receiptData); // Keep for now, might be useful if API changes
                    setError(
                      "Failed to download receipt due to unexpected data format."
                    );
                    return;
                  }

                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement("a");
                  a.href = url;
                  const filename =
                    blob.type === "application/pdf"
                      ? `receipt-${paymentId}.pdf`
                      : `receipt-${paymentId}.html`;
                  a.download = filename;
                  document.body.appendChild(a);
                  a.click();
                  window.URL.revokeObjectURL(url);
                  document.body.removeChild(a);
                } catch (receiptError) {
                  setError(
                    `Failed to download receipt: ${
                      receiptError instanceof Error
                        ? receiptError.message
                        : "Unknown error"
                    }`
                  );
                }
              } else {
                setError(
                  "No payment ID found for receipt download. Please pay first or check local storage."
                );
              }
            }}
            className="btn btn-secondary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
            Download Receipt
          </button>
        </div>
      </div>
    );
  }

  if (error && !assessment) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="alert alert-error">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div>
            <h2 className="text-xl font-semibold mb-2">
              Unable to Load Payment Information
            </h2>
            <p>{error}</p>
            <Link to="/properties" className="btn btn-primary mt-4">
              Back to Properties
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="alert alert-warning">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <div>
            <h2 className="text-xl font-semibold mb-2">
              No Pending Tax Payment
            </h2>
            <p>There are no pending tax payments for this property.</p>
            <Link to="/properties" className="btn btn-primary mt-4">
              Back to Properties
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-base-content mb-6">
        Property Tax Payment
      </h1>

      {error && (
        <div className="alert alert-error mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {property && assessment && (
        <>
          <div className="card bg-base-100 shadow-xl mb-6">
            <div className="card-body">
              <h2 className="card-title mb-4">Property Information</h2>
              <div className="space-y-2 mb-4">
                {/* Location Information */}
                <div className="border-b border-base-300 pb-3 mb-3">
                  <h3 className="text-md font-semibold text-base-content mb-2">
                    Location Details
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <span className="text-base-content/70 block">
                        Province:
                      </span>
                      <span className="text-base-content">
                        {property.province || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        District:
                      </span>
                      <span className="text-base-content">
                        {property.district || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Municipality:
                      </span>
                      <span className="text-base-content">
                        {property.municipalityName}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Ward Number:
                      </span>
                      <span className="text-base-content">
                        {property.wardNumber || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Street/Tole:
                      </span>
                      <span className="text-base-content">
                        {property.street || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-base-content/70 block">
                        Parcel Number (Kitta No):
                      </span>
                      <span className="text-base-content">
                        {property.parcelNumber || "Not specified"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Property Details */}
                <div>
                  <h3 className="text-md font-semibold text-base-content mb-2">
                    Property Details
                  </h3>
                  <div className="flex justify-between">
                    <span className="text-base-content/70">Address:</span>
                    <span className="text-base-content">
                      {property.address}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-base-content/70">Usage Type:</span>
                    <span className="text-base-content">
                      {property.usageType}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-base-content/70">Land Area:</span>
                    <span className="text-base-content">
                      {property.landAreaSqM.toLocaleString()} sq.m
                    </span>
                  </div>
                  {property.builtUpArea && (
                    <div className="flex justify-between">
                      <span className="text-base-content/70">
                        Built-up Area:
                      </span>
                      <span className="text-base-content">
                        {property.builtUpArea.toLocaleString()} sq.m
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl mb-6">
            <div className="card-body">
              <h2 className="card-title mb-4">Assessment Details</h2>
              <div className="space-y-2 mb-4">
                <div className="flex justify-between">
                  <span className="text-base-content/70">Assessment Year:</span>
                  <span className="text-base-content">
                    {assessment.assessmentYear}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">Assessed Value:</span>
                  <span className="text-base-content">
                    {formatCurrency(assessment.finalAssessedValue)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70 font-medium">
                    Tax Amount:
                  </span>
                  <span className="text-base-content font-bold">
                    {formatCurrency(assessment.taxAmount)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="card bg-base-100 shadow-xl mb-6">
            <div className="card-body">
              <h2 className="card-title mb-4">Payment Method</h2>
              <div className="space-y-4">
                <div className="form-control">
                  <label className="label cursor-pointer justify-start">
                    <input
                      type="radio"
                      className="radio radio-primary"
                      name="paymentMethod"
                      value="card"
                      checked={paymentMethod === "card"}
                      onChange={() => setPaymentMethod("card")}
                    />
                    <span className="label-text ml-2">Credit/Debit Card</span>
                  </label>
                  {paymentMethod === "card" && (
                    <div className="ml-6 space-y-3 mt-3">
                      <div className="form-control">
                        <label className="label" htmlFor="cardNumber">
                          <span className="label-text">Card Number</span>
                        </label>
                        <input
                          type="text"
                          id="cardNumber"
                          className="input input-bordered w-full"
                          placeholder="1234 5678 9012 3456"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="form-control">
                          <label className="label" htmlFor="expiry">
                            <span className="label-text">Expiry Date</span>
                          </label>
                          <input
                            type="text"
                            id="expiry"
                            className="input input-bordered w-full"
                            placeholder="MM/YY"
                          />
                        </div>
                        <div className="form-control">
                          <label className="label" htmlFor="cvc">
                            <span className="label-text">CVC</span>
                          </label>
                          <input
                            type="text"
                            id="cvc"
                            className="input input-bordered w-full"
                            placeholder="123"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer justify-start">
                    <input
                      type="radio"
                      className="radio radio-primary"
                      name="paymentMethod"
                      value="bank"
                      checked={paymentMethod === "bank"}
                      onChange={() => setPaymentMethod("bank")}
                    />
                    <span className="label-text ml-2">Bank Transfer</span>
                  </label>
                  {paymentMethod === "bank" && (
                    <div className="ml-6 space-y-3 mt-3">
                      <div className="form-control">
                        <label className="label" htmlFor="accountNumber">
                          <span className="label-text">Account Number</span>
                        </label>
                        <input
                          type="text"
                          id="accountNumber"
                          className="input input-bordered w-full"
                          placeholder="Enter your account number"
                        />
                      </div>
                      <div className="form-control">
                        <label className="label" htmlFor="bankName">
                          <span className="label-text">Bank Name</span>
                        </label>
                        <select
                          id="bankName"
                          className="select select-bordered w-full"
                          defaultValue=""
                        >
                          <option value="" disabled>
                            Select your bank
                          </option>
                          <option value="nepal-bank">Nepal Bank Limited</option>
                          <option value="nabil">Nabil Bank</option>
                          <option value="nic-asia">NIC Asia Bank</option>
                          <option value="global-ime">Global IME Bank</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </div>
                  )}
                </div>

                <div className="form-control">
                  <label className="label cursor-pointer justify-start">
                    <input
                      type="radio"
                      className="radio radio-primary"
                      name="paymentMethod"
                      value="digital"
                      checked={paymentMethod === "digital"}
                      onChange={() => setPaymentMethod("digital")}
                    />
                    <span className="label-text ml-2">Digital Wallet</span>
                  </label>
                  {paymentMethod === "digital" && (
                    <div className="ml-6 space-y-3 mt-3">
                      <div className="flex space-x-4">
                        <button className="btn btn-outline btn-sm">
                          eSewa
                        </button>
                        <button className="btn btn-outline btn-sm">
                          Khalti
                        </button>
                        <button className="btn btn-outline btn-sm">
                          Connect IPS
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link to="/properties" className="btn btn-secondary">
              Cancel
            </Link>
            <button
              className="btn btn-primary"
              onClick={handlePayment}
              disabled={processing}
            >
              {processing ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  Processing...
                </>
              ) : (
                `Pay ${assessment ? formatCurrency(assessment.taxAmount) : ""}`
              )}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default TaxPayment;
