using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LandTaxSystem.Core.DTOs.PreliminaryAssessment
{
    public class PreliminaryAssessmentCreateDto
    {
        [Required]
        [MaxLength(50)]
        public string TaxpayerRegistration { get; set; } = string.Empty;

        [Required]
        public string TaxpayerName { get; set; } = string.Empty;

        [MaxLength(255)]
        public string? Address { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(50)]
        public string? AccountNumber { get; set; }

        [Required]
        public DateTime AssessmentPeriodFrom { get; set; }

        [Required]
        public DateTime AssessmentPeriodTo { get; set; }

        [MaxLength(100)]
        public string? ActSection { get; set; }

        [MaxLength(100)]
        public string? Rule { get; set; }

        [MaxLength(100)]
        public string? Bank { get; set; }

        [MaxLength(100)]
        public string? Branch { get; set; }

        [MaxLength(500)]
        public string? ReasonForAssessment { get; set; }

        [MaxLength(50)]
        public string? AppealNumber { get; set; }

        [MaxLength(500)]
        public string? OtherReasonDescription { get; set; }

        public DateTime? InterestCalculationDate { get; set; }

        public DateTime? PreliminaryAssessmentDate { get; set; }

        public string? Reason { get; set; }

        public string? Regulations { get; set; }

        [Required]
        public Guid MunicipalityId { get; set; }
        
        public Guid? ReturnFilingId { get; set; }

        public List<PreliminaryAssessmentDetailDto> TaxDetails { get; set; } = new List<PreliminaryAssessmentDetailDto>();

        [EmailAddress]
        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(20)]
        public string? MobileNumber { get; set; }

        [MaxLength(50)]
        public string? AssessmentPeriod { get; set; }
    }
    
    public class PreliminaryAssessmentUpdateDto
    {
        [Required]
        [MaxLength(50)]
        public string TaxpayerRegistration { get; set; } = string.Empty;

        [Required]
        public string TaxpayerName { get; set; } = string.Empty;

        [MaxLength(255)]
        public string? Address { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(50)]
        public string? AccountNumber { get; set; }

        [Required]
        public DateTime AssessmentPeriodFrom { get; set; }

        [Required]
        public DateTime AssessmentPeriodTo { get; set; }

        [MaxLength(100)]
        public string? ActSection { get; set; }

        [MaxLength(100)]
        public string? Rule { get; set; }

        [MaxLength(100)]
        public string? Bank { get; set; }

        [MaxLength(100)]
        public string? Branch { get; set; }

        [MaxLength(500)]
        public string? ReasonForAssessment { get; set; }

        [MaxLength(50)]
        public string? AppealNumber { get; set; }

        [MaxLength(500)]
        public string? OtherReasonDescription { get; set; }

        public DateTime? InterestCalculationDate { get; set; }

        public DateTime? PreliminaryAssessmentDate { get; set; }

        public string? Reason { get; set; }

        public string? Regulations { get; set; }

        [Required]
        public Guid MunicipalityId { get; set; }
        
        public Guid? ReturnFilingId { get; set; }

        public List<PreliminaryAssessmentDetailDto> TaxDetails { get; set; } = new List<PreliminaryAssessmentDetailDto>();

        [EmailAddress]
        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(20)]
        public string? MobileNumber { get; set; }

        [MaxLength(50)]
        public string? AssessmentPeriod { get; set; }
    }
    
    public class PreliminaryAssessmentDetailDto
    {
        public int SerialNumber { get; set; }

        [MaxLength(50)]
        public string FilingPeriod { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? Period { get; set; }

        [MaxLength(20)]
        public string TaxYear { get; set; } = string.Empty;

        [Range(0, double.MaxValue)]
        public decimal AssessedAmount { get; set; }

        [Range(0, double.MaxValue)]
        public decimal Penalty { get; set; }

        [Range(0, double.MaxValue)]
        public decimal AdditionalAmount { get; set; }

        [Range(0, double.MaxValue)]
        public decimal Interest { get; set; }

        public decimal Total { get; set; }
    }
    
    public class PreliminaryAssessmentResponseDto
    {
        public Guid Id { get; set; }
        public string TaxpayerRegistration { get; set; } = string.Empty;
        public string TaxpayerName { get; set; } = string.Empty;
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? AccountNumber { get; set; }
        public DateTime AssessmentPeriodFrom { get; set; }
        public DateTime AssessmentPeriodTo { get; set; }
        public string? ActSection { get; set; }
        public string? Rule { get; set; }
        public string? Bank { get; set; }
        public string? Branch { get; set; }
        public string? ReasonForAssessment { get; set; }
        public string? AppealNumber { get; set; }
        public string? OtherReasonDescription { get; set; }
        public DateTime? InterestCalculationDate { get; set; }
        public DateTime? PreliminaryAssessmentDate { get; set; }
        public string? Reason { get; set; }
        public string? Regulations { get; set; }
        public Guid MunicipalityId { get; set; }
        public Guid? ReturnFilingId { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
        public List<PreliminaryAssessmentDetailDto> TaxDetails { get; set; } = new List<PreliminaryAssessmentDetailDto>();
        public decimal GrandTotal { get; set; }
        public string? Email { get; set; }
        public string? MobileNumber { get; set; }
        public string? AssessmentPeriod { get; set; }
    }
    
    public class PreliminaryAssessmentListDto
    {
        public Guid Id { get; set; }
        public string TaxpayerRegistration { get; set; } = string.Empty;
        public Guid MunicipalityId { get; set; }
        public Guid? ReturnFilingId { get; set; }
        public string TaxpayerName { get; set; } = string.Empty;
        public DateTime AssessmentPeriodFrom { get; set; }
        public DateTime AssessmentPeriodTo { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public decimal GrandTotal { get; set; }
    }
}
