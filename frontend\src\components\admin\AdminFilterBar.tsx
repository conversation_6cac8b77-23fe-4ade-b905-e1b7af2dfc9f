import React, { type ReactNode } from 'react';
import AdminSearchBar from './AdminSearchBar';

interface FilterOption {
  label: string;
  value: string;
}

interface AdminFilterBarProps {
  onSearch?: (value: string) => void;
  searchPlaceholder?: string;
  searchValue?: string;
  filters?: {
    name: string;
    options: FilterOption[];
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
  }[];
  actions?: ReactNode;
  className?: string;
}

const AdminFilterBar: React.FC<AdminFilterBarProps> = ({
  onSearch,
  searchPlaceholder,
  searchValue = '',
  filters = [],
  actions,
  className = '',
}) => {
  return (
    <div className={`card bg-base-100 shadow-sm ${className}`}>
      <div className="card-body p-4">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          {/* Search */}
          {onSearch && (
            <div className="w-full md:w-auto md:flex-1">
              <AdminSearchBar
                onSearch={onSearch}
                placeholder={searchPlaceholder}
                initialValue={searchValue}
              />
            </div>
          )}

          {/* Filters */}
          {filters.map((filter) => (
            <div key={filter.name} className="w-full md:w-auto">
              <select
                name={filter.name}
                value={filter.value}
                onChange={(e) => filter.onChange(e.target.value)}
                className="select select-bordered w-full"
              >
                {filter.placeholder && (
                  <option value="">{filter.placeholder}</option>
                )}
                {filter.options.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          ))}

          {/* Actions */}
          {actions && (
            <div className="flex justify-end md:ml-auto">
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminFilterBar;